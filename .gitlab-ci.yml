include:
  - project: 'devops/templates'
    ref: master
    file: '/.front-admin-gitlab-ci.yaml'

build_pack:
  stage: build
  variables:
     BUILD_ART_PATH: bundler
     BUILD_CMD: |-
       master: npm run build
       dev*: npm run build:dev
       test*: npm run build:test
  tags:
  - k8s-front
  artifacts:
    expire_in: 7 days
    name: "$CI_PROJECT_NAME-$CI_COMMIT_BRANCH"
    paths:
    - project.zip
  script:
  - /init.sh 