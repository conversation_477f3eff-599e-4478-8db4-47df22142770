/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form','simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form,Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 600,
            store: 'jiakao-misc!auth-listener-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '用户昵称：',
                    dataIndex: 'nickName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '用户昵称',
                    check: 'required'

                },
                {
                    header: '被监听的手机号：',
                    dataIndex: 'phone',
                    xtype: 'text',
                    placeholder: '被监听的手机号',
                    check: 'required'
                },
                // {
                //     header: '监听的类型：',
                //     dataIndex: 'type',
                //     store: 'jiakao-misc!auth-listener-config/data/onType',
                //     xtype: 'checkbox',
                //     index: [
                //         {
                //             key: 'key', value: 'value'
                //         }
                //     ],
                //     check: 'required'
                // },
                // {
                //     header: '监听的状态：',
                //     dataIndex: 'active',
                //     xtype: 'radio',
                //     store: [
                //         {
                //             key: true,
                //             value: '是'
                //         },
                //         {
                //             key: false,
                //             value: '否'
                //         }
                //     ]
                // },
                {
                    header: '接收者手机号：',
                    dataIndex: 'notifyPhone',
                    xtype: 'text',
                    placeholder: '接收者手机号',
                },
                {
                    header: '接收者SSO账号：',
                    check: 'required',
                    dataIndex: 'notifyUserId',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!auth-listener-config/data/allList?id=1',
                        placeholder: '接收者SSO账号',
                        dataIndex: 'notifyUserId',
                        index: {

                            key: 'userId',
                            value: 'nickname',
                            search: 'username'
                        },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                    maxlength: 1024,
                    placeholder: '接收者SSO账号'
                },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'auth-listener-config列表',
            title: 'auth-listener-config列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 600,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!auth-listener-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户昵称：',
                            dataIndex: 'nickName'
                        },
                        {
                            header: '被监听的手机号：',
                            dataIndex: 'phone'
                        },
                        {
                            header: '监听的类型：',
                            dataIndex: 'type'
                        },
                        {
                            header: '监听的状态：',
                            render: function (data) {
                              return data?'是':'否'
                            },
                            dataIndex: 'active'
                        },
                        {
                            header: '接收者手机号：',
                            dataIndex: 'notifyPhone'
                        },
                        {
                            header: '接收者SSO账号',
                            dataIndex: 'nameList'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },


                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 600,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!auth-listener-config/data/view',
                        save: 'jiakao-misc!auth-listener-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '用户昵称：',
                            dataIndex: 'nickName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '用户昵称',
                            check: 'required'
                        },
                        {
                            header: '被监听的手机号：',
                            dataIndex: 'phone',
                            xtype: 'text',
                            placeholder: '被监听的手机号',
                            check: 'required'
                        },
                        // {
                        //     header: '接收者手机号：',
                        //     dataIndex: 'type',
                        //     store: 'jiakao-misc!auth-listener-config/data/onType',
                        //     xtype: 'checkbox',
                        //     check:'required',
                        //     index: [
                        //         {
                        //             key: 'key', value: 'value'
                        //         }
                        //     ],
                        //
                        // },
                        {
                            header: '接收者手机号：',
                            dataIndex: 'notifyPhone',
                            xtype: 'text',
                            placeholder: '接收者手机号',
                        },
                        {
                            header: '接收者SSO账号：',
                            check: 'required',
                            dataIndex: 'notifyUserId',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: 'jiakao-misc!auth-listener-config/data/allList?id=1',
                                placeholder: '接收者SSO账号',
                                dataIndex: 'notifyUserId',
                                index: {

                                    key: 'userId',
                                    value: 'nickname',
                                    search: 'username'
                                },
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            }),
                            maxlength: 1024,
                            placeholder: '接收者SSO账号'
                        }


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!auth-listener-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户昵称',
                    dataIndex: 'nickName'
                },
                {
                    header: '被监听的手机号',
                    dataIndex: 'phone'
                },
                // {
                //     header: '监听的类型',
                //     dataIndex: 'type'
                // },
                {
                    header: '接收者手机号',
                    dataIndex: 'notifyPhone'
                },
                {
                    header: '接收者SSO账号',
                    dataIndex: 'nameList'
                },
                {
                    header: '监听的状态',
                    render: function (data) {
                        return data?'开启':'关闭'
                    },
                    dataIndex: 'active'
                },

                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                // {
                //     header: 'createUserId',
                //     dataIndex: 'createUserId'
                // },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                // {
                //     header: '修改时间',
                //     render: function (data) {
                //         return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                //     },
                //     dataIndex: 'updateTime'
                // },
                // {
                //     header: 'updateUserId',
                //     dataIndex: 'updateUserId'
                // },
                // {
                //     header: '修改人',
                //     dataIndex: 'updateUserName'
                // }

            ]
        }, ['jiakao-misc!auth-listener-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
