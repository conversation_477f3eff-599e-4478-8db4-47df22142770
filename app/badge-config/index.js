/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
  "simple!core/template",
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
  "simple!core/form",
  'jiakao-misc!app/common/constants',
  "simple!core/plugin",
  'jiakao-misc!app/common/tiku',

], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU) {
  var carTypeArr = [];

  for (var k in TIKU) {
    carTypeArr.push({
      key: k,
      value: TIKU[k]
    })
  }
  var shareTypeMap = {
    0:'默认',
    1:'分享页'
  }
  var shareTypeArray = []
  for (var k in shareTypeMap) {
    shareTypeArray.push({
      key: k,
      value: shareTypeMap[k]
    })
  }
  var addRule = function (table, dom, data) {
    Store([`jiakao-misc!badge-rule/data/view?badgeConfigId=${data.id}`]).load().done((retData) => {
      const lineData = retData.data['badge-rule'].data.view.data || {};
      Table({
        success: function (obj, dialog, e) {
          dialog.close();
          obj.render();
        },
        store: 'jiakao-misc!badge-rule/data/save?badgeConfigId=' + data.id + (lineData.id ? '&id=' + lineData.id : ''),
        columns: [
          {
            header: '科目：',
            dataIndex: 'kemu',
            xtype: 'select',
            check: 'required',
            store: Constants.kemuStore,
            value: lineData.kemu,
            placeholder: '科目'
          },
          {
            header: '场景',
            dataIndex: 'scene',
            xtype: 'select',
            check: 'required',
            value: lineData.scene,
            store: [{
              key: 1,
              value: '做题'
            }, {
              key: 2,
              value: '考试'
            }],
          },
          {
            header: '规则：',
            dataIndex: 'rule',
            xtype: 'textarea',
            maxlength: 512,
            value: lineData.rule,
            placeholder: '  规则'
          },
          {
            header: '  跳转链接：',
            dataIndex: 'jumpUrl',
            xtype: 'textarea',
            maxlength: 512,
            value: lineData.jumpUrl,
            placeholder: '  跳转链接'
          }]
      }).add();
    })
  }
  var add = function (table) {
    Table({
      title: "添加",
      width: 500,
      store: "jiakao-misc!badge-config/data/insert",
      success: function (obj, dialog) {
        dialog.close();
        table.render();
      },
      renderAfter: function (table, dom, data) {
        $(dom).item('preload-group').append('<div class="col-sm-3 control-label"></div><div class="col-sm-8" style="margin-top: 10px;padding: 10px;background-color:#ffecd1;"><span style="display:inline-block;width:20px;height:20px;background:url(http://exam-room.mc-cdn.cn/exam-room/2023/11/13/19/7ed80e7c1c0846b7bb746544a2e50ed8.png) no-repeat center center/cover"></span>危险操作提示：通常仅做题场景下的徽章需要预下载，若你不了解此项含义，请不要将此项改到“是”，这很可能导致CDN成本激增！！！</div>');
      },
      columns: [
        {
          header: " 徽章code：",
          dataIndex: "code",
          xtype: "text",
          maxlength: 32,
          check: "required",
          placeholder: " 徽章code",
        },
        {
          header: " 徽章类型：",
          dataIndex: "badgeType",
          xtype: "checkbox",
          store: [{ key: 1, value: '闯关成就' }, { key: 2, value: '答题成就' }, { key: 3, value: '特殊成就' }, { key: 4, value: '练车生涯' }, { key: 5, value: '科目一' }, { key: 6, value: '科目二' }, { key: 7, value: '科目三' }, { key: 8, value: '科目四' }],
          check: "required",
        },
        {
          header: "分享活动Id：",
          dataIndex: "shareActivityId",
          xtype: "text",
          placeholder: "分享活动Id",
        },
        {
          header: "分享类型:",
          dataIndex: "shareType",
          xtype: "select",
          store: [
            ...shareTypeArray
          ]
        },
        {
          header: "车型:",
          dataIndex: "carType",
          xtype: "checkbox",
          store: [].concat(carTypeArr)
        },
        {
          header: " 徽章名：",
          dataIndex: "badgeName",
          xtype: "text",
          maxlength: 32,
          check: "required",
          placeholder: " 徽章名",
        },
        {
          header: " 解锁文案：",
          dataIndex: "unlockMessage",
          xtype: "text",
          maxlength: 32,
          check: "required",
          placeholder: " 解锁文案",
        },
        {
          header: "默认图标：",
          dataIndex: "image",
          xtype: Plugin(
            "jiakao-misc!upload",
            {
              dataIndex: "image",
              uploadIndex: "image",
              bucket: "exam-room",
              isSingle: true,
              maxFileSize: 50,
              placeholder: "请选择上传文件",
              url: "simple-upload3://upload/file.htm",
            },
            function () {
              console.log(arguments);
            }
          ),
        },

        {
          header: "触发图标：",
          dataIndex: "triggerImage",
          xtype: Plugin(
            "jiakao-misc!upload",
            {
              dataIndex: "triggerImage",
              uploadIndex: "triggerImage",
              bucket: "exam-room",
              isSingle: true,
              placeholder: "请选择上传文件",
              url: "simple-upload3://upload/file.htm",
            },
            function () {
              console.log(arguments);
            }
          ),
        },
        {
          header: "达成gif图：",
          dataIndex: "triggeringImage",
          xtype: Plugin(
            "jiakao-misc!upload",
            {
              dataIndex: "triggeringImage",
              uploadIndex: "triggeringImage",
              bucket: "exam-room",
              isSingle: true,
              maxFileSize: 1500,
              placeholder: "请选择上传文件",
              url: "simple-upload3://upload/file.htm",
            },
            function () {
              console.log(arguments);
            }
          ),
        },
        {
          header: "模型文件",
          data: 'animationImage',
          xtype: Plugin(
            "jiakao-misc!upload3",
            {
              dataIndex: "animationImage",
              uploadIndex: "animationImage",
              bucket: "exam-room",
              type: "file",
              isSingle: true,
              placeholder: "请选择上传文件",
              url: "simple-upload3://upload/file.htm",
            },
            function () {
              console.log(arguments);
            }
          ),
        },
        {
          header: "开关",
          dataIndex: "status",
          xtype: "radio",
          store: [
            {
              key: 1,
              value: "是",
            },
            {
              key: 0,
              value: "否",
            },
          ],
        },
        {
          header: "是否预下载",
          dataIndex: "preload",
          xtype: "radio",
          value: false,
          store: [
            {
              key: true,
              value: "是",
            },
            {
              key: false,
              value: "否",
            },
          ],
        },
        {
          header: " 成就中心展示顺序：",
          dataIndex: "sort",
          xtype: "text",
          check: "required",
          placeholder: " 成就中心展示顺序",
        },
        {
          header: "优先级：",
          dataIndex: "priority",
          xtype: "text",
          check: "required",
          placeholder: " 优先级",
        },
        {
          header: '成就达成表达式',
          dataIndex: "eventExpression",
          xtype: "textarea",
          placeholder: " 成就达成表达式",
        },
        {
          header: " 触发规则描述：",
          dataIndex: "description",
          xtype: "text",
          maxlength: 128,
          check: "required",
          placeholder: " 触发规则描述",
        },
        {
          header: "未达成是否可见",
          dataIndex: "visible",
          xtype: "radio",
          store: [{ key: true, value: '成就中心可见' }, { key: false, value: '成就中心不可见' }]
        },
        {
          header: "是否周期性发放",
          dataIndex: "repeatEarn",
          xtype: "radio",
          value: false,
          store: [{ key: true, value: '会周期性发放' }, { key: false, value: '不会周期性发放' }]
        },
        {
          header: "去完成跳转连接",
          dataIndex: "finishJumpUrl",
          xtype: 'text'
        },
        {
          header: '配置科目对应跳转：',
          dataIndex: 'archivedUrl',
          xtype: Plugin('jiakao-misc!group', {
            dataIndex: 'archivedUrl'
          }, function (plugin, value) {

          })
        },

      ],
    }).add();
  };

  let modelUrl = '';
  var list = function (panel) {
    Table(
      {
        description: "徽章列表",
        title: "徽章列表",
        search: [{
          dataIndex: 'code',
          xtype: 'text',
          placeholder: '徽章code'
        }, {
          dataIndex: 'badgeName',
          xtype: 'text',
          placeholder: '徽章名称'
        }],
        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "添加",
              class: "primary",
              click: add,
            },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
          ],
        },
        operations: [
          {
            name: "查看",
            xtype: "view",
            width: 400,
            class: "success",
            title: "查看",
            store: "jiakao-misc!badge-config/data/view",
            columns: [
              {
                header: "#",
                dataIndex: "id",
              },
              {
                header: " 徽章code：",
                dataIndex: "code",
              },
              {
                header: " 徽章类型 1 闯关成就 2 答题成就 3 特殊成就：",
                dataIndex: "badgeType",
              },
              {
                header: "车型：",
                dataIndex: "carType",
                render: function (data) {
                  let text = [];
                  data = data.split(',');
                  data.forEach(item => {
                    text.push(TIKU[item]);
                  })
                  return text + ''
                }
              },
              {
                header: " 徽章名：",
                dataIndex: "badgeName",
              },
              {
                header: " 解锁文案：",
                dataIndex: "unlockMessage",
              },
              {
                header: " 默认图标",
                dataIndex: "image",
                render: function (data) {
                  return `<img src="${data}" style="width:100px">`;
                },
              },
              {
                header: " 触发图标",
                dataIndex: "triggerImage",
                render: function (data) {
                  return `<img src="${data}" style="width:100px">`;
                },
              },
              {
                header: " 开关 0 关  1 开：",
                render: function (data) {
                  if (data) {
                    return "是";
                  } else {
                    return "否";
                  }
                },
                dataIndex: "status",
              },
              {
                header: " 成就中心展示顺序：",
                dataIndex: "sort",
              },
              {
                header: "优先级",
                dataIndex: "priority"
              },
              {
                header: '成就达成表达式',
                dataIndex: "eventExpression",
                render: function () {
                  return `<a>点击查看</a>`
                },
                click: function (table, row, lineData) {
                  Widgets.dialog.html('成就达成表达式', {}).done(function (dialog) {
                    var data = lineData.eventExpression && JSON.stringify(JSON.parse(lineData.eventExpression), null, 4)
                    $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                  })
                }
              },
              {
                header: " 触发规则描述：",
                dataIndex: "description",
              },
              {
                header: "创建时间",
                render: function (data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                },
                dataIndex: "createTime",
              },

              {
                header: "创建人",
                dataIndex: "createUserName",
              },
              {
                header: "更新时间",
                render: function (data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                },
                dataIndex: "updateTime",
              },

              {
                header: "更新人",
                dataIndex: "updateUserName",
              },

            ],
          },
          {
            name: "编辑",
            xtype: "edit",
            width: 500,
            class: "warning",
            title: "编辑",
            success: function (obj, dialog, e) {
              dialog.close();
              obj.render();
            },
            form: {
              submitHandler(form) {
                const value = $('input[name="animationImage"]').val();

                if (!value && modelUrl) {
                  return {
                    animationImage: modelUrl
                  }
                }
                return {}
              },
              reverseParam: true
            },
            renderAfter: function (table, dom, data) {

              $(dom).item('preload-group').append('<div class="col-sm-3 control-label"></div><div class="col-sm-8" style="margin-top: 10px;padding: 10px;background-color:#ffecd1;"><span style="display:inline-block;width:20px;height:20px;background:url(http://exam-room.mc-cdn.cn/exam-room/2023/11/13/19/7ed80e7c1c0846b7bb746544a2e50ed8.png) no-repeat center center/cover"></span>危险操作提示：通常仅做题场景下的徽章需要预下载，若你不了解此项含义，请不要将此项改到“是”，这很可能导致CDN成本激增！！！</div>');
              console.log(data, 'asdas');
              const animationImageVal = data.data.animationImage;
              modelUrl = animationImageVal;
              $('input[name="animationImage"]').val(animationImageVal)
              dom.item('bug-manage-value').val(animationImageVal);
              console.log(animationImageVal, dom.item('bug-manage-value').val(), 'animationImage');
            },
            store: {
              load: "jiakao-misc!badge-config/data/view",
              save: "jiakao-misc!badge-config/data/update",
            },
            columns: [
              {
                dataIndex: "id",
                xtype: "hidden",
              },
              {
                header: " 徽章code：",
                dataIndex: "code",
                xtype: "text",
                maxlength: 32,
                check: "required",
                placeholder: " 徽章code",
              },
              {
                header: " 徽章类型：",
                dataIndex: "badgeType",
                xtype: "checkbox",
                store: [{ key: '1', value: '闯关成就' }, { key: '2', value: '答题成就' }, { key: '3', value: '特殊成就' }, { key: '4', value: '练车生涯' }, { key: '5', value: '科目一' }, { key: '6', value: '科目二' }, { key: '7', value: '科目三' }, { key: '8', value: '科目四' }],
                check: "required",
              },
              {
                header: "分享活动Id：",
                dataIndex: "shareActivityId",
                xtype: "text",
                placeholder: "分享活动Id",
              },
              {
                header: "分享类型:",
                dataIndex: "shareType",
                xtype: "select",
                store: [
                  ...shareTypeArray
                ]
              },
              {
                header: "车型:",
                dataIndex: "carType",
                xtype: "checkbox",
                store: [].concat(carTypeArr)
              },
              {
                header: " 徽章名：",
                dataIndex: "badgeName",
                xtype: "text",
                maxlength: 32,
                check: "required",
                placeholder: " 徽章名",
              },
              {
                header: " 解锁文案：",
                dataIndex: "unlockMessage",
                xtype: "text",
                maxlength: 32,
                check: "required",
                placeholder: " 解锁文案",
              },
              {
                header: "默认图标：",
                dataIndex: "image",
                xtype: Plugin(
                  "jiakao-misc!upload",
                  {
                    dataIndex: "image",
                    uploadIndex: "image",
                    bucket: "exam-room",
                    isSingle: true,
                    maxFileSize: 50,
                    placeholder: "请选择上传文件",
                    url: "simple-upload3://upload/file.htm",
                  },
                  function () {
                    console.log(arguments);
                  }
                ),
              },

              {
                header: "触发图标：",
                dataIndex: "triggerImage",
                xtype: Plugin(
                  "jiakao-misc!upload",
                  {
                    dataIndex: "triggerImage",
                    uploadIndex: "triggerImage",
                    bucket: "exam-room",
                    isSingle: true,
                    placeholder: "请选择上传文件",
                    url: "simple-upload3://upload/file.htm",
                  },
                  function () {
                    console.log(arguments);
                  }
                ),
              },
              {
                header: "达成gif 图：",
                dataIndex: "triggeringImage",
                xtype: Plugin(
                  "jiakao-misc!upload",
                  {
                    dataIndex: "triggeringImage",
                    uploadIndex: "triggeringImage",
                    bucket: "exam-room",
                    isSingle: true,
                    maxFileSize: 1500,
                    placeholder: "请选择上传文件",
                    url: "simple-upload3://upload/file.htm",
                  },
                  function () {
                    console.log(arguments);
                  }
                ),
              },
              {
                header: "模型文件",
                data: 'animationImage',
                xtype: Plugin(
                  "jiakao-misc!upload3",
                  {
                    dataIndex: "animationImage",
                    uploadIndex: "animationImage",
                    bucket: "exam-room",
                    type: "file",
                    isSingle: true,
                    placeholder: "请选择上传文件",
                    url: "simple-upload3://upload/file.htm",
                  },
                  function () {
                    // $('input[data-item="bug-mange-value"]').val(arguments)
                    console.log(arguments, 1251);
                  }
                ),
              },
              // {
              //     header: "模型文件连接", 
              //       data: 'triggeringImage',
              //     xtype: 'text',
              //     render: function (data) {
              //         console.log(data,'125');

              //     }
              //   },


              {
                header: " 开关：",
                dataIndex: "status",
                xtype: "radio",
                store: [
                  {
                    key: 1,
                    value: "是",
                  },
                  {
                    key: 0,
                    value: "否",
                  },
                ],
              },
              {
                header: "是否预下载",
                dataIndex: "preload",
                xtype: "radio",
                store: [
                  {
                    key: true,
                    value: "是",
                  },
                  {
                    key: false,
                    value: "否",
                  },
                ],
              },
              {
                header: " 成就中心展示顺序：",
                dataIndex: "sort",
                xtype: "text",
                placeholder: " 成就中心展示顺序",
              },
              {
                header: '成就达成表达式',
                dataIndex: "eventExpression",
                xtype: "textarea",
                placeholder: " 成就达成表达式",
              },
              {
                header: "优先级",
                dataIndex: "priority",
                xtype: "text",
                check: "required",
                placeholder: " 优先级",
              },
              {
                header: " 触发规则描述：",
                dataIndex: "description",
                xtype: "text",
                maxlength: 128,
                check: "required",
                placeholder: " 触发规则描述",
              },
              {
                header: "未达成是否可见",
                dataIndex: "visible",
                xtype: "radio",
                store: [{ key: true, value: '成就中心可见' }, { key: false, value: '成就中心不可见' }]
              },
              {
                header: "是否周期性发放",
                dataIndex: "repeatEarn",
                xtype: "radio",
                store: [{ key: true, value: '会周期性发放' }, { key: false, value: '不会周期性发放' }]
              },
              {
                header: "去完成跳转连接",
                dataIndex: "finishJumpUrl",
                xtype: 'text'
              },
              {
                header: '配置科目对应跳转：',
                dataIndex: 'archivedUrl',
                xtype: Plugin('jiakao-misc!group', {
                  dataIndex: 'archivedUrl'
                }, function (plugin, value) {

                })
              },
            ],
          },
          {
            name: "删除",
            class: "danger",
            xtype: "delete",
            store: "jiakao-misc!badge-config/data/delete",
          },
        ],
        columns: [
          {
            header: "#",
            dataIndex: "id",
            width: 20,
          },
          {
            header: " 徽章code",
            dataIndex: "code",
          },
          {
            header: "分享活动Id",
            dataIndex: "shareActivityId",
          },
          {
            header: "分享类型",
            dataIndex: "shareType",
            render:function(data){
              return shareTypeMap[data]
            }
          },
          {
            header: " 徽章类型 1 闯关成就 2 答题成就 3 特殊成就",
            dataIndex: "badgeType",
          },
          {
            header: "车型",
            dataIndex: "carType",
            render: function (data) {
              let text = [];
              data = data.split(',');
              data.forEach(item => {
                text.push(TIKU[item]);
              })
              return text + ''
            }
          },
          {
            header: " 徽章名",
            dataIndex: "badgeName",
          },
          {
            header: " 解锁文案",
            dataIndex: "unlockMessage",
          },
          {
            header: " 默认图标",
            dataIndex: "image",
            render: function (data) {
              return `<img src="${data}" style="width:100px">`;
            },
          },
          {
            header: " 触发图标",
            dataIndex: "triggerImage",
            render: function (data) {
              return `<img src="${data}" style="width:100px">`;
            },
          },
          {
            header: "模型链接",
            dataIndex: "animationImage",
            render: function (data) {
              return `<a>点击查看</a>`
            },
            click: function (table, row, lineData) {
              Widgets.dialog.html('模型链接', {}).done(function (dialog) {
                $(dialog.body).html('<div>' + lineData.animationImage + '</div><br/>')
              })
            }
          },
          {
            header: " 开关 0 关  1 开",
            render: function (data) {
              if (data) {
                return "是";
              } else {
                return "否";
              }
            },
            dataIndex: "status",
          },
          {
            header: " 成就中心展示顺序",
            dataIndex: "sort",
          },
          {
            header: "优先级",
            dataIndex: "priority",
          },
          {
            header: '成就达成表达式',
            dataIndex: "eventExpression",
            render: function () {
              return `<a>点击查看</a>`
            },
            click: function (table, row, lineData) {
              Widgets.dialog.html('成就达成表达式', {}).done(function (dialog) {
                var data = lineData.eventExpression && JSON.stringify(JSON.parse(lineData.eventExpression), null, 4)
                $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
              })
            }
          },

          {
            header: " 触发规则描述",
            dataIndex: "description",
          },

          {
            header: "触发规则",
            render: function (data) {
              return `<a>配置</a>`
            },
          },
          {
            header: "是否周期性发放",
            dataIndex: "repeatEarn",
            render: function (data) {
              return data ? '是' : '否'
            }
          },
          {
            header: "触发规则",
            render: function (data) {
              return `<a>配置</a>`
            },
            click: function (table, dom, data) {
              addRule(table, dom, data);
            }
          },
          {
            header: "创建人",
            dataIndex: "createUserName",
          },
          {
            header: "更新时间",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
            dataIndex: "updateTime",
          },

          {
            header: "更新人",
            dataIndex: "updateUserName",
          },

        ],
      },
      ["jiakao-misc!badge-config/data/list"],
      panel,
      null
    ).render();
  };

  return {
    list: list,
  };
});
