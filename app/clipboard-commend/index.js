/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!clipboard-commend/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '名称'
                },
                {
                    header: '口令类型：',
                    dataIndex: 'type',
                    xtype: 'select',
                    placeholder: '口令类型',
                    store: 'jiakao-misc!clipboard-commend/data/typeList',
                    index: [
                        {
                            key: 'key', value: 'value'
                        }
                    ],
                },
                {
                    header: '间隔时间，单位秒：',
                    dataIndex: 'intervalTime',
                    xtype: 'text',
                    placeholder: '间隔时间，单位秒'
                },
                {
                    header: '百分比：',
                    dataIndex: 'rate',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '名称'
                },
                {
                    header: '口令内容：',
                    dataIndex: 'value',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '口令内容'
                },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'clipboard-commend列表',
            title: 'clipboard-commend列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!clipboard-commend/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '口令类型：',
                            dataIndex: 'type'
                        },
                        {
                            header: '间隔时间，单位秒：',
                            dataIndex: 'intervalTime'
                        },
                        {
                            header: '口令内容：',
                            dataIndex: 'value'
                        },
                        {
                            header: '百分比：',
                            dataIndex: 'rate'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!clipboard-commend/data/view',
                        save: 'jiakao-misc!clipboard-commend/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '名称'
                        },
                        {
                            header: '口令类型：',
                            dataIndex: 'type',
                            xtype: 'select',
                            placeholder: '口令类型',
                            store: 'jiakao-misc!clipboard-commend/data/typeList',
                            index: [
                                {
                                    key: 'key', value: 'value'
                                }
                            ],
                        },
                        {
                            header: '间隔时间，单位秒：',
                            dataIndex: 'intervalTime',
                            xtype: 'text',
                            placeholder: '间隔时间，单位秒'
                        },
                        {
                            header: '百分比：',
                            dataIndex: 'rate',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '名称'
                        },
                        {
                            header: '口令内容：',
                            dataIndex: 'value',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '口令内容'
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!clipboard-commend/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '口令类型',
                    dataIndex: 'type'
                },
                {
                    header: '间隔时间，单位秒',
                    dataIndex: 'intervalTime'
                },
                {
                    header: '口令内容',
                    dataIndex: 'value'
                },
                {
                    header: '百分比：',
                    dataIndex: 'rate'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!clipboard-commend/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
