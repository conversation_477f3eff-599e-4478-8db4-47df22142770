"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'jiakao-misc!app/common/tools'],
    function (Template, Table, Utils, Tools) {
        var kemuStore = [
            {
                key: '',
                value: '请选择科目'
            },
            {
                key: '1',
                value: '科目一'
            },
            {
                key: '2',
                value: '科目二'
            },
            {
                key: '3',
                value: '科目三'
            },
            {
                key: '4',
                value: '科目四'
            }
        ]
        var kemuMap = Tools.getMapfromArray(kemuStore);

        var carTypeStore = [
            {
                key: '',
                value: '请选择车型'
            },
            {
                key: 'car',
                value: '小车'
            },
            {
                key: 'truck',
                value: '货车'
            },
            {
                key: 'bus',
                value: '客车'
            },
            {
                key: 'moto',
                value: '摩托车'
            },
            {
                key: 'light_trailer',
                value: '轻型牵引挂车'
            }
        ]
        var carTypeMap = Tools.getMapfromArray(carTypeStore);
        var senceStore = [
            {
                key: '101',
                value: '普通场景'
            }, {
                key: '102',
                value: '扣满12分'
            }
        ]
        var senceMap = Tools.getMapfromArray(senceStore);

        var editionStore = [{
            key: '101',
            value: '普通模式'
        }, {
            key: '102',
            value: '长辈模式'
            }]
        
            var editionMap = Tools.getMapfromArray(editionStore);
        var languageTypeStore = [{
             key:'HANYU',value:'汉语'
            },
            {
                key: 'WEIYU', value:'维语'
            }
           ]
        var languageTypeMap = Tools.getMapfromArray(languageTypeStore)

        var orientationStore = [{
            key: 2,
            value: '横屏(16:9)'
        },{
            key: 1,
            value: '竖屏(全面屏)'
        }]
        var orientationMap = Tools.getMapfromArray(orientationStore);

        return {
            kemuStore,
            kemuMap,
            carTypeStore,
            carTypeMap,
            senceStore,
            senceMap,
            editionStore,
            editionMap,
            languageTypeStore,
            languageTypeMap,
            orientationStore,
            orientationMap
        };
    });