'use strict';

define(function () {
    // clone https://github.com/eligrey/FileSaver.js/blob/master/src/FileSaver.js
    function click(node) {
        try {
            node.dispatchEvent(new MouseEvent('click'))
        } catch (e) {
            var evt = document.createEvent('MouseEvents')
            evt.initMouseEvent(
                'click',
                true,
                true,
                window,
                0,
                0,
                0,
                80,
                20,
                false,
                false,
                false,
                false,
                0,
                null
            )
            node.dispatchEvent(evt)
        }
    }

    function downloadURL(url, name) {
        var link = document.createElement('a')
        link.download = name
        link.href = url

        if ('download' in link) {
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        } else {
            // 对不支持download进行兼容
            link.target = '_blank';
            click(link)
        }
    }

    function downloadBlob(url, isPDF) {
        return new Promise(function (resolve, reject) {
            var xhr = new XMLHttpRequest()
            xhr.open('GET', url)
            xhr.responseType = isPDF ? 'arraybuffer' : 'blob'

            xhr.onload = function () {
                if (xhr.status === 200) {
                    resolve(xhr.response)
                } else {
                    reject(new Error(xhr.statusText || 'Download failed.'))
                }
            }
            xhr.onerror = function () {
                reject(new Error('Download failed.'))
            }
            xhr.send()
        })
    }

    function downloadFile(url, fileName, isPDF) {
        return downloadBlob(url, isPDF)
            .then(function (resp) {
                var blob = resp.blob ? resp.blob() : new Blob([resp])
                var url = URL.createObjectURL(blob)
                downloadURL(url, fileName)
                URL.revokeObjectURL(url)
            })
    }

    return downloadFile
})