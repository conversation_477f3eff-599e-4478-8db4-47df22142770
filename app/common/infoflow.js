"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'jiakao-misc!app/common/tools'],
    function (Template, Table, Utils, Tools) {
        var infoflowType = [
            {
                key: 1,
                value: '帖子'
            },
            {
                key: 2,
                value: '考场'
            },
            {
                key: 3,
                value: '项目讲解视频'
            },
            {
                key: 4,
                value: '教练带学视频'
            },
            {
                key: 5,
                value: '短视频'
            },
            {
                key: 6,
                value: '直播'
            },
            {
                key: 7,
                value: '话题pk'
            },
            {
                key: 8,
                value: '用户调研'
            },
            {
                key: 9,
                value: '选车必备'
            },
            {
                key: 10,
                value: '运营banner'
            },
            {
                key: 11,
                value: '广告'
            }
        ]
        var infoflowMap = Tools.getMapfromArray(infoflowType);
        return {
            infoflowMap,
            infoflowType
        };
    });