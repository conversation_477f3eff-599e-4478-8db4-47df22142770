"use strict";

define(['simple!core/template', 'simple!core/table','simple!core/widgets', 'simple!core/store','simple!core/utils', 'jiakao-misc!app/common/tools'],
    function (Template, Table, Widgets, Store, Utils, Tools) {
        var mcRequestList = function (url) {
            return new Promise((resolve) => {
                Store([url]).load().done(ret => {
                    console.log(ret,'ret');
                    resolve(ret)
                })
            })
        }

        function toast(tips) {
            return new Promise((resolve) => {
                var $toast = $('<div class="_easy_dialog-toast-container" style="opacity:1" data-id="' + Simple.UUID.get('toast') + '">' + tips + '</div>');
                var $body = $('body');
                $toast.appendTo( $body );
                setTimeout(function(){
                    resolve($toast)
                }, 10);
            })
        }

        var renderPan = async function(config, selectCallback) {
            let {type} = config
            type = type || 'mine'
            var key
            if(type === 'mine') {
                key = 'mcMineRoot'
            } else if(type === 'share') {
                key = 'getmucangNetworkShare'
            }
            
            const retData = await mcRequestList(`jiakao-misc!mc-pan/data/${key}`)
            mucangNetworkDisk(config, retData.data['mc-pan'].data[key].data, selectCallback);
        }

        var mucangNetworkDisk = function (config, dataList, selectCallback, dirList = []) {
            let {panel, multiple} = config
            Table({
                description: '全部文件',
                selector: {
                    render: function (rowData, data, config, index) {
                        return JSON.stringify({
                            id: rowData.id,
                            type: rowData.type
                        });
                    }
                },
                buttons: {
                    top: [
                        {
                            name: '我的',
                            class: 'info',
                            click: async function (obj) {
                                renderPan({...config, type: 'mine'}, selectCallback)
                            }
                        },
                        {
                            name: '共享',
                            class: 'primary',
                            click: async function () {
                                renderPan({...config, type: 'share'}, selectCallback)
                            }
                        },
                        {
                            name: '确认上传',
                            class: 'danger',
                            click: function (table, dom, config, selectedArr) {
                                if (selectedArr.length > 0) { 
                                    const notDirectory = selectedArr.every(item => {
                                        item = JSON.parse(item)
                                        return item.type === 'File';
                                    });
                                    if (selectedArr.length > 1 && !multiple) {
                                        Widgets.dialog.alert('请上传单个文件');
                                        return;
                                    }
                                    if (!notDirectory) {
                                        Widgets.dialog.alert('请勿上传文件夹');
                                        return;
                                    }
                                    Widgets.dialog.confirm('确认上传吗？', async function (ev, status) {
                                        if (status) {
                                            const ids = selectedArr.map(item => {
                                                item = JSON.parse(item)
                                                return item.id
                                            }) + '';
                                            var params = {
                                                fileIdList: ids,
                                                targetBucket: 'jiakao-image',
                                                targetPath: 'ke2Exam'
                                            }
                                            panel.close();
                                            var $toast = await toast('上传中');

                                            Store([`jiakao-misc!mc-pan/data/transfer`]).save([{
                                                params
                                            }]).done(function (tab, dom, confirm, data) {
                                                let itemList =  data.data.itemList.map(item => 'http://jiakao-image.mc-cdn.cn/'+item + '!default')
                                                selectCallback(itemList)
                                
                                                $toast.remove();
                                            });
                                            
                                        }
                                    })
                                } else {
                                    Widgets.dialog.alert('请选择数据')
                                }
                            }
                        }
                    ],
                },
                columns: [{
                    header: '名称',
                    dataIndex: 'name',
                    render: function (data, list, lineData) {
                        return `<a data-item="mc-lineData" style="cursor:pointer"><img src="${lineData.icon}" style="width: 50px"/> ${data}</a>`
                    },
                    click: async function (tab, dom, lineData) {
                        if (lineData.type == 'File') {
                            return;
                        }
                        const res =  await mcRequestList(`jiakao-misc!mc-pan/data/findChild?id=${lineData.id}`);
                        const retData = res.data['mc-pan'].data.findChild.data;
                        const dirList = retData.dirList;
                        const fileList = retData.fileList;
                        mucangNetworkDisk(config, fileList, selectCallback, dirList)
                    }
                }, {
                    header: '上传时间',
                    dataIndex: 'uploadTime',
                }, {
                    header: '上传人',
                    dataIndex: 'owner'
                }, {
                    header: '大小',
                    dataIndex: 'size'
                }]
            }, { data: dataList }, panel.body, function (target, self, item, table) {
                var allMulu = [{ id: '', name: '全部文件' }, ...dirList];
                var html = (allMulu.map(item => {
                    return `<span data-id="${item.id}" style="cursor:pointer" data-item="link">${item.name}</span>`
                })).join(' / ')

                item('description').html(html);
                setTimeout(() => {
                    item('link').on('click', async (e) => {
                        var id = e.target.getAttribute('data-id');
                        if (id) {
                            const res =  await mcRequestList(`jiakao-misc!mc-pan/data/findChild?id=${id}`);
                            const retData = res.data['mc-pan'].data.findChild.data;
                            const dirList = retData.dirList;
                            const fileList = retData.fileList;
                            mucangNetworkDisk(config, fileList, selectCallback, dirList)
                        } else {
                            const retData = await mcRequestList('jiakao-misc!mc-pan/data/getmucangNetworkShare')
                            mucangNetworkDisk(config, retData.data['mc-pan'].data.getmucangNetworkShare.data, selectCallback);
                        }
                    });
                },200)
            }).render();
        }

        return {
            renderPan,
            mucangNetworkDisk
        };
    });