"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils'],
    function (Template, Table, Utils) {
        var getMapfromArray = function (store) {
            var obj = {};
            store.forEach(item => { obj[item.key] = item.value })
            return obj;
        }
        var getArrayFromMap = function (map) {
            let array = []
            for (let key in map) {
                array.push({
                    key: key,
                    value: map[key],
                    search: map[key]
                })
            }
            return array
        }
        return {
            getMapfromArray,
            getArrayFromMap
        };
    });