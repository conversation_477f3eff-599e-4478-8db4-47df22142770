/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!coupon-user/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '用户id：',
                 dataIndex: 'userId',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: '用户id'
             },
             {
                 header: '手机号：',
                 dataIndex: 'userPhone',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: '手机号'
             },
             {
                 header: '优惠券：',
                 dataIndex: 'couponUniqKey',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: '优惠券'
             },
             {
                 header: '用户唯一的优惠券码：',
                 dataIndex: 'couponCode',
                 xtype: 'text',
                 maxlength: 128,
                 placeholder: '用户唯一的优惠券码'
             },
             {
                 header: '有效期开始时间：',
                 dataIndex: 'validStartTime',
                 xtype: 'date',
                 placeholder: '有效期开始时间'
             },
             {
                 header: '有效期截至时间：',
                 dataIndex: 'validEndTime',
                 xtype: 'date',
                 placeholder: '有效期截至时间'
             },
             {
                 header: '业务类型：',
                 dataIndex: 'bizType',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: '业务类型'
             },
             {
                 header: '是否已使用：',
                 dataIndex: 'used',
                 xtype: 'radio',
                 store: [
                     {
                         key: true,
                         value: '是'
                     },
                     {
                         key: false,
                         value: '否'
                     }
                 ]
             }

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: 'coupon-user列表',
            title: 'coupon-user列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    // {
                    //     name: '添加',
                    //     class: 'primary',
                    //     click: add
                    // }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!coupon-user/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '用户id：',
                         dataIndex: 'userId'
                     },
                     {
                         header: '手机号：',
                         dataIndex: 'userPhone'
                     },
                     {
                         header: '优惠券：',
                         dataIndex: 'couponUniqKey'
                     },
                     {
                         header: '用户唯一的优惠券码：',
                         dataIndex: 'couponCode'
                     },
                     {
                         header: '有效期开始时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'validStartTime'
                     },
                     {
                         header: '有效期截至时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'validEndTime'
                     },
                     {
                         header: '业务类型：',
                         dataIndex: 'bizType'
                     },
                     {
                         header: '是否已使用：',
                         render: function (data) {
                             if (data) {
                                 return '是';
                             } else {
                                 return '否';
                             }
                         },
                         dataIndex: 'used'
                     },
                     {
                         header: 'createTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!coupon-user/data/view',
                        save: 'jiakao-misc!coupon-user/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '用户id：',
                 dataIndex: 'userId',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: '用户id'
             },
             {
                 header: '手机号：',
                 dataIndex: 'userPhone',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: '手机号'
             },
             {
                 header: '优惠券：',
                 dataIndex: 'couponUniqKey',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: '优惠券'
             },
             {
                 header: '用户唯一的优惠券码：',
                 dataIndex: 'couponCode',
                 xtype: 'text',
                 maxlength: 128,
                 placeholder: '用户唯一的优惠券码'
             },
             {
                 header: '有效期开始时间：',
                 dataIndex: 'validStartTime',
                 xtype: 'date',
                 placeholder: '有效期开始时间'
             },
             {
                 header: '有效期截至时间：',
                 dataIndex: 'validEndTime',
                 xtype: 'date',
                 placeholder: '有效期截至时间'
             },
             {
                 header: '业务类型：',
                 dataIndex: 'bizType',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: '业务类型'
             },
             {
                 header: '是否已使用：',
                 dataIndex: 'used',
                 xtype: 'radio',
                 store: [
                     {
                         key: true,
                         value: '是'
                     },
                     {
                         key: false,
                         value: '否'
                     }
                 ]
             }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!coupon-user/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '用户id',
                         dataIndex: 'userId'
                     },
                     {
                         header: '手机号',
                         dataIndex: 'userPhone'
                     },
                     {
                         header: '优惠券',
                         dataIndex: 'couponUniqKey'
                     },
                     {
                         header: '用户唯一的优惠券码',
                         dataIndex: 'couponCode'
                     },
                     {
                         header: '有效期开始时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'validStartTime'
                     },
                     {
                         header: '有效期截至时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'validEndTime'
                     },
                     {
                         header: '业务类型',
                         dataIndex: 'bizType'
                     },
                     {
                         header: '是否已使用',
                         render: function (data) {
                             if (data) {
                                 return '是';
                             } else {
                                 return '否';
                             }
                         },
                         dataIndex: 'used'
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     }

            ]
        }, ['jiakao-misc!coupon-user/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
