/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {


    // var bizIdTypeArr = [
    //     {
    //         key: '',
    //         value: ''
    //     },
    //     {
    //         key: '',
    //         value: ''
    //     }
    // ]
    //
    // var bizIdTypeMap = {
    //     '': '',
    //     '': ''
    // }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!coupon/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 32,
                placeholder: '名称'
            },
            {
                header: '描述：',
                dataIndex: 'desc',
                xtype: 'text',
                maxlength: 32,
                placeholder: '描述'
            },
            {
                header: 'uniqKey：',
                dataIndex: 'uniqKey',
                xtype: 'text',
                maxlength: 64,
                placeholder: 'uniqKey'
            },
            {
                header: '优惠券有效期开始时间：',
                dataIndex: 'validStartDate',
                xtype: 'date',
                placeholder: '优惠券有效期开始时间'
            },
            {
                header: '有效期，单位秒：',
                dataIndex: 'validTimeSecond',
                xtype: 'text',
                placeholder: '有效期，单位秒'
            },
            {
                header: '适用于哪些业务商品的id列表：',
                dataIndex: 'bizIds',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '适用于哪些业务商品的id列表'
            },
            {
                header: '业务类型：',
                dataIndex: 'bizType',
                xtype: 'select',
                store: 'jiakao-misc!coupon/data/bizList',
                index: [{
                    key: 'key',
                    value: 'value'
                }],
            },
            {
                header: '业务ID类型：',
                dataIndex: 'bizIdType',
                xtype: 'select',
                store: 'jiakao-misc!coupon/data/bizIdList',


            },
            {
                header: '金额，单位分：',
                dataIndex: 'priceCent',
                xtype: 'text',
                placeholder: '金额，单位分'
            },
            {
                header: '是否在可领列表里展示：',
                dataIndex: 'showInList',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                },
                {
                    key: false,
                    value: '否'
                }
                ]
            },

            {
                header: '是否用户端可领：',
                dataIndex: 'shouldOpenSend',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                },
                {
                    key: false,
                    value: '否'
                }
                ]
            },
            {
                header: '是否在已领列表里展示：',
                dataIndex: 'showInReceivedList',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                },
                {
                    key: false,
                    value: '否'
                }
                ]
            },

            {
                header: '是否可直接兑换商品：',
                dataIndex: 'exchange',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                },
                {
                    key: false,
                    value: '否'
                }
                ]
            },
            {
                header: '是否在线：',
                dataIndex: 'online',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                },
                {
                    key: false,
                    value: '否'
                }
                ]
            },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'coupon列表',
            title: 'coupon列表',

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!coupon/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '名称：',
                    dataIndex: 'name'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc'
                },
                {
                    header: 'uniqKey：',
                    dataIndex: 'uniqKey'
                },
                {
                    header: '优惠券有效期开始时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'validStartDate'
                },
                {
                    header: '有效期，单位秒：',
                    dataIndex: 'validTimeSecond'
                },
                {
                    header: '适用于哪些业务商品的id列表：',
                    dataIndex: 'bizIds'
                },
                {
                    header: '业务类型：',
                    dataIndex: 'bizType'
                },
                {
                    header: '是否在可领列表里展示：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'showInList'
                },
                {
                    header: '是否用户端可领：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'shouldOpenSend'
                },
                {
                    header: '是否在已领列表里展示：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'showInReceivedList'
                },
                {
                    header: '金额，单位分：',
                    dataIndex: 'priceCent'
                },
                {
                    header: '是否可直接兑换商品：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'exchange'
                },
                {
                    header: '是否在线：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'online'
                },
                {
                    header: '创建时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人：',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人：',
                    dataIndex: 'updateUserName'
                }

                ]
            },
            {
                name: '编辑',
                xtype: 'edit',
                width: 500,
                class: 'warning',
                title: '编辑',
                success: function (obj, dialog, e) {
                    dialog.close();
                    obj.render();
                },
                store: {
                    load: 'jiakao-misc!coupon/data/view',
                    save: 'jiakao-misc!coupon/data/update'
                },
                columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '名称'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '描述'
                },
                {
                    header: 'uniqKey：',
                    dataIndex: 'uniqKey',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: 'uniqKey'
                },
                {
                    header: '优惠券有效期开始时间：',
                    dataIndex: 'validStartDate',
                    xtype: 'date',
                    placeholder: '优惠券有效期开始时间'
                },
                {
                    header: '有效期，单位秒：',
                    dataIndex: 'validTimeSecond',
                    xtype: 'text',
                    placeholder: '有效期，单位秒'
                },
                {
                    header: '适用于哪些业务商品的id列表：',
                    dataIndex: 'bizIds',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '适用于哪些业务商品的id列表'
                },
                // {
                //     header: '业务类型：',
                //     dataIndex: 'bizType',
                //     xtype: 'text',
                //     maxlength: 32,
                //     placeholder: '业务类型'
                // },
                {
                    header: '业务类型：',
                    dataIndex: 'bizType',
                    xtype: 'select',
                    store: 'jiakao-misc!coupon/data/bizList',
                    index: [{
                        key: 'key',
                        value: 'value'
                    }],
                },
                {
                    header: '业务ID类型：',
                    dataIndex: 'bizIdType',
                    xtype: 'select',
                    store: 'jiakao-misc!coupon/data/bizIdList',

                },
                {
                    header: '是否在可领列表里展示：',
                    dataIndex: 'showInList',
                    xtype: 'radio',
                    store: [{
                        key: true,
                        value: '是'
                    },
                    {
                        key: false,
                        value: '否'
                    }
                    ]
                },
                {
                    header: '是否用户端可领：',
                    dataIndex: 'shouldOpenSend',
                    xtype: 'radio',
                    store: [{
                        key: true,
                        value: '是'
                    },
                    {
                        key: false,
                        value: '否'
                    }
                    ]
                },
                {
                    header: '是否在已领列表里展示：',
                    dataIndex: 'showInReceivedList',
                    xtype: 'radio',
                    store: [{
                        key: true,
                        value: '是'
                    },
                    {
                        key: false,
                        value: '否'
                    }
                    ]
                },
                {
                    header: '金额，单位分：',
                    dataIndex: 'priceCent',
                    xtype: 'text',
                    placeholder: '金额，单位分'
                },
                {
                    header: '是否可直接兑换商品：',
                    dataIndex: 'exchange',
                    xtype: 'radio',
                    store: [{
                        key: true,
                        value: '是'
                    },
                    {
                        key: false,
                        value: '否'
                    }
                    ]
                },
                {
                    header: '是否在线：',
                    dataIndex: 'online',
                    xtype: 'radio',
                    store: [{
                        key: true,
                        value: '是'
                    },
                    {
                        key: false,
                        value: '否'
                    }
                    ]
                },
                ]
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!coupon/data/delete'
            },
                // {
                //     name: '发优惠券',
                //     class: 'primary',
                //     click: function (table, row, lineData) {
                //         Table({
                //             title: '发优惠券',
                //             width: 500,
                //             store: 'jiakao-misc!coupon/data/send',
                //             success: function (obj, dialog) {
                //                 dialog.close();
                //                 table.render();
                //             },
                //             columns: [{
                //                     header: '优惠券ID：',
                //                     dataIndex: 'id',
                //                     xtype: 'hidden',
                //                     readonly: true,
                //                     value: lineData.id
                //                 },
                //                 {
                //                     header: '电话号码：',
                //                     dataIndex: 'phone',
                //                     xtype: 'text',
                //                     placeholder: '电话号码'
                //                 },
                //             ]
                //         }).add();
                //     }

                // },
                // {
                //     name: '生成惠券',
                //     class: 'warning',
                //     click: function (table, row, lineData) {
                //         Table({
                //             title: '生成惠券',
                //             width: 500,
                //             store: 'jiakao-misc!coupon/data/insert',
                //             success: function (obj, dialog) {
                //                 dialog.close();
                //                 table.render();
                //             },
                //             form: {
                //                 submitHandler: function (form) {
                //                     var count = form.count.value;
                //                     var validStartDate = form.validStartDate.value;
                //                     window.open(window.j.host.local + '/api/admin/coupon/generate.htm?id=' + lineData.id + '&count=' + count + '&validStartDate=' + validStartDate);
                //                     $(form).find('.close').trigger('click');
                //                     return false;
                //                 }
                //             },
                //             columns: [{
                //                     header: '优惠券ID：',
                //                     dataIndex: 'id',
                //                     xtype: 'hidden',
                //                     readonly: true,
                //                     value: lineData.id
                //                 },
                //                 {
                //                     header: '数量：',
                //                     dataIndex: 'count',
                //                     xtype: 'text',
                //                     placeholder: '数量'
                //                 },
                //                 {
                //                     header: '开始日期：',
                //                     dataIndex: 'validStartDate',
                //                     xtype: 'date',
                //                     placeholder: '开始日期'
                //                 },
                //             ]
                //         }).add();
                //     }

                // }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '名称',
                dataIndex: 'name'
            },
            {
                header: '描述',
                dataIndex: 'desc'
            },
            {
                header: 'uniqKey',
                dataIndex: 'uniqKey'
            },
            {
                header: '优惠券有效期开始时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'validStartDate'
            },
            {
                header: '有效期，单位秒',
                dataIndex: 'validTimeSecond'
            },
            {
                header: '适用于哪些业务商品的id列表',
                dataIndex: 'bizIds'
            },
            {
                header: '业务类型',
                dataIndex: 'bizType'
            },
            {
                header: '业务ID的类型',
                dataIndex: 'bizIdType'
            },
            {
                header: '是否在可领列表里展示',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'showInList'
            },
            {
                header: '是否用户端可领',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'shouldOpenSend'
            },
            {
                header: '是否在已领列表里展示',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'showInReceivedList'
            },
            {
                header: '金额，单位分',
                dataIndex: 'priceCent'
            },
            {
                header: '是否可直接兑换商品',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'exchange'
            },
            {
                header: '是否在线',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'online'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '更新时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '更新人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!coupon/data/list'], panel, function () {
            let html = `<div style="display:inline-block;font-weight: bold;color:red;margin-left:20px;margin-top:5px">优惠券已迁至商业化</div>`
            $(panel).find('.table-search').after(`
              <div style="clear:both"></div>
             
            `)
            $(panel).find('table').before(html)
            ''
        }).render();
    }
    return {
        list: list
    }

});