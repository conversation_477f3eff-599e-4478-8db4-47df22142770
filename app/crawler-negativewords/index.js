/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var storeBaseUrl = 'jiakao-misc!crawler-negativewords/data/';
    var stateMap = {
        2: '待审核',
        3: '已发布'

    }
    var list = function (panel) {
        Table({
            description: '采集列表',
            title: '采集列表',

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    },

                },


                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            search: [
                // {
                //     xtype: 'select',
                //     dataIndex: 'publish',
                //     store: [{
                //         key: '',
                //         value: '选择审核状态'
                //     }, {
                //         key: '2',
                //         value: '未审核'
                //     }, {
                //         key: '3',
                //         value: '已审核'
                //     }]
                // }
            ],
            operations: [

                // {
                //     name: '审核',
                //     class: 'success',
                //     click: function (table, rows, lineData) {
                //
                //         require(['simple!app/layout/main', 'jiakao-misc!app/qa-audit/index'], function (main, QA) {
                //
                //
                //             var panel = main.panel('qa-audit' + lineData.id)
                //             if (panel.length == 0) {
                //                 panel = main.panel({
                //
                //                     name: lineData.id + '的问答审核',
                //
                //                     id: 'qa-audit' + lineData.wordId
                //                 });
                //                 QA.list(panel, lineData.wordId, lineData.word);
                //             }
                //         });
                //     },
                //     render: function (name, arr, i) {
                //         return arr[i].state == 2 && arr[i].answerCount > arr[i].auditCount ? name : ''
                //     }
                // },

                // {
                //     name: '发布',
                //     class: 'info',
                //     click: function (table, rows, lineData) {
                //         Widgets.dialog.confirm('确认发布吗？', function (ev, status) {
                //             if (status) {
                //                 Store([storeBaseUrl + 'publish?id=' + lineData.id], [{
                //                     aliases: 'list'
                //                 }]).save().done(function (store) {
                //                     table.render();
                //                 }).fail(function () {});
                //             } else {}
                //         })
                //
                //     },
                //     render: function (name, arr, i) {
                //         return arr[i].state == 2 && arr[i].answerCount == arr[i].auditCount ? name : ''
                //     }
                // },

                {
                    name: '预览',
                    class: 'warning',
                    click: function (table, rows, lineData) {

                        require(['simple!app/layout/main', 'jiakao-misc!app/qa-audit1/index'], function (main, QA) {


                            var panel = main.panel('qa-audit1' + lineData.id)
                            if (panel.length == 0) {
                                panel = main.panel({

                                    name: lineData.id + '的问答审核',

                                    id: 'qa-audit1' + lineData.id
                                });
                                QA.list(panel, lineData.id, lineData.word);
                            }
                        });
                    },
                }


            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
                {
                    header: '负面词',
                    dataIndex: 'word'
                },
                {
                    header: '链接数量',
                    dataIndex: 'linkCount'
                },
                // {
                //     header: '采集回答数',
                //     dataIndex: 'answerCount'
                // },
                // {
                //     header: '审核通过数',
                //     dataIndex: 'auditPassCount'
                // },
                // {
                //     header: '审核状态',
                //     dataIndex: 'state',
                //     render: function (data) {
                //         return stateMap[data]
                //     }
                // },
                {
                    header: '采集时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },


            ]
        }, [storeBaseUrl + 'list?type=2'], panel, null).render();
    }

    return {
        list: list
    }

});