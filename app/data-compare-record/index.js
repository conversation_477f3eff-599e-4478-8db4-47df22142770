/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {


    var list = function(panel) {
        Table({
            description: '数据对比列表',
            title: '数据对比列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '请求url',
                         dataIndex: 'requestUrl'
                     },
                     {
                         header: '请求参数',
                         dataIndex: 'requestParam',
                         render: function (data) {
                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('请求参数', {}).done(function (dialog) {
                               $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + lineData.requestParam + '</pre>')
                           })
                        }
                     },
                     {
                         header: '原始数据结果',
                         dataIndex: 'sourceResult',
                         render: function (data) {
                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('对比内容', {}).done(function (dialog) {
                               $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + lineData.sourceResult + '</pre>')
                           })
                        }
                     },
                     {
                         header: '目标数据结果',
                         dataIndex: 'targetResult',
                         render: function (data) {
                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('对比内容', {}).done(function (dialog) {
                               $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + lineData.targetResult + '</pre>')
                           })
                        }
                     },
                     {
                         header: '结果是否一致',
                         render: function (data) {
                             if (data) {
                                 return '是';
                             } else {
                                 return '否';
                             }
                         },
                         dataIndex: 'consistentFlag'
                     },
                     {
                         header: 'sourceResultSize',
                         dataIndex: 'sourceResultSize'
                     },
                     {
                         header: 'targetResultSize',
                         dataIndex: 'targetResultSize'
                     },
                     {
                        header: '对比内容',
                        dataIndex: 'diffContent',
                        render: function (data) {
                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('对比内容', {}).done(function (dialog) {
                               $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + lineData.diffContent + '</pre>')
                           })
                        }
                    },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '更新时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: '更新人',
                         dataIndex: 'updateUserName'
                     }

            ]
        }, ['jiakao-misc!data-compare-record/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});