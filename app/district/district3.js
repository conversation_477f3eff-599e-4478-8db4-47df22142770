/**
 * Created by sunbin on 2015/5/12.
 */
/*
 * district v0.0.1
 *
 * name: xiaojia
 * date: 2013/9/16
 */

"use strict";

define(['simple!core/utils'], function (Utils) {


    var district = Simple.DISTRICT ;
    var getProvinceOfCity = function (city) {
        var list = [];
        for (var i = 0; i < district.length; i++)
            if (district[i].cities) {
                for (var j = 0; j < district[i].cities.length; j++)
                    if (district[i].cities[j].code === city)
                        list = {
                            "name": district[i].name,
                            "code": district[i].code
                        };
            }

        return list;
    };

    var getcitiesOfProvince = function (province) {
        var list = [];
        for (var i = 0; i < district.length; i++)
            if (district[i].code === province)
                list = district[i].cities;
        return list
    };

    var provinces = function () {
        var list = [];
        for (var i = 0; i < district.length; i++) {
            list.push({
                "code": district[i].code,
                'name': district[i].name
            });
        };
        return list;
    };
    var getAreasOfCity = function (province, city) {
        var list = [];
        for (var i = 0; i < district.length; i++) {
            if (district[i].code === province) {
                for (var j = 0; j < district[i].cities.length; j++) {
                    if (district[i].cities[j].code == city) {
                        list = district[i].cities[j].counties;
                        return list
                    }
                }
            }
        }
    };
    var getSingleCity = function (country) {
        var list = [];
        for (var i = 0; i < district.length; i++) {
            if (district[i].cities) {
                for (var j = 0; j < district[i].cities.length; j++) {
                    for (var k = 0; k < district[i].cities[j].counties.length; k++) {
                        if (district[i].cities[j].counties[k].code == country) {
                            list = {
                                "provinceCode": district[i].code,
                                "cityCode": district[i].cities[j].code
                            };
                            return list
                        }
                    }
                }
            }
        }
    };

    var getCitysOfProvince = function (province) {//兼容之前的代码，和上面的getcitiesOfProvince一样的
        var list = [];
        for (var i = 0; i < district.length; i++)
            if (district[i].code === province)
                list = district[i].cities;
        return list
    }
    return {
        list: district,
        provinces: provinces,
        getProvinceOfCity: getProvinceOfCity,
        getcitiesOfProvince: getcitiesOfProvince,
        getAreasOfCity: getAreasOfCity,
        getSingleCity: getSingleCity,
        getCitysOfProvince: getCitysOfProvince
    }

});