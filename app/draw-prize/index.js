/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!draw-prize/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var time = $(form).find('#time').val();
                    console.log($(form).find('#time').val(), '124');
                    time = time.slice(0, 5);
                    return {
                        time
                    };
                },
                reverseParam: true
            },
            columns: [
                {
                    header: '开奖日期：',
                    dataIndex: 'date',
                    xtype: 'date',
                    maxlength: 10,
                    check: 'required',
                    placeholder: '开奖日期'
                },
                {
                    header: '课程id：',
                    dataIndex: 'lessonItemId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '课程id'
                },
                {
                    header: '开奖时间：',
                    dataIndex: 'time',
                    xtype: 'time',
                    maxlength: 10,
                    check: 'required',
                    placeholder: '开奖时间'
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 200,
                    placeholder: '标题'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    check: 'required',
                    store: Constants.kemuStore,
                },
                {
                    header: '备注：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 200,
                    placeholder: 'remark'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '直播抽奖场次配置',
            title: '直播抽奖场次配置',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form) {
                            var time = $(form).find('#time').val();
                            console.log($(form).find('#time').val(), '124');
                            time = time.slice(0, 5);
                            return {
                                time
                            };
                        },
                        reverseParam: true
                    },
                    store: {
                        load: 'jiakao-misc!draw-prize/data/view',
                        save: 'jiakao-misc!draw-prize/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '开奖日期：',
                            dataIndex: 'date',
                            xtype: 'date',
                            maxlength: 10,
                            check: 'required',
                            placeholder: '开奖日期'
                        },
                        {
                            header: '课程id：',
                            dataIndex: 'lessonItemId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '课程id'
                        },
                        {
                            header: '开奖时间：',
                            dataIndex: 'time',
                            xtype: 'time',
                            maxlength: 10,
                            check: 'required',
                            placeholder: '开奖时间'
                        },
                        {
                            header: '标题',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 200,
                            placeholder: '标题'
                        },
                        {
                            header: '科目',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            check: 'required',
                            store: Constants.kemuStore,
                        },
                        {
                            header: '备注：',
                            dataIndex: 'remark',
                            xtype: 'text',
                            maxlength: 200,
                            placeholder: 'remark'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!draw-prize/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '开奖日期',
                    dataIndex: 'date'
                },
                {
                    header: '课程id',
                    dataIndex: 'lessonItemId'
                },
                {
                    header: '开奖时间',
                    dataIndex: 'time'
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: 'remark',
                    dataIndex: 'remark'
                },
            ]
        }, ['jiakao-misc!draw-prize/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});