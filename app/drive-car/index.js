/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {


    var add = function (table) {
        var size = 0;
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!drive-car/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    //参数form为 form元素对象(非jQuery对象)
                    //返回值为false 或者 无返回值，不提交
                    return {
                        size: size
                    }

                }
            },
            columns: [
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-car/data/carName'
                },
                {
                    header: '封面图：',
                    dataIndex: 'cover',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'cover',
                        uploadIndex: 'cover',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '下载地址：',
                    dataIndex: 'url',
                    xtype: Plugin('jiakao-misc!upload2', {
                        dataIndex: 'url',
                        uploadIndex: 'url',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function (plugin, b, url, Size) {
                        size = Size;
                    })

                },
                {
                    header: '校验码：',
                    dataIndex: 'crc',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '校验码'
                },
                {
                    header: '车型类型：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-car/data/carTypes',
                    insert: [
                        {
                            key: '',
                            value: '选择车型'
                        }
                    ]

                },
                {
                    header: '平台',
                    dataIndex: 'platform',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '选择平台'
                        },
                        {
                            key: 'Android',
                            value: '安卓'
                        },
                        {
                            key: 'Iphone',
                            value: '苹果'
                        }
                    ]
                }


            ]
        }).add();
    }
    var edit = function (table, dom, lineData) {
        Table().edit(lineData, {
            title: '编辑',
            width: 500,
            success: function (obj, dialog, e) {
                dialog.close();
                table.render();
            },
            store: {
                load: 'jiakao-misc!drive-car/data/view',
                save: 'jiakao-misc!drive-car/data/update'
            },

            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    dataIndex: 'size',
                    xtype: 'hidden'
                },
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-car/data/carName'
                },
                {
                    header: '封面图：',
                    dataIndex: 'cover',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'cover',
                        uploadIndex: 'cover',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '下载地址：',
                    dataIndex: 'url',
                    xtype: Plugin('jiakao-misc!upload2', {
                        dataIndex: 'url',
                        uploadIndex: 'url',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function (plugin, b, c, Size) {
                        $('[name="size"]').val(Size);
                    })
                },
                {
                    header: '校验码：',
                    dataIndex: 'crc',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '校验码'
                },
                {
                    header: '车型类型：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-car/data/carTypes',
                    insert: [
                        {
                            key: '',
                            value: '选择车型'
                        }
                    ]

                },
                {
                    header: '平台',
                    dataIndex: 'platform',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '选择平台'
                        },
                        {
                            key: 'Android',
                            value: '安卓'
                        },
                        {
                            key: 'Iphone',
                            value: '苹果'
                        }
                    ]
                }


            ]
        })
    }

    var list = function (panel) {
        var size = 0;
        Table({
            description: '3D驾驶项目的车型列表列表',
            title: '3D驾驶项目的车型列表列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!drive-car/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '封面图：',
                            dataIndex: 'cover'
                        },
                        {
                            header: '下载地址：',
                            dataIndex: 'url'
                        },
                        {
                            header: '校验码：',
                            dataIndex: 'crc'
                        },
                        {
                            header: '车型类型：',
                            dataIndex: 'type'
                        },
                        {
                            header: '平台',
                            dataIndex: 'platform'
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },


                    ]
                },
                {
                    name: '编辑',
                    class: 'warning',
                    click: edit
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!drive-car/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '封面图',
                    dataIndex: 'cover',
                    render(data) {
                        if (data) {
                            return `<img  src="${data}" style="width: 200px">`;
                        }
                        else {
                            return '';
                        }
                    }
                },
                {
                    header: '下载地址',
                    dataIndex: 'url',
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, trDom, lineData) {

                        Widgets.dialog.html('查看').done(function (dialog) {
                            // dialog.body 为弹窗dom对象，用于展示自定义内容

                            var oA = $(`<p>${lineData.url}</p>`);

                            dialog.body.append(oA);

                        })
                    }
                },
                {
                    header: '校验码',
                    dataIndex: 'crc'
                },
                {
                    header: '车型类型',
                    dataIndex: 'type'
                },
                {
                    header: '平台',
                    dataIndex: 'platform'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },


            ]
        }, ['jiakao-misc!drive-car/data/list'], panel, function () {
            size = 0;
        }).render();
    }

    return {
        list: list
    }

});