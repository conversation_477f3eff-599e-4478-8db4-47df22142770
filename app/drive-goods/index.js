/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!drive-goods/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '商品名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '商品名称'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '描述'
                },
                {
                    header: '商品的类型：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-goods/data/typeList',
                    insert: [
                        {
                            key: '',
                            value: '选择商品类型'
                        }
                    ]
                },
                {
                    header: '加个，单位分：',
                    dataIndex: 'price',
                    xtype: 'text',
                    placeholder: '加个，单位分'
                },
                {
                    header: 'key：',
                    dataIndex: 'key',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: 'key'
                },
                {
                    header: '车型类型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-car/data/carTypes',
                    insert: [
                        {
                            key: '',
                            value: '选择车型'
                        }
                    ]

                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择科目'
                    },
                        {
                            key: '2',
                            value: '科目二'
                        }, {
                            key: '3',
                            value: '科目三'
                        }
                    ]

                },
                {
                    header: '权限列表：',
                    dataIndex: 'permissionList',
                    xtype: 'checkbox',
                    store: 'jiakao-misc!drive-goods/data/permissionList'
                },
                {
                    header: '状态：',
                    dataIndex: 'status',
                    xtype: 'text',
                    placeholder: '状态'
                },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'drive-goods列表',
            title: 'drive-goods列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!drive-goods/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '商品名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '商品的类型：',
                            dataIndex: 'type'
                        },
                        {
                            header: '加个，单位分：',
                            dataIndex: 'price'
                        },
                        {
                            header: 'key：',
                            dataIndex: 'key'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType'
                        },
                        {
                            header: '科目：',
                            dataIndex: 'kemu'
                        },
                        {
                            header: '权限列表：',
                            dataIndex: 'permissionList'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status'
                        },

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!drive-goods/data/view',
                        save: 'jiakao-misc!drive-goods/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '商品名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '商品名称'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '描述'
                        },
                        {
                            header: '商品的类型：',
                            dataIndex: 'type',
                            xtype: 'select',
                            store: 'jiakao-misc!drive-goods/data/typeList',
                            insert: [
                                {
                                    key: '',
                                    value: '选择商品类型'
                                }
                            ]
                        },
                        {
                            header: '加个，单位分：',
                            dataIndex: 'price',
                            xtype: 'text',
                            placeholder: '加个，单位分'
                        },
                        {
                            header: 'key：',
                            dataIndex: 'key',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: 'key'
                        },
                        {
                            header: '车型类型：',
                            dataIndex: 'carType',
                            xtype: 'select',
                            store: 'jiakao-misc!drive-car/data/carTypes',
                            insert: [
                                {
                                    key: '',
                                    value: '选择车型'
                                }
                            ]

                        },
                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '选择科目'
                            },
                                {
                                    key: '2',
                                    value: '科目二'
                                }, {
                                    key: '3',
                                    value: '科目三'
                                }
                            ]

                        },
                        {
                            header: '权限列表：',
                            dataIndex: 'permissionList',
                            xtype: 'checkbox',
                            store: 'jiakao-misc!drive-goods/data/permissionList'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            xtype: 'text',
                            placeholder: '状态'
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!drive-goods/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '商品名称',
                    dataIndex: 'name'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '商品的类型',
                    dataIndex: 'type'
                },
                {
                    header: '加个，单位分',
                    dataIndex: 'price'
                },
                {
                    header: 'key',
                    dataIndex: 'key'
                },
                {
                    header: '车型',
                    dataIndex: 'carType'
                },
                {
                    header: '科目',
                    dataIndex: 'kemu'
                },
                {
                    header: '权限列表',
                    dataIndex: 'permissionList'
                },
                {
                    header: '状态',
                    dataIndex: 'status'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!drive-goods/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});