/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!drive-permission/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    //参数form为 form元素对象(非jQuery对象)
                    //返回值为false 或者 无返回值，不提交
                    var name = $(form).find('#key>option:selected').text()
                    return {
                        name: name
                    }


                }
            },
            columns: [

                {
                    header: '名称：',
                    dataIndex: 'key',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-permission/data/keyList',
                    insert: [{
                        key: '',
                        value: '选择名称'
                    }
                    ]
                },

                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择科目'
                    },
                        {
                            key: '2',
                            value: '科目二'
                        }, {
                            key: '3',
                            value: '科目三'
                        }
                    ]

                },
                {
                    header: '有效期天数：',
                    dataIndex: 'validDays',
                    xtype: 'text',
                    placeholder: '有效期天数'
                },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'drive-permission列表',
            title: 'drive-permission列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!drive-permission/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '权限名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: 'key：',
                            dataIndex: 'key'
                        },

                        {
                            header: '科目：',
                            dataIndex: 'kemu'
                        },
                        {
                            header: '有效期天数：',
                            dataIndex: 'validDays'
                        },

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form, fun) {
                            //参数form为 form元素对象(非jQuery对象)
                            //返回值为false 或者 无返回值，不提交
                            var name = $(form).find('#key>option:selected').text()
                            return {
                                name: name
                            }


                        }
                    },
                    store: {
                        load: 'jiakao-misc!drive-permission/data/view',
                        save: 'jiakao-misc!drive-permission/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },

                        {
                            header: '名称：',
                            dataIndex: 'key',
                            xtype: 'select',
                            store: 'jiakao-misc!drive-permission/data/keyList'
                        },

                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '选择科目'
                            },
                                {
                                    key: '2',
                                    value: '科目二'
                                }, {
                                    key: '3',
                                    value: '科目三'
                                }
                            ]

                        },
                        {
                            header: '有效期天数：',
                            dataIndex: 'validDays',
                            xtype: 'text',
                            placeholder: '有效期天数'
                        },


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!drive-permission/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '权限名称',
                    dataIndex: 'name'
                },
                {
                    header: 'key',
                    dataIndex: 'key'
                },

                {
                    header: '科目',
                    dataIndex: 'kemu'
                },
                {
                    header: '有效期天数',
                    dataIndex: 'validDays'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!drive-permission/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});