/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!drive-user-permission/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },

            columns: [
                {
                    header: '用户id：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '用户id'
                },
                {
                    header: '权限key：',
                    dataIndex: 'permissionKey',
                    xtype: 'text',

                },

                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择科目'
                    },
                        {
                            key: '2',
                            value: '科目二'
                        }, {
                            key: '3',
                            value: '科目三'
                        }
                    ]
                },
                {
                    header: '订单号：',
                    dataIndex: 'orderNumber',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '订单号'
                },
                {
                    header: '过期时间：',
                    dataIndex: 'expireTime',
                    xtype: 'date',
                    placeholder: '过期时间'
                },
                {
                    header: '对应的订单状态：',
                    dataIndex: 'status',
                    xtype: 'text',
                    placeholder: '对应的订单状态'
                },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'drive-user-permission列表',
            title: 'drive-user-permission列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!drive-user-permission/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '权限key：',
                            dataIndex: 'permissionKey'
                        },

                        {
                            header: '科目：',
                            dataIndex: 'kemu'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber'
                        },
                        {
                            header: '过期时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'expireTime'
                        },
                        {
                            header: '对应的订单状态：',
                            dataIndex: 'status'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!drive-user-permission/data/view',
                        save: 'jiakao-misc!drive-user-permission/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '用户id'
                        },
                        {
                            header: '权限key：',
                            dataIndex: 'permissionKey',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '权限key'
                        },

                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '选择科目'
                            },
                                {
                                    key: '2',
                                    value: '科目二'
                                }, {
                                    key: '3',
                                    value: '科目三'
                                }
                            ]

                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '订单号'
                        },
                        {
                            header: '过期时间：',
                            dataIndex: 'expireTime',
                            xtype: 'date',
                            placeholder: '过期时间'
                        },
                        {
                            header: '对应的订单状态：',
                            dataIndex: 'status',
                            xtype: 'text',
                            placeholder: '对应的订单状态'
                        },


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!drive-user-permission/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户id',
                    dataIndex: 'userId'
                },
                {
                    header: '权限key',
                    dataIndex: 'permissionKey'
                },
                {
                    header: '科目',
                    dataIndex: 'kemu'
                },
                {
                    header: '订单号',
                    dataIndex: 'orderNumber'
                },
                {
                    header: '过期时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'expireTime'
                },
                {
                    header: '对应的订单状态',
                    dataIndex: 'status'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!drive-user-permission/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});