/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var typeList = [
        "初始化",
        "直角转弯",
        "曲线行驶",
        "倒车入库",
        "坡道定点停车与起步",
        "侧方停车",
        "起步",
        "会车",
        "加减挡",
        "变更车道",
        "路口左转",
        "路口直行",
        "学校区域",
        "公共汽车站",
        "掉头",
        "直线行驶",
        "超车",
        "人行横道",
        "路口右转",
        "靠边停车"
    ]
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!drive-video/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '视频名称：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-video/data/typeList'
                },
                {
                    header: '视频地址：',
                    dataIndex: 'video',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '视频地址'
                },
                {
                    header: '封面图：',
                    dataIndex: 'cover',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'cover',
                        uploadIndex: 'cover',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '车型类型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: 'jiakao-misc!drive-car/data/carTypes',
                    insert: [
                        {
                            key: '',
                            value: '选择车型'
                        }
                    ]

                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择科目'
                    },
                        {
                            key: '2',
                            value: '科目二'
                        }, {
                            key: '3',
                            value: '科目三'
                        }
                    ]
                },


            ]
        }).add();
    }

    var list = function (panel) {

        Table({
            description: 'drive-video列表',
            title: 'drive-video列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!drive-video/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '视频名称：',
                            dataIndex: 'type'
                        },
                        {
                            header: '视频地址：',
                            dataIndex: 'video'
                        },
                        {
                            header: '视频封面：',
                            dataIndex: 'cover'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType'
                        },
                        {
                            header: '科目：',
                            dataIndex: 'kemu'
                        },

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!drive-video/data/view',
                        save: 'jiakao-misc!drive-video/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '视频名称：',
                            dataIndex: 'type',
                            xtype: 'select',
                            store: 'jiakao-misc!drive-video/data/typeList'
                        },
                        {
                            header: '视频地址：',
                            dataIndex: 'video',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '视频地址'
                        }, {
                            header: '封面图：',
                            dataIndex: 'cover',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'cover',
                                uploadIndex: 'cover',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '车型类型：',
                            dataIndex: 'carType',
                            xtype: 'select',
                            store: 'jiakao-misc!drive-car/data/carTypes',
                            insert: [
                                {
                                    key: '',
                                    value: '选择车型'
                                }
                            ]

                        },
                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '选择科目'
                            },
                                {
                                    key: '2',
                                    value: '科目二'
                                }, {
                                    key: '3',
                                    value: '科目三'
                                }
                            ]
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!drive-video/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '视频名称',
                    dataIndex: 'type',
                    render: function (data) {
                        return typeList[data];
                    }

                },
                {
                    header: '视频地址',
                    dataIndex: 'video'
                },
                {
                    header: '视频封面',
                    dataIndex: 'cover'
                },
                {
                    header: '车型',
                    dataIndex: 'carType'
                },
                {
                    header: '科目',
                    dataIndex: 'kemu'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!drive-video/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});