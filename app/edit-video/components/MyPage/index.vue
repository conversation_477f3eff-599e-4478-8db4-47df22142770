<template>
    <div class="edit-video">
        <div class="header">
            {{ props.data.title }}
        </div>

        <template v-if="props.data.title.includes('头')">
            <div class="select">
                选择片头阶段：
                <div class="wrap">
                    <template v-for="(item, i) in selectHeader" :key="item">
                        <label :for="'header'+i">
                            <input
                                type="radio"
                                name="header"
                                :id="'header'+i"
                                :checked="i == 0"
                                :value="item"
                                @click="onRadio"
                            />&nbsp;{{ item }} &nbsp;&nbsp;&nbsp;
                        </label>
                    </template>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="select">
                选择片尾阶段：
                <div class="wrap">
                    <input
                        type="radio"
                        name="footer"
                        id="footer"
                        checked
                        value="路线重难点总结"
                        @click="onRadio"
                    />&nbsp;路线重难点总结 &nbsp;&nbsp;&nbsp;
                </div>
            </div>
        </template>

        <div class="content">
            <div class="l">
                <Priview />
            </div>
            <div class="r">
                <template v-if="nowSelect === '路线说明'">
                    <RouteDes :data="props.data"/>
                </template>
                <template v-else-if="nowSelect === '考前准备'">
                    此模块为固定模板，不需要编辑。
                </template>
                <template v-else-if="nowSelect === '灯光模拟'">
                    <LightSim :data="props.data"/>
                </template>
                <template v-else-if="nowSelect === '灯光模拟注意事项'">
                    <LightSimNotice :data="props.data"/>
                </template>

                <template v-else-if="nowSelect === '路线重难点总结'">
                    <LightSimNotice :data="props.data"/>
                </template>
            </div>
        </div>

    </div>
</template>

<script setup>
import { defineProps, reactive, ref } from "vue";
import { useCommonStore } from "../../piniaStore/common.ts";
import Priview from "../preview/index.vue";
import RouteDes from "../routeDes/index.vue";
import LightSim from "../lightSim/index.vue";

import LightSimNotice from "../lightSimNotice/index.vue";
const commonStore = useCommonStore();
const selectHeader = reactive([
    "路线说明",
    "考前准备",
    "灯光模拟",
    "灯光模拟注意事项",
]);
let title;

const props = defineProps({
    data: Object,
});


if (props.data.title === '编辑片尾') {
    title = '路线重难点总结'
} else {
    title = '路线说明'
}


const nowSelect = ref(title);
commonStore.nowSelect = nowSelect.value

const formState = props.data;
console.log(formState,' formStateformState',formState.lightExamJson);
formState.carType1 = formState.vehicleModel?.split(',')[0]
formState.carType2 = formState.vehicleModel?.split(',')[1]
formState.carType3 = formState.vehicleModel?.split(',')[2]
if (formState.lightExamJson) {
    const lightExamJson = JSON.parse(formState.lightExamJson);
    if (Array.isArray(lightExamJson)) {
        commonStore.lightData = lightExamJson
    }

    console.log(commonStore.lightData,'commonStore.lightDatacommonStore.lightData');
}
commonStore.formState = formState;
if(formState.lightExamItems){
commonStore.linghtKeyData = formState.lightExamItems && JSON.parse(formState.lightExamItems).reduce((current,pre) => current + ',' + pre).split(',')
}

console.log('commonStore.linghtKeyData',commonStore.linghtKeyData)

function onRadio(e) {
    nowSelect.value = e.target.value;
    commonStore.nowSelect = nowSelect.value
}
</script>

<style lang="less" scoped>
.edit-video {
    height: 100%;
    .header {
        font-weight: bold;
        font-size: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ccc;
    }
    .select {
        display: flex;
        margin: 40px 0;
        .wrap {
            margin-left: 20px;
            display: flex;
            align-items: center;
            input {
                margin: 0 !important;
                cursor: pointer;
            }
        }
    }

    .content{
        display: flex;
        
        .l{
            
        }
        .r{
            margin-left: 20px;
            flex: 1;
        }
    }
}
</style>
