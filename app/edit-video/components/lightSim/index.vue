<template>
  <div class="lignt-sim">
    <div class="title">编辑关键数据</div>
    <div class="wrap">
      <a-form
        :model="commonStore.formState"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-item label="灯光视频模板:">
          <a-select v-model:value="commonStore.formState.lightTemplate">
            <a-select-option value="jieda">捷达车类灯光</a-select-option>
            <a-select-option value="ailise">爱丽舍车类灯光</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="灯光模拟考试秘籍:"> </a-form-item>
      </a-form>

      <a-table
        bordered
        :columns="columns"
        :data-source="commonStore.lightData"
        :pagination="{
            pageSize:10
        }"
        :row-selection="{
          selectedRowKeys: commonStore.linghtKeyData,
          onChange: onSelectChange,
        }"
        :scroll="{ x: 1500, y: 400 }"
      >
        <template
          v-for="col in ['sort', 'page']"
          #[col]="{ text, record }"
          :key="col"
        >
          <div>
            <a-input
              v-if="editableData[record.key]"
              v-model:value="editableData[record.key][col]"
              style="margin: -5px 0"
            />
            <template v-else>
              {{ text }}
            </template>
          </div>
        </template>
        <template #operation="{ record }" >
          <div class="editable-row-operations">
            <span v-if="editableData[record.key]">
              <a @click="save(record.key)">保存</a>
            </span>
            <span v-else>
              <a @click="edit(record.key)">编辑</a>
            </span>
          </div>
        </template>
      </a-table>

      <div style="display:flex;">
          <a-button style="margin-left: 10px" @click="onSave">保存</a-button>
        </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, reactive, ref } from "vue";
import { useCommonStore } from '../../piniaStore/common.ts';
import Store from "simple!core/store";
import { message } from "ant-design-vue";

const commonStore = useCommonStore();

const props = defineProps({
    data: Object,
});

const columns = [
  {
    title: "灯光题目&答案",
    dataIndex: "name",
    width: "40%",
    slots: {
      customRender: "name",
    },
  },
  {
    title: "排序",
    dataIndex: "sort",
    width: "15%",
    slots: {
      customRender: "sort",
    },
  },
  {
    title: "分页",
    dataIndex: "page",
    slots: {
      customRender: "page",
    },
  },
  {
    title: "operation",
    dataIndex: "operation",
    slots: {
      customRender: "operation",
    },
  },
];

 
const onSelectChange = (selectedRowKeys) => {
    commonStore.linghtKeyData = selectedRowKeys;
};

const editableData = reactive({});

const save = (key) => {
  Object.assign(
    commonStore.lightData.filter((item) => key === item.key)[0],
    editableData[key]
  );
  delete editableData[key];
};
const edit = (key) => {
  editableData[key] = JSON.parse(
    JSON.stringify(commonStore.lightData.filter((item) => key === item.key)[0])
  );
};


function onSave(params) {
    let lightItems = {}


    commonStore.linghtKeyData.forEach((item) => {
        const { page,sort} = commonStore.lightData.find(el => el.key == item)
        if (!lightItems[page]) {
            lightItems[page] = {};
        }
        lightItems[page][`${sort}`] = item;
    })


    let res = {};
    
    for (const key in lightItems) {
        const item = lightItems[key];
        if (!res[key]) {
            res[key] = [];
        }

        for (const key2 in item) { 
            res[key].push(item[key2])
        }
    }

    let resData = [];
    console.log(res,'resres');
    for (const key in res) {
        console.log(key,'keykey');
        resData.push(res[key]+'')
    }

 

    Store([`jiakao-misc!route-video-process/data/update`]).save([{
        params: {
            id: props.data.id,
            lightExamItems: JSON.stringify(resData),
            lightTemplate: commonStore.formState.lightTemplate,
            lightExamJson:JSON.stringify(commonStore.lightData)
        }
    }]).done(data => {
        message.info('保存成功');
    }).fail(err => {
        message.error(err.message);
    })
}
</script>

<style scoped lang="less">
.lignt-sim {
  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .wrap {
    width: 800px;
  }
}

.editable-row-operations a {
  margin-right: 8px;
}
</style>
