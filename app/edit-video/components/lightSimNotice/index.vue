<template>
    <div class="lignt-sim-notice">
        <div class="title">编辑关键数据</div>
        <div class="wrap">
            <textarea class="rich" v-if="props.data.title === '编辑片头'" cols="80" :value="commonStore.formState.lightTipText" rows="20" ref="richDom" :placeholder='`<p>表示换页，\n<font color="#FFBD30">黄色字体</font>\n<br/>表示换行以下完整示例\n<p>\n1.除考试开始与结束外，只会从以上<font color="FFBD30">12种</font>场景中抽取<font color="FFBD30">5种</font>进行考试；<br/>\n2.<font color="FFBD30">远、近光交替</font>只用打<font color="FFBD30">两次</font>，不要少打或多打；<br/>\n3.若遇到<font color="FFBD30">均为</font>近光灯的场景，则<font color="FFBD30">无需</font>进行任何操作，等待语音结束即可；<br/>\n4.上一个场景为<font color="FFBD30">开启远光灯</font>或<font color="FFBD30">危险报警闪光灯</font>的，下一个场景一定先<font color="FFBD30">关闭</font>远光灯或危险报警闪光灯，再进行其他操作。`'></textarea>
            <textarea class="rich" v-if="props.data.title === '编辑片尾'" cols="80" :value="commonStore.formState.endTipText" rows="20" ref="richDom" :placeholder='`<p>表示换页，\n<font color="#FFBD30">黄色字体</font>\n<br/>表示换行以下完整示例\n<p>\n1.除考试开始与结束外，只会从以上<font color="FFBD30">12种</font>场景中抽取<font color="FFBD30">5种</font>进行考试；<br/>\n2.<font color="FFBD30">远、近光交替</font>只用打<font color="FFBD30">两次</font>，不要少打或多打；<br/>\n3.若遇到<font color="FFBD30">均为</font>近光灯的场景，则<font color="FFBD30">无需</font>进行任何操作，等待语音结束即可；<br/>\n4.上一个场景为<font color="FFBD30">开启远光灯</font>或<font color="FFBD30">危险报警闪光灯</font>的，下一个场景一定先<font color="FFBD30">关闭</font>远光灯或危险报警闪光灯，再进行其他操作。`'></textarea>

            <div class="btns">

                <a-button style="margin-left: 10px" @click="onPreview">预览</a-button>

                <a-button style="margin-left: 10px" @click="onSave">保存</a-button>
            </div>
        </div>

    </div>
</template>

<script setup>
import { defineProps, ref } from "vue";
import { useCommonStore } from '../../piniaStore/common.ts';
import Store from "simple!core/store";
import { message } from "ant-design-vue";

const props = defineProps({
    data: Object,
});
const richDom = ref(null);
const commonStore = useCommonStore();

function onSave() {
    let url = '';

    if (props.data.title === '编辑片头') {
        url =  'jiakao-misc!route-video-process/data/seveLightTip'
    } else if (props.data.title === '编辑片尾') {
        url =  'jiakao-misc!route-video-process/data/saveEndTip'
    }
 Store([url]).save([{
        params: {
            id: props.data.id,
            html:richDom.value.value
        }
 }]).done(data => {
     onPreview();
        message.info('保存成功');
    }).fail(err => {
        message.error(err.message);
    })
}

function onPreview() {
    if (props.data.title === '编辑片头') {
        commonStore.formState.lightTipText = richDom.value.value
    } else if (props.data.title === '编辑片尾') {
        commonStore.formState.endTipText= richDom.value.value
    }
}



</script>

<style scoped lang="less">
    .lignt-sim-notice{
        .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .wrap {
            width: 800px;
        }
        .btns{
            margin-top: 20px;
        }
    }

</style>
