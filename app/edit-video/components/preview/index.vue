<template>
    <div class="priview">
       
        <div class="bg">
            <img v-if="commonStore.formState.openingCover" :src="commonStore.formState.openingCover" />
            <img v-else src="./bg.png" />
        </div>

        <div style="position: relative; height: 100%;">
            <div class="info">
                <div class="exam-name">
                    {{ commonStore.formState.subSceneName }}
                </div>

                <div class="route-name">
                    {{ commonStore.formState.sceneName }}
                    <span v-show="commonStore.formState.routeName">
                        {{ commonStore.formState.routeName }}
                    </span>
                </div>
            </div>

            <template v-if="commonStore.nowSelect === '路线说明'">

                <div class="upload-img" v-if="commonStore.formState.routeExplainImgUrl">
                    <img :src="commonStore.formState.routeExplainImgUrl" alt="">
                </div> 

            </template>


            <template v-if="commonStore.nowSelect === '考前准备'">
                <div class="exam-ready">
                    <div class="title">考前注意事项:</div>
                    <div class="content">
                        1.该讲解为全国<span style="color:#f8ba38;">通用版本</span>，具体情况以本地考场为主；<br/>
                        2.进入考场前进行<span style="color:#f8ba38;">身份</span>认证并签到；<br/>
                        3.等待电脑<span style="color:#f8ba38;">叫号</span>；<br/>
                        4.前往<span style="color:#f8ba38;">指定</span>考试车辆上车准备考试。
                    </div>
                </div>
            </template>

            <template v-if="commonStore.nowSelect === '灯光模拟'">
                <div class="dec">灯光模拟考试秘籍:</div>
                <div class="light-exam-data">
                    <div class="item" v-for="item in (lightExamData.sort((a,b)=> a.sort - b.sort))" :key="item.key">

                        <div class="l-text">
                            {{ item.sort + ':' + item.name.split('：')[0] }} : &nbsp;&nbsp;
                        </div>
                        <div class="r-text">
                            {{ item.name.split('：')[1] }}
                        </div>
                    
                    </div>
                </div>
            </template>

            <template v-if="commonStore.nowSelect === '灯光模拟注意事项'">
                <div class="linght-text-wrap">
                    <div v-html="commonStore.formState.lightTipText"></div>
                </div>
            </template>

            <template v-if="commonStore.nowSelect === '路线重难点总结'">
                <div class="linght-text-wrap2">
                    <div v-html="commonStore.formState.endTipText"></div>
                </div>
            </template>
        </div>

    </div>
</template>

<script setup>
import { computed, defineProps, reactive ,ref} from "vue";
import { useCommonStore, showClock } from '../../piniaStore/common.ts';

const commonStore = useCommonStore();


const getImgUrl = (name) => {
    const carMap = {
        '荣威350':'rongwei-350',
        '威驰':'weichi',
        '江淮和悦':'jianghuaiheyue',
        '悦动':'yuedong',
        'e爱丽舍':'e-ailishe',
        '新爱丽舍':'xinailishe',
        '捷达VA3':'jieda-VA3',
        '奇瑞艾瑞泽':'qiruiairuize',
        'GA3':'GA3',
        '老桑塔纳':'laosangtana',
        '伊兰特':'yilante',
        '锋范':'fengfan',
        '比亚迪F3':'biyadi-F3',
        'GA5':'GA5',
        '奇瑞e3':'qirui-e3',
        '老爱丽舍':'laoailishe',
        '荣威360':'rongwei-360',
        '新捷达':'xinjieda',
        '新桑塔纳':'xinsangtana',
        '斯柯达昕锐':'sikedaxinrui'
    }



    return window.j.root['jiakao-misc'] + '/resources/carList/' + carMap[name] + '.png';
}


const lightExamData = computed(() => {
    return commonStore.lightData.filter(item => commonStore.linghtKeyData?.includes(item.key)) 
})

 


</script>

<style scoped lang="less">
@font-face {
    font-family: 'PangMenZhengDao';
    src: url('./font_pmzd_title_ti.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

.priview {
    height: 594px;
    width: 1056px;
    position: relative;
    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    .info {
        position: absolute;
        top: 440px;
        left: 46px;
    }
    .exam-name{
        font-family: 'PangMenZhengDao', sans-serif;
        font-size: 18px;
        font-weight: bold;
        padding: 14px 0 0 42px;
        background: url(./title-bg.png) no-repeat;
        background-size: 100% auto;
        width: 220px;
        height: 50px;

    }
    .route-name{
        font-family: 'PangMenZhengDao', sans-serif;
        font-size: 40px;
        font-weight: bold;
        color: #fff;
        line-height: 60px;
        span {
            color: #f8ba38;
        }
    }

    .exam-ready{
        position: absolute;
        top: 105px;
        left: 340px;
        color: #fff;
        .title{
            font-size: 26px;
        }
        .content{
            font-size: 18px;
            width: 560px;
            margin-top: 26px;
        }
    }

    .upload-img{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        img{
            width: 100%;
            height: 100%;
        }
    }
    .dec{
        font-size: 30px;
        font-weight: bold;
        color: #fff;
        line-height: 40px;
        position: absolute;
        top:110px;
        left: 230px;
    }
    .light-exam-data{
        position: absolute;
        top:160px;
        left: 300px;
        height: 350px;
        overflow-y: scroll;

        .item{
            display: flex;
            align-items: center;
            font-size: 20px;
            .l-text{
                color: #fff;
            }
            .r-text{
                color: #d2ac38;
            }
        }
    }
    .linght-text-wrap{
        width: 500px;
        height: 400px;
        position: absolute;
        top: 55px;
        left: 490px;
        color: white;
    }

    .linght-text-wrap2{
        width: 400px;
        height: 400px;
        position: absolute;
        top:90px;
        left: 360px;
        color: white;
    }
}

::-webkit-scrollbar {
    display: none;
    /* Chrome Safari */
}
</style>
