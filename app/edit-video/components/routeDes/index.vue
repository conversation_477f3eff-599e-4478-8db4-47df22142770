<template>
  <div class="route-des">
    <div class="title">编辑关键数据</div>

    <div class="wrap">
      <a-form
        :model="formState"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 14 }"
        :rules="rules"
        ref="formRef"
      >
        <a-form-item label="视频底图:">
            <component v-if="uploadComp" :is="uploadComp" v-model:value="formState.openingCover" :config="{}"></component>
        </a-form-item>

        <a-form-item label="考场名称第二行:" name="subSceneName" extra="subSceneName">
          <a-input v-model:value="formState.subSceneName" placeholder="考场名称第二行" maxlength="7"/>
        </a-form-item>

        <a-form-item label="考场名称第一行:"  name="sceneName" extra="sceneName">
          <a-input v-model:value="formState.sceneName" placeholder="考场名称第一行" maxlength="7"/>
        </a-form-item>

        <a-form-item label="路线名称:"  name="routeName" extra="routeName">
          <a-input v-model:value="formState.routeName" placeholder="路线名称"  maxlength="7"/>
        </a-form-item>

        <a-form-item label="路线讲解预览图片:">
            <div ref="phoneRef"></div>
          <!-- <a-upload
            action="https://upload-image.kakamobi.cn/api/admin/upload/file.htm?wenaho=1"
            :data=uploadInfo
            list-type="picture-card"
            :withCredentials="true"
            v-model:file-list="fileList"
            @change="handleChange"
          >
            <div v-if="fileList.length < 1">
              <div class="ant-upload-text">上传图片</div>
            </div>
          </a-upload> -->
        </a-form-item>

        <a-form-item label="路线讲解文件url:">
            <div ref="luxianRef"></div>
            <!-- <a-upload
                v-model:file-list="fileList2"
                :withCredentials="true"
                :data=uploadInfo
                action="https://upload-image.kakamobi.cn/api/admin/upload/file.htm?wenaho=1"
                @change="handleChange2">
                <a-button>
                <upload-outlined></upload-outlined>
                上传文件
                </a-button>
            </a-upload> -->

        </a-form-item>

        <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
          <a-button style="margin-left: 10px" @click="onSumit">保存</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import Store from "simple!core/store";
import { Plugin as APlugin } from '@simplex/admin-vue';
import { defineProps, nextTick, reactive, ref, createApp } from "vue";
import { useCommonStore ,showClock} from '../../piniaStore/common.ts';
import { message } from 'ant-design-vue';
import Plugin from 'simple!core/plugin';
import { Widgets } from "@simplex/admin-vue";
import videoFrameUpload from ':plugin/video-frame-upload.vue';

const commonStore = useCommonStore();
const carList = reactive(['请选择车型','荣威350','威驰','江淮和悦','悦动','e爱丽舍','新爱丽舍','捷达VA3','奇瑞艾瑞泽','GA3','老桑塔纳','伊兰特','锋范','比亚迪F3','GA5','奇瑞e3','老爱丽舍','荣威360','新捷达','新桑塔纳','斯柯达昕锐'])
const formState = commonStore.formState;
console.log(formState,'formState');
const fileList = ref([]);
const phoneRef = ref(null);
const luxianRef = ref(null);
const formRef = ref();
nextTick(() => {
    Plugin('jiakao-misc!upload3', {
        dataIndex: "file",
        uploadIndex: "file",
        bucket: "jiakao-web",
        type: "file",
        isSingle: true,
        value:formState.routeExplainFileUrl,
        placeholder: "请选择上传文件",
        url: "simple-upload3://upload/file.htm",
        target: luxianRef.value,
        deleteImgInfo:function(){
            formState.routeExplainFileUrl = ''
        }
    }, function (plugin, status, data) {
        formState.routeExplainFileUrl = data
    }).render();

    Plugin('jiakao-misc!upload', {
        dataIndex: 'imgUrl',
        uploadIndex: 'imgUrl',
        bucket: "jiakao-web",
        isSingle: true,
        value:  formState.routeExplainImgUrl,
        placeholder: '请选择上传文件',
        url:'simple-upload3://upload/file.htm',
        target: phoneRef.value,
        deleteImgInfo:function(){
            formState.routeExplainImgUrl = ''
        }
    }, function (plugin,status,data) {
        formState.routeExplainImgUrl = data
    }).render();
   
})


if (formState.routeExplainImgUrl) {
    fileList.value = [{
        url: formState.routeExplainImgUrl
    }]
}
const fileList2 = ref([{
    url: formState.routeExplainFileUrl
}]);

if (formState.routeExplainFileUrl) {
    fileList2.value = [{
        url: formState.routeExplainFileUrl,
        name:'zip'
    }]
}

// const handleChange = ({ file , fileList}) => {
//     fileList.value = fileList;
//     console.log(fileList.value,'fileListfileList');
//     formState.routeExplainImgUrl = file?.response?.data[0]?.url
// };
// const handleChange2 = ({ file, fileList }) => {
//     fileList2.value = fileList;
//     formState.routeExplainFileUrl = file?.response?.data[0]?.url
// }
const props = defineProps({
    data: Object,
});
const uploadComp = APlugin(videoFrameUpload, {
    bucket: "jiakao-web",
    placeholder: '请选择视频截图',
    videoUrl: props.data.originalVideoUrl,
})
const rules = {
  sceneName:[{ required: true,message: '考场名称第一行必填', trigger: 'blur' }],
  subSceneName:[{ required: true, message: '考场名称第二行必填',trigger: 'blur' }],
  routeName:[{ required: true, message: '路线名称必填',trigger: 'blur' }],
}
const uploadInfo = reactive({
    bucket: 'jiakao-web',
    type: 'zip'
})
 
function onSumit(){
 formRef.value
        .validate().then(()=>{
          onSave();
      })
}
function onSave() {
    // if(!formState.routeExplainFileUrl){
    //   Widgets.dialog.alert('请上传路线讲解文件url')
    //   return
    // }
    Store([`jiakao-misc!route-video-process/data/update`]).save([{
        params: {
            id: props.data.id,
            sceneName: formState.sceneName,
            subSceneName: formState.subSceneName,
            routeName: formState.routeName,
            routeExplainImgUrl: formState.routeExplainImgUrl,
            routeExplainFileUrl: formState.routeExplainFileUrl,
            openingCover: formState.openingCover
        }
    }]).done(data => {
        message.info('保存成功');
    }).fail(err => {
        message.error(err.message);
    })
}
</script>

<style scoped lang="less">
.route-des {
  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .own-style-lable{
    display: none;
    &::after{
      contain: '';
    }
  }
  .wrap {
    width: 600px;
    :deep(.ant-upload-list-item-actions) {
      a {
        display: none;
      }
    }
    .btns {
      margin: 50px;
      margin-left: 80px;
      width: 200px;
      display: flex;
      justify-content: space-around;
    }
  }
}
</style>
