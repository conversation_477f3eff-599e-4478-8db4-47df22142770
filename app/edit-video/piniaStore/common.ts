import { defineStore } from "pinia";
import Store from "simple!core/store";

export const formatNumber = (n) => {
    const s = n.toString();
    return s[1] ? s : "0" + s;
};

export function showClock(time) {
    var mm = formatNumber(Math.floor(time / 60));
    var ss = formatNumber(Math.floor(time % 60));

    return mm + ":" + ss;
}

/** 生成UUID */
export function getUUID() {
    const S4 = function () {
        // eslint-disable-next-line no-bitwise
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    };

    return (
        S4() +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        "-" +
        S4() +
        S4() +
        S4()
    );
}

export const useCommonStore = defineStore("common", {
    state: () => {
        return {
            /**路线说明的编辑数据 */
            formState: {
                sceneName: "", //
                routeName: "",
                carType1: "",
                carType2: "",
                carType3: "",
                routeExplainImgUrl: '',
                /**灯光模拟注意事项 */
                lightTipText: '',
                //片尾
                endTipText:'',
                openingCover:''
            },

            /**灯光模拟列表死数据 */
            lightData:[
              {
                key: "2",

                name: "考试开始请开启前照灯：近光灯",
                sort: 1,
                page: 1,
              },
              {
                key: "14",
                name: "夜间在没有路灯，照明不良的道路上行驶：远光灯",
                sort: 2,
                page: 1,
              },
              {
                key: "3",
                name: "夜间同方向近距离跟车行驶：近光灯",
                sort: 3,
                page: 1,
              },
              {
                key: "4",
                name: "夜间直行通过路口：近光灯",
                sort: 4,
                page: 1,
              },
              {
                key: "5",
                name: "夜间与机动车会车：近光灯",
                sort: 5,
                page: 1,
              },
                {
                  key: "6",
                  name: "夜间在有路灯的道路上行驶：近光灯",
                  sort: 6,
                  page: 2,
                },

                {
                    key: "7",
                    name: "夜间在窄桥与非机动车会车：近光灯",
                    sort: 7,
                    page: 2,
                  },
                  {
                    key: "8",
                    name: "夜间在照明良好的道路上行驶：近光灯",
                    sort: 8,
                    page: 2,
                  },


                {
                  key: "9",
                  name: "路边临时停车：示廓灯+危险警报灯",
                  sort: 9,
                  page: 2,
                },
                {
                  key: "10",
                  name: "夜间在道路上发生交通事故，妨碍交通又难以移动：示廓灯+危险警报灯",
                  sort: 10,
                  page: 2,
                },
                {
                  key: "11",
                  name: "夜间超越前方车辆：远、近光交替两次",
                  sort: 11,
                  page: 3,
                },
                {
                  key: "12",
                  name: "夜间通过急弯、坡道、拱桥、人行横道：远、近光交替两次",
                  sort: 12,
                  page: 3,
                },
                {
                  key: "13",
                  name: "夜间通过没有交通信号灯控制的路口：远、近光交替两次",
                  sort: 13,
                  page: 3,
                },
              {
                key: "1",
                name: "考试结束：关闭所有灯光",
                sort: 14,
                page: 3,
              },
                {
                  key: "15",
                  name: "夜间通过急弯、坡道：远、近光交替两次",
                  sort: 15,
                  page: 3,
                },
                {
                  key: "16",
                  name: "夜间通过拱桥、人行横道：远、近光交替两次",
                  sort: 16,
                  page: 4,
                },
                {
                  key: "17",
                  name: "夜间通过急弯：远、近光交替两次",
                  sort: 17,
                  page: 4,
                },
                {
                  key: "18",
                  name: "夜间通过坡道：远、近光交替两次",
                  sort: 18,
                  page: 4,
                },
                {
                  key: "19",
                  name: "夜间通过拱桥：远、近光交替两次",
                  sort: 19,
                  page: 4,
                },
                {
                  key: "20",
                  name: "夜间通过人行横道：远、近光交替两次",
                  sort: 20,
                  page: 4,
                },
                {
                  key: "21",
                  name: "夜间在窄路与非机动车会车：近光灯",
                  sort: 21,
                  page: 5,
                },
                {
                  key: "22",
                  name: "夜间在照明不良的道路上行驶：远光灯",
                  sort: 22,
                  page: 5,
                },
             
            ],

            /**灯光模拟选中的数据key */
          linghtKeyData: ['2', '14','3','4','5','6','7','8','9','10','11','12','13','1'],
          
            nowSelect:'路线说明'
        };
    },
    actions: {},
});
