/*
 * index v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/tiku'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, TIKU) {
    // var entranceMap = {
    //     'half_done': '1000题完成',
    //     'all_done': '做题通关',
    //     'weekend_done': '周末题霸',
    //     'night_done': '深夜奋斗君',
    //     '500_done': 'vip精简题库',
    //     'first_qualified_done': '首次考试合格',
    //     'first_fullmark_done': '首次考试满分',
    //     'police_thumbup': '交警也给我点赞',
    //     'firstaid_done': '驾考急救先锋',
    //     'never_deducted': '永不扣分',
    //     'car_ke1_done': '小车科目一',
    //     'car_ke2_done': '小车科目二',
    //     'car_ke3_done': '小车科目三',
    //     'car_ke4_done': '小车科目四',
    //     'truck_ke1_done': '货车科目一',
    //     'truck_ke2_done': '货车科目二',
    //     'truck_ke3_done': '货车科目三',
    //     'truck_ke4_done': '货车科目四',
    //     'bus_ke1_done': '客车科目一',
    //     'bus_ke2_done': '客车科目二',
    //     'bus_ke3_done': '客车科目三',
    //     'bus_ke4_done': '客车科目四',
    //     'moto_ke1_done': '摩托车科目一',
    //     'moto_ke2_done': '摩托车科目二',
    //     'moto_ke3_done': '摩托车科目三',
    //     'moto_ke4_done': '摩托车科目四',
    //     'miscmark_done': '标标必答',
    //     'sign_done': '认线能手',
    //     'roadorder_done': '有原则的老司机',
    //     'briefenglish_done': 'YYDS',
    // }
    var entranceMap = {}
    var entranceArray = []
    var carTypeArr = []

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k],
        })
    }
    // for (let key in entranceMap) {
    //     entranceArray.push({ key: key, value: key + '-' + entranceMap[key], search: key + '-' + entranceMap[key] })
    // }
    var addOrEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        let config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'jiakao-misc!egg-entrance-config/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: function (table,dom,data) {
                console.log(data,'12351');
                if (data?.data?.openType == 2) {
                    dom.item('openImg-group').hide();
                    dom.item('url-group').hide();
                    dom.item('activityId-group').show();
                } else {
                    dom.item('openImg-group').show();
                    dom.item('url-group').show();
                    dom.item('activityId-group').hide();
                }


                dom.item('openType').on('change', function(){
                    
                    let value = $(this).val();
                    if (value == 1) {
                        dom.item('openImg-group').show();
                        dom.item('url-group').show();
                        dom.item('activityId-group').hide();
                    } else {
                        dom.item('openImg-group').hide();
                        dom.item('url-group').hide();
                        dom.item('activityId-group').show();
                    }
                })
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '彩蛋名称：',
                    dataIndex: 'eggName',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '彩蛋名称'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{ key: '', value: '请选择车型' }, ...carTypeArr],
                },
                {
                    header: '平台：',
                    dataIndex: 'platform',
                    xtype: 'checkbox',
                    store: [
                        { key: 'android', value: '安卓' },
                        { key: 'iphone', value: 'IOS' },
                    ],
                    placeholder: '平台'
                },
                {
                    header: '选择徽章入口：',
                    dataIndex: 'entrance',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: entranceArray,
                        placeholder: '选择徽章入口',
                        dataIndex: 'entrance',
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                    check: 'required',
                    placeholder: '选择徽章入口'
                },
                {
                    header: '打开方式：',
                    dataIndex: 'openType',
                    xtype: 'radio',
                    store: [{ key: 1, value: '图标打开' },{key: 2, value: '成就达成页直接领取'}],
                    check: 'required',
                },
                {
                    header: '图片：',
                    dataIndex: 'openImg',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'openImg',
                        uploadIndex: 'openImg',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '彩蛋连接：',
                    dataIndex: 'url',
                    xtype: 'textarea',
                    placeholder: '彩蛋连接'
                },
                {
                    header: '关联活动',
                    dataIndex: 'activityId',
                    xtype: 'select',
                    store: 'jiakao-misc!egg-entrance-config/data/activityList',
                    index: {
                        key: 'id',
                        value: 'name',
                        search: 'name'
                    }
                }
            ]
        }
        if (isEdit) {
            Table().edit(lineData, config)
        } else {
            Table(config).add();
        }
    }

    var list = function (panel) {
        Store(['jiakao-misc!badge-config/data/list?limit=999']).load().done(data => {
            const retData = data.data['badge-config'].data.list.data;
            const temArr = []
            retData.forEach(item => {
                temArr.push({
                    key: item.code,
                    value: item.code + '-' + item.badgeName,
                    search:item.badgeName
                })
                entranceMap[item.code] = item.badgeName
            })
            entranceArray = temArr;

            Table({
                description: '彩蛋管理',
                title: '彩蛋管理',
               
                buttons: {
                    top: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            }
                        },
                        {
                            name: '添加',
                            class: 'primary',
                            click: function (table) {
                                addOrEdit(table)
                            }
                        }
                    ],
                    bottom: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            }
                        }
                    ]
                },
                operations: [
                    {
                        name: '查看',
                        xtype: 'view',
                        width: 400,
                        class: 'success',
                        title: '查看',
                        store: 'jiakao-misc!egg-entrance-config/data/view',
                        columns: [
                            {
                                header: '#',
                                dataIndex: 'id'
                            },
                            {
                                header: '彩蛋名称：',
                                dataIndex: 'eggName'
                            },
                            {
                                header: '车型',
                                dataIndex: 'carType',
                                render:function(data){
                                  return TIKU[data]
                                }
                            },
                            {
                                header: '入口：',
                                dataIndex: 'entrance',
                                render: function (data) {
                                    if (data) {
                                        let newArray = []
                                        var dataArray = data.split(",")
                                        dataArray && dataArray.forEach((res) => {
                                            newArray.push(entranceMap[res])
                                        })
                                        return newArray.join('，')
                                    }
    
                                }
                            },
    
                            {
                                header: '彩蛋连接：',
                                dataIndex: 'url'
                            },
                            {
                                header: '打开方式：',
                                dataIndex: 'openType',
                                render: function (data) {
                                    if (data == 1) {
                                        return '图标打开'
                                    }
                                }
                            },
                            {
                                header: '平台：',
                                dataIndex: 'platform',
                                render: function (data) {
                                    if (data) {
                                        var newArray = []
                                        var dataArray = data.split(',')
                                        dataArray && dataArray.forEach((res) => {
                                            if (res == 'android') {
                                                newArray.push('安卓')
                                            } else if (res == 'iphone') {
                                                newArray.push('IOS')
                                            }
                                        })
                                        return newArray.join('，')
                                    }
    
    
                                }
                            },
                            {
                                header: '创建时间：',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'createTime'
                            },
                            {
                                header: '创建人Id：',
                                dataIndex: 'createUserId'
                            },
                            {
                                header: '创建人名称：',
                                dataIndex: 'createUserName'
                            },
                            {
                                header: '更新时间：',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'updateTime'
                            },
                            {
                                header: '更新人Id：',
                                dataIndex: 'updateUserId'
                            },
                            {
                                header: '更新人名称：',
                                dataIndex: 'updateUserName'
                            }
                        ]
                    },
                    {
                        name: '编辑',
                        class: 'warning',
                        click: function (table, dom, lineData) {
                            addOrEdit(table, lineData)
                        }
                    },
                    {
                        name: '投放策略',
                        class: 'success',
                        click: function (table, lineDom, lineData, dom, data, index) {
                            Plugin('simple!product-filter', {
                                store: 'jiakao-misc!egg-entrance-config/data/filter?id=' + lineData.id
                            }).render().done(function () {
                                table.render();
                            });
                        }
                    },
                    {
                        name: '删除',
                        class: 'danger',
                        xtype: 'delete',
                        store: 'jiakao-misc!egg-entrance-config/data/delete'
                    }
                ],
                columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '彩蛋名称',
                        dataIndex: 'eggName'
                    },
                    {
                        header: '彩蛋入口',
                        dataIndex: 'entrance',
                        render: function (data) {
                            if (data) {
                                let newArray = []
                                var dataArray = data.split(",")
                                dataArray && dataArray.forEach((res) => {
                                    newArray.push(entranceMap[res])
                                })
                                return newArray.join('，')
                            }
    
                        }
                    },
                    {
                        header: '车型',
                        dataIndex: 'carType',
                        render: function (data) {
                            return TIKU[data]
                        }
                    },
                    {
                        header: '打开方式',
                        dataIndex: 'openType',
                        render: function (data) {
                            if (data == 1) {
                                return '图标打开'
                            }
                        }
                    },
                    {
                        header: '彩蛋连接',
                        dataIndex: 'url'
                    },
                    {
                        header: '状态',
                        dataIndex: 'status'
                    },
                    {
                        header: '平台',
                        dataIndex: 'platform',
                        render: function (data) {
                            if (data) {
                                var newArray = []
                                var dataArray = data.split(',')
                                dataArray && dataArray.forEach((res) => {
                                    if (res == 'android') {
                                        newArray.push('安卓')
                                    } else if (res == 'iphone') {
                                        newArray.push('IOS')
                                    }
                                })
                                return newArray.join('，')
                            }
    
    
                        }
                    },
                    {
                        header: '创建时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    }
    
                ]
            }, ['jiakao-misc!egg-entrance-config/data/list'], panel, null).render();



        }).fail(err => {
            console.log(err);
        })


       
    }

    return {
        list: list
    }

});