/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var carTypeMap = {
        "car": "小车",
        // "bus": "客车",
        "truck": "货车",
        // "moto": "摩托车",
        // "keyun": "客运",
        // "huoyun": "货运",
        // "weixian": "危险品",
        // "jiaolian": "教练员",
        // "chuzu": "出租车",
        // "wangyue": "网约车"
    }
    var carTypeStore = Object.keys(carTypeMap).map(a=> ({ key: a, value: carTypeMap[a]}));

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-banner/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '标题'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '描述'
                },
                {
                    header: '排序：',
                    dataIndex: 'sort',
                    xtype: 'text',
                    placeholder: '排序'
                },
                {
                    header: '驾照类型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: carTypeStore,
                    check: 'required',
                    placeholder: '驾照类型'
                },
                {
                    header: '类型：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: [{
                        key: '1',
                        value: '考试车'
                    }, {
                        key: '2',
                        value: '广告'
                    }],
                    placeholder: '类型'
                },
                {
                    header: '图片地址：',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '跳转链接：',
                    dataIndex: 'clickUrl',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '跳转链接'
                },
                {
                    header: '状态：',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{
                        key: -1,
                        value: '不可用'
                    }, {
                        key: 1,
                        value: '可用'
                    }],
                    placeholder: '状态'
                },
                {
                    header: '内置的图片：',
                    dataIndex: 'icon',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'icon',
                        uploadIndex: 'icon',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '灯光模拟banner列表',
            title: '灯光模拟banner列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [
                {
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{key: '', value: '所有驾照类型'}].concat(carTypeStore)
                },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '所有状态'
                    }, {
                        key: -1,
                        value: '不可用'
                    }, {
                        key: 1,
                        value: '可用'
                    }],
                },
            ],
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!emulator-banner/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sort'
                        },
                        {
                            header: '驾照类型',
                            dataIndex: 'carType',
                            render: function (data) {
                                return carTypeMap[data];
                            }
                        },
                        {
                            header: '类型：',
                            dataIndex: 'type'
                        },
                        {
                            header: '图片：',
                            dataIndex: 'image',
                            render: function (data) {
                                if (data) {
                                    return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                                }
                            },
                        },
                        {
                            header: '内置的图片：',
                            dataIndex: 'icon',
                            render: function (data) {
                                if (data) {
                                    return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                                }
                            },
                        },
                        {
                            header: '跳转链接：',
                            dataIndex: 'clickUrl'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人名称：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },


                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!emulator-banner/data/view',
                        save: 'jiakao-misc!emulator-banner/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '标题'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '描述'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sort',
                            xtype: 'text',
                            placeholder: '排序'
                        },
                        {
                            header: '驾照类型：',
                            dataIndex: 'carType',
                            xtype: 'select',
                            store: carTypeStore,
                            check: 'required',
                            placeholder: '驾照类型'
                        },
                        {
                            header: '类型：',
                            dataIndex: 'type',
                            xtype: 'select',
                            store: [{
                                key: '1',
                                value: '考试车'
                            }, {
                                key: '2',
                                value: '广告'
                            }],
                            placeholder: '类型'
                        },
                        {
                            header: '图片：',
                            dataIndex: 'image',
                            check: 'required',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'image',
                                uploadIndex: 'image',
                                bucket: "exam-room",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                check: 'required',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '跳转链接：',
                            dataIndex: 'clickUrl',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '跳转链接'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            xtype: 'select',
                            store: [{
                                key: -1,
                                value: '不可用'
                            }, {
                                key: 1,
                                value: '可用'
                            }],
                            placeholder: '状态'
                        },
                        {
                            header: '内置的图片：',
                            dataIndex: 'icon',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'icon',
                                uploadIndex: 'icon',
                                bucket: "exam-room",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!emulator-banner/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '排序',
                    dataIndex: 'sort'
                },
                {
                    header: '驾照类型',
                    dataIndex: 'carType',
                    render: function (data) {
                        return carTypeMap[data];
                    }
                },
                {
                    header: '类型',
                    dataIndex: 'type',
                    render: function (data) {
                        let text = '';
                        if (data == 1) {
                            text = '考试车'
                        } else if (data == 2) {
                            text = '广告'
                        }
                        return text;
                    }
                },
                {
                    header: '图片地址',
                    dataIndex: 'image',
                    render: function (data) {
                        if (data) {
                            return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                        }
                    },
                },
                {
                    header: '内置的图片',
                    dataIndex: 'icon',
                    render: function (data) {
                        if (data) {
                            return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                        }
                    },
                },
                {
                    header: '跳转链接',
                    dataIndex: 'clickUrl'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        let text = '';
                        if (data == -1) {
                            text = '不可用'
                        } else if (data == 1) {
                            text = '可用'
                        }
                        return text;
                    }
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },

                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },


            ]
        }, ['jiakao-misc!emulator-banner/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});