/*
 * index v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var statusMap = {
        '0': '未发布',
        '1': '测试发布',
        '2': '正式发布'
    }
    var statusStore = Object.keys(statusMap).map(a => ({ key: a, value: statusMap[a] }));

    var classificationType = [
        { key: '', value: '请选择车辆类型' },
        { key: 1, value: '考试车' },
        { key: 2, value: '豪车' }
    ]
    var carTypeMap = {
        "car": "小车",
        "bus": "客车",
        "truck": "货车",
        // "moto": "摩托车",
        // "keyun": "客运",
        // "huoyun": "货运",
        // "weixian": "危险品",
        // "jiaolian": "教练员",
        // "chuzu": "出租车",
        // "wangyue": "网约车"
    }
    var carTypeStore = Object.keys(carTypeMap).map(a => ({ key: a, value: carTypeMap[a] }));
    var freeMap = {
        'true': "免费",
        'false': "不免费",
    }
    var freeType = [
        { key: '', value: '请选择免费体检类型' },
        { key: true, value: '免费' },
        { key: false, value: '不免费' }
    ]

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-brand/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '品牌名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '品牌名称'
                },
                {
                    header: '车辆类型：',
                    dataIndex: 'free',
                    xtype: 'select',
                    store: freeType,
                    check: 'required',
                    placeholder: '免费体检类型'
                },
                {
                    header: '排序：',
                    dataIndex: 'sort',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '排序'
                }
            ]
        }).add();
    }


    var list = function (panel) {
        Table({
            description: '品牌列表',
            title: '品牌列表',
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            search: [
                {
                    dataIndex: 'name',
                    xtype: 'text',
                    placeholder: '品牌名称',
                },
            ],
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!emulator-brand/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '品牌名称',
                    dataIndex: 'name'
                },
                {
                    header: '排序',
                    dataIndex: 'sort'
                },
                {
                    header: '是否免费体验',
                    dataIndex: 'free',
                    render: function (data) {
                        return freeMap[data];
                    }
                },
                {
                    header: 'createTime：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: 'createUserId：',
                    dataIndex: 'createUserId'
                },
                {
                    header: 'createUserName：',
                    dataIndex: 'createUserName'
                },
                {
                    header: 'updateTime：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: 'updateUserId：',
                    dataIndex: 'updateUserId'
                },
                {
                    header: 'updateUserName：',
                    dataIndex: 'updateUserName'
                }]
            },
            {
                name: '编辑',
                xtype: 'edit',
                width: 500,
                class: 'warning',
                title: '编辑',
                success: function (obj, dialog, e) {
                    dialog.close();
                    obj.render();
                },
                store: {
                    load: 'jiakao-misc!emulator-brand/data/view',
                    save: 'jiakao-misc!emulator-brand/data/update'
                },
                columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '品牌名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '品牌名称'
                },
                {
                    header: '车辆类型：',
                    dataIndex: 'free',
                    xtype: 'select',
                    store: freeType,
                    check: 'required',
                    placeholder: '免费体检类型'
                },
                {
                    header: '排序：',
                    dataIndex: 'sort',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '排序'
                }
                ]
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!emulator-brand/data/delete'
            }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '品牌名称',
                dataIndex: 'name'
            },
            {
                header: '排序',
                dataIndex: 'sort'
            },
            {
                header: '是否免费体验',
                dataIndex: 'free',
                render: function (data) {
                    return freeMap[data];
                }
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!emulator-brand/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});