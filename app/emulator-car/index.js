/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var statusMap = {
        '0': '未发布',
        '1': '测试发布',
        '2': '正式发布'
    }
    var statusStore = Object.keys(statusMap).map(a => ({ key: a, value: statusMap[a] }));

    var classificationType = [
        { key: '', value: '请选择车辆类型' },
        { key: 1, value: '考试车' },
        { key: 2, value: '豪车' }
    ]
    var carTypeMap = {
        "car": "小车",
        "bus": "客车",
        "truck": "货车",
        // "moto": "摩托车",
        // "keyun": "客运",
        // "huoyun": "货运",
        // "weixian": "危险品",
        // "jiaolian": "教练员",
        // "chuzu": "出租车",
        // "wangyue": "网约车"
    }
    var carTypeStore = Object.keys(carTypeMap).map(a => ({ key: a, value: carTypeMap[a] }));

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-car/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '驾照类型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: carTypeStore,
                    check: 'required',
                    placeholder: '驾照类型'
                },
                {
                    header: '车辆类型：',
                    dataIndex: 'classification',
                    xtype: 'select',
                    store: classificationType,
                    check: 'required',
                    placeholder: '车辆类型'
                },
                {
                    header: '车型名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '车型名称'
                },
                {
                    header: '模拟器类型id：',
                    dataIndex: 'typeId',
                    xtype: 'select',
                    store: 'jiakao-misc!emulator-package/data/typeList',
                },
                {
                    header: '大版本号：',
                    dataIndex: 'majorVersion',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '大版本号'
                }, {
                    header: '品牌Logo：',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '车型年份名称：',
                    dataIndex: 'nameYear',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '车型年份名称'
                },
                {
                    header: '车型描述：',
                    dataIndex: 'description',
                    xtype: 'textarea',
                    placeholder: '车型描述'
                },
                {
                    header: '品牌Id：',
                    dataIndex: 'brandId',
                    xtype: 'select',
                    store: 'jiakao-misc!emulator-brand/data/brandList',
                    insert: [
                        {
                            key: '',
                            value: '选择车型'
                        }
                    ]
                },
                {
                    header: '指引操作图片：',
                    dataIndex: 'guideOperationUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'guideOperationUrl',
                        uploadIndex: 'guideOperationUrl',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '外观图片：',
                    dataIndex: 'exteriorViewUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'exteriorViewUrl',
                        uploadIndex: 'exteriorViewUrl',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                }

            ]
        }).add();
    }


    var list = function (panel) {
        Table({
            description: '车型列表',
            title: '车型列表',
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            search: [
                {
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{ key: '', value: '所有驾照类型' }].concat(carTypeStore)
                },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{ key: '', value: '所有状态' }].concat(statusStore)
                },
            ],
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!emulator-car/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '驾照类型',
                    dataIndex: 'carType',
                    render: function (data) {
                        return carTypeMap[data];
                    }
                },
                {
                    header: '车辆类型',
                    dataIndex: 'classification',
                    render: function (data) {
                        let span = ''
                        classificationType.forEach((ele) => {
                            if (ele.key == data) {
                                span = ele.value
                            }
                        })
                        return span
                    }
                },
                {
                    header: '车型名称：',
                    dataIndex: 'name'
                },
                {
                    header: '模拟器类型id：',
                    dataIndex: 'typeId'
                },
                {
                    header: '车型年份名称：',
                    dataIndex: 'nameYear'
                },
                {
                    header: '车型描述',
                    dataIndex: 'description'
                },
                {
                    header: '品牌Id',
                    dataIndex: 'brandId'
                },
                {
                    header: 'createTime：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: 'createUserId：',
                    dataIndex: 'createUserId'
                },
                {
                    header: 'createUserName：',
                    dataIndex: 'createUserName'
                },
                {
                    header: 'updateTime：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: 'updateUserId：',
                    dataIndex: 'updateUserId'
                },
                {
                    header: 'updateUserName：',
                    dataIndex: 'updateUserName'
                }

                ]
            },
            {
                name: '编辑',
                xtype: 'edit',
                width: 500,
                class: 'warning',
                title: '编辑',
                success: function (obj, dialog, e) {
                    dialog.close();
                    obj.render();
                },
                store: {
                    load: 'jiakao-misc!emulator-car/data/view',
                    save: 'jiakao-misc!emulator-car/data/update'
                },
                columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '驾照类型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: carTypeStore,
                    check: 'required',
                    placeholder: '驾照类型'
                },
                {
                    header: '车辆类型：',
                    dataIndex: 'classification',
                    xtype: 'select',
                    store: classificationType,
                    check: 'required',
                    placeholder: '车辆类型'
                },
                {
                    header: '车型名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '车型名称'
                },
                {
                    header: '模拟器类型id：',
                    dataIndex: 'typeId',
                    xtype: 'text',
                    placeholder: '模拟器类型id'
                },
                {
                    header: '大版本号：',
                    dataIndex: 'majorVersion',
                    xtype: 'text',
                    // check: 'required',
                    placeholder: '大版本号'
                },
                {
                    header: '品牌Logo：',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '车型年份名称：',
                    dataIndex: 'nameYear',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '车型年份名称'
                },
                {
                    header: '车型描述：',
                    dataIndex: 'description',
                    xtype: 'textarea',
                    placeholder: '车型描述'
                },
                {
                    header: '品牌Id：',
                    dataIndex: 'brandId',
                    xtype: 'select',
                    store: 'jiakao-misc!emulator-brand/data/brandList',
                    insert: [
                        {
                            key: '',
                            value: '选择车型'
                        }
                    ]
                },
                {
                    header: '指引操作图片：',
                    dataIndex: 'guideOperationUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'guideOperationUrl',
                        uploadIndex: 'guideOperationUrl',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '外观图片：',
                    dataIndex: 'exteriorViewUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'exteriorViewUrl',
                        uploadIndex: 'exteriorViewUrl',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                }
                ]
            },
            {
                name: '正式发布',
                class: 'info',
                click: function (table, row, lineData) {
                    Widgets.dialog.confirm('确认正式发布吗？', function (ev, status) {
                        if (status) {
                            Store(['jiakao-misc!emulator-car/data/update?status=2&id=' + lineData.id]).save().done(function () {
                                table.render()
                            }).fail(err => {
                                if (err.message != null) {
                                    Widgets.dialog.alert(err.message);
                                } else {
                                    Widgets.dialog.alert('接口请求失败...');

                                }
                            })

                        }
                    })

                },
                render: function (name, arr, index) {
                    return arr[index].status != 2 ? name : '';
                }
            },
            {
                name: '测试发布',
                class: 'primary',
                click: function (table, row, lineData) {
                    Widgets.dialog.confirm('确认测试发布吗？', function (ev, status) {
                        if (status) {
                            Store(['jiakao-misc!emulator-car/data/update?status=1&id=' + lineData.id]).save().done(function () {
                                table.render()
                            }).fail(err => {
                                if (err.message != null) {
                                    Widgets.dialog.alert(err.message);
                                } else {
                                    Widgets.dialog.alert('接口请求失败...');

                                }
                            })

                        }
                    })

                },
                render: function (name, arr, index) {
                    return arr[index].status != 1 ? name : '';
                }
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!emulator-car/data/delete'
            }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '驾照类型',
                dataIndex: 'carType',
                render: function (data) {
                    return carTypeMap[data];
                }
            },
            {
                header: '车辆类型',
                dataIndex: 'classification',
                render: function (data) {
                    let span = ''
                    classificationType.forEach((ele) => {
                        if (ele.key == data) {
                            span = ele.value
                        }
                    })
                    return span
                }
            },
            {
                header: '车型名称',
                dataIndex: 'name'
            },
            {
                header: '模拟器类型id',
                dataIndex: 'typeId'
            },
            {
                header: "状态",
                dataIndex: 'status',
                render: function (data, arr, i) {
                    return statusMap[data]
                }
            },
            {
                header: '品牌Logo',
                dataIndex: 'image',
                render: function (data) {
                    if (data) {
                        return '<a><img style="width:120px;" src="' + data + '" /></a>';
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('品牌Logo', {}).done(function (dialog) {
                        $(dialog.body).append('<img src="' + lineData.image + '" />')
                    })
                }
            },
            {
                header: '车型年份名称：',
                dataIndex: 'nameYear'
            },
            {
                header: '车型描述',
                dataIndex: 'description'
            },
            {
                header: '品牌Id',
                dataIndex: 'brandId'
            },
            {
                header: '指引操作图片',
                dataIndex: 'guideOperationUrl',
                render: function (data) {
                    if (data) {
                        return '<a><img style="width:120px;" src="' + data + '" /></a>';
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('指引操作图片', {}).done(function (dialog) {
                        $(dialog.body).append('<img src="' + lineData.guideOperationUrl + '" />')
                    })
                }
            },
            {
                header: '外观图片',
                dataIndex: 'exteriorViewUrl',
                render: function (data) {
                    if (data) {
                        return '<a><img style="width:120px;" src="' + data + '" /></a>';
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('外观图片', {}).done(function (dialog) {
                        $(dialog.body).append('<img src="' + lineData.exteriorViewUrl + '" />')
                    })
                }
            },
            {
                header: '大版本号',
                dataIndex: 'majorVersion'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!emulator-car/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});