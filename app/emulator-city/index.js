/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var carTypeMap = {
        "car": "小车",
        "bus": "客车",
        "truck": "货车",
        // "moto": "摩托车",
        // "keyun": "客运",
        // "huoyun": "货运",
        // "weixian": "危险品",
        // "jiaolian": "教练员",
        // "chuzu": "出租车",
        // "wangyue": "网约车"
    }
    var carTypeStore = Object.keys(carTypeMap).map(a => ({ key: a, value: carTypeMap[a] }));

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-city/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    var provinceName = $(form).find('#province').find('option:selected').text();

                    var cityName = $(form).find('#city').find('option:selected').text();
                    if (provinceName == '全国') {
                        cityName = '全国'
                    }
                    return {

                        cityName: cityName
                    };


                }
            },
            renderAfter: function (table, dom) {
                function r() {
                    const value = dom.item('carType')[0].value;
                    Plugin('simple!auto-prompt', {
                        store: `jiakao-misc!emulator-car/data/carList?carType=${value}`,
                        value: '',
                        dataIndex: 'carList',
                        isMulti: true,
                        index: {
                            key: 'key',
                            value: 'value',
                            search: 'value'
                        },
                        target: dom.item('carList2')
                    }).render();
                    Plugin('simple!auto-prompt', {
                        store: `jiakao-misc!emulator-car/data/carList?classification=2&carType=${value}`,
                        value: '',
                        dataIndex: 'luxuryCarList',
                        isMulti: true,
                        index: {
                            key: 'key',
                            value: 'value',
                            search: 'value'
                        },
                        target: dom.item('luxuryCarList2')
                    }).render();
                }

                dom.item('carType').on('change', function () {
                    r();
                });

                r();
            },
            columns: [
                {
                    header: '驾照类型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: carTypeStore,
                    check: 'required',
                    placeholder: '驾照类型'
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district2', {

                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    header: '试题id列表：',
                    dataIndex: 'questionId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '试题id列表'
                },
                {
                    header: '车型列表：',
                    dataIndex: 'carList',
                    xtype: function () {
                        return '<div data-item="carList2"></div>';
                    }
                },
                {
                    header: '豪车列表(旧版本)：',
                    dataIndex: 'luxuryCarList',
                    xtype: function () {
                        return '<div data-item="luxuryCarList2"></div>';
                    }
                },
                {
                    header: '是否地方题:',
                    dataIndex: 'area',
                    xtype: 'radio',
                    store: [{
                        key: true,
                        value: '是'
                    },
                    {
                        key: false,
                        value: '否'
                    }
                    ]
                }

            ]
        }).add();
    }

    var addGoodsEdit = function (table) {
        Store().load().done(function (store) {
            Table().edit({}, {
                title: '添加',
                width: 500,
                store: {
                    load: 'jiakao-misc!emulator-city/data/getConfig',
                    save: 'jiakao-misc!emulator-city/data/updateConfig'
                },
                success: function (obj, dialog) {
                    dialog.close();
                    table.render();
                },
                columns: [{
                    header: '配置',
                    dataIndex: 'config',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择'
                    }, {
                        key: 1,
                        value: '外置弹窗'
                    }, {
                        key: 2,
                        value: '内置弹窗'
                    }],
                },

                ]
            });
        }).fail(function () { });
    }
    var list = function (panel) {
        Table({
            description: '模拟器的城市关系列表',
            title: '模拟器的城市关系列表',

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                },
                {
                    name: '商品购买窗口配置',
                    class: 'info',
                    click: addGoodsEdit
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            search: [{
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }]
                    }
                }, function (plugin, code) {

                }),
            },
            {
                dataIndex: 'carType',
                xtype: 'select',
                store: [{ key: '', value: '所有驾照类型' }].concat(carTypeStore)
            },
            ],
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!emulator-city/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '驾照类型：',
                    dataIndex: 'carType',
                    render: function (data) {
                        return carTypeMap[data];
                    }
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode'
                },
                {
                    header: '城市名称：',
                    dataIndex: 'cityName'
                },
                {
                    header: '试题id列表：',
                    dataIndex: 'questionId'
                },
                {
                    header: '车型列表：',
                    dataIndex: 'carList'
                },
                {
                    header: '豪车列表(旧版本)：',
                    dataIndex: 'luxuryCarList'
                },
                {
                    header: '创建时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人ID：',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人：',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人ID：',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人：',
                    dataIndex: 'updateUserName'
                }

                ]
            },
            {
                name: '编辑',
                width: 500,
                class: 'warning',
                click: function (table, lineDom, lineData, dom, data, index) {
                    Store([`jiakao-misc!emulator-city/data/view?id=${lineData.id}`]).load([{}]).done(function (store, data) {
                        let lineData = data['emulator-city']['data']['view']['data'] || {};

                        Table().edit(lineData, {
                            store: `jiakao-misc!emulator-city/data/update?id=${lineData.id}`,
                            success: function (obj, dialog, e) {
                                dialog.close();
                                table.render()
                            },
                            submitHandler: function (form, fun) {
                                var provinceName = $(form).find('#province').find('option:selected').text();

                                var cityName = $(form).find('#city').find('option:selected').text();
                                if (provinceName == '全国') {
                                    cityName = '全国'
                                }
                                return {

                                    cityName: cityName
                                };

                            },
                            renderAfter: function (table, dom) {
                                function r(isChange) {
                                    const value = dom.item('carType')[0].value;
                                    Plugin('simple!auto-prompt', {
                                        store: `jiakao-misc!emulator-car/data/carList?carType=${value}`,
                                        value: !isChange ? lineData.carList : '',
                                        dataIndex: 'carList',
                                        isMulti: true,
                                        index: {
                                            key: 'key',
                                            value: 'value',
                                            search: 'value'
                                        },
                                        target: dom.item('carList2')
                                    }).render();
                                    Plugin('simple!auto-prompt', {
                                        store: `jiakao-misc!emulator-car/data/carList?classification=2&carType=${value}`,
                                        value: !isChange ? lineData.luxuryCarList : '',
                                        dataIndex: 'luxuryCarList',
                                        isMulti: true,
                                        index: {
                                            key: 'key',
                                            value: 'value',
                                            search: 'value'
                                        },
                                        target: dom.item('luxuryCarList2')
                                    }).render();
                                }

                                dom.item('carType').on('change', function () {
                                    r(true);
                                });

                                r();
                            },
                            columns: [{
                                dataIndex: 'id',
                                xtype: 'hidden'
                            },
                            {
                                header: '驾照类型：',
                                dataIndex: 'carType',
                                xtype: 'select',
                                store: carTypeStore,
                                check: 'required',
                                placeholder: '驾照类型'
                            },
                            {
                                header: '城市编码：',
                                dataIndex: 'cityCode',
                                xtype: Plugin('jiakao-misc!select-district2', {
                                    name: 'cityCode',
                                    value: lineData.cityCode.toString(),
                                    insert: {
                                        province: [
                                            {
                                                code: '',
                                                name: '请选择'
                                            }
                                        ],
                                        city: [
                                            {
                                                code: '',
                                                name: '请选择'
                                            }
                                        ]
                                    },
                                }, function (plugin, code) {
                                }),
                                placeholder: '城市编码'
                            },
                            {
                                header: '城市名称：',
                                dataIndex: 'cityName',
                                xtype: 'hidden',
                                maxlength: 45,
                                placeholder: '城市名称'
                            },
                            {
                                header: '试题id列表：',
                                dataIndex: 'questionId',
                                xtype: 'text',
                                placeholder: '试题id列表'
                            },
                            {
                                header: '车型列表：',
                                dataIndex: 'carList',
                                xtype: function () {
                                    return '<div data-item="carList2"></div>';
                                }
                            },
                            {
                                header: '豪车列表(旧版本)：',
                                dataIndex: 'luxuryCarList',
                                xtype: function () {
                                    return '<div data-item="luxuryCarList2"></div>';
                                }
                            },
                            {
                                header: '是否地方题:',
                                dataIndex: 'area',
                                xtype: 'radio',
                                store: [{
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                                ]
                            }

                            ]
                        });
                    })
                },
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!emulator-city/data/delete'
            }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },

            {
                header: '城市名称',
                dataIndex: 'cityName'
            },
            {
                header: '驾照类型',
                dataIndex: 'carType',
                render: function (data) {
                    return carTypeMap[data];
                }
            },
            {
                header: '试题id列表',
                dataIndex: 'questionId'
            },
            {
                header: '是否地方题',
                dataIndex: 'area',
                render: function (data) {
                    return data ? '是' : '否'
                }
            },

            {
                header: '车型列表',
                dataIndex: 'carNameList'
            },
            {
                header: '豪车列表(旧版本)',
                dataIndex: 'luxuryCarNameList'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!emulator-city/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});