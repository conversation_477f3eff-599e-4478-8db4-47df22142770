/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var createExam = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-exam/data/createExam',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    var cityName = $(form).find('#city').find('option:selected').text()
                    var cityCode = form.cityCode.value
                    var metaId = form.newmetaId && form.newmetaId.value || ''
                    if (cityCode == '000000' || !cityCode) {
                        metaId = ''
                    }
                    return {
                        cityName: cityName,
                        metaId: metaId
                    };
                }
            },
            renderAfter(table, dom) {
                function renderExam(cityCode) {
                    Plugin('simple!auto-prompt', {
                        store: `jiakao-misc!emulator-exam/data/getMetaByCity?cityCode=${cityCode}`,
                        value: '',
                        dataIndex: 'newmetaId',
                        isMulti: false,
                        index: {
                            key: 'metaId',
                            value: 'metaName',
                            search: 'metaName'
                        },
                        target: dom.item('newmetaId')
                    }).render();
                }
                renderCity()
                dom.item('newmetaId-group').css('display', 'none')
                function renderCity() {
                    Plugin('jiakao-misc!select-district', {

                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        },
                        change: function (event) {
                            var cityDom = $(event.target).parent().parent().find('select[name="cityCode"]');
                            if (!$(event.target).val()) {
                                dom.item('newmetaId-group').css('display', 'none')
                                return
                            }
                            dom.item('newmetaId-group').css('display', 'block')
                            renderExam(cityDom.val())
                        },
                        target: dom.item('cityCode')
                    }, function (plugin, code) {

                    }).render()
                }
            },
            columns: [{
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: function () {
                    return '<div data-item="cityCode"></div>'
                }
                // xtype: Plugin('jiakao-misc!select-district', {

                //     name: 'cityCode',
                //     areaName: 'areaCode',
                //     hideArea: true,
                //     insert: {
                //         province: [{
                //             code: '',
                //             name: '请选择省份'
                //         }],
                //         city: [{
                //             code: '',
                //             name: '请选择市'
                //         }],
                //         area: [{
                //             code: '',
                //             name: '请选择区域'
                //         }]
                //     }
                // }, function (plugin, code) {

                // }),
            },
            {
                header: '考场：',
                dataIndex: 'newmetaId',
                xtype: function () {
                    return '<div data-item="newmetaId"></div>';
                }
            },

            {
                header: '数量：',
                dataIndex: 'count',
                xtype: 'text',
                value: 6
            },


            ]
        }).add();
    }
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-exam/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    var cityName = $(form).find('#city').find('option:selected').text()
                    var cityCode = form.cityCode.value
                    var metaId = form.newmetaId && form.newmetaId.value || ''
                    if (cityCode == '000000' || !cityCode) {
                        metaId = ''
                    }
                    return {
                        cityName: cityName,
                        metaId: metaId
                    };
                }
            },
            renderAfter(table, dom) {
                function renderExam(cityCode) {
                    Plugin('simple!auto-prompt', {
                        store: `jiakao-misc!emulator-exam/data/getMetaByCity?cityCode=${cityCode}`,
                        value: '',
                        dataIndex: 'newmetaId',
                        isMulti: false,
                        index: {
                            key: 'metaId',
                            value: 'metaName',
                            search: 'metaName'
                        },
                        target: dom.item('newmetaId')
                    }).render();
                }
                renderCity()
                dom.item('newmetaId-group').css('display', 'none')
                function renderCity() {
                    Plugin('jiakao-misc!select-district', {

                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        },
                        change: function (event) {
                            var cityDom = $(event.target).parent().parent().find('select[name="cityCode"]');
                            if (!$(event.target).val()) {
                                dom.item('newmetaId-group').css('display', 'none')
                                return
                            }
                            dom.item('newmetaId-group').css('display', 'block')
                            renderExam(cityDom.val())
                        },
                        target: dom.item('cityCode')
                    }, function (plugin, code) {

                    }).render()
                }
            },
            columns: [{
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: function () {
                    return '<div data-item="cityCode"></div>'
                }
            },
            {
                header: '考场列表：',
                dataIndex: 'newmetaId',
                xtype: function () {
                    return '<div data-item="newmetaId"></div>';
                }
            },
            {
                header: '试题id列表：',
                dataIndex: 'questionIds',
                xtype: 'text',
                // maxlength: 32,
                placeholder: '试题id列表'
            },
            {
                header: '是否新规：',
                dataIndex: 'news',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                },
                {
                    key: false,
                    value: '否'
                }
                ]
            }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '城市通用考试规则列表',
            title: '城市通用考试规则列表',

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                },
                {
                    name: '生成考试',
                    class: 'warning',
                    click: createExam
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!emulator-exam/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode'
                },
                {
                    header: '城市名称：',
                    dataIndex: 'cityName'
                },
                {
                    header: '试题id列表：',
                    dataIndex: 'questionIds'
                },
                {
                    header: '是否新规：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'news'
                },
                {
                    header: 'createTime：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人ID：',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人：',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人ID：',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人：',
                    dataIndex: 'updateUserName'
                }

                ]
            },
            {
                name: '编辑',
                class: 'warning',
                click: function (dom, item, lineData) {
                    //
                    Store(['jiakao-misc!emulator-exam/data/view?id=' + lineData.id]).load().done(function (plugin, data) {
                        var avaiCodeObject = data['emulator-exam'].data.view.data
                        Table().edit(avaiCodeObject, {
                            title: '编辑',
                            width: 500,
                            store: 'jiakao-misc!emulator-exam/data/update',
                            success: function (obj, dialog, e) {
                                dialog.close();
                                obj.render();
                            },
                            columns: [{
                                dataIndex: 'id',
                                xtype: 'hidden'
                            },
                            {
                                header: '城市编码：',
                                dataIndex: 'cityCode',
                                xtype: 'text',
                                placeholder: '城市编码'
                            },
                            {
                                header: '城市名称：',
                                dataIndex: 'cityName',
                                xtype: 'text',
                                maxlength: 64,
                                placeholder: '城市名称'
                            },
                            {
                                header: '考场id：',
                                dataIndex: 'metaId',
                                xtype: avaiCodeObject.metaId <= 0 ? 'hidden' : 'text',
                                placeholder: '考场id'
                            },
                            {
                                header: '考场名称：',
                                dataIndex: 'metaName',
                                xtype: avaiCodeObject.metaId <= 0 ? 'hidden' : 'text',
                                placeholder: '考场名称'
                            },
                            {
                                header: '试题id列表：',
                                dataIndex: 'questionIds',
                                xtype: 'text',
                                // maxlength: 32,
                                placeholder: '试题id列表'
                            },
                            {
                                header: '是否新规：',
                                dataIndex: 'news',
                                xtype: 'radio',
                                store: [{
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                                ]
                            }

                            ]
                        })
                    }).fail(function (str) {
                        Widgets.dialog.alert(str.message);
                    })


                }
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!emulator-exam/data/delete'
            }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },

            {
                header: '城市名称',
                dataIndex: 'cityName'
            },
            {
                header: '考场名称',
                dataIndex: 'metaName'
            },
            {
                header: '试题id列表',
                dataIndex: 'questionIds'
            },
            {
                header: '是否新规',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'news'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!emulator-exam/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});