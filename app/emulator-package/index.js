/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'simple!core/ajax'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Ajax) {
    var statusMap = {
        '0': '未发布',
        '1': '测试发布',
        '2': '正式发布'
    }
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-package/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '模拟器名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '模拟器名称'
                },
                {
                    header: '模拟器类型id：',
                    dataIndex: 'typeId',
                    xtype: 'select',
                    store: 'jiakao-misc!emulator-package/data/typeList',
                },
                {
                    header: '资源下载地址：',
                    dataIndex: 'resUrl',
                    xtype: 'textarea',
                    maxlength: 512,
                    placeholder: '资源下载地址'
                },
                {
                    header: '打包文件md5：',
                    dataIndex: 'md5',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '打包文件md5'
                },
                {
                    header: '资源版本：',
                    dataIndex: 'version',
                    xtype: 'text',
                    placeholder: '资源版本'
                },
                {
                    header: '大版本号：',
                    dataIndex: 'majorVersion',
                    xtype: 'text',
                    placeholder: '大版本号'
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: 'text',
                    placeholder: '城市编码'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '模拟器打包资sear源列表',
            title: '模拟器打包资源列表',
            search: [

                {

                    dataIndex: 'typeId',
                    xtype: 'select',
                    store: 'jiakao-misc!emulator-package/data/typeList',
                    insert: [{
                        key: '',
                        value: '模拟器类型id'
                    }],


                },
                {
                    // dataIndex: 'cityCode',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),




                },
                {

                    dataIndex: 'majorVersion',
                    xtype: 'text',
                    placeholder: '大版本号'


                }
            ],
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }, {
                        name: '上传基础包',
                        class: 'warning',
                        click: function (table) {

                            Table({
                                title: '添加',
                                // store: 'jiakao-misc!emulator-package/data/uploadDefaultData',

                                //table渲染完成的回调函数
                                renderAfter: function (config, dom, data) {


                                },
                                form: {
                                    submitHandler: function (form) {

                                        var name = $(form).find('#name').val();
                                        var desc = $(form).find('#desc').val()
                                        var typeId = $(form).find('#typeId').val()
                                        var cityCode = $(form).find('[name="cityCode"]').val()
                                        var majorVersion = $(form).find('#majorVersion').val();
                                        var data = $(form).find('#data').get(0).files[0];
                                        var formData = new FormData();
                                        formData.append('data', data)

                                        Ajax.request(window.j.host.local + '/api/admin/emulator-package/upload-default-data.htm?name=' + name + '&majorVersion=' + majorVersion + '&typeId=' + typeId + "&desc=" + desc, {
                                            data: formData,
                                            method: 'post',
                                            contentType: false,
                                            processData: false,
                                            success: function (data) {
                                                $(form).find('.close').click();
                                                table.render();

                                            },
                                            error: function (err) {
                                                Widgets.dialog.alert(err.message);
                                                console.log(err);



                                            },
                                            // progress: function (percentage) {
                                            //     progress.call(this, percentage);
                                            // }
                                        })



                                        //返回值为false，不提交
                                        return false;
                                    }
                                },

                                columns: [{
                                        header: '模拟器类型id：',
                                        dataIndex: 'typeId',
                                        xtype: 'select',
                                        store: 'jiakao-misc!emulator-package/data/typeList',
                                    },
                                    {
                                        header: '描述：',
                                        dataIndex: 'desc',
                                        xtype: 'text',
                                        placeholder: '描述'

                                    },
                                    // {
                                    //     header:'城市编码：',
                                    //     dataIndex:'cityCode',
                                    //     xtype: Plugin('jiakao-misc!select-district', {
                                    //         name: 'cityCode',
                                    //         areaName: 'areaCode',
                                    //         hideArea: true,
                                    //         insert: {
                                    //             province: [{
                                    //                 code: '',
                                    //                 name: '请选择省份'
                                    //             }],
                                    //             city: [{
                                    //                 code: '',
                                    //                 name: '请选择市'
                                    //             }],
                                    //             area: [{
                                    //                 code: '',
                                    //                 name: '请选择区域'
                                    //             }]
                                    //         }
                                    //     }, function (plugin, code) {

                                    //     }),
                                    // },
                                    {
                                        header: '大版本号：',
                                        dataIndex: 'majorVersion',
                                        xtype: 'text',
                                        placeholder: '大版本号'


                                    }, {
                                        header: '上传文件：',
                                        dataIndex: 'data',
                                        xtype: 'file',




                                    },

                                ]
                            }).add()
                        }
                    },
                    {
                        name: '打包',
                        class: 'danger',
                        click: function (table) {

                            Table({
                                title: '打包',
                                renderAfter: function (config, dom, data) {

                                },
                                form: {
                                    submitHandler: function (form) {


                                        var typeId = $(form).find('#typeId').val()
                                        var cityCode = $(form).find('[name="cityCode"]').val()
                                        var majorVersion = $(form).find('#majorVersion').val();
                                        var desc = $(form).find('#desc').val();
                                        Store(['jiakao-misc!emulator-package/data/createPackage?typeId=' + typeId + '&cityCode=' + cityCode + '&majorVersion=' + majorVersion + "&desc=" + desc]).load().done(data => {
                                            $(form).find('.close').click();
                                            table.render();

                                        }).fail(err => {
                                            Widgets.dialog.alert(err.message);
                                            console.log(err);
                                        });





                                        return false;
                                    }
                                },

                                columns: [

                                    {
                                        header: '模拟器类型id：',
                                        dataIndex: 'typeId',
                                        xtype: 'select',
                                        store: 'jiakao-misc!emulator-package/data/typeList',
                                    },
                                    {
                                        header: '描述：',
                                        dataIndex: 'desc',
                                        xtype: 'text',
                                        placeholder: '描述'

                                    },
                                    {
                                        header: '城市编码：',
                                        dataIndex: 'cityCode',
                                        xtype: Plugin('jiakao-misc!select-district', {
                                            name: 'cityCode',
                                            areaName: 'areaCode',
                                            hideArea: true,
                                            insert: {
                                                province: [{
                                                    code: '',
                                                    name: '请选择省份'
                                                }],
                                                city: [{
                                                    code: '',
                                                    name: '请选择市'
                                                }],
                                                area: [{
                                                    code: '',
                                                    name: '请选择区域'
                                                }]
                                            }
                                        }, function (plugin, code) {

                                        }),
                                    },
                                    {
                                        header: '大版本号：',
                                        dataIndex: 'majorVersion',
                                        xtype: 'text',
                                        placeholder: '大版本号'


                                    },

                                ]
                            }).add()
                        }
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!emulator-package/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '模拟器名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '模拟器类型id：',
                            dataIndex: 'typeId'
                        },
                        {
                            header: '资源下载地址：',
                            dataIndex: 'resUrl'
                        },
                        {
                            header: '打包文件md5：',
                            dataIndex: 'md5'
                        },
                        {
                            header: '资源版本：',
                            dataIndex: 'version'
                        },
                        {
                            header: '大版本号：',
                            dataIndex: 'majorVersion'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人ID：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人ID：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!emulator-package/data/view',
                        save: 'jiakao-misc!emulator-package/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '模拟器名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '模拟器名称'
                        },
                        {
                            header: '模拟器类型id：',
                            dataIndex: 'typeId',
                            xtype: 'text',
                            placeholder: '模拟器类型id'
                        },
                        {
                            header: '资源下载地址：',
                            dataIndex: 'resUrl',
                            xtype: 'textarea',
                            maxlength: 512,
                            placeholder: '资源下载地址'
                        },
                        {
                            header: '打包文件md5：',
                            dataIndex: 'md5',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '打包文件md5'
                        },
                        {
                            header: '资源版本：',
                            dataIndex: 'version',
                            xtype: 'text',
                            placeholder: '资源版本'
                        },
                        {
                            header: '大版本号：',
                            dataIndex: 'majorVersion',
                            xtype: 'text',
                            placeholder: '大版本号'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode',
                            xtype: 'text',
                            placeholder: '城市编码'
                        }

                    ]
                },
                {
                    name: '正式发布',
                    class: 'info',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认正式发布吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!emulator-package/data/update?status=2&id=' + lineData.id]).save().done(function () {
                                    table.render()
                                }).fail(err => {
                                    if (err.message != null) {
                                        Widgets.dialog.alert(err.message);
                                    } else {
                                        Widgets.dialog.alert('接口请求失败...');

                                    }
                                })

                            }
                        })

                    },
                    render: function (name, arr, index) {
                        return arr[index].status != 2 ? name : '';
                    }
                },
                {
                    name: '测试发布',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认测试发布吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!emulator-package/data/update?status=1&id=' + lineData.id]).save().done(function () {
                                    table.render()
                                }).fail(err => {
                                    if (err.message != null) {
                                        Widgets.dialog.alert(err.message);
                                    } else {
                                        Widgets.dialog.alert('接口请求失败...');

                                    }
                                })

                            }
                        })

                    },
                    render: function (name, arr, index) {
                        return arr[index].status != 1 ? name : '';
                    }
                }, {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!emulator-package/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '模拟器名称',
                    dataIndex: 'name'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '模拟器类型id',
                    dataIndex: 'typeId'
                },
                {
                    header: '资源下载地址',
                    dataIndex: 'resUrl'
                },
                {
                    header: "状态",
                    dataIndex: 'status',
                    render: function (data, arr, i) {
                        return statusMap[data]
                    }
                }, {
                    header: '打包文件md5',
                    dataIndex: 'md5'
                },
                {
                    header: '资源版本',
                    dataIndex: 'version'
                },
                {
                    header: '大版本号',
                    dataIndex: 'majorVersion'
                },

                {
                    header: '城市名称',
                    dataIndex: 'cityName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!emulator-package/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
