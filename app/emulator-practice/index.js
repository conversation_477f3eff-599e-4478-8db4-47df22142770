/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {



    var list = function (panel) {
        Table({
            description: '灯光模拟练习数据列表',
            title: '灯光模拟练习数据列表',
            search: [{
                dataIndex: 'type',
                xtype: 'select',
                store: [{
                    key: 1,
                    value: '以题目搜索'
                }, {
                    key: 2,
                    value: '以用户搜索'
                }]
            }, {
                dataIndex: 'questionId',
                xtype: 'text',
                placeholder: '试题id',
            }, {
                dataIndex: 'userId',
                xtype: 'text',
                placeholder: 'mucangId',
            },
            {
                header: '所有车型：',
                dataIndex: 'carNameId',
                xtype: 'select',
                store: 'jiakao-misc!emulator-practice/data/carList',
                insert: [
                    {
                        key: '',
                        value: '选择车型'
                    }
                ]
            },
            {
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                    }
                }, function (plugin, code) {
                }),
            },
            {
                dataIndex: 'startTime',
                xtype: 'date',
                placeholder: '开始时间'
            },
            {
                dataIndex: 'endTime',
                xtype: 'date',
                placeholder: '结束时间'
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!emulator-practice/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '试题id：',
                            dataIndex: 'questionId'
                        },
                        {
                            header: '车id：',
                            dataIndex: 'carNameId'
                        },
                        {
                            header: '车名称：',
                            dataIndex: 'carName'
                        },
                        {
                            header: '城市编号：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '城市名称：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '练习次数：',
                            dataIndex: 'allAnswerPv'
                        },
                        {
                            header: '练习人数：',
                            dataIndex: 'answerUserNum'
                        },
                        {
                            header: '正确次数：',
                            dataIndex: 'rightAnswerPv'
                        },
                        {
                            header: '正确率：',
                            dataIndex: 'correctness'
                        },
                        {
                            header: '答题时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'practiceTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建者名称：',
                            dataIndex: 'createUesrName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '更新人名称：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!emulator-practice/data/view',
                        save: 'jiakao-misc!emulator-practice/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '试题id：',
                            dataIndex: 'questionId',
                            xtype: 'text',
                            placeholder: '试题id'
                        },
                        {
                            header: '车id：',
                            dataIndex: 'carNameId',
                            xtype: 'text',
                            placeholder: '车id'
                        },
                        {
                            header: '车名称：',
                            dataIndex: 'carName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '车名称'
                        },
                        {
                            header: '城市编号：',
                            dataIndex: 'cityCode',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '城市编号'
                        },
                        {
                            header: '城市名称：',
                            dataIndex: 'cityName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '城市名称'
                        },
                        {
                            header: '练习次数：',
                            dataIndex: 'allAnswerPv',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '练习次数'
                        },
                        {
                            header: '练习人数：',
                            dataIndex: 'answerUserNum',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '练习人数'
                        },
                        {
                            header: '正确次数：',
                            dataIndex: 'rightAnswerPv',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '正确次数'
                        },
                        {
                            header: '正确率：',
                            dataIndex: 'correctness',
                            xtype: 'text',
                            placeholder: '正确率'
                        },
                        {
                            header: '答题时间：',
                            dataIndex: 'practiceTime',
                            xtype: 'date',
                            placeholder: '答题时间'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId',
                            xtype: 'text',
                            placeholder: '创建人id'
                        },
                        {
                            header: '创建者名称：',
                            dataIndex: 'createUesrName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '创建者名称'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId',
                            xtype: 'text',
                            placeholder: '更新人id'
                        },
                        {
                            header: '更新人名称：',
                            dataIndex: 'updateUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '更新人名称'
                        },
                        {
                            header: '更新时间：',
                            dataIndex: 'updateTime',
                            xtype: 'date',
                            placeholder: '更新时间'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!emulator-practice/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '日期',
                    dataIndex: 'practiceTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd');
                    }
                },
                {
                    header: '车名称',
                    dataIndex: 'carName'
                },
                {
                    header: '试题id',
                    dataIndex: 'questionId'
                },
                {
                    header: '练习次数',
                    dataIndex: 'allAnswerPv'
                },
                {
                    header: '练习人数',
                    dataIndex: 'answerUserNum'
                },
                {
                    header: '正确次数',
                    dataIndex: 'rightAnswerPv'
                },
                {
                    header: '正确率',
                    dataIndex: 'correctness'
                },
            ]
        }, ['jiakao-misc!emulator-practice/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});