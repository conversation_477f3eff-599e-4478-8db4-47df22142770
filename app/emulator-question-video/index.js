/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-question-video/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '题目id：',
                    dataIndex: 'questionId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '题目id'
                },
                {
                    header: '车型id：',
                    dataIndex: 'carId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '车型id'
                },
                {
                    header: '视频：',
                    dataIndex: 'video',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '视频'
                },
                {
                    header: '加密视频：',
                    dataIndex: 'videoEncode',
                    xtype: 'textarea',
                    rows: 6,
                    placeholder: '加密视频',
                    value: '{"1.0":""}'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '模拟器操作视频列表',
            title: '模拟器操作视频列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!emulator-question-video/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '题目id：',
                            dataIndex: 'questionId'
                        },
                        {
                            header: '车型id：',
                            dataIndex: 'carId'
                        },
                        {
                            header: '视频：',
                            dataIndex: 'video'
                        },
                        {
                            header: '加密视频：',
                            dataIndex: 'videoEncode'
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: 'createUserId：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: 'updateUserId：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!emulator-question-video/data/view',
                        save: 'jiakao-misc!emulator-question-video/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '题目id：',
                            dataIndex: 'questionId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '题目id'
                        },
                        {
                            header: '车型id：',
                            dataIndex: 'carId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '车型id'
                        },
                        {
                            header: '视频：',
                            dataIndex: 'video',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '视频'
                        },
                        {
                            header: '加密视频：',
                            dataIndex: 'videoEncode',
                            xtype: 'textarea',
                            rows: 6,
                            placeholder: '加密视频'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!emulator-question-video/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '题目id',
                    dataIndex: 'questionId'
                },
                {
                    header: '车型id',
                    dataIndex: 'carId'
                },
                {
                    header: '视频',
                    dataIndex: 'video'
                },
                {
                    header: '加密视频',
                    dataIndex: 'videoEncode'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!emulator-question-video/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});