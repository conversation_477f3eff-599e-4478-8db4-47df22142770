/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-question/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '试题内容：',
                    dataIndex: 'question',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '试题内容'
                },
                {
                    header: '试题答案：',
                    dataIndex: 'answer',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '试题答案'
                },
                {
                    header: '操作时间：',
                    dataIndex: 'operatingTime',
                    xtype: 'number',
                    check: 'required',
                    placeholder: '请输入，单位为秒'
                },
                {
                    header: '语音链接：',

                    dataIndex: 'voiceUrl',
                    xtype: Plugin('jiakao-misc!upload3', {
                        dataIndex: 'voiceUrl',
                        uploadIndex: 'voiceUrl',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm',

                    }, function () {
                        console.log(arguments)
                    })
                },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '模拟器试题列表',
            title: '模拟器试题列表',

            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }, {
                        name: '刷新试题',
                        class: 'danger',
                        click: function (table) {
                            // Store(['jiakao-misc!emulator-question/data/refresh']).load().done(data=>{table.render()}).fail(err=>{console.log(err);})
                            Widgets.dialog.confirm('确认刷新试题吗？', function (ev, status) {
                                if (status) {
                                    Store(['jiakao-misc!emulator-question/data/refresh']).load().done(data => {
                                        table.render()
                                    }).fail(err => {
                                        console.log(err);
                                    })


                                }
                            })

                        }
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!emulator-question/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '试题内容：',
                            dataIndex: 'question'
                        },
                        {
                            header: '试题答案：',
                            dataIndex: 'answer'
                        },
                        {
                            header: '操作时间：',
                            dataIndex: 'operatingTime',
                            render: function (data) {
                                return data + "s"
                            }
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人ID：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人ID：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!emulator-question/data/view',
                        save: 'jiakao-misc!emulator-question/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '试题内容：',
                            dataIndex: 'question',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '试题内容'
                        },
                        {
                            header: '试题答案：',
                            dataIndex: 'answer',
                            xtype: 'text',
                            maxlength: 128,
                            placeholder: '试题答案'
                        },
                        {
                            header: '操作时间：',
                            dataIndex: 'operatingTime',
                            xtype: 'number',
                            check: 'required',
                            placeholder: '请输入，单位为秒'
                        },
                        {
                            header: '语音链接：',
                            dataIndex: 'voiceUrl',
                            xtype: Plugin('jiakao-misc!upload3', {
                                dataIndex: 'voiceUrl',
                                uploadIndex: 'voiceUrl',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm',

                            }, function () {
                                console.log(arguments)
                            })
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!emulator-question/data/delete'
                },
                {
                    name: '文字自动转语音',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('文字自动转语音', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!emulator-question/data/audio?id=' + lineData.id]).load().done(function (store) {
                                    // Widgets.dialog.alert('审核结束成功')
                                }).fail(function (res) {
                                    Widgets.dialog.alert(res.message)
                                });
                            } else {}
                        })
                    }
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '试题内容',
                    dataIndex: 'question'
                },
                {
                    header: '试题答案',
                    dataIndex: 'answer'
                },
                {
                    header: '操作时间',
                    dataIndex: 'operatingTime',
                    render: function (data) {
                        return data + "s"
                    }

                },
                {
                    header: '语音链接',
                    dataIndex: 'voiceUrl',
                    render: function (table, rows, lineData) {
                        if (!lineData.voiceUrl) {
                            return '无';
                        }
                        return `<a href="${lineData.voiceUrl}" target="__blank">${lineData.voiceUrl}</a>`;
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!emulator-question/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});