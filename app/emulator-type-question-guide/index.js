/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var list = function (panel, routeData, id) {
        Table({
                description: '设置操作步骤列表',
                title: '设置操作步骤列表',

                buttons: {
                    top: [{
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            }
                        },
                        {
                            name: '添加',
                            class: 'primary',
                            click: function (table, dom, config, selectedArr) {
                                //  console.log(selectedArr.join(','));

                                if (selectedArr.length > 0) {


                                    Widgets.dialog.confirm('确认添加吗？', function (ev, status) {
                                        if (status) {
                                            Store(['jiakao-misc!emulator-type-question/data/updateGuide?instruction=' + selectedArr.join(',') + '&id=' + routeData.id], [{

                                            }]).load().done(function (store) {
                                                Widgets.dialog.alert('添加成功')
                                                table.render()
                                            }).fail(function (err) {
                                                Widgets.dialog.alert(err.message)
                                            });
                                        } else {}
                                    })
                                } else {
                                    Widgets.dialog.alert('请选择添加的数据')
                                }

                            },

                        },
                    ],
                    bottom: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }]
                },
                selector: {
                    dataIndex: 'keyword',
                },
                columns: [{
                        header: 'keyword',
                        dataIndex: 'keyword'
                    },
                    {
                        header: 'region',
                        dataIndex: 'region'
                    },
                    {
                        header: 'slot',
                        dataIndex: 'slot'
                    },
                    {
                        header: 'tip',
                        dataIndex: 'tip'
                    },
                ]
            },
            ['jiakao-misc!emulator-type-question/data/getGuideList?id=' + id],
            panel,
            null).render();
    }

    return {
        list: list
    }

});