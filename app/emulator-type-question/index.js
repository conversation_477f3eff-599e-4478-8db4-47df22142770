/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var list = function (panel, routeData) {
        Table({
            description: 'emulator-type-question列表',
            title: 'emulator-type-question列表',

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }, ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '编辑',
                    class: 'info',
                    click: function (table, $line, lineData, dom, allData, index) {
                        Table().edit(lineData, {
                            title: '编辑',
                            width: 500,
                            store: 'jiakao-misc!emulator-type-question/data/sigleUpdate?id=' + lineData.id,
                            success: function (obj, dialog2) {
                                dialog2.close();
                                table.render()
                            },
                            columns: [{
                                header: '操作顺序',
                                dataIndex: 'instruction',
                                xtype: 'textarea',
                                placeholder: 'instruction',
                            }, ]
                        });
                    }
                },
                {
                    name: '设置操作步骤',
                    class: 'info',
                    click: function (table, row, lineData) {
                        if (!routeData.guide) {
                            return Widgets.dialog.alert("请先在emulator-type列表设置操作流程")
                        }
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('emulator-type-question-guide-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'emulator-type-question-guide-' + lineData.id,
                                    name: '设置操作流程' + lineData.id
                                })
                            }
                            require(['jiakao-misc!app/emulator-type-question-guide/index'], function (Item) {
                                Item.list(nPanel, lineData, routeData.id)
                            })
                        });
                    }
                },
                {
                    name: '取消关联',
                    class: 'danger',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认取消关联吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!emulator-type-question/data/deleteRelation?id=' + lineData.id], [{

                                }]).load().done(function (store) {
                                    Widgets.dialog.alert('取消关联成功')
                                    table.render()
                                }).fail(function (err) {
                                    Widgets.dialog.alert(err.message)
                                });
                            } else {}
                        })
                    }
                }



            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 80
                },
                {
                    header: '模拟器名称',
                    dataIndex: 'name',
                    render: function () {
                        return routeData.name
                    }

                },

                {
                    header: '试题id',
                    dataIndex: 'questionId'
                },
                {
                    header: '试题内容',
                    dataIndex: 'question'
                },
                {
                    header: '试题答案',
                    dataIndex: 'answer'
                },
                {
                    header: '操作顺序',
                    dataIndex: 'instruction'
                },
            ]
        }, ['jiakao-misc!emulator-type-question/data/list?typeId=' + routeData.id], panel, null).render();
    }

    return {
        list: list
    }

});