/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-type/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '模拟器名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 64,
                placeholder: '模拟器名称'
            },
            {
                header: '描述：',
                dataIndex: 'desc',
                xtype: 'text',
                maxlength: 64,
                placeholder: '描述'
            },
            {
                header: '练习的配置：',
                dataIndex: 'practice',
                xtype: 'textarea',
                placeholder: '练习的配置'
            },
            {
                header: '操作流程：',
                dataIndex: 'guide',
                xtype: 'textarea',
                placeholder: '操作流程'
            },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '类型列表',
            title: '类型列表',

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!emulator-type/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '模拟器名称：',
                    dataIndex: 'name'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc'
                },
                {
                    header: '练习的配置：',
                    dataIndex: 'practice'
                },

                {
                    header: '创建时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人ID：',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人：',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人ID：',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人：',
                    dataIndex: 'updateUserName'
                }

                ]
            },
            {
                name: '编辑',
                xtype: 'edit',
                width: 500,
                class: 'warning',
                title: '编辑',
                success: function (obj, dialog, e) {
                    dialog.close();
                    obj.render();
                },
                store: {
                    load: 'jiakao-misc!emulator-type/data/view',
                    save: 'jiakao-misc!emulator-type/data/update'
                },
                columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '模拟器名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '模拟器名称'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '描述'
                },
                {
                    header: '练习的配置：',
                    dataIndex: 'practice',
                    xtype: 'textarea',
                    placeholder: '练习的配置'
                },
                {
                    header: '操作流程：',
                    dataIndex: 'guide',
                    xtype: 'textarea',
                    placeholder: '操作流程'
                },


                ]
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!emulator-type/data/delete'
            },
            {
                name: '关联试题',
                class: 'info',
                click: function (table, row, lineData) {

                    Widgets.dialog.html('关联试题', {
                        width: 900,
                    }).done(function (dialog) {

                        Table({
                            buttons: {
                                top: [{
                                    name: '刷新',
                                    class: 'info',
                                    click: function (obj) {
                                        obj.render();
                                    }
                                },
                                {
                                    name: '添加关联',
                                    class: 'primary',
                                    click: function (table, dom, config, selectedArr) {
                                        //  console.log(selectedArr.join(','));

                                        if (selectedArr.length > 0) {


                                            Widgets.dialog.confirm('确认添加关联吗？', function (ev, status) {
                                                if (status) {
                                                    Store(['jiakao-misc!emulator-question/data/relationCreate?questionIds=' + selectedArr.join(',') + '&typeId=' + lineData.id], [{

                                                    }]).load().done(function (store) {
                                                        Widgets.dialog.alert('关联成功')
                                                        table.render()
                                                    }).fail(function (err) {
                                                        Widgets.dialog.alert(err.message)
                                                    });
                                                } else { }
                                            })
                                        } else {
                                            Widgets.dialog.alert('请选择添加关联的数据')
                                        }

                                    },
                                },
                                ],
                            },
                            selector: {
                                dataIndex: 'id',
                            },
                            columns: [{
                                header: '#',
                                dataIndex: 'id',
                                width: 80
                            },
                            {
                                header: '试题内容',
                                dataIndex: 'question'
                            },

                            ]
                        }, ['jiakao-misc!emulator-question/data/getNeedRelation?typeId=' + lineData.id], $(dialog.body), function (dom, data, init, Table) {

                        }).render();
                    })
                }
            },
            {
                name: '查看已关联试题',
                class: 'info',
                click: function (table, row, lineData) {
                    console.log("lineData", lineData.id)
                    require(['simple!app/layout/main'], function (Main) {
                        var nPanel = Main.panel('emulator-type-question-' + lineData.id);
                        if (nPanel.length == 0) {
                            nPanel = Main.panel({
                                id: 'emulator-type-question-' + lineData.id,
                                name: '已关联试题' + lineData.id
                            })
                        }
                        require(['jiakao-misc!app/emulator-type-question/index'], function (Item) {
                            Item.list(nPanel, lineData)
                        })
                    });
                }
            }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '模拟器名称',
                dataIndex: 'name'
            },
            {
                header: '描述',
                dataIndex: 'desc'
            },
            {
                header: '练习的配置',
                dataIndex: 'practice',
                render: function (data) {
                    if (data) {
                        return '<a>点击查看</a>'
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('练习的配置', {}).done(function (dialog) {
                        $(dialog.body).html('<div>' + lineData.practice + '</div><br/>')
                    })
                }
            },
            {
                header: '操作流程',
                dataIndex: 'guide',
                render: function (data) {
                    if (data) {
                        return '<a>点击查看</a>'
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('操作流程', {}).done(function (dialog) {
                        $(dialog.body).html('<div>' + lineData.guide + '</div><br/>')
                    })
                }
            },

            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }
            ]
        },
            ['jiakao-misc!emulator-type/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});