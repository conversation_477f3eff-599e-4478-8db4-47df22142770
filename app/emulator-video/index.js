/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!emulator-video/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            // form: {
            //     submitHandler: function (form, fun) {
            //         var cityName = $(form).find('[data-item="undefined-citys"]').find('option:selected').text()



            //     }
            // },
            columns: [{
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '标题'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '描述'
                },
                {
                    header: '车型：',
                    dataIndex: 'carId',
                    xtype: 'select',
                    store: 'jiakao-misc!emulator-car/data/carList',

                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district', {

                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    header: '视频顺序:',
                    dataIndex: 'order',
                    xtype: 'number'


                },

                {
                    header: '封面图：',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '列表图片：',
                    dataIndex: 'listImage',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'listImage',
                        uploadIndex: 'listImage',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '视频时间(秒)：',
                    dataIndex: 'duration',
                    xtype: 'text',
                    placeholder: '单位：秒'
                },
                {
                    header: '标清视频：',
                    dataIndex: 'videoL',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '标清视频'
                },
                {
                    header: '高清视频：',
                    dataIndex: 'videoM',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '高清视频'
                },
                {
                    header: '超清视频：',
                    dataIndex: 'videoH',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '超清视频'
                },
                {
                    header: '标清加密地址：',
                    dataIndex: 'videoEncodeL',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '标清加密地址'
                },
                {
                    header: '高清加密地址：',
                    dataIndex: 'videoEncodeM',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '高清加密地址'
                },
                {
                    header: '超清加密地址：',
                    dataIndex: 'videoEncodeH',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '超清加密地址'
                },
                {
                    header: '点评id：',
                    dataIndex: 'commentId',
                    xtype: 'text',  
                    maxlength: 64,
                    placeholder: '点评id'
                },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '模拟器视频列表',
            title: '模拟器视频列表',
            search: [{
                    dataIndex: 'carId',
                    xtype: 'select',
                    store: 'jiakao-misc!emulator-car/data/carList',
                    insert: [{
                        key: '',
                        value: '车型'
                    }],

                },
                {
                    dataIndex: 'cityName',
                    xtype: 'text',
                    placeholder: '城市'



                },
               
            ],
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!emulator-video/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carId'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '城市：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '视频顺序:',
                            dataIndex: 'order',



                        },
                        {
                            header: '封面图：',
                            dataIndex: 'image',
                            render: function (data) {
                                if (data) {
                                    return '<a><img style="width:120px;" src="' + data + '" /></a>';
                                }
                            },
                        },
                        {
                            header: '列表图片：',
                            dataIndex: 'listImage',
                            render: function (data) {
                                if (data) {
                                    return '<a><img style="width:120px;" src="' + data + '" /></a>';
                                }
                            },
                        },
                        {
                            header: '视频时间(秒)：',
                            dataIndex: 'duration',
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoL'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoM'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoH'
                        },
                        {
                            header: '标清加密地址：',
                            dataIndex: 'videoEncodeL'
                        },
                        {
                            header: '高清加密地址：',
                            dataIndex: 'videoEncodeM'
                        },
                        {
                            header: '超清加密地址：',
                            dataIndex: 'videoEncodeH'
                        },
                        {
                            header: '点评id：',
                            dataIndex: 'commentId'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人Id:',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人Id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!emulator-video/data/view',
                        save: 'jiakao-misc!emulator-video/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '标题'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '描述'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carId',
                            xtype: 'text',
                            placeholder: '车型'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode',
                            xtype: 'text',
                            placeholder: '城市编码'
                        },
                        {
                            header: '城市：',
                            dataIndex: 'cityName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '城市'
                        },
                        {
                            header: '视频顺序:',
                            dataIndex: 'order',
                            xtype: 'number'


                        },
                        {
                            header: '封面图：',
                            dataIndex: 'image',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'image',
                                uploadIndex: 'image',
                                bucket: "exam-room",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '列表图片：',
                            dataIndex: 'listImage',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'listImage',
                                uploadIndex: 'listImage',
                                bucket: "exam-room",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })


                        },
                        {
                            header: '视频时间(秒)：',
                            dataIndex: 'duration',
                            xtype: 'text',
                            placeholder: '单位：秒'
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoL',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '标清视频'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoM',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '高清视频'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoH',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '超清视频'
                        },
                        {
                            header: '标清加密地址：',
                            dataIndex: 'videoEncodeL',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '标清加密地址'
                        },
                        {
                            header: '高清加密地址：',
                            dataIndex: 'videoEncodeM',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '高清加密地址'
                        },
                        {
                            header: '超清加密地址：',
                            dataIndex: 'videoEncodeH',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '超清加密地址'
                        },
                        {
                            header: '标清试看视频：',
                            dataIndex: 'previewL',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '标清试看视频'
                        },
                        {
                            header: '高清试看视频：',
                            dataIndex: 'previewM',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '高清试看视频'
                        },
                        {
                            header: '超清试看视频：',
                            dataIndex: 'previewH',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '超清试看视频'
                        },
                        {
                            header: '标清加密试看视频：',
                            dataIndex: 'previewEncodeL',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '标清试看视频'
                        },
                        {
                            header: '高清加密试看视频：',
                            dataIndex: 'previewEncodeM',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '高清试看视频'
                        },
                        {
                            header: '超清加密试看视频：',
                            dataIndex: 'previewEncodeH',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '超清试看视频'
                        },
                        {
                            header: '点评id：',
                            dataIndex: 'commentId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '点评id'
                        },
                        {
                            header: '创建人Id：',
                            dataIndex: 'createUserId',
                            xtype: 'text',
                            placeholder: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            dataIndex: 'updateTime',
                            xtype: 'date',
                            placeholder: 'updateTime'
                        },
                        {
                            header: '修改人Id：',
                            dataIndex: 'updateUserId',
                            xtype: 'text',
                            placeholder: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!emulator-video/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '车型名称',
                    dataIndex: 'carName'
                },
           
                {
                    header: '城市',
                    dataIndex: 'cityName'
                },
                {
                    header: '视频顺序',
                    dataIndex: 'order',
                },
                {
                    header: '封面图',
                    dataIndex: 'image',
                    render: function (data) {
                        if (data) {
                            return '<a><img style="width:120px;" src="' + data + '" /></a>';
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('封面图', {}).done(function (dialog) {
                            $(dialog.body).append('<img src="' + lineData.image + '" />')
                        })
                    }
                },
                {
                    header: '列表图片',
                    dataIndex: 'listImage',
                    render: function (data) {
                        if (data) {
                            return '<a><img style="width:120px;" src="' + data + '" /></a>';
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('列表图片', {}).done(function (dialog) {
                            $(dialog.body).append('<img src="' + lineData.listImage + '" />')
                        })
                    }
                },
                {
                    header: '视频时间(秒)：',
                    dataIndex: 'duration',
                },
                {
                    header: '标清视频',
                    dataIndex: 'videoL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('标清试看视频', {}).done(function (dialog) {
                            console.log(lineData)


                            $(dialog.body).html('<a href="' + lineData.videoL + '" target="_blank">' + lineData.videoL + '</a><br><video src="' + lineData.videoL + '" controls></video>')

                        })
                    }
                },
                {
                    header: '高清视频',
                    dataIndex: 'videoM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('标清试看视频', {}).done(function (dialog) {
                            console.log(lineData)


                            $(dialog.body).html('<a href="' + lineData.videoM + '" target="_blank">' + lineData.videoM + '</a><br><video src="' + lineData.videoM + '" controls></video>')

                        })
                    }
                },
                {
                    header: '超清视频',
                    dataIndex: 'videoH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('标清试看视频', {}).done(function (dialog) {



                            $(dialog.body).html('<a href="' + lineData.videoH + '" target="_blank">' + lineData.videoH + '</a><br><video src="' + lineData.videoH + '" controls></video>')

                        })
                    }
                },
                {
                    header: '标清加密地址',
                    dataIndex: 'videoEncodeL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('标清加密地址', {}).done(function (dialog) {



                            $(dialog.body).html(lineData.videoEncodeL)


                        })
                    }
                },
                {
                    header: '高清加密地址',
                    dataIndex: 'videoEncodeM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('高清加密地址', {}).done(function (dialog) {
                            $(dialog.body).html(lineData.videoEncodeM)
                        })
                    }
                },
                {
                    header: '超清加密地址',
                    dataIndex: 'videoEncodeH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('超清加密地址', {}).done(function (dialog) {

                            $(dialog.body).html(lineData.videoEncodeH)

                        })
                    }
                },
                {
                    header: '标清试看视频',
                    dataIndex: 'previewL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('标清试看视频', {}).done(function (dialog) {



                            $(dialog.body).html(lineData.previewL)


                        })
                    }
                },
                {
                    header: '高清试看视频',
                    dataIndex: 'previewM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('高清试看视频', {}).done(function (dialog) {



                            $(dialog.body).html(lineData.previewM)


                        })
                    }
                },
                {
                    header: '超清试看视频',
                    dataIndex: 'previewH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('超清试看视频', {}).done(function (dialog) {



                            $(dialog.body).html(lineData.previewH)


                        })
                    }
                },
                {
                    header: '标清加密试看视频',
                    dataIndex: 'previewEncodeL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('标清加密试看视频', {}).done(function (dialog) {



                            $(dialog.body).html(lineData.previewEncodeL)


                        })
                    }
                },
                {
                    header: '高清加密试看视频',
                    dataIndex: 'previewEncodeM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('高清加密试看视频', {}).done(function (dialog) {

                            $(dialog.body).html(lineData.previewEncodeM)


                        })
                    }
                },
                {
                    header: '超清加密试看视频',
                    dataIndex: 'previewEncodeH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        Widgets.dialog.html('超清加密试看视频', {}).done(function (dialog) {



                            $(dialog.body).html(lineData.previewEncodeH)


                        })
                    }
                },
                {
                    header: '点评id',
                    dataIndex: 'commentId'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人Id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改日期',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人Id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!emulator-video/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});