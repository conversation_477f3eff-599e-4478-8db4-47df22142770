/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!entries-classify/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '分类名称：',
                    dataIndex: 'classifyName',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '分类名称'
                },
                {
                    header: '顺序：',
                    dataIndex: 'displayOrder',
                    xtype: 'number',
                    check: 'required',
                    placeholder: '顺序'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'entries-classify列表',
            title: 'entries-classify列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!entries-classify/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '分类名称：',
                            dataIndex: 'classifyName'
                        },
                        {
                            header: '顺序：',
                            dataIndex: 'displayOrder'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人名称：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人名称：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!entries-classify/data/view',
                        save: 'jiakao-misc!entries-classify/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '分类名称：',
                            dataIndex: 'classifyName',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '分类名称'
                        },
                        {
                            header: '顺序：',
                            dataIndex: 'displayOrder',
                            xtype: 'number',
                            check: 'required',
                            placeholder: '顺序'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!entries-classify/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '分类名称',
                    dataIndex: 'classifyName'
                },
                {
                    header: '顺序',
                    dataIndex: 'displayOrder'
                },
                {
                    header: '创建人名称',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改人名称',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!entries-classify/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});