/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table, data) {
        Table({
            title: '添加',
            width: 800,
            // ?entryId=${data.entryId}&parentId=${data.parentId}
            store: `jiakao-misc!entries/data/detailInsert`,
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'entryId',
                    xtype: 'hidden',
                    value: data.entryId,
                },
                {
                    dataIndex: 'parentId',
                    xtype: 'hidden',
                    value: data.parentId,
                },
                {
                    dataIndex: 'level',
                    xtype: 'hidden',
                    value: data.level,
                },
                {
                    header: 'Key：',
                    dataIndex: 'entryKey',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: 'entryKey'
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 100,
                    placeholder: '标题'
                },
                // {
                //     header: '级别：',
                //     dataIndex: 'level',
                //     xtype: 'text',
                //     check: 'required',
                //     placeholder: '级别'
                // },
                
                // {
                //     header: '上级id：',
                //     dataIndex: 'parentId',
                //     xtype: 'text',
                //     placeholder: '上级id'
                // },
                {
                    header: '内容：',
                    dataIndex: 'content',
                    xtype: 'richtext',
                    maxlength: 1024,
                    placeholder: '内容'
                },
                {
                    header: '排序：',
                    dataIndex: 'displayOrder',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '排序'
                },
            ]
        }).add();
    }

    var list = function (panel, lineData) {
        const level = parseInt(lineData.level || 0)  + 1;
        const parentId = level==1 ? -1 : lineData.id;
        const entryId = lineData.entryId || lineData.id;
        Table({
            title: (lineData.name || lineData.title) + '-内容管理',
            search: [
                {
                    dataIndex: 'entryKey',
                    xtype: 'text',
                    placeholder: '请输入key'
                },
                {
                    dataIndex: 'name',
                    xtype: 'text',
                    placeholder: '请输入名称'
                },
                {
                    dataIndex: 'startTime',
                    xtype: 'date',
                    placeholder: '创建开始时间'
                },
                {
                    dataIndex: 'endTime',
                    xtype: 'date',
                    placeholder: '创建结束时间'
                },
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function(table){
                            add(table,{
                                entryId,
                                parentId,
                                level
                            })
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    // name: `${level + 1}级内容`,
                    class: 'success',
                    render: function (name, arr, i) {
                        if(arr[i].level >= 3){
                            return '';
                        }else{
                            return `${arr[i].level + 1}级内容`
                        }
                    },
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel(lineData.title+lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: lineData.id,
                                    name: lineData.title + '-内容管理'
                                })
                            }
                            require(['jiakao-misc!app/entries/entries-one'], function (Log) {
                                Log.list(nPanel, lineData)
                            })
                        });

                    } 
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: `jiakao-misc!entries/data/detailView?entryId=${entryId}&parentId=${parentId}&level=${level}`,
                        save: `jiakao-misc!entries/data/detailUpdate`
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            dataIndex: 'parentId',
                            xtype: 'hidden',
                            value: parentId
                        },
                        {
                            dataIndex: 'level',
                            xtype: 'hidden',
                        },
                        {
                            header: '词条id：',
                            dataIndex: 'entryId',
                            xtype: 'hidden',
                            value: entryId
                        },
                        {
                            header: 'Key：',
                            dataIndex: 'entryKey',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: 'entryKey'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '标题'
                        },
                        
                        {
                            header: '排序：',
                            dataIndex: 'displayOrder',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '排序'
                        },
                        
                        {
                            header: '内容：',
                            dataIndex: 'content',
                            xtype: 'richtext',
                            maxlength: 1024,
                            placeholder: '内容'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: `jiakao-misc!entries/data/detailDel?entryId=${entryId}&parentId=${parentId}&level=${level}`
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '词条id',
                    dataIndex: 'entryId'
                },
                {
                    header: 'Key',
                    dataIndex: 'entryKey'
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '目录层级',
                    dataIndex: 'level'
                },
                {
                    header: '内容',
                    dataIndex: 'content'
                },
                {
                    header: '排序',
                    dataIndex: 'displayOrder'
                },
                // {
                //     header: '上级id',
                //     dataIndex: 'parentId',
                //     render: function(data){
                //         if(data == -1){
                //             return '';
                //         }
                //         return data;
                //     }
                // },
                
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }
            ]
        }, [`jiakao-misc!entries/data/detailList?entryId=${entryId}&parentId=${parentId}&level=${level}`], panel, null).render();
    }

    return {
        list: list
    }

});