/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var entryStatusMap = {
        "WAIT": "待发布",
        "PUBLISHED": "已发布",
        "DISCARED": "已废弃",
    }
    var typeMap = {
        "DETAIL": "跳转词条详情",
        "EXTEMAL": "跳转指定URL"
    }
    var handleArray = function (map) {
        let newArray = []
        for (let key in map) {
            newArray.push({ key: key, value: map[key], search: map[key] })
        }
        return newArray
    }
    var entryStatusStore = handleArray(entryStatusMap);
    var typeStore = handleArray(typeMap);
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!entries/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: function (table, dom, data) {
                if (data?.data?.type == 'EXTEMAL') {
                    dom.item('entryUrl-group').show();
                } else {
                    dom.item('entryUrl-group').hide();
                }
                dom.item('type').on('change', function () {

                    let value = $(this).val();
                    if (value == 'EXTEMAL') {
                        dom.item('entryUrl-group').show();
                    } else {
                        dom.item('entryUrl-group').hide();
                    }
                })
            },
            form: {
                submitHandler: function (form) {
                    var type = $(form).find('#type').val();
                    var entryUrl = $(form).find('#entryUrl').val();
                    if (type === "EXTEMAL" && !/^https?:\/\//.test(entryUrl)) {
                        Widgets.dialog.alert("跳转链接必须以http或https开头");
                        return false;
                    }

                    var classifyIds = form.classifyIds.value;
                    if (classifyIds.split(',').length > 3) {
                        Widgets.dialog.alert('条分类不能超过3个，请检查')
                        return false;
                    }
                    return true;
                }
            },
            columns: [
                {
                    header: '词条key：',
                    dataIndex: 'entryKey',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '词条key',
                    check: 'required'
                },
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '名称',
                    check: 'required'
                },
                {
                    header: '简介：',
                    dataIndex: 'brief',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '简介',
                    check: 'required'
                },
                {
                    header: '描述：',
                    dataIndex: 'description',
                    xtype: 'textarea',
                    maxlength: 600,
                    placeholder: '描述',
                    check: 'required'
                },
                {
                    header: '分类：',
                    check: 'required',
                    dataIndex: 'classifyIds',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!entries-classify/data/allClassify',
                        placeholder: '分类',
                        dataIndex: 'classifyIds',
                        index: {
                            key: 'id',
                            value: 'classifyName',
                            search: 'classifyName'
                        },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    })
                },
                {
                    header: '状态：',
                    dataIndex: 'entryStatus',
                    xtype: 'select',
                    placeholder: '状态',
                    store: [{ key: '', value: '请选择状态' }].concat(entryStatusStore),
                    check: 'required'
                },
                {
                    header: '是否热词：',
                    dataIndex: 'hotwordMark',
                    xtype: 'radio',
                    check: 'required',
                    store: [{ key: true, value: '是' }, { key: false, value: '否' }]
                },
                {
                    header: '类型：',
                    dataIndex: 'type',
                    xtype: 'select',
                    placeholder: '类型',
                    store: [{ key: '', value: '请选择类型' }].concat(typeStore),
                    check: 'required'
                },
                {
                    header: '跳转链接：',
                    dataIndex: 'entryUrl',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: 'url'
                },
                {
                    header: '封面：',
                    dataIndex: 'coverImage',
                    xtype: Plugin(
                        "jiakao-misc!upload",
                        {
                            dataIndex: "coverImage",
                            uploadIndex: "coverImage",
                            bucket: "exam-room",
                            isSingle: true,
                            placeholder: "请选择上传文件",
                            url: "simple-upload3://upload/file.htm",
                        },
                        function () {
                            console.log(arguments);
                        }
                    ),

                },
                {
                    header: '顺序：',
                    dataIndex: 'displayOrder',
                    xtype: 'text',
                    placeholder: '顺序'
                },
            ]
        }).add();
    }

    var addRelatedFunctions = function (table, lineData) {
        Table({
            title: '添加',
            width: 800,
            store: 'jiakao-misc!entries/data/functionsInsert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'entryId',
                    xtype: 'hidden',
                    value: lineData.id
                },

                {
                    header: '相关功能：',
                    dataIndex: 'functionIds',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!related-function/data/list',
                        placeholder: '添加相关功能',
                        dataIndex: 'functionIds',
                        index: {
                            key: 'id',
                            value: 'functionName',
                            search: 'functionName'
                        },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    })
                },


            ]
        }).add();
    }
    var addReferenceMaterial = function (table, lineData) {
        Table({
            title: '添加',
            width: 800,
            store: 'jiakao-misc!entries/data/referenceMaterialInsert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'entryId',
                    xtype: 'hidden',
                    value: lineData.id
                },

                {
                    header: '参考资料：',
                    dataIndex: 'materialIds',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!reference-material/data/list',
                        placeholder: '添加参考资料',
                        dataIndex: 'materialIds',
                        index: {
                            key: 'id',
                            value: 'materialName',
                            search: 'materialName'
                        },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    })
                },


            ]
        }).add();
    }
    var list = function (panel) {
        Table({
            description: '词条列表列表',
            title: '词条列表列表',
            search: [
                {
                    dataIndex: 'entryKey',
                    xtype: 'text',
                    placeholder: '请输入词条'
                },
                {
                    dataIndex: 'name',
                    xtype: 'text',
                    placeholder: '请输入名称'
                },

                {
                    dataIndex: 'entryStatus',
                    xtype: 'select',
                    placeholder: '状态',
                    store: [{ key: '', value: '请选择状态' }].concat(entryStatusStore)
                },
                {
                    header: '类型：',
                    dataIndex: 'searchScope',
                    xtype: 'select',
                    placeholder: '类型',
                    store: [
                        { key: '', value: '请选择查看范围' },
                        { key: 1, value: '只看我创建的' },
                        { key: 2, value: '只看我更新的' },
                    ]
                },
                {
                    dataIndex: 'startTime',
                    xtype: 'date',
                    placeholder: '创建开始时间'
                },
                {
                    dataIndex: 'endTime',
                    xtype: 'date',
                    placeholder: '创建结束时间'
                },
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '相关功能',
                    width: 800,
                    class: 'primary',
                    click: function (tableObj, fn, lineData) {
                        Simple.Dialog.html('相关功能', {
                            width: 800,
                            buttons: [{
                                name: '关闭',
                                xtype: 'success',
                                click: function () {
                                    this.close();
                                }
                            }]
                        }).done(function (dialog) {
                            var id = lineData.id;
                            Table({
                                title: '相关功能',
                                buttons: {
                                    top: [
                                        {
                                            name: '刷新',
                                            class: 'info',
                                            click: function (obj) {
                                                obj.render();
                                            }
                                        },
                                        {
                                            name: '添加',
                                            class: 'info',
                                            click: function (obj) {
                                                addRelatedFunctions(obj, lineData)
                                            }
                                        },
                                    ],

                                },
                                operations: [
                                    {
                                        name: '删除',
                                        class: 'danger',
                                        xtype: 'delete',
                                        store: 'jiakao-misc!entries/data/functionsDel'
                                    }
                                ],
                                columns: [


                                    {
                                        header: '序号',
                                        dataIndex: 'functionId'
                                    },
                                    {
                                        header: '名称',
                                        dataIndex: 'functionName'
                                    }
                                ]
                            }, ['jiakao-misc!entries/data/functionsList'], dialog.body).render({
                                entryId: lineData.id
                            });

                        });
                    },

                },
                {
                    name: '参考资料',
                    width: 800,
                    class: 'primary',
                    click: function (tableObj, fn, lineData) {
                        Simple.Dialog.html('参考资料', {
                            width: 800,
                            buttons: [{
                                name: '关闭',
                                xtype: 'success',
                                click: function () {
                                    this.close();
                                }
                            }]
                        }).done(function (dialog) {
                            var id = lineData.id;
                            Table({
                                title: '参考资料',
                                buttons: {
                                    top: [
                                        {
                                            name: '刷新',
                                            class: 'info',
                                            click: function (obj) {
                                                obj.render();
                                            }
                                        },
                                        {
                                            name: '添加',
                                            class: 'info',
                                            click: function (obj) {
                                                addReferenceMaterial(obj, lineData)
                                            }
                                        },
                                    ],

                                },
                                operations: [
                                    {
                                        name: '删除',
                                        class: 'danger',
                                        xtype: 'delete',
                                        store: 'jiakao-misc!entries/data/referenceMaterialDel'
                                    }
                                ],
                                columns: [


                                    {
                                        header: '序号',
                                        dataIndex: 'meterialId'
                                    },
                                    {
                                        header: '名称',
                                        dataIndex: 'materialName'
                                    }
                                ]
                            }, ['jiakao-misc!entries/data/referenceMaterialList'], dialog.body).render({
                                entryId: lineData.id
                            });

                        });
                    },

                },
                {
                    name: '内容管理',
                    class: 'success',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('内容管理-' + lineData.name);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'open' + lineData.id,
                                    name: lineData.name + '-内容管理'
                                })
                            }
                            require(['jiakao-misc!app/entries/entries-one'], function (Log) {
                                Log.list(nPanel, { level: 0, ...lineData })
                            })
                        });

                    }
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!entries/data/view',
                        save: 'jiakao-misc!entries/data/update'
                    },
                    renderAfter: function (table, dom, data) {
                        if (data?.data?.type == 'EXTEMAL') {
                            dom.item('entryUrl-group').show();
                        } else {
                            dom.item('entryUrl-group').hide();
                        }
                        dom.item('type').on('change', function () {

                            let value = $(this).val();
                            if (value == 'EXTEMAL') {
                                dom.item('entryUrl-group').show();
                            } else {
                                dom.item('entryUrl-group').hide();
                            }
                        })
                    },
                    form: {
                        submitHandler: function (form) {
                            var type = $(form).find('#type').val();
                            var entryUrl = $(form).find('#entryUrl').val();
                            if (type === "EXTEMAL" && !/^https?:\/\//.test(entryUrl)) {
                                Widgets.dialog.alert("跳转链接必须以http或https开头");
                                return false;
                            }
                            var classifyIds = form.classifyIds.value;
                            if (classifyIds.split(',').length > 3) {
                                Widgets.dialog.alert('条分类不能超过3个，请检查')
                                return false;
                            }
                            return true;
                        }
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '词条key：',
                            dataIndex: 'entryKey',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '词条key',
                            check: 'required'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '名称',
                            check: 'required'
                        },
                        {
                            header: '简介：',
                            dataIndex: 'brief',
                            xtype: 'text',
                            maxlength: 128,
                            placeholder: '简介',
                            check: 'required'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '描述',
                            check: 'required'
                        },
                        {
                            header: '分类：',
                            check: 'required',
                            dataIndex: 'classifyIds',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: 'jiakao-misc!entries-classify/data/allClassify',
                                placeholder: '分类',
                                dataIndex: 'classifyIds',
                                index: {
                                    key: 'id',
                                    value: 'classifyName',
                                    search: 'classifyName'
                                },
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            })
                        },
                        {
                            header: '状态：',
                            dataIndex: 'entryStatus',
                            xtype: 'select',
                            placeholder: '状态',
                            check: 'required',
                            store: [{ key: '', value: '请选择状态' }].concat(entryStatusStore)
                        },
                        {
                            header: '是否热词：',
                            dataIndex: 'hotwordMark',
                            xtype: 'radio',
                            check: 'required',
                            store: [{ key: true, value: '是' }, { key: false, value: '否' }]
                        },
                        {
                            header: '类型：',
                            dataIndex: 'type',
                            xtype: 'select',
                            placeholder: '类型',
                            store: [{ key: '', value: '请选择类型' }].concat(typeStore),
                            check: 'required'
                        },
                        {
                            header: '跳转链接：',
                            dataIndex: 'entryUrl',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: 'url'
                        },
                        {
                            header: '封面：',
                            dataIndex: 'coverImage',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '封面',
                            xtype: Plugin(
                                "jiakao-misc!upload",
                                {
                                    dataIndex: "coverImage",
                                    uploadIndex: "coverImage",
                                    bucket: "exam-room",
                                    isSingle: true,
                                    placeholder: "请选择上传文件",
                                    url: "simple-upload3://upload/file.htm",
                                },
                                function () {
                                    console.log(arguments);
                                }
                            ),
                        },
                        {
                            header: '顺序：',
                            dataIndex: 'displayOrder',
                            xtype: 'text',
                            placeholder: '顺序'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!entries/data/del'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '词条key',
                    dataIndex: 'entryKey'
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '简介',
                    dataIndex: 'brief'
                },
                {
                    header: '描述',
                    dataIndex: 'description'
                },
                {
                    header: '状态',
                    dataIndex: 'entryStatus',
                    render: function (data) {
                        return entryStatusMap[data]
                    }
                },
                {
                    header: '是否热词',
                    dataIndex: 'hotwordMark',
                    render: function (data) {
                        return data ? '是' : '否'
                    }
                },
                {
                    header: '类型',
                    dataIndex: 'type',
                    render: function (data) {
                        return typeMap[data]
                    }
                },
                // {
                //     header: 'url',
                //     dataIndex: 'entryUrl'
                // },
                // {
                //     header: '封面',
                //     dataIndex: 'coverImage'
                // },
                {
                    header: '顺序',
                    dataIndex: 'displayOrder'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                // {
                //     header: '修改人id',
                //     dataIndex: 'updateUserId'
                // },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
            ]
        }, ['jiakao-misc!entries/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});