
define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'simple!core/ajax'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Ajax) {

    var list = function (panel, data = { startTime: '', endTime: '', cityCode: '' }) {
        let { startTime, endTime, cityCode } = data;
        // item('beginTime')[0].value = startTime;
        // item('endTime')[0].value = endTime;


        Store([`jiakao-misc!exam-data-admin/data/list?startTime=${startTime}&endTime=${endTime}&cityCode=${cityCode}`]).load().done(function (data) {
            var examData = data.data['exam-data-admin'].data.list.data;
            Template('jiakao-misc!examDataAdmin/main', panel, function (dom, data, item, obj) {
                startTime && (item('beginTime')[0].value = startTime);
                endTime && (item('endTime')[0].value = endTime);

                Plugin('jiakao-misc!select-district3', {
                    name: "cityCode",
                    value: cityCode,
                    target: item('topic-select-city')
                }).render();

                var reloadBtn = item('reload');
                var searchBtn = item('search');
                reloadBtn.on('click', function () {
                    list(panel)
                })

                searchBtn.on('click', function () {
                    var startTime = item('beginTime')[0].value

                    var endTime = item('endTime')[0].value

                    var cityCodeDom = $('select[name="cityCode"]')[0];
                    var cityCode = cityCodeDom[cityCodeDom.selectedIndex].value;

                    console.log({
                        startTime, endTime, cityCode
                    });

                    list(panel, {
                        startTime, endTime, cityCode
                    })
                })
            }, {
                data: {
                    examData,
                }
            }, {
                method: Utils
            }).render();

        }).fail(function (data) {
            Widgets.dialog.alert(data.message);
        })

    };

    return {
        list: list,
    }
});
