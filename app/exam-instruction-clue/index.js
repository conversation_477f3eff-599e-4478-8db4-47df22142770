/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!exam-instruction-clue/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '用户姓名：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '用户姓名'
                },
                {
                    header: '驾校名称：',
                    dataIndex: 'jiaxiaoName',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '驾校名称'
                },
                {
                    header: '驾校id：',
                    dataIndex: 'jiaxiaoId',
                    xtype: 'text',
                    placeholder: '驾校id'
                },
                {
                    header: '手机号：',
                    dataIndex: 'phone',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '手机号'
                },
                {
                    header: '登陆用户的昵称：',
                    dataIndex: 'nickName',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '登陆用户的昵称'
                },
                {
                    header: '用户的城市名称：',
                    dataIndex: 'city',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '用户的城市名称'
                },
                {
                    header: '平台：',
                    dataIndex: 'platform',
                    xtype: 'text',
                    maxlength: 8,
                    placeholder: '平台'
                },
                {
                    header: 'app名称：',
                    dataIndex: 'appName',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: 'app名称'
                },
                {
                    header: 'app版本：',
                    dataIndex: 'appVersion',
                    xtype: 'text',
                    maxlength: 8,
                    placeholder: 'app版本'
                },
                {
                    header: '用户机型：',
                    dataIndex: 'mobile',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '用户机型'
                },
                {
                    header: '处理状态：',
                    dataIndex: 'handle',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '已处理'
                        },
                        {
                            key: false,
                            value: '未处理'
                        }
                    ]
                },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'exam-instruction-clue列表',
            title: 'exam-instruction-clue列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!exam-instruction-clue/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户姓名：',
                            dataIndex: 'name'
                        },
                        {
                            header: '驾校名称：',
                            dataIndex: 'jiaxiaoName'
                        },
                        {
                            header: '驾校id：',
                            dataIndex: 'jiaxiaoId'
                        },
                        {
                            header: '手机号：',
                            dataIndex: 'phone'
                        },
                        {
                            header: '登陆用户的昵称：',
                            dataIndex: 'nickName'
                        },
                        {
                            header: '用户的城市名称：',
                            dataIndex: 'city',
                            render:function (data) {
                                return "<div style='width: 30px'> " + data +" </div>"

                            }
                        },
                        {
                            header: '平台：',
                            dataIndex: 'platform'
                        },
                        {
                            header: 'app名称：',
                            dataIndex: 'appName'
                        },
                        {
                            header: 'app版本：',
                            dataIndex: 'appVersion'
                        },
                        {
                            header: '用户机型：',
                            dataIndex: 'mobile'
                        },
                        {
                            header: '处理状态：',
                            render: function (data) {
                                if(data === null || data === false){
                                    return '未处理'
                                }else{
                                    return '已处理'
                                }
                            },
                            dataIndex: 'handle'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        // {
                        //     header: 'createUserId：',
                        //     dataIndex: 'createUserId'
                        // },
                        // {
                        //     header: 'createUserName：',
                        //     dataIndex: 'createUserName'
                        // },
                        // {
                        //     header: 'updateTime：',
                        //     render: function (data) {
                        //         return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        //     },
                        //     dataIndex: 'updateTime'
                        // },
                        // {
                        //     header: 'updateUserId：',
                        //     dataIndex: 'updateUserId'
                        // },
                        // {
                        //     header: 'updateUserName：',
                        //     dataIndex: 'updateUserName'
                        // }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!exam-instruction-clue/data/view',
                        save: 'jiakao-misc!exam-instruction-clue/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '用户姓名：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '用户姓名'
                        },
                        {
                            header: '驾校名称：',
                            dataIndex: 'jiaxiaoName',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '驾校名称'
                        },
                        {
                            header: '驾校id：',
                            dataIndex: 'jiaxiaoId',
                            xtype: 'text',
                            placeholder: '驾校id'
                        },
                        {
                            header: '手机号：',
                            dataIndex: 'phone',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '手机号'
                        },
                        {
                            header: '登陆用户的昵称：',
                            dataIndex: 'nickName',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '登陆用户的昵称'
                        },
                        {
                            header: '用户的城市名称：',
                            dataIndex: 'city',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '用户的城市名称'
                        },
                        {
                            header: '平台：',
                            dataIndex: 'platform',
                            xtype: 'text',
                            maxlength: 8,
                            placeholder: '平台'
                        },
                        {
                            header: 'app名称：',
                            dataIndex: 'appName',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: 'app名称'
                        },
                        {
                            header: 'app版本：',
                            dataIndex: 'appVersion',
                            xtype: 'text',
                            maxlength: 8,
                            placeholder: 'app版本'
                        },
                        {
                            header: '用户机型：',
                            dataIndex: 'mobile',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '用户机型'
                        },
                        {
                            header: '处理状态：',
                            dataIndex: 'handle',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '已处理'
                                },
                                {
                                    key: false,
                                    value: '未处理'
                                }
                            ]
                        },

                    ]
                },
                {
                    name: '处理',
                    class: 'danger',
                    // store: 'jiakao-misc!exam-instruction-clue/data/delete',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    render:function (data,arr,index) {
                        // console.log(arguments)
                        if(arr[index].handle ===null || arr[index].handle === false){
                            return "处理";
                        }else{
                            return ""
                        }
                    },
                    click:function (table,row,lineData) {
                        var storeUrl ='jiakao-misc!exam-instruction-clue/data/update?handle=true&id='+lineData.id;
                        Widgets.dialog.confirm('确认处理吗',function (e,confirm) {
                            if(confirm){
                                Store([storeUrl]).save().done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }

                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!exam-instruction-clue/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户姓名',
                    dataIndex: 'name'
                },
                {
                    header: '驾校名称',
                    dataIndex: 'jiaxiaoName'
                },
                {
                    header: '驾校id',
                    dataIndex: 'jiaxiaoId'
                },
                {
                    header: '手机号',
                    dataIndex: 'phone'
                },
                {
                    header: '登陆用户的昵称',
                    dataIndex: 'nickName'
                },
                {
                    header: '用户的城市名称',
                    dataIndex: 'city',
                },
                {
                    header: '平台',
                    dataIndex: 'platform'
                },
                {
                    header: 'app名称',
                    dataIndex: 'appName'
                },
                {
                    header: 'app版本',
                    dataIndex: 'appVersion'
                },
                {
                    header: '用户机型',
                    dataIndex: 'mobile'
                },
                {
                    header: '处理状态',
                    render: function (data) {
                        if(data === null || data === false){
                            return '未处理'
                        }else{
                            return '已处理'
                        }
                    },
                    dataIndex: 'handle'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },


            ]
        }, ['jiakao-misc!exam-instruction-clue/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
