/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form' ,'simple!core/plugin'], function(Template, Table, Utils, Widgets, Store, Form,Plugin) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!exam-process/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    placeholder: '城市编码',
                    xtype: Plugin('jiakao-misc!select-district2', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },
             {
                 header: '顺序：',
                 dataIndex: 'index',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '顺序'
             },
             {
                 header: '标题：',
                 dataIndex: 'title',
                 xtype: 'text',
                 maxlength: 16,
                 placeholder: '标题'
             },
             {
                 header: '考试流程内容：',
                 dataIndex: 'content',
                 xtype: 'textarea',
                 maxlength: 1024,
                 placeholder: '考试流程内容'
             },
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '考试流程配置列表',
            title: '考试流程配置列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!exam-process/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '城市编码：',
                         dataIndex: 'cityCode'
                     },
                     {
                         header: '城市名称：',
                         dataIndex: 'cityName'
                     },
                     {
                         header: '顺序：',
                         dataIndex: 'index'
                     },
                     {
                         header: '标题：',
                         dataIndex: 'title'
                     },
                     {
                         header: '考试流程内容：',
                         dataIndex: 'content'
                     },

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!exam-process/data/view',
                        save: 'jiakao-misc!exam-process/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode',
                            xtype: Plugin('jiakao-misc!select-district2', {
                                name: 'cityCode',
                                areaName: 'areaCode',
                                hideArea: true,
                                insert: {
                                    province: [{
                                        code: '',
                                        name: '请选择省份'
                                    }],
                                    city: [{
                                        code: '',
                                        name: '请选择市'
                                    }],
                                    area: [{
                                        code: '',
                                        name: '请选择区域'
                                    }]
                                }
                            }, function (plugin, code) {

                            })
                        },

                        {
                            header: '顺序：',
                            dataIndex: 'index',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '顺序'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '标题'
                        },
                        {
                            header: '考试流程内容：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '考试流程内容'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!exam-process/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                 {
                    header: '顺序',
                    dataIndex: 'index'
                },
                     {
                         header: '城市编码',
                         dataIndex: 'cityCode'
                     },
                     {
                         header: '城市名称',
                         dataIndex: 'cityName'
                     },
                   
                     {
                         header: '标题',
                         dataIndex: 'title'
                     },
                     {
                         header: '考试流程内容',
                         dataIndex: 'content'
                     },
            ]
        }, ['jiakao-misc!exam-process/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});