/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!exam-record-token/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 16,
                 placeholder: '名称'
             },
             {
                 header: 'token：',
                 dataIndex: 'token',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: 'token'
             },
             {
                 header: '驾校id白名单：',
                 dataIndex: 'jiaxiaoId',
                 xtype: 'text',
                 placeholder: '驾校id白名单'
             },
             {
                 header: '请求ip白名单：',
                 dataIndex: 'ipList',
                 xtype: 'textarea',
                 maxlength: 256,
                 placeholder: '请求ip白名单'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             }

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: 'exam-record-token列表',
            title: 'exam-record-token列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!exam-record-token/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '名称：',
                         dataIndex: 'name'
                     },
                     {
                         header: 'token：',
                         dataIndex: 'token'
                     },
                     {
                         header: '驾校id白名单：',
                         dataIndex: 'jiaxiaoId'
                     },
                     {
                         header: '请求ip白名单：',
                         dataIndex: 'ipList'
                     },
                     {
                         header: 'createTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserId：',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: 'createUserName：',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: 'updateTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: 'updateUserId：',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: 'updateUserName：',
                         dataIndex: 'updateUserName'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!exam-record-token/data/view',
                        save: 'jiakao-misc!exam-record-token/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 16,
                 placeholder: '名称'
             },
             {
                 header: 'token：',
                 dataIndex: 'token',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: 'token'
             },
             {
                 header: '驾校id白名单：',
                 dataIndex: 'jiaxiaoId',
                 xtype: 'text',
                 placeholder: '驾校id白名单'
             },
             {
                 header: '请求ip白名单：',
                 dataIndex: 'ipList',
                 xtype: 'textarea',
                 maxlength: 256,
                 placeholder: '请求ip白名单'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!exam-record-token/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '名称',
                         dataIndex: 'name'
                     },
                     {
                         header: 'token',
                         dataIndex: 'token'
                     },
                     {
                         header: '驾校id白名单',
                         dataIndex: 'jiaxiaoId'
                     },
                     {
                         header: '请求ip白名单',
                         dataIndex: 'ipList'
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserId',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '修改时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: 'updateUserId',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: '修改人',
                         dataIndex: 'updateUserName'
                     }

            ]
        }, ['jiakao-misc!exam-record-token/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
