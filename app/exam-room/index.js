/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!exam-room/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '省份编码：',
                    dataIndex: 'provinceCode',
                    xtype: 'text',
                    placeholder: '省份编码'
                },
                {
                    header: '省份名：',
                    dataIndex: 'provinceName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '省份名'
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: 'text',
                    placeholder: '城市编码'
                },
                {
                    header: '城市名：',
                    dataIndex: 'cityName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '城市名'
                },
                {
                    header: '区域编码：',
                    dataIndex: 'areaCode',
                    xtype: 'text',
                    placeholder: '区域编码'
                },
                {
                    header: '区域名：',
                    dataIndex: 'areaName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '区域名'
                },
                {
                    header: '考场名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '考场名称'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '122考场列表',
            title: '122考场列表',

            search: [
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district2', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    dataIndex: 'name',
                    xtype: 'text',
                    placeholder: '考场名'
                },
                {
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '全部车型'
                    }, {
                        key: 'car',
                        value: '小车'
                    }, {
                        key: 'bus',
                        value: '客车'
                    }, {
                        key: 'truck',
                        value: '货车'
                    }, {
                        key: 'moto',
                        value: '摩托车'
                    }],
                    placeholder: '车型'
                }
            ],

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!exam-room/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '省份编码：',
                            dataIndex: 'provinceCode'
                        },
                        {
                            header: '省份名：',
                            dataIndex: 'provinceName'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '城市名：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '区域编码：',
                            dataIndex: 'areaCode'
                        },
                        {
                            header: '区域名：',
                            dataIndex: 'areaName'
                        },
                        {
                            header: '考场名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '车型',
                            dataIndex: 'carType'
                        },
                        {
                            header: '创建者',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更新者',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更新时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!exam-room/data/view',
                        save: 'jiakao-misc!exam-room/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '省份编码：',
                            dataIndex: 'provinceCode',
                            xtype: 'text',
                            placeholder: '省份编码'
                        },
                        {
                            header: '省份名：',
                            dataIndex: 'provinceName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '省份名'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode',
                            xtype: 'text',
                            placeholder: '城市编码'
                        },
                        {
                            header: '城市名：',
                            dataIndex: 'cityName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '城市名'
                        },
                        {
                            header: '区域编码：',
                            dataIndex: 'areaCode',
                            xtype: 'text',
                            placeholder: '区域编码'
                        },
                        {
                            header: '区域名：',
                            dataIndex: 'areaName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '区域名'
                        },
                        {
                            header: '考场名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '考场名称'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '全部车型'
                            }, {
                                key: 'car',
                                value: '小车'
                            }, {
                                key: 'bus',
                                value: '客车'
                            }, {
                                key: 'truck',
                                value: '货车'
                            }, {
                                key: 'moto',
                                value: '摩托车'
                            }],
                            placeholder: '车型'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!exam-room/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '省份编码',
                    dataIndex: 'provinceCode'
                },
                {
                    header: '省份名',
                    dataIndex: 'provinceName'
                },
                {
                    header: '城市编码',
                    dataIndex: 'cityCode'
                },
                {
                    header: '城市名',
                    dataIndex: 'cityName'
                },
                {
                    header: '区域编码',
                    dataIndex: 'areaCode'
                },
                {
                    header: '区域名',
                    dataIndex: 'areaName'
                },
                {
                    header: '考场名称',
                    dataIndex: 'name'
                },
                {
                    header: '车型',
                    dataIndex: 'carType'
                },
                {
                    header: '创建者',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新者',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!exam-room/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});