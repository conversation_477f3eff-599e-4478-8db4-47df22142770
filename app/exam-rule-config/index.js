/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form,Constants,TIKU,Tools) {

    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    

    var kemuStore = [
        {
            key: '',
            value: '请选择科目'
        },
        {
            key: 'course1',
            value: '科目一'
        },
        {
            key:'course2',
            value: '科目二'
        },
        {
            key: 'course3',
            value: '科目三'
        },
        {
            key: 'course4',
            value: '科目四'
        }
    ]
    var kemuMap = Tools.getMapfromArray(kemuStore);


    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!exam-rule-config/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    let userMaterial = $(form).find('[data-item="userMaterial"]').val();
                    let mainMaterial = $(form).find('[data-item="mainMaterial"]').val();
                    const json = {
                        1:mainMaterial,
                        2:userMaterial
                    }
         
                    return {
                        material:JSON.stringify(json)
                    };
                },
            },
            columns: [
          
            {
                header: '车型:',
                dataIndex: 'carType',
                xtype: 'checkbox',
                store: [].concat(carTypeArr)
            },
           
            {
                header: '科目：',
                dataIndex: 'course',
                xtype: 'select',
                store: kemuStore,
            },
            {
                header: '场景code：',
                dataIndex: 'sceneCode',
                xtype: 'checkbox',
                store: Constants.senceStore,
            },
            {
                header: "访问模式：",
                dataIndex: "patternCode",
                xtype: "checkbox",
                store: Constants.editionStore,
                placeholder: "访问模式",
            },
             {
                 header: '做题数：',
                 dataIndex: 'questionCount',
                 xtype: 'text',
                 maxlength: 100,
                 placeholder: '做题数'
             },
             {
                header: '考试分数',
                dataIndex: 'examScore',
                xtype: 'text',
                maxlength: 100,
                placeholder: '考试分数'
            },
          
              {
                header: '考试次数',
                dataIndex: 'examCount',
                xtype: 'text',
                maxlength: 100,
                placeholder: '考试次数'
            },

            
            {
                header: '身份证明',
                dataIndex: 'identityCode',
                xtype: 'text',
                maxlength: 100,
                placeholder: '身份证明'
                }, 
                {
                    header: '科目首页',
                    dataIndex: 'mainMaterial',
                    xtype: 'textarea',
                    placeholder: '科目首页'
                },
                {
                    header: '我的页面',
                    dataIndex: 'userMaterial',
                    xtype: 'textarea',
                    placeholder: '我的页面',
                   
                },
                
            ]
        }).add();
    }

 

    var list = function(panel) {
        Table({
            description: '考前辅导规则配置列表',
            title: '考前辅导规则配置列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form) {
                            let userMaterial = $(form).find('[data-item="userMaterial"]').val();
                            let mainMaterial = $(form).find('[data-item="mainMaterial"]').val();
                            const json = {
                                1: mainMaterial,
                                2: userMaterial
                            }
                 
                            return {
                                material:JSON.stringify(json)
                            };
                        },
                    },
                    renderAfter: function (table, dom, data) {
                        let material = JSON.parse(data.data.material);
                        dom.item('mainMaterial').val(material[1])
                        dom.item('userMaterial').val(material[2])
                    },
                    store: {
                        load: 'jiakao-misc!exam-rule-config/data/view',
                        save: 'jiakao-misc!exam-rule-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '车型:',
                            dataIndex: 'carType',
                            xtype: 'checkbox',
                            store: [].concat(carTypeArr)
                        },
                       
                        {
                            header: '科目：',
                            dataIndex: 'course',
                            xtype: 'select',
                            store: kemuStore,
                        },
                        {
                            header: '场景code：',
                            dataIndex: 'sceneCode',
                            xtype: 'checkbox',
                            store: Constants.senceStore,
                        },
                        {
                            header: "访问模式：",
                            dataIndex: "patternCode",
                            xtype: "checkbox",
                            store: Constants.editionStore,
                            placeholder: "访问模式",
                        },

                         {
                             header: '做题数：',
                             dataIndex: 'questionCount',
                             xtype: 'text',
                             maxlength: 100,
                             placeholder: '做题数'
                         },
                         {
                            header: '考试分数',
                            dataIndex: 'examScore',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '考试分数'
                        },
                        {
                            header: '考试次数',
                            dataIndex: 'examCount',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '考试次数'
                        },
                        {
                            header: '身份证明',
                            dataIndex: 'identityCode',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '身份证明'
                        },
                        {
                            header: '科目首页',
                            dataIndex: 'mainMaterial',
                            xtype: 'textarea',
                            placeholder: '科目首页'
                        },
                        {
                            header: '我的页面',
                            dataIndex: 'userMaterial',
                            xtype: 'textarea',
                            placeholder: '我的页面',
                           
                        },
                       
                    ]
                },
                {
                    name: '下线',
                    class: 'danger',
                    render: function (name, arr, i) {
                        console.log(arr[i].status,name, arr, i,'arr[i].status');
                        return arr[i].ruleStatus  == 2 ? '下线' : '发布';
                    },
                    click: function (table, row, lineData) {
                        const status = lineData.ruleStatus ? 0 : 2;
                        const text = lineData.ruleStatus ? '下线' : '发布'
                        Widgets.dialog.confirm(`确定${text}吗？`, function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!exam-rule-config/data/updateStatus?status=' + status]).save([{
                                    params: {
                                        id: lineData.id,
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!exam-rule-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     
           
                     {
                         header: '车型',
                         dataIndex: 'carType',
                         render: function (data) {
                             let text = [];
                             data = data.split(',');
                             data.forEach(item => {
                                 text.push(TIKU[item]);
                             })
                             return  text + ''
                         }
                },
                {
                    header: '科目',
                    dataIndex: 'course',
                    render: function (data) {
                        return kemuMap[data]
                    }
                },
                {
                    header: '场景code',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        return data && Constants.senceMap[data]
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'patternCode',
                    render: function (data, arr, i) {
                        return data &&  Constants.editionMap[data]
                    }
                },
                {
                    header: '做题数',
                    dataIndex: 'questionCount'
                },
                {   
                    header: '考试分数',
                    dataIndex: 'examScore'
                },
                {   
                    header: '考试次数',
                    dataIndex: 'examCount'
                },
                {
                    header: '身份证明',
                    dataIndex: 'identityCode'
                },

                {
                    header: '科目首页',
                    dataIndex: 'userMaterial',
                    render: function (un,data,lineData) {
                        let material = JSON.parse(lineData.material);
                        return material[1]
                    }
                },
                {
                    header: '我的页面',
                    dataIndex: 'mainMaterial',
                    render: function (un,data,lineData) {
                        let material = JSON.parse(lineData.material);
                        return material[2]
                    }
                },
                 
                {
                    header: '状态：',
                    dataIndex: 'ruleStatus',
                    render: function (data) {
                        let text;
                        if (data == 0) {
                            text = '未发布'
                        } else if(data == 2){
                            text = '发布'
                        }
                        return text
                    }
                },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                   
                     {
                         header: '修改人名称',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '修改时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     }

            ]
        }, ['jiakao-misc!exam-rule-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});