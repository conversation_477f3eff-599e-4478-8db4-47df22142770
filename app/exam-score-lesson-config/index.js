/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var lessonTypeStore = [
        {
            key: 1,
            value: "父课程"
        }, {
            key: 2,
            value: "子课程"
        }
    ]

    var lessonTypeMap = Tools.getMapfromArray(lessonTypeStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required',
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                check: 'required',
                store: Constants.kemuStore,
            },
            {
                header: '访问场景:',
                dataIndex: 'sceneCode',
                xtype: 'select',
                check: 'required',
                store: [{
                    key: '101',
                    value: '普通场景'
                }, {
                    key: '102',
                    value: '扣满12分'
                }]
            },
            {
                header: '访问模式：',
                dataIndex: 'editionCode',
                xtype: 'select',
                check: 'required',
                store: [{
                    key: '101',
                    value:'普通模式'
                }, {
                    key: '102',
                    value:'长辈模式'
                }]
            },
            {
                header: '范围起：',
                dataIndex: 'rangeStart',
                xtype: 'text',
                check: 'required',
                placeholder: '范围起'
            },
            {
                header: '范围止：',
                dataIndex: 'rangeEnd',
                xtype: 'text',
                check: 'required',
                placeholder: '范围止'
            },
            {
                header: '课程类型：',
                dataIndex: 'lessonType',
                xtype: 'select',
                store: lessonTypeStore,
            },
            {
                header: '课程id：',
                dataIndex: 'lessonId',
                xtype: 'text',
                check: 'required',
                placeholder: '课程id'
            },
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!exam-score-lesson-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '课程模拟考试',
            title: '课程模拟考试',
            search: [{
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
            },{
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
            },{
                dataIndex: 'sceneCode',
                xtype: 'select',
                store: [{ key: '', value: '请选择访问场景' }].concat(Constants.senceStore)
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!exam-score-lesson-config/data/view',
                        save: 'jiakao-misc!exam-score-lesson-config/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!exam-score-lesson-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '访问场景：',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        return Constants.senceMap[data]
                    }
                },
                {
                    header: '访问模式：',
                    dataIndex: 'editionCode',
                    render: function (data, arr, i) {
                        return data == '102' ? '长辈模式' : '普通模式'
                    }
                },
                {
                    header: '范围起：',
                    dataIndex: 'rangeStart'
                },
                {
                    header: '范围止：',
                    dataIndex: 'rangeEnd'
                },
                {
                    header: '课程类型：',
                    dataIndex: 'lessonType',
                    render: function (data, arr, i) {
                        return lessonTypeMap[data]
                    }
                },
                {
                    header: '课程id：',
                    dataIndex: 'lessonId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!exam-score-lesson-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
