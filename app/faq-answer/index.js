/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var answerType = function (id) {
        switch (id) {
            case 1:
                return '一问一答';
            case 2:
                return '一问多答';
        }
    }
    var questionId = 0;
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!faq-answer/data/insert?questionId='+questionId,
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var contentV = $(form).item('content').val();
                    if (contentV.length > 100) {
                        Widgets.dialog.alert('回答内容100字以内');
                        return false
                    }
                    return {}
                }
            },
            columns: [
                {
                    header: '分类：',
                    dataIndex: 'category',
                    xtype: 'hidden',
                    check: 'required',
                    value:1
                },
                {
                    header: '内容：',
                    dataIndex: 'content',
                    xtype: 'textarea',
                    maxlength: 1024,
                    check: 'required',
                    placeholder: '内容'
                },
                {
                    header: '头像',
                    dataIndex: 'avatar',
                    xtype: 'text'
                    // xtype: Plugin('jiakao-misc!upload', {
                    //     dataIndex: 'avatar',
                    //     uploadIndex: 'avatar',
                    //     bucket: "jiakao-web",
                    //     isSingle: true,
                    //     placeholder: '请选择上传文件',
                    //     url: 'simple-upload3://upload/file.htm'
                    // }, function () {
                    //     console.log(arguments)
                    // })
                },
                {
                    header: '昵称：',
                    dataIndex: 'nickName',
                    xtype: 'text',
                    maxlength: 6,
                    placeholder: '昵称'
                },
                {
                    header: '购买时间：',
                    dataIndex: 'buyTime',
                    xtype: 'date',
                    placeholder: '购买时间'
                },
                {
                    header: '标签字符串：',
                    dataIndex: 'tag',
                    xtype: 'text',
                    maxlength: 8,
                    placeholder: '标签字符串'
                }
            ]
        }).add();
    }

    var list = function (panel, routeData) {

        questionId = routeData.id;
        Table({
            description: '答案列表',
            title: '答案列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!faq-answer/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        // {
                        //     header: '类型1问题，2答案：',
                        //     dataIndex: 'type'
                        // },
                        
                        // {
                        //     header: '题目id：',
                        //     dataIndex: 'questionId'
                        // },
                        {
                            header: '内容：',
                            dataIndex: 'content'
                        },
                        {
                            header: '头像：',
                            dataIndex: 'avatar'
                        },
                        {
                            header: '昵称：',
                            dataIndex: 'nickName'
                        },
                        {
                            header: '购买时间：',
                            dataIndex: 'buyTime'
                        },
                        {
                            header: '标签字符串：',
                            dataIndex: 'tag'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        // {
                        //     header: '创建人id：',
                        //     dataIndex: 'createUserId'
                        // },
                        // {
                        //     header: 'createUserName：',
                        //     dataIndex: 'createUserName'
                        // },
                        // {
                        //     header: '更新时间：',
                        //     render: function (data) {
                        //         return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        //     },
                        //     dataIndex: 'updateTime'
                        // },
                        // {
                        //     header: '更新人id：',
                        //     dataIndex: 'updateUserId'
                        // },
                        // {
                        //     header: 'updateUserName：',
                        //     dataIndex: 'updateUserName'
                        // }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form) {
                            var contentV = $(form).item('content').val();
                            if (contentV.length > 100) {
                                Widgets.dialog.alert('回答内容100字以内');
                                return false
                            }
                            return {}
                        }
                    },
                    store: {
                        load: 'jiakao-misc!faq-answer/data/view',
                        save: 'jiakao-misc!faq-answer/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '分类：',
                            dataIndex: 'category',
                            xtype: 'hidden',
                            check: 'required',
                            value:1
                        },
                        {
                            header: '内容：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            maxlength: 100,
                            check: 'required',
                            placeholder: '内容'
                        },
                        {
                            header: '头像',
                            dataIndex: 'avatar',
                            xtype: 'text'
                            // xtype: Plugin('jiakao-misc!upload', {
                            //     dataIndex: 'avatar',
                            //     uploadIndex: 'avatar',
                            //     bucket: "jiakao-web",
                            //     isSingle: true,
                            //     placeholder: '请选择上传文件',
                            //     url: 'simple-upload3://upload/file.htm'
                            // }, function () {
                            //     console.log(arguments)
                            // })
                        },
                        {
                            header: '昵称：',
                            dataIndex: 'nickName',
                            xtype: 'text',
                            maxlength: 6,
                            placeholder: '昵称'
                        },
                        {
                            header: '购买时间：',
                            dataIndex: 'buyTime',
                            xtype: 'date',
                            placeholder: '购买时间'
                        },
                        {
                            header: '标签字符串：',
                            dataIndex: 'tag',
                            xtype: 'text',
                            maxlength: 8,
                            placeholder: '标签字符串'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!faq-answer/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                // {
                //     header: '类型1问题，2答案',
                //     dataIndex: 'type'
                // },
                {
                    header: '题目id',
                    dataIndex: 'questionId'
                },
                {
                    header: '内容',
                    dataIndex: 'content'
                },
                {
                    header: '头像',
                    dataIndex: 'avatar',
                    render: function (data) {
                        if (data) {
                            return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('头像', {}).done(function (dialog) {
                            $(dialog.body).append('<img src="' + lineData.avatar + '" />')
                        })
                    }
                },
                {
                    header: '昵称',
                    dataIndex: 'nickName'
                },
                {
                    header: '购买时间',
                    dataIndex: 'buyTime'
                },
                {
                    header: '标签字符串',
                    dataIndex: 'tag'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                // {
                //     header: '创建人id',
                //     dataIndex: 'createUserId'
                // },
                // {
                //     header: 'createUserName',
                //     dataIndex: 'createUserName'
                // },
                // {
                //     header: '更新时间',
                //     render: function (data) {
                //         return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                //     },
                //     dataIndex: 'updateTime'
                // },
                // {
                //     header: '更新人id',
                //     dataIndex: 'updateUserId'
                // },
                // {
                //     header: 'updateUserName',
                //     dataIndex: 'updateUserName'
                // }

            ]
        }, ['jiakao-misc!faq-answer/data/list?questionId=' + questionId], panel, null).render();
    }

    return {
        list: list
    }

});