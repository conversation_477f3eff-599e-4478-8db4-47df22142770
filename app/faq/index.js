/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var answerType = function (id) {
        switch (id) {
            case 1:
                return '一问一答';
            case 2:
                return '一问多答';
        }
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!faq/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var contentV = $(form).item('content').val();
                    if (contentV.length > 100) {
                        Widgets.dialog.alert('回答内容100字以内');
                        return false
                    }

                    let status = [];
                    let types = $(form).item('types');
                    let typesL = $(form).item('types').length;
                    for (let i = 0; i < typesL; i++) {
                        if (types[i].checked) {
                            status.push(types[i].value)
                        }
                    }

                    return {
                        status: status.toString()
                    }
                }
            },
            columns: [
                {
                    header: '分类：',
                    dataIndex: 'category',
                    xtype: 'radio',
                    check: 'required',
                    xtype: 'hidden',
                    value: 1
                    // store: [
                    //     {
                    //         key: 1,
                    //         value: '一问一答'
                    //     },
                    //     {
                    //         key: 2,
                    //         value: '一问多答'
                    //     }
                    // ]
                },
                {
                    header: '内容：',
                    dataIndex: 'content',
                    xtype: 'textarea',
                    maxlength: 100,
                    check: 'required',
                    placeholder: '内容'
                },
                {
                    header: '头像',
                    dataIndex: 'avatar',
                    xtype: 'text'
                    // xtype: Plugin('jiakao-misc!upload', {
                    //     dataIndex: 'avatar',
                    //     uploadIndex: 'avatar',
                    //     bucket: "jiakao-web",
                    //     isSingle: true,
                    //     placeholder: '请选择上传文件',
                    //     url: 'simple-upload3://upload/file.htm'
                    // }, function () {
                    //     console.log(arguments)
                    // })
                },
                // {
                //     header: '头像：',
                //     dataIndex: 'avatar',
                //     xtype: 'textarea',
                //     maxlength: 256,
                //     placeholder: '头像'
                // },
                {
                    header: '昵称：',
                    dataIndex: 'nickName',
                    xtype: 'text',
                    maxlength: 6,
                    placeholder: '昵称'
                },
                {
                    header: '科目:',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择'
                    }, {
                        key: 1,
                        value: '科目一'
                    },
                    {
                        key: 2,
                        value: '科目二'
                    }, {
                        key: 3,
                        value: '科目三'
                    },
                    {
                        key: 4,
                        value: '科目四'
                    }],
                    placeholder: '科目'
                },
                {
                    header: '购买时间：',
                    dataIndex: 'buyTime',
                    xtype: 'date',
                    placeholder: '购买时间'
                },
                {
                    header: '状态',
                    dataIndex: 'types',
                    xtype: 'checkbox',
                    store: [
                        {
                            key: 1,
                            value: '未购买'
                        },
                        {
                            key: 2,
                            value: '已购买'
                        }]
                },
                {
                    header: '标签字符串：',
                    dataIndex: 'tag',
                    xtype: 'text',
                    maxlength: 8,
                    placeholder: '标签字符串'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '常用faq列表',
            title: '常用faq列表',
            search: [{
                dataIndex: 'kemu',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择'
                }, {
                    key: 1,
                    value: '科目一'
                },
                {
                    key: 2,
                    value: '科目二'
                }, {
                    key: 3,
                    value: '科目三'
                },
                {
                    key: 4,
                    value: '科目四'
                }],
            }, {
                dataIndex: 'status',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择'
                }, {
                    key: 1,
                    value: '未购买'
                },
                {
                    key: 2,
                    value: '已购买'
                },
                {
                    key: '1,2',
                    value: '未购买/已购买'
                }],
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!faq/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        // {
                        //     header: '类型1问题，2答案：',
                        //     dataIndex: 'type'
                        // },
                        // {
                        //     header: '分类',
                        //     render: function (id) {
                        //         return answerType(id);
                        //     },
                        //     dataIndex: 'category'
                        // },
                        // {
                        //     header: '题目id：',
                        //     dataIndex: 'questionId'
                        // },
                        {
                            header: '内容：',
                            dataIndex: 'content'
                        },
                        {
                            header: '头像：',
                            dataIndex: 'avatar'
                        },
                        {
                            header: '昵称：',
                            dataIndex: 'nickName'
                        },
                        {
                            header: '购买时间：',
                            dataIndex: 'buyTime'
                        },
                        {
                            header: '标签字符串：',
                            dataIndex: 'tag'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        // {
                        //     header: '创建人id：',
                        //     dataIndex: 'createUserId'
                        // },
                        // {
                        //     header: 'createUserName：',
                        //     dataIndex: 'createUserName'
                        // },
                        // {
                        //     header: '更新时间：',
                        //     render: function (data) {
                        //         return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        //     },
                        //     dataIndex: 'updateTime'
                        // },
                        // {
                        //     header: '更新人id：',
                        //     dataIndex: 'updateUserId'
                        // },
                        // {
                        //     header: 'updateUserName：',
                        //     dataIndex: 'updateUserName'
                        // }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    renderAfter: function (config, dom, data) {
                        let status = data.data.status.toString().split(',');
                        for (let i = 0; i < status.length; i++) {
                            $(dom).item('types')[status[i] - 1].checked = true;
                        }
                    },
                    form: {
                        submitHandler: function (form) {
                            var contentV = $(form).item('content').val();
                            if (contentV.length > 100) {
                                Widgets.dialog.alert('回答内容100字以内');
                                return false
                            }

                            let status = [];
                            let types = $(form).item('types');
                            let typesL = $(form).item('types').length;
                            for (let i = 0; i < typesL; i++) {
                                if (types[i].checked) {
                                    status.push(types[i].value)
                                }
                            }

                            return {
                                status: status.toString()
                            }
                        }
                    },
                    store: {
                        load: 'jiakao-misc!faq/data/view',
                        save: 'jiakao-misc!faq/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '分类：',
                            dataIndex: 'category',
                            xtype: 'hidden',
                            check: 'required',
                            value: 1
                        },
                        {
                            header: '内容：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            check: 'required',
                            placeholder: '内容'
                        },
                        {
                            header: '头像',
                            dataIndex: 'avatar',
                            xtype: 'text'
                            // xtype: Plugin('jiakao-misc!upload', {
                            //     dataIndex: 'avatar',
                            //     uploadIndex: 'avatar',
                            //     bucket: "jiakao-web",
                            //     isSingle: true,
                            //     placeholder: '请选择上传文件',
                            //     url: 'simple-upload3://upload/file.htm'
                            // }, function () {
                            //     console.log(arguments)
                            // })
                        },
                        {
                            header: '昵称：',
                            dataIndex: 'nickName',
                            xtype: 'text',
                            maxlength: 6,
                            placeholder: '昵称'
                        },
                        {
                            header: '科目:',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '请选择'
                            }, {
                                key: 1,
                                value: '科目一'
                            },
                            {
                                key: 2,
                                value: '科目二'
                            }, {
                                key: 3,
                                value: '科目三'
                            },
                            {
                                key: 4,
                                value: '科目四'
                            }],
                            placeholder: '科目'
                        },
                        {
                            header: '购买时间：',
                            dataIndex: 'buyTime',
                            xtype: 'date',
                            placeholder: '购买时间'
                        },
                        {
                            header: '状态',
                            dataIndex: 'types',
                            xtype: 'checkbox',
                            store: [
                                {
                                    key: 1,
                                    value: '未购买'
                                },
                                {
                                    key: 2,
                                    value: '已购买'
                                }]
                        },
                        {
                            header: '标签字符串：',
                            dataIndex: 'tag',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '标签字符串'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!faq/data/delete'
                },
                {
                    name: '答案列表',
                    class: 'success',
                    click: function (table, row, lineData) {
                        console.log(lineData)
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('faq-answer-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'faq-answer-' + lineData.id,
                                    name: '答案列表'
                                })
                            }
                            require(['jiakao-misc!app/faq-answer/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                // {
                //     header: '类型1问题，2答案',
                //     dataIndex: 'type'
                // },
                // {
                //     header: '分类',
                //     render: function (id) {
                //         return answerType(id);
                //     },
                //     dataIndex: 'category'
                // },
                // {
                //     header: '题目id',
                //     dataIndex: 'questionId'
                // },
                {
                    header: '内容',
                    dataIndex: 'content'
                },
                {
                    header: '头像',
                    dataIndex: 'avatar',
                    render: function (data) {
                        if (data) {
                            return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('头像', {}).done(function (dialog) {
                            $(dialog.body).append('<img src="' + lineData.avatar + '" />')
                        })
                    }
                },
                {
                    header: '昵称',
                    dataIndex: 'nickName'
                },
                {
                    header: '科目',
                    dataIndex: 'kemu'
                },
                {
                    header: '购买时间',
                    dataIndex: 'buyTime'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        if (data == 1) {
                            return '未购买'
                        } else if (data == 2) {
                            return '已购买'
                        } else if (data == '1,2') {
                            return '已购买/未购买'
                        }
                    }
                },
                {
                    header: '标签字符串',
                    dataIndex: 'tag'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                // {
                //     header: '创建人id',
                //     dataIndex: 'createUserId'
                // },
                // {
                //     header: 'createUserName',
                //     dataIndex: 'createUserName'
                // },
                // {
                //     header: '更新时间',
                //     render: function (data) {
                //         return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                //     },
                //     dataIndex: 'updateTime'
                // },
                // {
                //     header: '更新人id',
                //     dataIndex: 'updateUserId'
                // },
                // {
                //     header: 'updateUserName',
                //     dataIndex: 'updateUserName'
                // }

            ]
        }, ['jiakao-misc!faq/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});