<template>
    <div class="film-video">
        <div class="header">
            {{props.pageParamsType.title}}
        </div>
        <div class="content">
            <div class="l">
                <VideoView :lineData="props.lineData" :pageParamsType="props.pageParamsType"/>
            </div>
            <div class="r">
                <MTable :pageParamsType="props.pageParamsType"/>
                <div class="">
                    <label>是否已确认：</label>
                    <input
                        type="radio"
                        name="zijian"
                         @change="onRadioChange($event)"
                        class="zijian-radio"
                        :checked='!commonStore.timelineVideoCheck'
                        :value="false"
                    />&nbsp;未自检 &nbsp;&nbsp;&nbsp;&nbsp;
                     <input
                        type="radio"
                        @change="onRadioChange($event)"
                        name="zijian"
                         :checked='commonStore.timelineVideoCheck'
                         class="zijian-radio"
                        :value="true"
                    />&nbsp;已自检 &nbsp;
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import VideoView from '../VideoView/index.vue';
import MTable from '../Table/index.vue';
import { defineProps,ref } from 'vue';
import { useCommonStore } from '../../piniaStore/common.ts';
const commonStore = useCommonStore();
const props = defineProps({
    lineData: Object,
    pageParamsType: Object,
})
commonStore.lineData = props.lineData;
commonStore.timelineVideoCheck = props.lineData.timelineVideoCheck
console.log(props.pageParamsType)
if (props.pageParamsType.type === 'timelineVideo') {
    if (props.lineData.timelineVideo) {
        commonStore.getVideDetail(JSON.parse(props.lineData.timelineVideo))
    }
} else {
    if (props.lineData.timelinePractice) {
        commonStore.getVideDetail(JSON.parse(props.lineData.timelinePractice))
    }
}
function onRadioChange($event){
  commonStore.timelineVideoCheck = $event.target.value
}
</script>

<style lang="less" scoped>
.film-video{
    height: 100%;
    .header{
        font-weight: bold;
        font-size: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ccc;
    }
    .zijian-radio{
        vertical-align: text-top;
        cursor: pointer;
    }
    .content{
        display: flex;
        justify-content:space-between;
        height: 100%;

        .l{
            width: 800px;
            height: 100%;
        }
        .r{
            margin-top: 40px;
            flex-grow: 1;
            height: 100%;
            padding-left: 20px;
        }
    }
}

</style>
