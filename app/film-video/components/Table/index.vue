<template>
    <div class="table">
        <div class="top">
            <div class="title">
                编辑制作策略
            </div>
            <div style="margin-left: auto"></div>
            <div class="btn label label-warning" style="margin-right: 10px" @click="onKeepOriginalSound">
                一键保留原声
            </div>
            <div class="btn label label-danger" style="margin-right: 10px" @click="onRemoveOriginalSound">
                一键去掉原声
            </div>
            <div class="btn label label-primary" v-if="props.pageParamsType.type === 'timelineVideo'" style="margin-right: 10px" @click="onBatchInsertBlueSign">
                一键插入蓝牌
            </div>
            <div class="btn label label-success" @click="onSave">
                保存并提交
            </div>
        </div>
        <Table  :dataSource="commonStore.data" 
                :columns="lineDetailColumns" 
                :bordered="true"
                :pagination="{ pageSize: 15 }"
                :customRow="rowClick"
                :rowClassName="classs"
            >
            <template #operation="{ record  }">
                <div class="table-operation">
                   <template v-if="!isExamEndingTemplate(record)">
                       <a @click.stop="upInsert(record.uuid)" :class="record.flag && record.flag">向上插入</a>
                       <a @click.stop="downInsert(record.uuid)" :class="record.flag && record.flag">向下插入</a>
                       <a @click.stop="deleteItem(record.uuid)" :class="record.flag && record.flag">删除</a>
                   </template>
                   <template v-else>
                       <span style="color: #999;">片尾模板</span>
                   </template>
                </div>
            </template>
        </Table>
    </div>
</template>

<script setup>
import {find} from 'lodash'
import { onMounted, reactive, ref } from 'vue';
import {  Table } from 'ant-design-vue';
import { useCommonStore ,showClock, toChineseNum, findPointDataByKeys} from '../../piniaStore/common.ts';
import { pointMap, blueSingData, examItemList } from '../../data';
import Widgets from 'simple!core/widgets';

function addSeconds(time, seconds) {
    const timeParts = time.split(':').map(Number);
    const totalSeconds = timeParts[0] * 60 + timeParts[1] + seconds;
    const minutes = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
}

const commonStore = useCommonStore();
const props = defineProps({
    pageParamsType: Object
})
function classs(record){
    const string = [];
    if(record.uuid === commonStore.selectUUid){
        string.push('now-select')
    }
    if (record.type == 'noExam') {
        string.push('no-exam')
    }
    if (record.type == 'important') {
        string.push('important-class')
    }
    if (record.type == 'exam') {
        string.push('exam')
    }

    return string;
}

function onSave() {
    commonStore.save(props.pageParamsType.type);
    // commonStore.getVideDetail(commonStore.data)
}

function onBatchInsertBlueSign() {
    console.log(commonStore.data)
    if (commonStore.data.some(item => item.type == 'noExam' && item.itemId == '蓝牌')) {
        Widgets.dialog.alert('已存在蓝牌数据，无法一键插入新的蓝牌');
        return;
    }
    let bIndex = 1
    for (let index = 0; index < commonStore.data.length; index++) {
        const item = commonStore.data[index];
        if (item.type === 'exam') {
            const examItem = find(examItemList, {key: Number(item.itemId)});
            if (examItem) {
                const bulueSign = find(blueSingData, {key: examItem.value});
                if (bulueSign) {
                    let {beginTime} = item;
                    let endTime
                    if (beginTime >= 0) {
                        endTime = beginTime + Number(bulueSign.time);
                    }

                    const data = {
                        type: 'noExam',
                        itemId: '蓝牌',
                        itemName: '蓝牌',
                        templateName: bulueSign.value,
                        hint: `考试第${toChineseNum(bIndex++)}项`,
                        beginTime,
                        endTime,
                        duration: findPointDataByKeys('noExam', '蓝牌', bulueSign.value)?.time || '',
                    };
                    commonStore.insertBlueSign(index+1, data)
                }
            }
        }
    }
}

const lineDetailColumns = props.pageParamsType.type === 'timelineVideo'? reactive([
    {
        title: '打点类型',
        dataIndex: 'type',
        key: 'type',
        customRender: (lineData) =>{
            return pointMap[lineData.value]
        }
    },
    {
        title: '打点名称',
        dataIndex: 'itemName',
        key: 'itemName',
    },
    {
        title: '选择字幕&音频模板',
        dataIndex: 'templateName',
        key: 'templateName',
    },
    {
        title: '音频时长',
        dataIndex: 'templateName',
        customRender: (lineData, record, index) => {
            return lineData?.record?.duration
        }
    },
    {
        title: '选择提示模板',
        dataIndex: 'hint',
        key: 'hint',
    },
    {
        title: '起止时间',
        dataIndex: 'beginTime',
        key: 'beginTime',
        customRender: (lineData, record, index) => {
           // 对于考试结束模板，不显示结束时间
           if (isExamEndingTemplate(lineData.record)) {
               return `${showClock(lineData.record.beginTime)}`;
           }
           // 对于最后一个考试项目，显示起止时间范围
           if (lineData.record.type === 'exam' && isLastExamItem(index)) {
               return `${showClock(lineData.record.beginTime)} ${lineData.record.endTime ? '- ' + showClock(lineData.record.endTime) : ''}`;
           }
           return `${showClock(lineData.record.beginTime)} ${lineData.record.endTime ? '- ' + showClock(lineData.record.endTime) : ''}`
        }
    },
    {
        title: '操作',
        dataIndex: 'operation',
        slots: { customRender: 'operation' }
    },
]) : reactive([
    {
        title: '打点类型',
        dataIndex: 'type',
        key: 'type',
        customRender: (lineData) =>{
            return pointMap[lineData.value]
        }
    },
    {
        title: '打点名称',
        dataIndex: 'itemName',
        key: 'itemName',
    },
    {
        title: '起止时间',
        dataIndex: 'beginTime',
        key: 'beginTime',
        customRender: (lineData, record, index) => {
           // 对于考试结束模板，不显示结束时间
           if (isExamEndingTemplate(lineData.record)) {
               return `${showClock(lineData.record.beginTime)}`;
           }
           // 对于最后一个考试项目，显示起止时间范围
           if (lineData.record.type === 'exam' && isLastExamItem(index)) {
               return `${showClock(lineData.record.beginTime)} ${lineData.record.endTime ? '- ' + showClock(lineData.record.endTime) : ''}`;
           }
           return `${showClock(lineData.record.beginTime)} ${lineData.record.endTime ? '- ' + showClock(lineData.record.endTime) : ''}`
        }
    },
    {
        title: '操作',
        dataIndex: 'operation',
        slots: { customRender: 'operation' }
    },
])

function upInsert(uuid) {
    const findIndex = commonStore.data.findIndex(item => item.uuid == uuid);
    commonStore.insert(findIndex);
}

function downInsert(uuid) {
    const findIndex = commonStore.data.findIndex(item => item.uuid == uuid);
    commonStore.insert(findIndex+1);
}

function deleteItem(uuid) {
    commonStore.deleteItem(uuid);
}

function rowClick(record) {
    return {
        onClick: () => {
            commonStore.selectUUid = record.uuid;
        },
    }
}

function onKeepOriginalSound() {
    commonStore.data.forEach(item => {
        if (item.type === 'exam') {
            const examItem = find(examItemList, {key: Number(item.itemId)});
            if (examItem) {
                const templates = findPointDataByKeys('exam', Number(item.itemId))?.children || [];
                const noSoundTemplate = templates.find(t => t.key === '无音频无字幕');
                if (noSoundTemplate) {
                    item.templateName = noSoundTemplate.value;
                    item.duration = noSoundTemplate.time;
                }
            }
        }
    });
}

function onRemoveOriginalSound() {
    commonStore.data.forEach(item => {
        if (item.type === 'exam') {
            const examItem = find(examItemList, {key: Number(item.itemId)});
            if (examItem) {
                const templates = findPointDataByKeys('exam', Number(item.itemId))?.children || [];
                const defaultTemplate = templates.find(t => t.key.startsWith('通用')) || templates[0];
                if (defaultTemplate) {
                    item.templateName = defaultTemplate.value;
                    item.duration = defaultTemplate.time;
                }
            }
        }
    });
}

function isExamEndingTemplate(record) {
    return record.type === 'noExam' && record.itemId === '考试结束';
}

function isLastExamItem(index) {
    // 检查是否是最后一个考试项目
    const examItems = commonStore.data.filter(item => item.type === 'exam');
    if (examItems.length === 0) return false;

    const lastExamItem = examItems[examItems.length - 1];
    return commonStore.data[index] === lastExamItem;
}

</script>

<style lang="less" scoped>
    .table{
        .top{
            display: flex;
            justify-content:space-between;
            align-items:center;
            margin-bottom:10px;
            padding-right: 40px;
            .title{
                font-weight: bold;
                font-size: 16px;
            }
        }
        :deep(.now-select){
            background-color: #93cce6;
        }

        :deep(.no-exam){
            color: red;
            background-color: #fff3f3;
        }

        :deep(.important-class){
            color: red;
            background-color: #fff3f3;
        }

        :deep(tr.exam){
            background-color: #f6ffed;
        }
     
        .table-operation{
            display: flex;
            a{
                margin: 0 5px;
            }
            .orange{
                color: rgb(255, 201, 5);
            }
        }

        :deep(.ant-table-cell-row-hover){
            background-color: #93cce6!important;
        }
    }
</style>
