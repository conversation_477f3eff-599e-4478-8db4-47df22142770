<template>
    <div class="video-view">
        <div class="text-info">
            考场KEY: {{ props.lineData.sceneKey }} &nbsp;&nbsp;&nbsp; 视频ID:
            {{ props.lineData.id }}&nbsp;&nbsp;&nbsp; 考场:
            {{ props.lineData.sceneName + ' — ' + props.lineData.subSceneName }}
            &nbsp;&nbsp;&nbsp; 线路:
            {{ props.lineData.routeName }}&nbsp;&nbsp;&nbsp; 教练上传视频ID:
            {{ props.lineData.coachVideoId }}
        </div>
        <div class="video-wrap">
            <video
                :src="props.lineData.originalVideoUrl"
                controls
                ref="$video"
                v-if="props.pageParamsType.type == 'timelineVideo'"
            ></video>
            <video
                :src="props.lineData.videoItemData"
                controls
                ref="$video"
                v-if="props.pageParamsType.type == 'timelinePractice'"
            ></video>
            <div
                class="line"
                v-if="props.pageParamsType.type == 'timelineVideo'"
            ></div>
        </div>

        <div class="action-bar">
            <div class="title">编辑当前选中时间轴</div>
            <div class="select-group">
                <template v-if="props.pageParamsType.type === 'timelineVideo'">
                    打点类型:
                    <select
                        name="point-type"
                        v-model="detail.type"
                        @change="onPintTypeChange"
                    >
                        <option
                            :value="item.key"
                            v-for="item in [
                                { key: '', value: '请选择' },
                                ...pointType,
                            ]"
                            :key="item.key"
                        >
                            {{ item.value }}
                        </option>
                    </select>
                    &nbsp;&nbsp;&nbsp;
                </template>
                <template
                    v-else-if="props.pageParamsType.type === 'timelinePractice'"
                >
                    打点类型:
                    <select
                        name="point-type"
                        v-model="detail.type"
                        @change="onPintTypeChange"
                    >
                        <option
                            :value="item.key"
                            v-for="item in [
                                { key: '', value: '请选择' },
                                ...pointType2,
                            ]"
                            :key="item.key"
                        >
                            {{ item.value }}
                        </option>
                    </select>
                    &nbsp;&nbsp;&nbsp;
                </template>

                <template v-if="detail.type === 'exam'">
                    打点名称:
                    <select
                        name="exam-names"
                        v-model="detail.itemId"
                        @change="onItemName($event, detail.itemId)"
                    >
                        <option
                            :value="item.key"
                            v-for="item in [
                                { key: '', value: '请选择' },
                                ...examNames,
                            ]"
                            :key="item.key"
                        >
                            {{ item.value }}
                        </option></select
                    >&nbsp;&nbsp;&nbsp;
                </template>
                <template v-else-if="detail.type === 'noExam'">
                    打点名称:
                    <select
                        name="exam-names"
                        v-model="detail.itemId"
                        @change="onItemName($event, detail.itemId)"
                    >
                        <option
                            :value="item.key"
                            v-for="item in [
                                { key: '', value: '请选择' },
                                ...examNames,
                            ]"
                            :key="item.key"
                        >
                            {{ item.value }}
                        </option></select
                    >&nbsp;&nbsp;&nbsp;
                </template>
                <template v-else-if="detail.type === 'important'">
                    打点名称:
                    <select
                        name="exam-names"
                        v-model="detail.itemId"
                        @change="onItemName($event, detail.itemId)"
                    >
                        <option
                            :value="item.key"
                            v-for="item in [
                                { key: '', value: '请选择' },
                                ...importantList,
                            ]"
                            :key="item.key"
                        >
                            {{ item.value }}
                        </option></select
                    >&nbsp;&nbsp;&nbsp;
                </template>

                <template v-if="props.pageParamsType.type === 'timelineVideo'">
                    选择字幕&音频:
                    <select
                        name="text-audio"
                        v-model="detail.templateName"
                        @change="onAudioTemplate($event, detail.templateName)"
                    >
                        <option
                            :value="item.key"
                            v-for="item in [
                                { key: '', value: '请选择' },
                                ...textAndAudioTemplate,
                            ]"
                            :key="item.key"
                        >
                            {{ item.value }}
                        </option></select
                    >&nbsp;&nbsp;&nbsp; <br /><br />

                    <template
                        v-if="
                            detail.type === 'noExam' && detail.itemId === '蓝牌'
                        "
                    >
                        选择提示模板:
                        <select
                            name="text-audio"
                            v-model="detail.hint"
                            style="width: 200px"
                            disabled
                        >
                            <option :value="detail.hint" :key="detail.hint">
                                {{ detail.hint }}
                            </option>
                        </select>
                        &nbsp;&nbsp;&nbsp;
                    </template>
                    <template
                        v-if="
                            detail.type === 'noExam' &&
                            useHintnoExamList.indexOf(detail.itemId) !== -1
                        "
                    >
                        选择提示模板:
                        <select
                            name="text-audio"
                            v-model="detail.hint"
                            style="width: 200px"
                        >
                            <option
                                :value="item.key"
                                v-for="item in [
                                    { key: '', value: '请选择' },
                                    ...hintTemplate,
                                ]"
                                :key="item.key"
                            >
                                {{ item.value }}
                            </option>
                        </select>
                        &nbsp;&nbsp;&nbsp;
                    </template>
                </template>

                开始时间:
                <input
                    type="text"
                    key="begin"
                    :value="showClock(detail.beginTime)"
                    @change="onTimeChange($event, 'beginTime')"
                />
                结束时间:
                <input
                    type="text"
                    key="end"
                    :value="showClock(detail.endTime)"
                    @change="onTimeChange($event, 'endTime')"
                    disabled
                />
                <template v-if="showDirection">
                    方向:
                    <select v-model="detail.turningDirection">
                        <option
                            :value="item.key"
                            v-for="item in [
                                { key: '', value: '请选择' },
                                ...changeDirection,
                            ]"
                            :key="item.key"
                        >
                            {{ item.value }}
                        </option>
                    </select>
                </template>
                <br /><br />
                <template
                    v-if="
                        detail.type === 'noExam' &&
                        (detail.itemId === '本地教练提醒' ||
                            detail.itemId === '重难点提醒')
                    "
                >
                    编辑文案:
                    <div
                        style="
                            width: 400px;
                            display: inline-block;
                            vertical-align: text-top;
                        "
                    >
                        <textarea
                            class="rich"
                            cols="80"
                            v-model="detail.extraInfo.content"
                            rows="4"
                            :placeholder="`表示换页，<font color=&quot;#FFBD30&quot;>黄色字体</font>\n<br/>表示换行`"
                        ></textarea>
                    </div>
                    <br /><br />
                    预览文案:
                    <div
                        v-html="detail.extraInfo.content"
                        class="preview-text"
                        :class="
                            getClassFromTeml(detail.itemId, detail.templateName)
                        "
                    ></div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Plugin as APlugin } from '@simplex/admin-vue';
import { computed, onMounted, ref, reactive, watchEffect } from 'vue';
import {
    pointData,
    pointType,
    pointType2,
    hintTemplate,
    examNameMap,
    importantList,
    changeDirection,
    useHintnoExamList,
} from '../../data.js';
import { useCommonStore, showClock } from '../../piniaStore/common.ts';
import umeditor from ':plugin/umeditor.vue';

const commonStore = useCommonStore();

console.log('commonStore------', commonStore);

const props = defineProps({
    lineData: Object,
    pageParamsType: Object,
});

const umeditorComp = APlugin(umeditor, {
    ueConfig: {
        initialFrameWidth: 400,
        initialFrameHeight: 100,
        toolbar: ['source | undo redo | bold | forecolor | fontsize'],
        fontsize: [12, 16, 18, 24, 32],
    },
});

const $video = ref(null);
const detail = computed(() => {
    return commonStore.data.find((item) => item.uuid == commonStore.selectUUid);
});

const preDetail = computed(() => {
    console.log('qwewewe', commonStore.selectUUid);
    const i = commonStore.data.findIndex(
        (item) => item.uuid == commonStore.selectUUid
    );
    return commonStore.data[i - 1] || null;
});
/** 是否显示方向下拉框 */
const showDirection = computed(() => {
    return detail.value.itemId === 6;
});

let examNames = ref([]);
let textAndAudioTemplate = ref([]);

watchEffect(() => {
    examNames.value = getExamNames(detail.value.type);
    textAndAudioTemplate.value = getTemplate(detail.value.itemId);
    detail.value.templateName = detail.value.templateName || '';
});

function onTimeChange(e, type) {
    const v = e.target.value;
    if (!v.includes(':')) {
        Simple.Dialog.toast('时间格式错误');
        return;
    }

    const data = v.split(':');

    if (data.length > 2) {
        Simple.Dialog.toast('请输入正确的时间格式');
        return;
    }

    if (data[1] > 59) {
        Simple.Dialog.toast('请输入正确的时间格式');
        return;
    }
    const videoDuration = $video.value.duration;

    console.log('$video------', $video.value.duration);

    const value = data[0] * 60 + +data[1];

    if (detail.value.templateName === '考试结束祝福语') {
        detail.value[type] = value;
        return;
    }

    if (
        type == 'beginTime' &&
        detail.value.endTime &&
        value > detail.value.endTime
    ) {
        Simple.Dialog.toast('开始时间需要小于结束时间');
        return;
    }

    if (
        type == 'endTime' &&
        detail.value.beginTime &&
        value < detail.value.beginTime
    ) {
        Simple.Dialog.toast('结束时间需要大于开始时间');
        return;
    }

    if (value > videoDuration) {
        Simple.Dialog.toast('时间范围不能大于视频总时长');
        return;
    }
    detail.value[type] = value;
    // console.log('detail-----', detail);
    // console.log('preDetail-----', preDetail);
    // console.log('textAndAudioTemplate.value', textAndAudioTemplate.value);
    // console.log(
    //     'textAndAudioTemplate.value.find(item => item.key == preDetail?.value?.templateName)-----',
    //     textAndAudioTemplate.value.find(
    //         (item) => item.key == preDetail?.value?.templateName
    //     )
    // );
    if (
        detail.value.beginTime - preDetail?.value?.beginTime <
        textAndAudioTemplate.value.find(
            (item) => item.key == preDetail?.value?.templateName
        ).time
    ) {
        detail.value.flag = 'orange';
    } else {
        delete detail.value.flag;
    }
}

function onPintTypeChange() {
    detail.value.itemId = '';
    detail.value.itemName = '';
    detail.value.templateName = '';
    detail.value.hint = '';
    detail.value.extraInfo = { content: '' };
    detail.value.duration = '';
    examNames.value = getExamNames(detail.value.type);
    console.log('onPintTypeChange click-----', detail, examNames);
}

function onItemName(e, id) {
    detail.value.templateName = '';
    detail.value.hint = '';
    detail.value.extraInfo = { content: '' };
    detail.value.duration = '';

    detail.value.itemName = examNameMap[id] || id;
    textAndAudioTemplate.value = getTemplate(detail.value.itemId);
    console.log('onItemName click-----', detail, textAndAudioTemplate);
}

function onAudioTemplate(e, templateName) {
    detail.value.duration = getDuration(templateName);
    if (detail.value.templateName === '考试结束祝福语') {
        detail.value.beginTime = $video.value.duration;
    }
    console.log('onAudioTemplate click-----', detail);
}

function getClassFromTeml(itemId, templateName) {
    let className = '';
    if (itemId === '本地教练提醒') {
        switch (templateName) {
            case '提醒（一行文字）':
                className = 'coach-tip1';
                break;
            case '提醒（两行文字）':
                className = 'coach-tip2';
                break;
            case '提醒（三行文字）':
                className = 'coach-tip3';
                break;
            default:
                break;
        }
    } else if (itemId === '重难点提醒') {
        if (templateName === '通用提示') {
            className = 'hard-tip1';
        }
    }
    return className;
}

function getExamNames(type) {
    if (type) {
        let data = pointData.filter((item) => {
            return item.key == type;
        });
        return data[0]?.children || [];
    }
    return [];
}
function getTemplate(examName) {
    let data = examNames.value.filter((item) => {
        return item.key == examName;
    });
    return data[0]?.children || [];
}
function getDuration(TemplateName) {
    let data = textAndAudioTemplate.value.filter((item) => {
        return item.key == TemplateName;
    });
    return data[0]?.time || '';
}

onMounted(() => {
    setTimeout(() => {
        commonStore.setVideoDuration(Math.floor($video.value.duration));
    }, 1000)
});
</script>

<style lang="less" scoped>
.video-view {
    .text-info {
        margin: 20px 0;
    }

    .video-wrap {
        width: 100%;
        height: 450px;
        position: relative;

        video {
            height: 100%;
            width: 100%;
        }
        .line {
            position: absolute;
            line-height: 0;
            font-size: 0;
            z-index: 1;
            top: 73%;
            left: 0;
            width: 100%;
            height: 1px;
            background-color: #fff;
        }
    }

    .action-bar {
        .title {
            margin: 20px 0;
        }
    }
}
.preview-text {
    vertical-align: text-top;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 360px;
    height: 160px;
    padding: 92px 34px 20px 64px;
    :deep(p) {
        margin-bottom: 0;
    }
    &.coach-tip1 {
        width: 360px;
        height: 160px;
        padding: 92px 34px 20px 64px;
        background: url('./coach-tip1.png') no-repeat center center;
        background-size: 100% 100%;
    }
    &.coach-tip2 {
        width: 360px;
        height: 200px;
        padding: 92px 34px 20px 64px;
        background: url('./coach-tip2.png') no-repeat center center;
        background-size: 100% 100%;
    }
    &.coach-tip3 {
        width: 360px;
        height: 210px;
        padding: 92px 34px 20px 64px;
        background: url('./coach-tip3.png') no-repeat center center;
        background-size: 100% 100%;
    }
    &.hard-tip1 {
        width: 325px;
        height: 175px;
        padding: 0 20px 0 82px;
        background: url('./hard-tip1.png') no-repeat center center;
        background-size: 100% 100%;
    }
}
</style>
