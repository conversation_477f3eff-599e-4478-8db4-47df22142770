import {find} from 'lodash'

/**考试项目 */
export const examNames = [
    {
        "value": "全局通用事件",
        "key": 3
    },
    {
        "value": "上车准备",
        "key": 1
    },
    {
        "value": "灯光模拟",
        "key": 2
    },
    {
        "value": "起步",
        "key": 4
    },
    {
        "value": "直线行驶",
        "key": 5
    },
    {
        "value": "变更车道",
        "key": 6
    },
    {
        "value": "超车",
        "key": 7
    },
    {
        "value": "前方会车",
        "key": 8
    },
    {
        "value": "路口左转",
        "key": 9
    },
    {
        "value": "路口右转",
        "key": 10
    },
    {
        "value": "掉头",
        "key": 11
    },
    {
        "value": "路口直行",
        "key": 12
    },
    {
        "value": "加减挡",
        "key": 13
    },
    {
        "value": "通过公共汽车站",
        "key": 14
    },
    {
        "value": "通过学校",
        "key": 15
    },
    {
        "value": "通过人行横道",
        "key": 16
    },
    {
        "value": "靠边停车",
        "key": 17
    },
    {
        "value": "减速",
        "key": 18
    },
    {
        "value": "限速",
        "key": 19
    },
    {
        "value": "通过隧道",
        "key": 20
    },
    {
        "value": "通过拱桥",
        "key": 21
    },
    {
        "value": "通过急弯坡路",
        "key": 22
    },
    {
        "value": "临时停车",
        "key": 23
    },
    {
        "value": "停车让行",
        "key": 24
    },
    {
        "value": "通过环岛",
        "key": 25
    },
    {
        "value": "通过禁止停车区域",
        "key": 26
    },
    {
        "value": "超车(仅变道)",
        "key": 27
    },
    {
        "value": "特殊区域",
        "key": 28
    }
]

export const examNameMap = arrToMap(examNames);

/*打点类型 */
export const pointType = [
    {
        value: '考试项目',
        key: 'exam'
    },
    {
        value: '非考试项目',
        key: 'noExam'
    }
];

/*打点类型 */
export const pointType2 = [
    {
        value: '考试项目',
        key: 'exam'
    },
    {
        value: '重点项目',
        key: 'important'
    }
];


export const pointMap = {
    important: '重点项目',
    exam: '考试项目',
    noExam: '非考试项目'
};

function arrToMap(arr) {
    const obj = {};
    arr.forEach(item => {
        obj[item.key] = item.value
    })
    return obj
}

/**提示模板 */
export const hintTemplate = [
    {
        key: '自主变道掉头(提示)',
        value: '自主变道掉头(提示)'
    },
    {
        key: '自主变道靠边停车(提示)',
        value: '自主变道靠边停车(提示)'
    },

    {
        key: '自主变道路到尽头(提示)',
        value: '自主变道路到尽头(提示)'
    },
    {
        key: '自主变道路口直行(提示)',
        value: '自主变道路口直行(提示)'
    },
    {
        key: '自主变道慢车道(提示)',
        value: '自主变道慢车道(提示)'
    },
    {
        key: '自主变道右转(提示)',
        value: '自主变道右转(提示)'
    },
    {
        key: '自主变道右转中间车道(提示)',
        value: '自主变道右转中间车道(提示)'
    },
    {
        key: '自主变道障碍物(提示)',
        value: '自主变道障碍物(提示)'
    },

    {
        key: '自主变道中间车道(提示)',
        value: '自主变道中间车道(提示)'
    },
    {
        key: '自主变道左转(提示)',
        value: '自主变道左转(提示)'
    },
    {
        key: '自主变道左转中间车道(提示)',
        value: '自主变道左转中间车道(提示)'
    }
]

/**重点项目 */
export const importantList = [{
    key: '向左打方向',
    value: '向左打方向'
},
{
    key: '向右打方向',
    value: '向右打方向'
},
{
    key: '回正方向',
    value: '回正方向'
},
{
    key: '停车',
    value: '停车'
}]

export const pointData = [
    {
        key: 'exam',
        value: '考试项目',
        children: [
            {
                key: 5,
                value: '直线行驶',
                children: [
                    {
                        key: '通用直线',
                        value: '通用直线',
                        time: '14.54'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 15,
                value: '学校区域',
                children: [
                    {
                        key: '通用学校',
                        value: '通用学校',
                        time: '5.54'
                    },
                    {
                        key: '学校车头过牌',
                        value: '学校车头过牌',
                        time: '4.78'
                    },
                    {
                        key: '学校白点',
                        value: '学校白点',
                        time: '7.18'
                    },
                    {
                        key: '学校不超过30',
                        value: '学校不超过30',
                        time: '4.73'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 16,
                value: '人行横道',
                children: [
                    {
                        key: '通用人行横道',
                        value: '通用人行横道',
                        time: '8.17'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 9,
                value: '路口左转',
                children: [
                    {
                        key: '通用左转',
                        value: '通用左转',
                        time: '10.65'
                    },
                    {
                        key: '左转考试中心',
                        value: '左转考试中心',
                        time: '9.9'
                    },
                    {
                        key: '左转停止牌',
                        value: '左转停止牌',
                        time: '15'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 12,
                value: '路口直行',
                children: [
                    {
                        key: '通用直行',
                        value: '通用直行',
                        time: '12.56'
                    },
                    {
                        key: '直行黄灯',
                        value: '直行黄灯',
                        time: '12.04'
                    },
                    {
                        key: '直行停止牌',
                        value: '直行停止牌',
                        time: '10'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 10,
                value: '路口右转',
                children: [
                    {
                        key: '通用右转',
                        value: '通用右转',
                        time: '10.68'
                    },
                    {
                        key: '右转停止牌',
                        value: '右转停止牌',
                        time: '15'
                    },
                    {
                        key: '右转专用道',
                        value: '右转专用道',
                        time: '12.25'
                    },
                    {
                        key: '右转考试中心',
                        value: '右转考试中心',
                        time: '6.9'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 17,
                value: '靠边停车',
                children: [
                    {
                        key: '通用靠边停车',
                        value: '通用靠边停车',
                        time: '19.94'
                    },
                    {
                        key: '中途靠边停车',
                        value: '中途靠边停车',
                        time: '14.37'
                    },
                    {
                        key: '靠边停车安全员',
                        value: '靠边停车安全员',
                        time: '6.74'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 13,
                value: '加减挡',
                children: [
                    {
                        key: '加减挡2-4-2',
                        value: '加减挡2-4-2',
                        time: '10'
                    },
                    {
                        key: '加减挡2-3-2',
                        value: '加减挡2-3-2',
                        time: '9.96'
                    },
                    {
                        key: '加减挡3-4-3',
                        value: '加减挡3-4-3',
                        time: '9.93'
                    },
                    {
                        key: '加减挡1-4-1',
                        value: '加减挡1-4-1',
                        time: '9.75'
                    },
                    {
                        key: '加减挡2-5-2',
                        value: '加减挡2-5-2',
                        time: '9'
                    },
                    {
                        key: '加减挡2-5-3',
                        value: '加减挡2-5-3',
                        time: '9'
                    },
                    {
                        key: '加减挡3-5-3',
                        value: '加减挡3-5-3',
                        time: '9'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 8,
                value: '会车',
                children: [
                    {
                        key: '通用会车',
                        value: '通用会车',
                        time: '3.92'
                    },
                    {
                        key: '会车向右变道',
                        value: '会车向右变道',
                        time: '8.6'
                    },
                    {
                        key: '会车向右打方向',
                        value: '会车向右打方向',
                        time: '8.52'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 14,
                value: '公交车站',
                children: [
                    {
                        key: '公交车(标志牌)',
                        value: '公交车(标志牌)',
                        time: '6.22'
                    },
                    {
                        key: '公交车(标志牌)特殊1',
                        value: '公交车(标志牌)特殊1',
                        time: '5.17'
                    },
                    {
                        key: '公交车(实体车站)',
                        value: '公交车(实体车站)',
                        time: '6.08'
                    },
                    {
                        key: '公交车(实体车站)特殊1',
                        value: '公交车(实体车站)特殊1',
                        time: '5.61'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 11,
                value: '掉头',
                children: [
                    {
                        key: '通用掉头',
                        value: '通用掉头',
                        time: '12.63'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 4,
                value: '车辆起步',
                children: [
                    {
                        key: '通用起步',
                        value: '通用起步',
                        time: '12.05'
                    },
                    {
                        key: '起步(特殊1)',
                        value: '起步(特殊1)',
                        time: '9.4'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 7,
                value: '超车',
                children: [
                    {
                        key: '通用超车',
                        value: '通用超车',
                        time: '16'
                    },
                    {
                        key: '超车无需返回',
                        value: '超车无需返回',
                        time: '10.18'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 6,
                value: '变更车道',
                children: [
                    {
                        key: '通用变道左',
                        value: '通用变道左',
                        time: '8.7'
                    },
                    {
                        key: '通用变道右',
                        value: '通用变道右',
                        time: '8.7'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 23,
                value: '临时停车',
                children: [
                    {
                        key: '通用临时停车',
                        value: '通用临时停车',
                        time: '19.94'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
            {
                key: 24,
                value: '停车让行',
                children: [
                    {
                        key: '停车让行(重庆) ',
                        value: '停车让行(重庆) ',
                        time: '6.9'
                    },
                    {
                        key: '无音频无字幕',
                        value: '无音频无字幕',
                        time: '5'
                    }
                ]
            },
        ]
    },
    {
        key: 'noExam',
        value: '非考试项目',
        children: [
            {
                key: '内部考场',
                value: '内部考场',
                children: [
                    {
                        key: '内部考场',
                        value: '内部考场',
                        time: '8'
                    }
                ]
            },
            {
                key: '左右观察',
                value: '左右观察',
                children: [
                    {
                        key: '丁字路口左右观察',
                        value: '丁字路口左右观察',
                        time: '4.2'
                    }
                ]
            },
            {
                key: '自主变道',
                value: '自主变道',
                children: [
                    {
                        key: '自主变道左',
                        value: '自主变道左',
                        time: '8.49'
                    },
                    {
                        key: '自主变道右',
                        value: '自主变道右',
                        time: '8.64'
                    },
                    {
                        key: '连续变道左',
                        value: '连续变道左',
                        time: '12.3'
                    },
                    {
                        key: '连续变道右',
                        value: '连续变道右',
                        time: '11.88'
                    },
                    // {
                    //     key: '自主变道左转(提示)',
                    //     value: '自主变道左转(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道路到尽头(提示)',
                    //     value: '自主变道路到尽头(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道中间车道(提示)',
                    //     value: '自主变道中间车道(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道靠边停车(提示)',
                    //     value: '自主变道靠边停车(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道右转中间车道(提示)',
                    //     value: '自主变道右转中间车道(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道右转(提示)',
                    //     value: '自主变道右转(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道路口直行(提示)',
                    //     value: '自主变道路口直行(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道慢车道(提示)',
                    //     value: '自主变道慢车道(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道掉头(提示)',
                    //     value: '自主变道掉头(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道左转中间车道(提示)',
                    //     value: '自主变道左转中间车道(提示)',
                    //     time: '9.92'
                    // },
                    // {
                    //     key: '自主变道障碍物(提示)',
                    //     value: '自主变道障碍物(提示)',
                    //     time: '9.92'
                    // }
                ]
            },
            {
                key: '路口组合使用',
                value: '路口组合使用',
                children: [
                    {
                        key: '路口上坡',
                        value: '路口上坡',
                        time: '4.13'
                    }
                ]
            },
            {
                key: '环岛',
                value: '环岛',
                children: [
                    {
                        key: '进入环岛',
                        value: '进入环岛',
                        time: '12.48'
                    },
                    {
                        key: '进入环岛特殊1',
                        value: '进入环岛特殊1',
                        time: '10.72'
                    }
                ]
            },
            {
                key: '辅路',
                value: '辅路',
                children: [
                    {
                        key: '驶入辅路',
                        value: '驶入辅路',
                        time: '5.2'
                    }
                ]
            },
            {
                key: '驶入中间车道',
                value: '驶入中间车道',
                children: [
                    {
                        key: '驶入中间车道',
                        value: '驶入中间车道',
                        time: '1.41'
                    }
                ]
            },
            {
                key: '驶入右侧车道',
                value: '驶入右侧车道',
                children: [
                    {
                        key: '驶入右侧车道',
                        value: '驶入右侧车道',
                        time: '1.28'
                    }
                ]
            },
            {
                key: '驶入左侧车道',
                value: '驶入左侧车道',
                children: [
                    {
                        key: '驶入左侧车道',
                        value: '驶入左侧车道',
                        time: '1.33'
                    }
                ]
            },
            {
                key: '停车让行',
                value: '停车让行',
                children: [
                    {
                        key: '停车让行',
                        value: '停车让行',
                        time: '6.9'
                    }
                ]
            },
            {
                key: '蓝牌',
                value: '蓝牌',
                children: [
                    {
                        key: '直线行驶',
                        value: '直线行驶',
                        time: '5'
                    },
                    {
                        key: '学校区域',
                        value: '学校区域',
                        time: '5'
                    },
                    {
                        key: '人行横道',
                        value: '人行横道',
                        time: '5'
                    },
                    {
                        key: '路口左转',
                        value: '路口左转',
                        time: '5'
                    },
                    {
                        key: '路口直行',
                        value: '路口直行',
                        time: '5'
                    },
                    {
                        key: '路口右转',
                        value: '路口右转',
                        time: '5'
                    },
                    {
                        key: '靠边停车',
                        value: '靠边停车',
                        time: '5'
                    },
                    {
                        key: '加减挡',
                        value: '加减挡',
                        time: '5'
                    },
                    {
                        key: '会车',
                        value: '会车',
                        time: '5'
                    },
                    {
                        key: '公交车站',
                        value: '公交车站',
                        time: '5'
                    },
                    {
                        key: '掉头',
                        value: '掉头',
                        time: '5'
                    },
                    {
                        key: '车辆起步',
                        value: '车辆起步',
                        time: '5'
                    },
                    {
                        key: '超车',
                        value: '超车',
                        time: '5'
                    },
                    {
                        key: '变更车道',
                        value: '变更车道',
                        time: '5'
                    },
                    {
                        key: '临时停车',
                        value: '临时停车',
                        time: '5'
                    },
                    {
                        key: '停车让行',
                        value: '停车让行',
                        time: '5'
                    }
                ]
            },
            {
                key: '本地教练提醒',
                value: '本地教练提醒',
                children: [
                    {
                        key: '提醒（一行文字）',
                        value: '提醒（一行文字）',
                        time: '5'
                    },
                    {
                        key: '提醒（两行文字）',
                        value: '提醒（两行文字）',
                        time: '8'
                    },
                    {
                        key: '提醒（三行文字）',
                        value: '提醒（三行文字）',
                        time: '10'
                    }
                ]
            },
            {
                key: '重难点提醒',
                value: '重难点提醒',
                children: [
                    {
                        key: '通用提示',
                        value: '通用提示',
                        time: '10'
                    }
                ]
            },
            {
                key: '踩刹车教学',
                value: '踩刹车教学',
                children: [
                    {
                        key: '停止线和刹车',
                        value: '停止线和刹车',
                        time: '2'
                    },
                    {
                        key: '停止线',
                        value: '停止线',
                        time: '2'
                    }
                ]
            },
            {
                key: '左转提醒',
                value: '左转提醒',
                children: [
                    {
                        key: '左转箭头5秒',
                        value: '左转箭头5秒',
                        time: '5'
                    },
                    {
                        key: '左转箭头20秒',
                        value: '左转箭头20秒',
                        time: '20'
                    }
                ]
            },
            {
                key: '右转提醒',
                value: '右转提醒',
                children: [
                    {
                        key: '右转箭头5秒',
                        value: '右转箭头5秒',
                        time: '5'
                    },
                    {
                        key: '右转箭头20秒',
                        value: '右转箭头20秒',
                        time: '20'
                    }
                ]
            },
            {
                key: '加减挡提醒',
                value: '加减挡提醒',
                children: [
                    {
                        key: '加减挡计时3秒',
                        value: '加减挡计时3秒',
                        time: '3'
                    },
                    {
                        key: '加减挡计时5秒',
                        value: '加减挡计时5秒',
                        time: '5'
                    }
                ]
            }
        ]
    }
]

export const changeDirection = [
    {
        key: 'left',
        value: '左'
    },
    {
        key: 'right',
        value: '右'
    }
]

export const blueSingData = find(find(pointData, {key: 'noExam'}).children, {key: '蓝牌'}).children

export const examItemList = find(pointData, {key: 'exam'}).children.map(item => {
    const {key, value} = item
    return {
        key,
        value
    }
})

export const useHintnoExamList = ['内部考场', '左右观察', '自主变道', '路口组合使用', '环岛', '辅路', '驶入中间车道', '驶入右侧车道', '驶入左侧车道', '停车让行']