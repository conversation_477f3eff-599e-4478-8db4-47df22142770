import { createApp } from "vue";
import MyPage from './components/MyPage/index.vue';
import { createPinia } from 'pinia'
import 'ant-design-vue/dist/antd.css';
import Antd from 'ant-design-vue';

export function list(panel,lineData,pageParamsType) {
    console.log(panel,2151244444444445);
    
    const app = createApp(MyPage, {lineData,pageParamsType});

    app.use(createPinia());

  
    app.mount(panel[0]);
    app.use(Antd);

    const t = $('#article .tab li.active span')[0]
    t.addEventListener('click', () => {
        console.log('film-video close')
        app.unmount()
    })

}