import { defineStore } from 'pinia'
import Store from 'simple!core/store';
import Widgets from 'simple!core/widgets';
import { useHintnoExamList, pointData } from '../data.js';

export const formatNumber = (n) => {
    const s = n.toString();
    return s[1] ? s : '0' + s;
};

export function findValueByKeys(data, keys) {
    let result = data.find(item => item.key === keys[0])
    if (result && keys.length > 1) {
        return findValueByKeys(result.children, keys.slice(1))
    }
    return result
}

export function findPointDataByKeys(...keys) {
    return findValueByKeys(pointData, keys)  
}

export function toChineseNum(num) {
    let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    let unit = ['', '十', '百', '千', '万']
    num = parseInt(num)
    let getWan = temp => {
        let strArr = temp.toString().split('').reverse()
        let newNum = ''
        let newArr = []
        strArr.forEach((item, index) => {
            newArr.unshift(item === '0' ? changeNum[item] : changeNum[item] + unit[index])
        })
        let numArr = []
        newArr.forEach((m, n) => {
            if (m !== '零') numArr.push(n)
        })
        if (newArr.length > 1) {
            newArr.forEach((m, n) => {
                if (newArr[newArr.length - 1] === '零') {
                    if (n <= numArr[numArr.length - 1]) {
                        newNum += m
                    }
                } else {
                    newNum += m
                }
            })
        } else {
            newNum = newArr[0]
        }
        return newNum
    }
    let overWan = Math.floor(num / 10000)
    let noWan = String(num % 10000)
    if (noWan.toString().length < 4) {
        noWan = '0' + noWan
    }
    return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
}

export function showClock(time) {
    if (isNaN(time) || time < 0) {
        return ''
    }
    var mm = formatNumber(Math.floor(time / 60));
    var ss = formatNumber(Math.floor(time % 60));

    return mm + ":" + ss;
}

/** 生成UUID */
export function getUUID() {
    const S4 = function () {
        // eslint-disable-next-line no-bitwise
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    };

    return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4());
}

export const useCommonStore = defineStore('common', {
    state: () => {
        return {
            data: [{
                beginTime: 0,
                endTime: 0,
                itemId: '',
                itemName: '',
                templateName: '',
                type: '',
                hint: '',
                turningDirection: '',
                extraInfo: {
                    content: '',
                },
                duration: '',
            }],
            // index: 0,
            selectUUid: '',
            lineData: {},
            timelineVideoCheck: false
        }
    },
    actions: {
        getVideDetail(data) {
            console.log(data, 'data');
            if (!(data && data.length > 0)) {
                data = [{ "beginTime": 0, "endTime": 0, "itemId": 0, "itemName": "", "templateName": "", "type": "", "uuid": getUUID() }];
            }

            let hintData = [];
            let notHintData = [];
            data.forEach(item => {
                if (item?.templateName?.includes('(提示)') || item?.templateName?.includes('考试第')) {
                    hintData.push(item)
                } else {
                    notHintData.push(item)
                }
            })

            notHintData.forEach(item => {
                hintData.forEach(el => {
                    if (item.itemName == el.itemName && item.beginTime == el.beginTime && item.endTime == el.endTime) {
                        item.hint = el.templateName
                    }
                })
                item.uuid = getUUID();
                if (!item.itemId) {
                    item.itemId = item.itemName
                }
                item.extraInfo = item.extraInfo || {content: ''}
                item.duration = findPointDataByKeys(item.type, item.itemId, item.templateName)?.time || ''
            })

            console.log('notHintData', notHintData);
            this.data = notHintData;
            this.selectUUid = notHintData[0].uuid;
        },
        resetBlueSignNum() {
            let bIndex = 1
            this.data.forEach(item => {
                if (item.type === 'noExam' && item.itemId === '蓝牌') {
                    console.log(item)
                    item.hint = `考试第${toChineseNum(bIndex++)}项`
                }
            })
        },
        fillDate() {
            let sequenceData = [];
            let notSequenceData = [];
            this.data.forEach(item => {
                if (item.type === 'exam' || useHintnoExamList.indexOf(item.itemId) !== -1) {
                    sequenceData.push(item)
                } else {
                    notSequenceData.push(item)
                }
            })
            // 考试项目和部分非考试项目，将这个集合的项目的endTime设置为下一个beginTime-1
            sequenceData.forEach((item, index) => {
                item.endTime = Number(sequenceData[index + 1]?.beginTime) - 1
            })
            notSequenceData.forEach(item => {
                if (item.beginTime >= 0) {
                    const t = findPointDataByKeys(item.type, item.itemId, item.templateName)
                    if (t?.time) {
                        item.endTime = Number(item.beginTime) + Number(t.time)
                    }

                }
            })
        },
        save(fieldName) {
            this.fillDate()
            this.resetBlueSignNum()
            let data = JSON.parse(JSON.stringify(this.data));
            let haveHintData = JSON.parse(JSON.stringify(data));
            haveHintData = haveHintData.filter(item => item?.hint)

            console.log('data', data);
            console.log('haveHintData', haveHintData);
            
            haveHintData.forEach(item => {
                item.templateName = item.hint;
            })

            const retData = [...data, ...haveHintData]
            retData.forEach(el => {
                if (el.type == 'noExam') {
                    el.itemId = '';
                }
                if (fieldName === 'timelinePractice') {
                    delete el.templateName;
                }
                delete el.hint;
                delete el.uuid;
                delete el.flag;
                delete el.duration;
            })
            const title = fieldName === 'timelinePractice' ? `视频ID：${this.lineData.id}  视频制作策略保存成功` : `视频ID：${this.lineData.id}  时间轴数据已保存成功。关键点管理服务。`
            Store([`jiakao-misc!route-video-process/data/update`]).save([{
                params: {
                    id: this.lineData.id,
                    timelineVideoCheck: this.timelineVideoCheck,
                    [fieldName]: JSON.stringify(retData)
                }
            }]).done(function () {
                Widgets.dialog.alert(title);
            }).fail(function (ret) {
                Widgets.dialog.alert(ret.message);
            })
        },
        insert(index) {
            const info = {
                beginTime: 0,
                endTime: 0,
                itemId: '',
                itemName: '',
                templateName: '',
                type: '',
                hint: '',
                extraInfo: {
                    content: '',
                },
                duration: '',
                uuid: getUUID()
            };
            this.data.splice(index, 0, info);
        },
        insertBlueSign(index, data = {}) {
            const info = {
                beginTime: 0,
                endTime: 0,
                itemId: '',
                itemName: '',
                templateName: '',
                type: '',
                hint: '',
                extraInfo: {
                    content: '',
                },
                duration: '',
                uuid: getUUID(),
                ...data
            };
            this.data.splice(index, 0, info);
        },
        deleteItem(uuid) {
            if (this.data.length == 1) {
                Widgets.dialog.alert('仅剩最后一个，不可以删除！');
                return
            }

            if (uuid == this.selectUUid) {
                Widgets.dialog.alert('正在编辑本条数据，不能删除！');
                return
            }

            const delIndex = this.data.findIndex((item) => item.uuid === uuid);



            this.data.splice(delIndex, 1);
            if (this.index >= delIndex) this.index -= 1;
            console.log(this.data, '111');
        }
    }
})