/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'jiakao-misc!app/common/tools'], function (Template, Table, Utils, Widgets, Store, Form, Tools) {

    const lessonTypeMap = {
        'jiakaoyi_coach': '机器人',
        'moniqi': '模拟器'
    }

    const onlineStatusMap = {
        0: '未上线',
        1: '测试上线',
        2: '上线'
    }

    var addEdit = function (table, lineData = {}) {

        var isEdit = !!lineData.id;

        if (isEdit) {
            lineData.coverImg = lineData.coverImgUrl;
            lineData.examBtnImg = lineData.examBtnImgUrl;
        }

        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 600,
            store: 'jiakao-misc!first-lesson-video/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close()
                table.render()
            },
            form: {
                submitHandler: function (form) {

                    const coverImg = JSON.parse(form.coverImg.value || '[]');
                    const examBtnImg = JSON.parse(form.examBtnImg.value || '[]');


                    const params = {
                        coverImgData: coverImg[0]?.encode,
                        examBtnImgData: examBtnImg[0]?.encode
                    }

                    return params
                },
            },
            renderAfter: function (table, $form) {
                if (isEdit) {
                    setTimeout(() => {
                        $form.item('coverImg-group').find("input[name=coverImg]").val(JSON.stringify([{ encode: lineData.coverImgData }]))
                        $form.item('examBtnImg-group').find("input[name=examBtnImg]").val(JSON.stringify([{ encode: lineData.examBtnImgData }]))
                    }, 300)
                }
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden',
                },
                {
                    header: '课程类型：',
                    dataIndex: 'lessonType',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择车型'
                        },
                        ...Tools.getArrayFromMap(lessonTypeMap)
                    ],
                    placeholder: '请选择课程类型'
                },
                {
                    header: '标题：',
                    dataIndex: 'lessonTitle',
                    xtype: 'text',
                    placeholder: '标题'
                },
                {
                    header: '封面图：',
                    dataIndex: 'coverImg',
                    xtype: Simple.Plugin('simple!select-images', {
                        useSecureUpload: true,
                        // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                        appSpaceId: '02fb648802fb886a5a5c',

                        imageSrc: {
                            // 必填。
                            dataIndex: 'encode',
                        },
                        parse: function (value) {
                            return [
                                {
                                    encode: value
                                }
                            ]
                        }
                    }),
                },
                {
                    header: '视频id：',
                    dataIndex: 'videoId',
                    xtype: 'text',
                    placeholder: '视频id'
                },
                {
                    header: '排序:',
                    dataIndex: 'priority',
                    xtype: 'text',
                    placeholder: '排序'
                },
                {
                    header: '允许视频操作',
                    dataIndex: 'videoOperation',
                    xtype: 'select',
                    store: [
                        {
                            key: 'true',
                            value: '是'
                        },
                        {
                            key: 'false',
                            value: '否'
                        }
                    ],
                },
                {
                    header: '上线状态',
                    dataIndex: 'onlineStatus',
                    xtype: 'select',
                    store: [
                        ...Tools.getArrayFromMap(onlineStatusMap)
                    ],
                },
                {
                    header: '禁止操作驾校id',
                    dataIndex: 'disableOperationJiaxiao',
                    xtype: 'text'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    placeholder: '描述'
                },
                {
                    header: '图文详细内容：',
                    dataIndex: 'detailContent',
                    xtype: 'richtext',
                    placeholder: '图文详细内容'
                },
                {
                    header: '考试按钮图：',
                    dataIndex: 'examBtnImg',
                    xtype: Simple.Plugin('simple!select-images', {
                        useSecureUpload: true,
                        // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                        appSpaceId: '02fb648802fb886a5a5c',

                        imageSrc: {
                            // 必填。
                            dataIndex: 'encode',
                        },
                        parse: function (value) {
                            return [
                                {
                                    encode: value
                                }
                            ]
                        }
                    }),
                    placeholder: '考试按钮图'
                },
                {
                    header: '考试按钮跳转地址：',
                    dataIndex: 'examBtnUrl',
                    xtype: 'text',
                    placeholder: '考试按钮跳转地址'
                }

            ],
        }

        if (isEdit) {
            Table().edit(lineData, config)
        } else {
            Table(config).add()
        }

    }

    var list = function (panel) {
        Table({
            description: '第一课视频列表',
            title: '第一课视频列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    click: function (table, $tr, lineData) {
                        addEdit(table, lineData);
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!first-lesson-video/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '3机器人,4模拟器',
                    dataIndex: 'lessonType',
                    render: function (data) {
                        return lessonTypeMap[data]
                    }
                },
                {
                    header: '标题',
                    dataIndex: 'lessonTitle'
                },
                {
                    header: '封面图',
                    dataIndex: 'coverImg',
                    render: function (data, tableData, lineData) {
                        return lineData.coverImgUrl && '<img style="width: 100px;height:100px" src=' + lineData.coverImgUrl + ' />'
                    }
                },
                {
                    header: '视频id',
                    dataIndex: 'videoId',
                },
                {
                    header: '排序',
                    dataIndex: 'priority',
                    width: 60
                },
                {
                    header: '允许视频操作',
                    dataIndex: 'videoOperation',
                    render: function (data, tableData, lineData) {
                        return data ? '是' : '否'
                    }
                },
                {
                    header: '上线状态',
                    dataIndex: 'onlineStatus',
                    render: function (value, tableData, lineData) {
                        return onlineStatusMap[value];
                    }
                },
                {
                    header: '禁止操作驾校id',
                    dataIndex: 'disableOperationJiaxiao',
                },
                {
                    header: '描述',
                    dataIndex: 'desc',
                    render: function (data, tableData, lineData) {
                        return `<div style="width: 200px;word-break: break-all">${data}</div>`
                    },
                },
                {
                    header: '图文详细内容',
                    dataIndex: 'detailContent',
                    render: function (data) {
                        return '<a>查看详情</a>'
                    },
                    click: function (table, $tr, lineData) {
                        Widgets.dialog.html('查看').then(dialog => {
                            $(dialog.body).append($(lineData.detailContent));
                        })
                    }
                },
                {
                    header: '考试按钮图',
                    dataIndex: 'examBtnImg',
                    render: function (data, tableData, lineData) {
                        return lineData.examBtnImgUrl && '<img style="width: 100px;height:100px;" src=' + lineData.examBtnImgUrl + ' />'
                    },
                },
                {
                    header: '考试按钮跳转地址',
                    dataIndex: 'examBtnUrl',
                    render: function (data, tableData, lineData) {
                        return `<div style="width: 100px;word-break: break-all">${data}</div>`
                    },
                },

                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }
            ]
        }, ['jiakao-misc!first-lesson-video/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});