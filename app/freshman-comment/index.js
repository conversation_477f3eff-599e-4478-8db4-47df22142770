/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    var statusMap = {
        0: '待审核',
        1: '审核通过',
        2: '审核未通过'
    }
    var statusStore = [{
        key: '',
        value: '审核状态'
    },{
        key: 0,
        value: '待审核'
    }, {
        key: 1,
        value: '审核通过'
    }, {
        key: 2,
        value: '审核未通过'
    }]

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!freshman-comment/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '评论内容：',
                    dataIndex: 'content',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '评论内容'
                },
                {
                    header: '用户id：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '用户id'
                },
                {
                    header: '审核状态：',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: statusStore

                },
            

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '评论表列表',
            title: '评论表列表',
            search:[{
                dataIndex:"status",
                xtype:'select',
                store:statusStore
            }],
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!freshman-comment/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '评论内容：',
                            dataIndex: 'content'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '审核状态：',
                            dataIndex: 'status'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!freshman-comment/data/view',
                        save: 'jiakao-misc!freshman-comment/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '评论内容：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '评论内容'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '用户id'
                        },
                        {
                            header: '审核状态：',
                            dataIndex: 'status',
                            xtype: 'select',
                            store: statusStore
                        },
             

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!freshman-comment/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '评论内容',
                    dataIndex: 'content'
                },
                {
                    header: '用户id',
                    dataIndex: 'userId'
                },
                {
                    header: '用户名称',
                    dataIndex: 'name'
                },
                {
                    header: '审核状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: 'createUserName',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: 'updateUserName',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!freshman-comment/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});