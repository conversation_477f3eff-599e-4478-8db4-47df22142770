/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!freshman-idol/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '姓名：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '姓名'
                },
                {
                    header: '昵称：',
                    dataIndex: 'nickName',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '昵称'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '描述'
                },
                {
                    header: '名字首字母：',
                    dataIndex: 'firstName',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '名字首字母'
                },
                {
                    header: '头像：',
                    dataIndex: 'avatar',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'avatar',
                        uploadIndex: 'avatar',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
              

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'freshman-idol列表',
            title: 'freshman-idol列表',

            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!freshman-idol/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '姓名：',
                            dataIndex: 'name'
                        },
                        {
                            header: '昵称：',
                            dataIndex: 'nickName'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '名字首字母：',
                            dataIndex: 'firstName'
                        },
                        {
                            header: '头像：',
                            dataIndex: 'avatar'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!freshman-idol/data/view',
                        save: 'jiakao-misc!freshman-idol/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '姓名：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '姓名'
                        },
                        {
                            header: '昵称：',
                            dataIndex: 'nickName',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '昵称'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '描述'
                        },
                        {
                            header: '名字首字母：',
                            dataIndex: 'firstName',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '名字首字母'
                        },
                        {
                            header: '头像：',
                            dataIndex: 'avatar',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'avatar',
                                uploadIndex: 'avatar',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                      
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!freshman-idol/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '姓名',
                    dataIndex: 'name'
                },
                {
                    header: '昵称',
                    dataIndex: 'nickName'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '名字首字母',
                    dataIndex: 'firstName'
                },
                {
                    header: '头像',
                    dataIndex: 'avatar',
                    render: function (data) {
                        if (data) {
                            return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('头像', {}).done(function (dialog) {
                            $(dialog.body).append('<img src="' + lineData.avatar + '" />')
                        })
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: 'createUserName',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: 'updateUserName',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!freshman-idol/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});