/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    var typeStore = [{
        key: 3,
        value: '总榜'
    }, {
        key: 0,
        value: '日榜'
    }]
    var typeMap = {
        3: '总榜',
        0: '日榜'
    }
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!freshman-rank/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: 'idolId：',
                    dataIndex: 'idolId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: 'idolId'
                },
                {
                    header: '榜单类型：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: typeStore
                },
                {
                    header: 'value：',
                    dataIndex: 'value',
                    xtype: 'text',
                    check: 'required',
                    placeholder: 'value'
                },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '明星排行榜列表',
            title: '明星排行榜列表',

            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!freshman-rank/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: 'idolId：',
                            dataIndex: 'idolId'
                        },
                        {
                            header: '榜单类型，0：总榜，1：每天的榜：',
                            dataIndex: 'type'
                        },
                        {
                            header: 'value：',
                            dataIndex: 'value'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!freshman-rank/data/view',
                        save: 'jiakao-misc!freshman-rank/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: 'idolId：',
                            dataIndex: 'idolId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: 'idolId'
                        },
                        {
                            header: '榜单类型：',
                            dataIndex: 'type',
                            xtype: 'select',
                            store: typeStore
                        },
                        {
                            header: 'value：',
                            dataIndex: 'value',
                            xtype: 'text',
                            check: 'required',
                            placeholder: 'value'
                        },


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!freshman-rank/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '偶像id',
                    dataIndex: 'idolId'
                }, {
                    header: '偶像名称',
                    dataIndex: 'idolName'
                },
                {
                    header: '榜单类型',
                    dataIndex: 'type',
                    render: function (data) {
                        return typeMap[data]
                    }
                },
                {
                    header: 'value',
                    dataIndex: 'value'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: 'createUserName',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: 'updateUserName',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!freshman-rank/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});