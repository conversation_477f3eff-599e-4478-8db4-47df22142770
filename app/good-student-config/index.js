/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tiku',
    'simple!core/plugin',
    'jiakao-misc!plugin/select-district/district3',
    'jiakao-misc!plugin/select-district/district2',
    'jiakao-misc!app/common/tools'

], function (Template, Table, Utils, Widgets, Store, Form,Constants,TIKU,Plugin,District3,District2,Tools) {
    var zxCitys = ['120000', '110000', '500000', '310000']

    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    var kemuStore = [
        {
            key: '',
            value: '请选择科目'
        },
        {
            key: 'kemu1',
            value: '科目一'
        },
        {
            key:'kemu2',
            value: '科目二'
        },
        {
            key: 'kemu3',
            value: '科目三'
        },
        {
            key: 'kemu4',
            value: '科目四'
        }
    ]
    var kemuMap = Tools.getMapfromArray(kemuStore);

    
    var getAllCity = function () {
        var list = District3.list;
        var city = [];

        for (var i = 0; i < list.length; ++i) {
            if (list[i].cities && list[i].code != zxCitys[0] && list[i].code != zxCitys[1] && list[i].code != zxCitys[2] && list[i].code != zxCitys[3]) {
                var data = list[i].cities;
                city.push({
                    key: list[i].code,
                    value: list[i].name,
                    search: list[i].name
                })
                for (var j = 0; j < data.length; ++j) {
                    city.push({
                        key: data[j].code,
                        value: data[j].name,
                        search: data[j].name
                    })
                }

            }

        }

        city.unshift({
            key: "310000",
            value: "上海",
            search: '上海'
        })
        city.unshift({
            key: "120000",
            value: "天津",
            search: '天津'
        })
        city.unshift({
            key: "500000",
            value: "重庆",
            search: '重庆'
        })
        city.unshift({
            key: "110000",
            value: "北京",
            search: '北京'
        })
        city.unshift({
            key: "000000",
            value: "全国",
            search: '全国'
        })
        return city;

    }
    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!good-student-config/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    const carTypeGroup =  $('input[name="carTypeTarget"]')
                    console.log(carTypeGroup,'125');
                    var aData = $(form).find('#aData').val();
                    var bData = $(form).find('#bData').val();

                    var cData = $(form).find('#cData').val();
                    var dData = $(form).find('#dData').val();


                    var data = [
                        {
                            name: 'a',
                            value:aData
                        },
                        {
                            name: 'b',
                            value:bData
                        }, {
                            name: 'c',
                            value:cData
                        }, {
                            name: 'd',
                            value:dData
                        },
                    ];
                    
                    data =  data.filter(item => item.value)



                    const carTypeArr = [];
                    for (let i = 0; i < carTypeGroup.length; i++){
                        const item = carTypeGroup[i];
                        if (item.checked) {
                            carTypeArr.push(item.value)
                        }
                    }

                    const carType = carTypeArr + '';

                    return {
                        abTestRequest:JSON.stringify(data),
                        carType
                    }
                }
            },
            renderAfter: function (table, dom) { 
                $(dom).item('aData-group').hide();
                $(dom).item('bData-group').hide();
                $(dom).item('cData-group').hide();
                $(dom).item('dData-group').hide();


                dom.item('abTestKey').on('change', function () {
                    let value = $(this).val();
                    let id = value.split('-').at(-1)
                    Store([`jiakao-misc!good-student-config/data/viewAbTest?id=${id}`]).load().done(function (data) {
                        let rateDistribution = JSON.parse(data.data['good-student-config'].data.viewAbTest.data.rateDistribution)
                        console.log(rateDistribution,'rateDistribution');
                        rateDistribution.forEach(item => {
                            $(dom).item(`${item.name}Data-group`).show();
                        })
                    })

                })
               
                // dom.item('abTestKey').on('change', function () {
                //    const value =  $(this).val();
                //    
                // })
            },
            columns: [
          
            {
                header: '车型:',
                dataIndex: 'carTypeTarget',
                xtype: 'checkbox',
                store: [].concat(carTypeArr)
            },
           
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: kemuStore,
            },
             {
                 header: '名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 100,
                 placeholder: '名称'
             },
             {
                header: 'ab/test_key：',
                dataIndex: 'abTestKey',
                xtype: 'select',
                store: "jiakao-misc!good-student-config/data/abTestList?testKey=vip-excellent-course",
                index: {
                    key: 'testKey',
                    value: 'testKey',
                    search: 'testKey'
                 },
                 insert: [
                    {
                        key: '',
                        value: '请选择'
                    }],
                 
                placeholder: 'ab/test_key'
                },
            
                {
                    header: 'a数据：',
                    dataIndex: 'aData',
                    xtype: 'textarea',
                    placeholder: 'a数据'
                },
                {
                    header: 'b数据：',
                    dataIndex: 'bData',
                    xtype: 'textarea',
                    placeholder: 'b数据'
                },
                {
                    header: 'c数据：',
                    dataIndex: 'cData',
                    xtype: 'textarea',
                    placeholder: 'c数据'
                },
                {
                    header: 'd数据：',
                    dataIndex: 'dData',
                    xtype: 'textarea',
                    placeholder: 'd数据'
                },
                {
                    header: '开始版本：',
                    dataIndex: 'fromVersion',
                    xtype: 'text',
                    placeholder: '开始版本'
                },
                {
                    header: '结束版本：',
                    dataIndex: 'toVersion',
                    xtype: 'text',
                    placeholder: '结束版本'
                },
                {
                    header: '平台:',
                    dataIndex: 'platform',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择平台'
                    },

                    {
                        key: 'android',
                        value: 'Android'
                    },
                    {
                        key: 'iphone',
                        value: 'Iphone'
                    }]
                },
                {
                    header: '过滤城市code:',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: getAllCity(),

                        dataIndex: 'cityCode',
                        // index: {
                        //
                        //     key: 'code',
                        //     value: 'name',
                        //     search: 'name'
                        // },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                },
                {
                    header: '城市匹配模式:',
                    dataIndex: 'cityType',
                    xtype: 'select',
                    store: [
                        {
                            key: 'gps',
                            value: 'GPS匹配'
                        },
                        {
                            key: 'obscure',
                            value: '模糊匹配'
                        }
                    ]
                },

                {
                    header: '过滤黑名单',
                    dataIndex: 'blackList',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '全部可见'
                    },
                    {
                        key: 'true',
                        value: '黑名单可见'
                    },
                    {
                        key: 'false',
                        value: '白名单可见'
                    },
                    ]
                },
            ]
        }).add();
    }

    let aId, bId;
    var list = function(panel) {
        Table({
            description: '优秀学员配置列表',
            title: '优秀学员配置列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!good-student-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '远程配置ids：',
                         dataIndex: 'remoteConfigIds'
                     },
                     {
                        header: '远程配置key',
                        dataIndex: 'key'
                    },
                    {
                        header: '名称',
                        dataIndex: 'name'
                    },
                     {
                        header: '车型',
                        dataIndex: 'carType',
                        render: function (data) {
                            let text = [];
                            data = data.split(',');
                            data.forEach(item => {
                                text.push(TIKU[item]);
                            })
                            return  text + ''
                        }    
                     },
                     {
                         header: 'ab/test_key：',
                         dataIndex: 'abTestKey'
                     },
                     {
                         header: 'ab/test id：',
                         dataIndex: 'abTestId'
                     },
                     {
                         header: '科目：',
                         dataIndex: 'kemu'
                     },
                    
                     {
                         header: '状态：',
                         dataIndex: 'configStatus'
                     },
                     {
                         header: '创建人id：',
                         dataIndex: 'createUserId'
                     },
                    
                     {
                         header: '修改人id：',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: '修改人名称：',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '修改时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form, fun) {
                            const carTypeGroup =  $('input[name="carTypeTarget"]')
                            console.log(carTypeGroup,'125');
                            var aData = $(form).find('#aData').val();
                            var bData = $(form).find('#bData').val();
        
                            const carTypeArr = [];
                            for (let i = 0; i < carTypeGroup.length; i++){
                                const item = carTypeGroup[i];
                                if (item.checked) {
                                    carTypeArr.push(item.value)
                                }
                            }
                            
                            const carType = carTypeArr + '';
                            
                            const configStatus = $('#configStatus').val();

                            return {
                                abTestRequest: JSON.stringify([{
                                    id:aId,
                                    name: 'a',
                                    value:aData
                                }, {
                                    id:bId,
                                    name: 'b',
                                    value:bData
                                }]),
                                carType,
                                status:configStatus
                            }
                        }
                    },
                    renderAfter: function (tab, dom, data) {
                        console.log(data,'125125');
                        const carType = data.data.carType;
                        const abTestAdminResponses = data.data.abTestAdminResponses;


                        setTimeout(function () {
                            const carTypeGroup = $('input[name="carTypeTarget"]')
                            for (let i = 0; i < carTypeGroup.length; i++){
                                const item = carTypeGroup[i];
                                console.log(item.value,'item.value');
                                if (carType.includes(item.value)) {
                                    item.checked = true;
                                }
                            }
                            aId = abTestAdminResponses[0].id
                            bId = abTestAdminResponses[1].id
                            dom.item('aData').val(abTestAdminResponses[0].value);
                            dom.item('bData').val(abTestAdminResponses[1].value);
                            
                      },200)
                    },
                    store: {
                        load: 'jiakao-misc!good-student-config/data/view',
                        save: 'jiakao-misc!good-student-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '车型:',
                            dataIndex: 'carTypeTarget',
                            xtype: 'checkbox',
                            store: [].concat(carTypeArr)
                        },
                       
                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: kemuStore,
                        },
                         {
                             header: '名称：',
                             dataIndex: 'name',
                             xtype: 'text',
                             maxlength: 100,
                             placeholder: '名称'
                        },
                       
                         {
                            header: 'ab/test_key：',
                            dataIndex: 'abTestKey',
                            xtype: 'text',
                            maxlength: 16,
                            value: 'vip-excellent-course',
                            disabled:true,
                            placeholder: 'ab/test_key'
                            },
                            {
                                header: '状态：',
                                dataIndex: 'configStatus',
                                disabled:true,
                                xtype: 'text',
                                placeholder: '状态'
                        },
                               
                            {
                                header: 'a数据：',
                                dataIndex: 'aData',
                                xtype: 'textarea',
                                placeholder: 'a数据'
                            },
                            {
                                header: 'b数据：',
                                dataIndex: 'bData',
                                xtype: 'textarea',
                                placeholder: 'b数据'
                        },
                        {
                            header: '开始版本：',
                            dataIndex: 'fromVersion',
                            xtype: 'text',
                            placeholder: '开始版本'
                        },
                        {
                            header: '结束版本：',
                            dataIndex: 'toVersion',
                            xtype: 'text',
                            placeholder: '结束版本'
                        },
                            {
                                header: '平台:',
                                dataIndex: 'platform',
                                xtype: 'select',
                                store: [{
                                    key: '',
                                    value: '选择平台'
                                },
            
                                {
                                    key: 'android',
                                    value: 'Android'
                                },
                                {
                                    key: 'iphone',
                                    value: 'Iphone'
                                }]
                        },
                        {
                            header: '过滤城市code:',
                            dataIndex: 'cityCode',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: getAllCity(),
        
                                dataIndex: 'cityCode',
                                // index: {
                                //
                                //     key: 'code',
                                //     value: 'name',
                                //     search: 'name'
                                // },
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            }),
                        },
                        {
                            header: '城市匹配模式:',
                            dataIndex: 'cityType',
                            xtype: 'select',
                            store: [
                                {
                                    key: 'gps',
                                    value: 'GPS匹配'
                                },
                                {
                                    key: 'obscure',
                                    value: '模糊匹配'
                                }
                            ]
                        },
        
                        {
                            header: '过滤黑名单',
                            dataIndex: 'blackList',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '全部可见'
                            },
                            {
                                key: 'true',
                                value: '黑名单可见'
                            },
                            {
                                key: 'false',
                                value: '白名单可见'
                            },
                            ]
                        },

                    ]
                },
                {
                    name: '下线',
                    class: 'danger',
                    render: function (name, arr, i) {
                        console.log(arr[i].status,name, arr, i,'arr[i].status');
                        return arr[i].configStatus  == 2 ? '下线' : '发布';
                    },
                    click: function (table, row, lineData) {
                        const status = lineData.configStatus ? 0 : 2;
                        const text = lineData.configStatus ? '下线' : '发布'
                        Widgets.dialog.confirm(`确定${text}吗？`, function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!good-student-config/data/updateStatus?status=' + status]).save([{
                                    params: {
                                        id: lineData.id,
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!good-student-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     
                     {
                         header: '远程配置key',
                         dataIndex: 'key'
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                     {
                        header: '车型',
                        dataIndex: 'carType',
                        render: function (data) {
                            let text = [];
                            data = data.split(',');
                            data.forEach(item => {
                                text.push(TIKU[item]);
                            })
                            return  text + ''
                        }
                     },
                     {
                         header: 'ab/test_key',
                         dataIndex: 'abTestKey'
                },
                {
                    header: '配置内容',
                    dataIndex: 'abTestAdminResponses',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log(Widgets.dialog.html, '125');
                        Widgets.dialog.html('配置内容', { width: 1200 }).done(function (dialog) {
                            var data = lineData.abTestAdminResponses;
                            
                            var html = ''
                            data.forEach(item => {
                                html += `
                                        <div>${'方案' + item.name}:${item.value}</div>
                                `
                            })
                         
                            $(dialog.body).html(html)
                        })
                    }
                },
              
                {
                    header: '平台',
                    dataIndex: 'platform',
                },
             
                {
                    header: '过滤的城市名称',
                    dataIndex: 'cityCode',
                    width: '160',
                    render: function (data) {
                        var str = '';
                        if (data) {
                            var data = data?.split(',')
                            for (var i = 0; i < data.length; ++i) {
                                str += '<span style="background-color: #8cc5ff;margin-left:5px;border-radius: 3px;">' + District2.getNameByCode(data[i]) + '</span>'
                            }
                            return str;
                        } else {
                            return '';
                        }
                    }
                },
                {
                    header: '城市匹配模式',
                    dataIndex: 'cityType',
                    render: function (v) {
                        switch (v) {
                            case 'gps':
                                return 'GPS匹配';
                            case 'obscure':
                                return '模糊匹配'
                        }
                    }
                },
                {
                    header: '开始版本',
                    dataIndex: 'fromVersion',


                },
                {
                    header: '结束版本',
                    dataIndex: 'toVersion',

                },
                {
                    header: '过滤黑名单',
                    dataIndex: 'blackList',
                    render: function (data) {
                        if (data == null) {
                            return '全部可见'
                        } else {
                            return data ? '黑名单可见' : '白名单可见'
                        }
                    }
                },
                     {
                         header: '科目',
                         dataIndex: 'kemu'
                     },
                     {
                         header: '状态',
                         dataIndex: 'configStatus',
                         render: function (data) {
                             return data == 0 ? '未发布' : data == 2 ? '已发布' : ''
                         }
                     },
                     {
                         header: '修改人名称',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '修改时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     }

            ]
        }, ['jiakao-misc!good-student-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});