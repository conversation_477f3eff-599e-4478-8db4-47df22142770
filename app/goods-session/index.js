/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!goods-session/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '名称'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '描述'
                },
                {
                    header: '唯一码：',
                    dataIndex: 'uniqKey',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '唯一码'
                },
                {
                    header: '价格，单位分：',
                    dataIndex: 'price',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '价格，单位分'
                },
                {
                    header: '原始价格：',
                    dataIndex: 'originalPrice',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '原始价格'
                },
                {
                    header: '苹果的价格，单位分：',
                    dataIndex: 'applePrice',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '苹果的价格，单位分'
                },
                {
                    header: '苹果的价格id：',
                    dataIndex: 'applePriceId',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '苹果的价格id'
                },
                {
                    header: '商品的id，逗号分隔：',
                    dataIndex: 'goodsId',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '商品的id，逗号分隔'
                },
                {
                    header: '业务类型：',
                    dataIndex: 'bizType',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '业务类型'
                },
                {
                    header: '商品组的状态：',
                    dataIndex: 'sessionStatus',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '商品组的状态'
                },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '商品组合表列表',
            title: '商品组合表列表',

            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [

                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!goods-session/data/view',
                        save: 'jiakao-misc!goods-session/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '名称'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '描述'
                        },
                        {
                            header: '唯一码：',
                            dataIndex: 'uniqKey',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '唯一码'
                        },
                        {
                            header: '价格，单位分：',
                            dataIndex: 'price',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '价格，单位分'
                        },
                        {
                            header: '苹果的价格，单位分：',
                            dataIndex: 'applePrice',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '苹果的价格，单位分'
                        },
                        {
                            header: '原始价格：',
                            dataIndex: 'originalPrice',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '原始价格'
                        },
                        {
                            header: '苹果的价格id：',
                            dataIndex: 'applePriceId',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '苹果的价格id'
                        },
                        {
                            header: '商品的id，逗号分隔：',
                            dataIndex: 'goodsId',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '商品的id，逗号分隔'
                        },
                        {
                            header: '业务类型：',
                            dataIndex: 'bizType',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '业务类型'
                        },
                        {
                            header: '商品组的状态：',
                            dataIndex: 'sessionStatus',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '商品组的状态'
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!goods-session/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '唯一码',
                    dataIndex: 'uniqKey'
                },
                {
                    header: '价格，单位分',
                    dataIndex: 'price'
                },
                {
                    header: '原始价格',
                    dataIndex: 'originalPrice',
                },
                {
                    header: '苹果的价格，单位分',
                    dataIndex: 'applePrice'
                },
                {
                    header: '苹果的价格id',
                    dataIndex: 'applePriceId'
                },
                {
                    header: '商品的id，逗号分隔',
                    dataIndex: 'goodsId'
                },
                {
                    header: '业务类型',
                    dataIndex: 'bizType'
                },
                {
                    header: '商品组的状态',
                    dataIndex: 'sessionStatus'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!goods-session/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});