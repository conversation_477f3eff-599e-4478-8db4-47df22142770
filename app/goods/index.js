/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!goods/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '商品名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 45,
                 check: 'required',
                 placeholder: '商品名称'
             },
             {
                 header: '商品描述：',
                 dataIndex: 'desc',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '商品描述'
             },
             {
                 header: '商品唯一码：',
                 dataIndex: 'uniqKey',
                 xtype: 'text',
                 maxlength: 45,
                 check: 'required',
                 placeholder: '商品唯一码'
             },
             {
                 header: '业务类型：',
                 dataIndex: 'bizType',
                 xtype: 'text',
                 maxlength: 45,
                 check: 'required',
                 placeholder: '业务类型'
             },
             {
                 header: '权限名称：',
                 dataIndex: 'permission',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '权限名称'
             },
             {
                 header: '权限的类型：',
                 dataIndex: 'permissionType',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '权限的类型'
             },
             {
                 header: '商品权限的有效天数：',
                 dataIndex: 'validDays',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '商品权限的有效天数'
             },
            
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '普通商品表列表',
            title: '普通商品表列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
             
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!goods/data/view',
                        save: 'jiakao-misc!goods/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '商品名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 45,
                 check: 'required',
                 placeholder: '商品名称'
             },
             {
                 header: '商品描述：',
                 dataIndex: 'desc',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '商品描述'
             },
             {
                 header: '商品唯一码：',
                 dataIndex: 'uniqKey',
                 xtype: 'text',
                 maxlength: 45,
                 check: 'required',
                 placeholder: '商品唯一码'
             },
             {
                 header: '业务类型：',
                 dataIndex: 'bizType',
                 xtype: 'text',
                 maxlength: 45,
                 check: 'required',
                 placeholder: '业务类型'
             },
             {
                 header: '权限名称：',
                 dataIndex: 'permission',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '权限名称'
             },
             {
                 header: '权限的类型：',
                 dataIndex: 'permissionType',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '权限的类型'
             },
             {
                 header: '商品权限的有效天数：',
                 dataIndex: 'validDays',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '商品权限的有效天数'
             },
           

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!goods/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '商品名称',
                         dataIndex: 'name'
                     },
                     {
                         header: '商品描述',
                         dataIndex: 'desc'
                     },
                     {
                         header: '商品唯一码',
                         dataIndex: 'uniqKey'
                     },
                     {
                         header: '业务类型',
                         dataIndex: 'bizType'
                     },
                     {
                         header: '权限名称',
                         dataIndex: 'permission'
                     },
                     {
                         header: '权限的类型',
                         dataIndex: 'permissionType'
                     },
                     {
                         header: '商品权限的有效天数',
                         dataIndex: 'validDays'
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '创建人id',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '更新时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: '更新人id',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: '修改人',
                         dataIndex: 'updateUserName'
                     }

            ]
        }, ['jiakao-misc!goods/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});