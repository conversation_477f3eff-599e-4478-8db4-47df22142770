/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!help-label/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '顺序：',
                    dataIndex: 'sort',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '顺序'
                },
                {
                    header: '标签名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '标签名称'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '标签配置',
            title: '标签配置',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!help-label/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '顺序：',
                            dataIndex: 'sort'
                        },
                        {
                            header: '标签名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            render: function (data) {
                                let text;
                                if (data == 0) {
                                    text = '下线'
                                } else {
                                    text = '上线'
                                }
                                return text
                            }
                        },

                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更新时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '上线/下线',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '上线/下线',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render()
                    },
                    store: {
                        load: 'jiakao-misc!help-label/data/view',
                        save: 'jiakao-misc!help-label/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    }, {
                        header: '状态：',
                        dataIndex: 'status',
                        xtype: 'select',
                        check: 'required',
                        store: [{
                            key: '0',
                            value: '下线'
                        },
                        {
                            key: 1,
                            value: '上线'
                        }]
                    },]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!help-label/data/view',
                        save: 'jiakao-misc!help-label/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '标签名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '标签名称'
                        },
                        {
                            header: '顺序：',
                            dataIndex: 'sort',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '顺序'
                        }
                    ]
                },
                {
                    name: '问答配置',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('help-label-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'help-label-' + lineData.id,
                                    name: '问答配置: ' + lineData.name
                                })
                            }
                            require(['jiakao-misc!app/help-question/index'], function (Item) {
                                Item.list(nPanel, null, lineData)
                            })
                        });
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!help-label/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '顺序',
                    dataIndex: 'sort',
                    order: 'asc'
                },
                {
                    header: '标签名称',
                    dataIndex: 'name'
                },
                {
                    header: '状态:',
                    dataIndex: 'status',
                    render: function (data) {
                        let text;
                        if (data == 0) {
                            text = '下线'
                        } else {
                            text = '上线'
                        }
                        return text
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '包含答题数',
                    dataIndex: 'count'
                }

            ]
        }, ['jiakao-misc!help-label/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});