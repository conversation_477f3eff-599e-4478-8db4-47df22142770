/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table, lineData, type, from) {
        var columns = [
            {
                header: '问题标题：',
                dataIndex: 'question',
                xtype: 'text',
                maxlength: 128,
                check: 'required',
                placeholder: '问题标题',
                value: lineData.question || ''
            },
            {
                header: '顺序：',
                dataIndex: 'sort',
                xtype: 'text',
                check: 'required',
                placeholder: '顺序',
                value: lineData.sort || ''
            },
            {
                header: '标签：',
                dataIndex: 'helpLabelId',
                xtype: Plugin('simple!auto-prompt', {
                    store: `jiakao-misc!help-label/data/list?limit=10000`,
                    value: '',
                    dataIndex: 'helpLabelId',
                    placeholder: '选择标签',
                    isMulti: true,
                    index: {
                        key: 'id',
                        value: 'name',
                        search: 'name'
                    },
                    value: lineData.helpLabelId || ''
                }, function () {
                    console.log('标签：', arguments)
                })
            },
            {
                header: '回答内容：',
                dataIndex: 'answer',
                xtype: Plugin('jiakao-misc!rich-text', {
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 500,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                    },
                    value: lineData.answer || ''
                }, function () {
                    console.log('回答内容：', arguments)
                })
            },
        ];

        if(from == 1){
            columns.splice(2, 1);
        }

        var storeUrl = '';

        if (type == 'add') {
            if (from == 1) {
                storeUrl = 'jiakao-misc!help-question/data/insert?helpLabelId=' + lineData.helpLabelId
            } else if (from == 2) {
                storeUrl = 'jiakao-misc!help-question/data/insert'
            }
        }

        if (type == 'edit') {
            storeUrl = 'jiakao-misc!help-question/data/update'
            columns = [{
                dataIndex: 'id',
                xtype: 'hidden',
                value: lineData.id
            },].concat(columns);
        }

        console.log(columns);
        Table({
            title: '添加',
            width: 1000,
            store: storeUrl,
            form: {
                submitHandler: function (form) {
                    var detail = $(form).find('[name="editorValue"]').val();

                    return {
                        answer: detail
                    };
                }
            },
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: function (table, dom, config) {
                setTimeout(function () {
                    var promptListDom = dom.item('data-list');
                    if (promptListDom) {
                        promptListDom.css('z-index', 1000)
                    }
                }, 500)

            },
            columns: columns
        }).add();
    }

    var list = function (panel, obj, labelLineData) {
        console.log(lineData)
        // 1: 从标签配置页面跳转过来 2: 从菜单点击进来
        var from;
        if (labelLineData) {
            from = 1;
            var lineData = {
                name: labelLineData.name,
                helpLabelId: labelLineData.id
            }
        } else {
            from = 2;
            lineData = {
                name: '',
                helpLabelId: ''
            }
        }

        Table({
            description: lineData.name + '问答列表',
            title: lineData.name + '问答列表',
            search: [
                {
                    dataIndex: 'key',
                    xtype: 'text',
                    placeholder: '问题标题'
                },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择',
                    }, {
                        key: 0,
                        value: '下线'
                    }, {
                        key: 1,
                        value: '上线'
                    }],
                    placeholder: '请选择状态'
                },
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            add(table, lineData, 'add', from);
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        add(table, lineData, 'edit', from);
                    }
                },
                {
                    name: '上线/下线',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '上线/下线',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render()
                    },
                    store: {
                        load: 'jiakao-misc!help-question/data/view',
                        save: 'jiakao-misc!help-question/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    }, {
                        header: '状态：',
                        dataIndex: 'status',
                        xtype: 'select',
                        check: 'required',
                        store: [{
                            key: '0',
                            value: '下线'
                        },
                        {
                            key: 1,
                            value: '上线'
                        }]
                    },]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!help-question/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '顺序',
                    dataIndex: 'sort',
                    order: 'asc'
                },
                {
                    header: '标签id',
                    dataIndex: 'helpLabelId'
                },

                {
                    header: '问题标题',
                    dataIndex: 'question'
                },
                {
                    header: '回答内容',
                    dataIndex: 'answer',
                    render: function (data) {
                        return data ? '<a>查看内容</a>' : ''
                    },
                    click: function (table, trDom, lineData) {

                        Widgets.dialog.html('查看').done(function (dialog) {
                            // dialog.body 为弹窗dom对象，用于展示自定义内容

                            var oA = $(`<p>${lineData.answer}</p>`);

                            dialog.body.append(oA);

                        })
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        let text;
                        if (data == 0) {
                            text = '下线'
                        } else {
                            text = '上线'
                        }
                        return text
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!help-question/data/list?helpLabelId=' + lineData.helpLabelId], panel, null).render();
    }

    return {
        list: list
    }

});