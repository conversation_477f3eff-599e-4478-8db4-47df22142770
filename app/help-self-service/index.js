/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin',
'jiakao-misc!app/common/constants',
'jiakao-misc!app/common/tiku',
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants,TIKU) {
    var carTypeArr = [];

    for (var k in TIKU) {
      carTypeArr.push({
        key: k,
        value: TIKU[k]
      })
    }
    var addEdit = function (table,lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'jiakao-misc!help-self-service/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '入口名称:',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '入口名称'
                },
                {
                    header: '图标:',
                    dataIndex: 'icon',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'icon',
                        uploadIndex: 'icon',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '车型:',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store: [].concat(carTypeArr),
                },
                {
                    header: '科目:',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store:  Constants.kemuStore.slice(1).concat([{ key: '5', value: '拿本' }]),
                },
                {
                    header: "访问模式:",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                {
                    header: '访问场景:',
                    dataIndex: 'sceneCode',
                    xtype: 'checkbox',
                    store: [{
                        key: '101',
                        value: '普通场景'
                    }, {
                        key: '102',
                        value: '扣满12分'
                    }]
                },
                {
                    header: '安卓跳转地址:',
                    dataIndex: 'androidUrl',
                    xtype: 'textarea',
                    maxlength: 512,
                    check: 'required',
                    placeholder: '安卓跳转地址'
                },
                {
                    header: 'Ios跳转地址:',
                    dataIndex: 'iosUrl',
                    xtype: 'textarea',
                    maxlength: 512,
                    check: 'required',
                    placeholder: 'Ios跳转地址'
                },
                {
                    header: '顺序:',
                    dataIndex: 'sort',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '顺序'
                },
            ]
        }
        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }

    }
    var list = function (panel) {
        Table({
            description: '自助服务配置',
            title: '自助服务配置',
            search: [{
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
            },{
                dataIndex: 'carType',
                xtype: 'select',
                store: [{key:'',value:'请选择车型'},...carTypeArr],
            },{
                dataIndex: 'sceneCode',
                xtype: 'select',
                store: [{ key: '', value: '请选择访问场景' }].concat(Constants.senceStore)
            },
            {
                dataIndex: 'patternCode',
                xtype: 'select',
                store: [{ key: '', value: '请选择访问模式' },...Constants.editionStore],
                placeholder: '访问模式',
                },
                {
                    placeholder: '状态:',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value:'请选择'
                    },{
                        key: '0',
                        value: '下线'
                    },
                    {
                        key: 1,
                        value: '上线'
                    }]
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '上线/下线',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!help-self-service/data/view',
                        save: 'jiakao-misc!help-self-service/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    }, {
                        header: '状态:',
                        dataIndex: 'status',
                        xtype: 'select',
                        check: 'required',
                        store: [{
                            key: '0',
                            value: '下线'
                        },
                        {
                            key: 1,
                            value: '上线'
                        }]
                    }]
                },
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!help-self-service/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '顺序',
                    dataIndex: 'sort',
                    order: 'asc'
                },
                {
                    header: '入口名称',
                    dataIndex: 'name'
                },
                {
                    header: '图标',
                    dataIndex: 'icon',
                    render: function (data) {
                        return `<img src='${data}' style="width:200px"/>`
                    }
                },
             
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                if(data[i]=='5'){
                                    strArr.push('拿本')
                                }else{
                                    strArr.push(Constants.kemuMap[data[i]])
                                }
                                
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '访问场景',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.senceMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'patternCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.editionMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        let text;
                        if (data == 0) {
                            text = '下线'
                        } else {
                            text = '上线'
                        }
                        return text
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!help-self-service/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});