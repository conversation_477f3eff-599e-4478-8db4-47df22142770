/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', "simple!core/plugin",], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var operateTypeMap = {
        1: '新增',
        2: '修改'
    }
    var onlineStatusMap = {
        1: '上架',
        2: '下架'
    }
    var handleArray = function (map) {
        var newArray = []
        for (let key in map) {
            newArray.push({ key: key, value: map[key] })
        }
        return newArray
    }
    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'jiakao-misc!icon-group-config/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '分组id：',
                    dataIndex: 'groupId',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!icon-group-config/data/groupIdslist?limit=1000',
                        placeholder: '分组id',
                        dataIndex: 'groupId',
                        index: {
                            key: 'id',
                            value: 'showName',
                            search: 'showName'
                        },
                        isMulti: false,

                    }, function (plugin, value) {
                    }),

                },
                {
                    header: '操作类型：',
                    dataIndex: 'operateType',
                    xtype: 'select',
                    store: [
                        { key: '', value: '请选择操作类型' },
                        ...handleArray(operateTypeMap)
                    ],
                    check: 'required',

                },
                {
                    header: "显示图：",
                    dataIndex: "showIconUrl",
                    xtype: Plugin(
                        "jiakao-misc!upload",
                        {
                            dataIndex: "showIconUrl",
                            uploadIndex: "showIconUrl",
                            bucket: "exam-room",
                            isSingle: true,
                            placeholder: "请选择上传文件",
                            url: "simple-upload3://upload/file.htm",
                        },
                        function () {
                            console.log(arguments);
                        }
                    ),
                },
                {
                    header: "详情图：",
                    dataIndex: "detailImgUrl",
                    xtype: Plugin(
                        "jiakao-misc!upload",
                        {
                            dataIndex: "detailImgUrl",
                            uploadIndex: "detailImgUrl",
                            bucket: "exam-room",
                            isSingle: true,
                            placeholder: "请选择详情图",
                            url: "simple-upload3://upload/file.htm",
                        },
                        function () {
                            console.log(arguments);
                        }
                    ),
                },
            ]
        }
        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Table({
            description: '新规分类管理',
            title: '新规分类管理',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!icon-group-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '分类',
                            dataIndex: 'operateType',
                            render: function (data) {
                                return operateTypeMap[data]
                            }
                        },
                        {
                            header: '分组id：',
                            dataIndex: 'groupId'
                        },
                        {
                            header: '显示图：',
                            dataIndex: 'showIconUrl'
                        },
                        {
                            header: '详情图：',
                            dataIndex: 'detailImgUrl'
                        },
                        {
                            header: '业务类型 101-新规活动：',
                            dataIndex: 'bizType'
                        },
                        {
                            header: '操作类型：',
                            dataIndex: 'operateType',
                            render: function (data) {
                                return operateTypeMap[data]
                            }
                        },

                        {
                            header: '上架状态',
                            dataIndex: 'onlineStatus',
                            render: function (data) {
                                return onlineStatusMap[data]
                            }
                        },

                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人Id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人Id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人',
                            dataIndex: 'updateUserName'
                        },


                    ]
                },
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '上架',
                    class: 'warning',
                    render: function (data, array, index) {
                        if (array[index].onlineStatus == 1) {
                            return '下架'
                        } else if (array[index].onlineStatus == 2) {
                            return '上架'
                        }

                    },
                    click: function (table, dom, lineData) {
                        let string = lineData.onlineStatus == 1 ? '下架' : '上架'
                        let onlineStatus = lineData.onlineStatus == 1 ? 2 : 1
                        Widgets.dialog.confirm('确认' + string + '吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!icon-group-config/data/update?onlineStatus=' + onlineStatus + '&id=' + lineData.id], [{
                                    aliases: 'list'
                                }]).save().done(function (store) {
                                    table.render();
                                }).fail(function () { });
                            } else { }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!icon-group-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '分类',
                    dataIndex: 'operateType',
                    render: function (data) {
                        return operateTypeMap[data]
                    }
                },
                {
                    header: '分组id',
                    dataIndex: 'groupId'
                },
                {
                    header: '显示图',
                    dataIndex: 'showIconUrl',
                    render: function (data) {
                        if (data) {
                            return '<a><img style="height:40px" src="' + data + '"/></a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('显示图', { width: 1200 }).done(function (dialog) {
                            $(dialog.body).html('<img src="' + lineData.showIconUrl + '"></img>')
                        })
                    }
                },
                {
                    header: '详情图',
                    dataIndex: 'detailImgUrl',
                    render: function (data) {
                        if (data) {
                            return '<a><img style="height:40px" src="' + data + '"/></a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('详情图', { width: 1200 }).done(function (dialog) {
                            $(dialog.body).html('<img src="' + lineData.detailImgUrl + '"></img>')
                        })
                    }
                },
                {
                    header: '操作类型：',
                    dataIndex: 'operateType',
                    render: function (data) {
                        return operateTypeMap[data]
                    }
                },
                {
                    header: '上架状态',
                    dataIndex: 'onlineStatus',
                    render: function (data) {
                        return onlineStatusMap[data]
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!icon-group-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});