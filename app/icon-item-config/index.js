/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', "simple!core/plugin", 'simple!core/ajax'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Ajax) {
    var operateTypeMap = {
        1: '新增',
        2: '修改'
    }
    var onlineStatusMap = {
        1: '上架',
        2: '下架'
    }
    var handleArray = function (map) {
        var newArray = []
        for (let key in map) {
            newArray.push({ key: key, value: map[key] })
        }
        return newArray
    }
    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var editcolumns = [
            {
                header: '操作类型：',
                dataIndex: 'operateType',
                xtype: 'select',
                store: [
                    { key: '', value: '请选择操作类型' },
                    ...handleArray(operateTypeMap)
                ],
                check: 'required',

            },
            {
                header: '老图标id：',
                dataIndex: 'oldIconItemId',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!icon-item-config/data/itemlist?limit=2000',
                    placeholder: '老图标id',
                    dataIndex: 'oldIconItemId',
                    index: {
                        key: 'id',
                        value: 'showName',
                        search: 'showName'
                    },
                    isMulti: false,

                }, function (plugin, value) {
                }),
            },
            {
                header: '图标更改文案：',
                dataIndex: 'iconUpdateInfo',
                xtype: 'textarea',
                cols: 5,
                rows: 5,
                placeholder: '图标更改文案'
            },
            {
                dataIndex: 'newoperateType',
                xtype: 'hidden',
                value: 2
            }
        ]
        var config = {
            title: isEdit ? '修改图标' : '添加图标',
            width: 500,
            store: 'jiakao-misc!icon-item-config/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var newoperateType = form.newoperateType && form.newoperateType.value
                    if (isEdit) {
                        return {
                            operateType: newoperateType
                        }
                    }
                    return true
                }
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '分组id：',
                    dataIndex: 'groupId',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!icon-group-config/data/groupIdslist?limit=1000',
                        placeholder: '分组id',
                        dataIndex: 'groupId',
                        index: {
                            key: 'id',
                            value: 'showName',
                            search: 'showName'
                        },
                        isMulti: false,

                    }, function (plugin, value) {
                    }),
                },
                {
                    header: '图标id：',
                    dataIndex: 'iconItemId',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!icon-item-config/data/itemlist?limit=2000',
                        placeholder: '图标id',
                        dataIndex: 'iconItemId',
                        index: {
                            key: 'id',
                            value: 'showName',
                            search: 'showName'
                        },
                        isMulti: false,

                    }, function (plugin, value) {
                    }),
                },

            ].concat(isEdit ? editcolumns : [{
                dataIndex: 'operateType',
                xtype: 'hidden',
                value: 1
            }])
        }
        if (isEdit) {
            Table().edit(lineData, config)
        } else {
            Table(config).add();
        }

    }
    var exportFile = function (listTable) {
        var fileUrl = ''
        Table({
            title: '导入文件',
            width: 650,
            store: '',
            success: function (obj, dialog) {
                dialog.close();
                listTable.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    var operateType = form.operateType.value
                    if (!fileUrl) {
                        Widgets.dialog.alert('请上传文件')
                        return
                    }
                    var formData = new FormData();
                    formData.append('file', fileUrl);
                    formData.append('operateType', operateType);
                    let url = window.j.host.local + '/api/admin/icon-item-config/excel-import.htm'
                    Ajax.request(url, {
                        data: formData,
                        type: 'post',
                        contentType: false,
                        processData: false,
                        success: function (data) {
                            $(form[0]).click();
                            // Widgets.dialog.alert('上传成功')
                            listTable.render();
                        },
                        error: function (data) {
                            console.log('data', data)
                            Widgets.dialog.alert(data.message)
                        },

                    });
                    return false
                }
            },
            renderAfter: function (table, dom, configData) {


                Plugin('jiakao-misc!upload-file2', {
                    dataIndex: 'fileUrl',
                    uploadIndex: 'file',
                    type: 'file',
                    bucket: "exam-room",
                    isSingle: false,
                    class: 'primary',
                    placeholder: '请选择上传文件',
                    target: dom.item('file-show'),
                    url: 'simple-upload3://upload/file.htm'
                }, function (obj, dataArray, data) {
                    fileUrl = data
                    // dom.item('title').val(data.name)
                }).render()

                dom.item('button-down').on('click', function () {
                    var operateType = dom.item('operateType').val()
                    window.location.href = window.j.host.local + '/api/admin/icon-item-config/template-download.htm?operateType=' + operateType;
                })
            },
            columns: [
                {
                    header: '操作类型',
                    dataIndex: 'operateType',
                    xtype: 'select',
                    store: [
                        ...handleArray(operateTypeMap)
                    ],
                    check: 'required',

                },
                {
                    header: '导入文件：',
                    dataIndex: 'newfile',
                    xtype: function () {
                        return `<div data-item="render-file-container">
                                <div data-item="file-show" style="width:73%;float:left"></div>
                                <div  style="float:right;margin-left:20px">
                                <button style="width:80px;" data-item="button-down" type="button" class="btn btn-default ">模板下载</button>
                                </div>
                        </div>`
                    }
                },
            ]
        }).add();
    }
    var list = function (panel) {
        Table({
            description: '新规图标配置',
            title: '新规图标配置',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    },
                    // {
                    //     name: '导入',
                    //     class: 'success',
                    //     click: function (table) {
                    //         exportFile(table)
                    //     }
                    // }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '上架',
                    class: 'warning',
                    render: function (data, array, index) {
                        if (array[index].onlineStatus == 1) {
                            return '下架'
                        } else if (array[index].onlineStatus == 2) {
                            return '上架'
                        }

                    },
                    click: function (table, dom, lineData) {
                        let string = lineData.onlineStatus == 1 ? '下架' : '上架'
                        let onlineStatus = lineData.onlineStatus == 1 ? 2 : 1
                        Widgets.dialog.confirm('确认' + string + '吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!icon-item-config/data/update?onlineStatus=' + onlineStatus + '&id=' + lineData.id], [{
                                    aliases: 'list'
                                }]).save().done(function (store) {
                                    table.render();
                                }).fail(function () { });
                            } else { }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!icon-item-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '类型',
                    dataIndex: 'operateType',
                    render: function (data) {
                        return operateTypeMap[data]
                    }
                },
                {
                    header: '分组id',
                    dataIndex: 'groupId'
                },
                {
                    header: '新图标Id',
                    dataIndex: 'iconItemId'
                },
                {
                    header: '新图标名称',
                    dataIndex: 'iconItemName'
                },
                {
                    header: '老图标id',
                    dataIndex: 'oldIconItemId'
                },
                {
                    header: '老图标名称',
                    dataIndex: 'oldIconItemName'
                },
                {
                    header: '更改文案',
                    dataIndex: 'iconUpdateInfo'
                },
                {
                    header: '上架状态',
                    dataIndex: 'onlineStatus',
                    render: function (data) {
                        return onlineStatusMap[data]
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },

                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },

                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!icon-item-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});