/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/tiku', 'jiakao-misc!app/common/tools'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, TIKU, Tools) {

    var kemuArr = [{
        key: 1,
        value: '科目一'
    },
    {
        key: 4,
        value: '科目四'
    }
    ]
    var kemuMap = {
        1: '科目一',
        4: '科目四'
    }

    var sceneCodeMap = {
        101: '基础场景',
        102: '扣满12分',
        103: '维语场景'
    }

    var sceneCodeArr = [];
    for (const key in sceneCodeMap) {
        sceneCodeArr.push({ key, value: sceneCodeMap[key] })
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!illiteracy-voice/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '试题id：',
                dataIndex: 'questionId',
                xtype: 'text',
                placeholder: '试题id'
            },
            {
                header: '场景:',
                dataIndex: 'sceneCode',
                xtype: 'select',
                store: sceneCodeArr
            },
            {
                header: '车型:',
                dataIndex: 'carType',
                xtype: 'select',
                store: [
                    {
                        key: '',
                        value: '请选择车型'
                    },
                    ...Tools.getArrayFromMap(TIKU)
                ]
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: kemuArr,
                placeholder: '科目'
            },
            {
                header: '语音的类型：',
                dataIndex: 'type',
                xtype: 'select',
                check: 'required',
                store: [{
                    key: '',
                    value: '选择类型'
                }].concat(typeArr)
            },
            {
                header: '语音的地址：',
                dataIndex: 'voiceUrl',
                xtype: Plugin('jiakao-misc!upload1', {

                    dataIndex: 'voiceUrl',
                    uploadIndex: 'voiceUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '语音内容：',
                dataIndex: 'content',
                xtype: 'text',
                maxlength: 128,
                placeholder: '语音内容'
            }
            ]
        }).add();
    }

    var uploadBatch = function (table) {
        Table({
            title: '批量添加',
            width: 500,
            store: 'jiakao-misc!illiteracy-voice/data/uploadBatch',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                //     {
                //         header: '配置文件：',
                //         dataIndex: 'config',
                //         xtype: 'file',
                //         placeholder: '配置文件'
                // },
                {
                    header: '配置文件：',
                    dataIndex: 'config',
                    xtype: Plugin('jiakao-misc!upload2', {
                        dataIndex: 'config',
                        uploadIndex: 'config',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function (plugin, b, c, Size) {
                        $('[name="size"]').val(Size);
                    })
                },
                {
                    header: '场景:',
                    dataIndex: 'sceneCode',
                    xtype: 'select',
                    store: sceneCodeArr
                },
                {
                    header: '男声：',
                    dataIndex: 'male',
                    xtype: Plugin('jiakao-misc!upload2', {
                        dataIndex: 'male',
                        uploadIndex: 'male',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function (plugin, b, c, Size) {
                        $('[name="size"]').val(Size);
                    })
                },

                {
                    header: '女声',
                    dataIndex: 'female',
                    xtype: Plugin('jiakao-misc!upload2', {
                        dataIndex: 'female',
                        uploadIndex: 'female',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function (plugin, b, c, Size) {
                        $('[name="size"]').val(Size);
                    })
                },
            ]
        }).add();
    }


    var typeArr = [{
        key: 0,
        value: '男声'
    },
    {
        key: 1,
        value: '女声'
    }
    ]
    var typeMap = {
        0: '男声',
        1: '女声'
    }
    var list = function (panel) {
        Table({
            description: 'illiteracy-voice列表',
            title: 'illiteracy-voice列表',
            search: [{
                placeholder: '试题id',
                dataIndex: 'questionId',
                xtype: 'text'
            },
            {
                dataIndex: 'type',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '选择类型'
                }].concat(typeArr)
            }
            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                },
                {
                    name: '批量添加',
                    class: 'warning',
                    click: uploadBatch
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!illiteracy-voice/data/view',
                        save: 'jiakao-misc!illiteracy-voice/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    },
                    {
                        header: '试题id：',
                        dataIndex: 'questionId',
                        xtype: 'text',
                        placeholder: '试题id'
                    },
                    {
                        header: '场景:',
                        dataIndex: 'sceneCode',
                        xtype: 'select',
                        store: sceneCodeArr
                    },
                    {
                        header: '车型:',
                        dataIndex: 'carType',
                        xtype: 'select',
                        store: [
                            {
                                key: '',
                                value: '请选择车型'
                            },
                            ...Tools.getArrayFromMap(TIKU)
                        ]
                    },
                    {
                        header: '科目：',
                        dataIndex: 'kemu',
                        xtype: 'select',
                        store: kemuArr,
                        placeholder: '科目'
                    },
                    {
                        header: '语音的类型：',
                        dataIndex: 'type',
                        xtype: 'select',
                        check: 'required',
                        store: [{
                            key: '',
                            value: '选择类型'
                        }].concat(typeArr)
                    },
                    {
                        header: '语音的地址：',
                        dataIndex: 'voiceUrl',
                        xtype: Plugin('jiakao-misc!upload1', {

                            dataIndex: 'voiceUrl',
                            uploadIndex: 'voiceUrl',
                            bucket: "jiakao-web",
                            isSingle: true,
                            placeholder: '请选择上传文件',
                            url: 'simple-upload3://upload/file.htm'
                        }, function () {
                            console.log(arguments)
                        })
                    },
                    {
                        header: '语音内容：',
                        dataIndex: 'content',
                        xtype: 'text',
                        maxlength: 128,
                        placeholder: '语音内容'
                    }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!illiteracy-voice/data/delete'
                }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '试题id',
                dataIndex: 'questionId'
            },
            {
                header: '场景:',
                dataIndex: 'sceneCode',
                render: function (data) {
                    return sceneCodeMap[data]
                }
            },
            {
                header: '车型',
                dataIndex: 'carType',
                render: function (data) {
                    return TIKU[data]
                }
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                render: function (data) {
                    return kemuMap[data];
                }
            },
            {
                header: '语音的类型',
                dataIndex: 'type',
                render: function (data) {
                    return typeMap[data];
                }
            },
            {
                header: '语音的地址',
                dataIndex: 'voiceUrl',
                render: function (data) {
                    if (data) {
                        return '<a>点击查看</a>'
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('语音的地址', {}).done(function (dialog) {
                        $(dialog.body).html('<a href="' + lineData.voiceUrl + '" target="_blank">' + lineData.voiceUrl + '</a>')
                    })
                }
            },
            {
                header: '语音内容',
                dataIndex: 'content'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!illiteracy-voice/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});