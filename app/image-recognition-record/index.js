/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {


    var matchTypeMap = {
        1: '内存',
        2: 'es匹配'
    }

    var resultStatusMap = {
        99: '本次操作无权限',
        100: '图片识别失败',
        101: '图片识别成功，不符合规范',
        102: '图片识别成功，符合规范，无搜索结果',
        103: '图片识别成功，符合规范，有搜索结果'
    }
    var statusMap = {
        0: '待处理',
        1: '无效内容',
        2: '已有试题',
        3: '新题待添加',
        4: '新题已添加'
    }
    var statusStore = Object.keys(statusMap).map(a=> ({ key: a, value: statusMap[a]}));

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!image-recognition-record/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: 'deviceId：',
                    dataIndex: 'deviceId',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: 'deviceId'
                },
                {
                    header: 'imageUrl：',
                    dataIndex: 'imageUrl',
                    xtype: 'textarea',
                    maxlength: 512,
                    placeholder: 'imageUrl'
                },
                {
                    header: 'content：',
                    dataIndex: 'content',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: 'content'
                },
                {
                    header: 'matchContent：',
                    dataIndex: 'matchContent',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: 'matchContent'
                },
                {
                    header: 'resultStatus：',
                    dataIndex: 'resultStatus',
                    xtype: 'text',
                    placeholder: 'resultStatus'
                },
                {
                    header: 'result：',
                    dataIndex: 'result',
                    xtype: 'textarea',
                    maxlength: 512,
                    placeholder: 'result'
                },
                {
                    header: 'deleted：',
                    dataIndex: 'deleted',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                

            ]
        }).add();
    }
    var batchMarking = function(table, dom, config, selectedArr){
        selectedArr
        if(selectedArr.length <= 0){
            return Widgets.dialog.alert('请先选择，然后再标记');
        }
        Table({
            title: '批量标记状态',
            width: 500,
            store: 'jiakao-misc!image-recognition-record/data/batchMark',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'ids',
                    xtype: 'hidden',
                    value: selectedArr.join(',')
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    xtype: 'select',
                    check: "required",
                    placeholder: '请选择状态',
                    store:[{key:'', value:'请选择状态'}].concat(statusStore)
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '图片识别记录列表',
            title: '图片识别记录列表',
            search: [
                {
                    xtype: 'text',
                    dataIndex: 'matchContent',
                    placeholder: '请输入匹配内容'
                },
                {
                    xtype: 'text',
                    dataIndex: 'deviceIds',
                    placeholder: '请输入设备id，多个用逗号(,)隔开'
                },
                {
                    xtype: 'date',
                    dataIndex: 'startTime',
                    placeholder: '请选择开始时间'
                },
                {
                    xtype: 'date',
                    dataIndex: 'endTime',
                    placeholder: '请选择结束时间'
                },
                {
                    xtype: 'select',
                    dataIndex: 'status',
                    store: [{key:'', value: '请选择状态'}].concat(statusStore)
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    },
                    {
                        name: '批量标记',
                        class: 'primary',
                        click: batchMarking
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            selector: {
                dataIndex: 'id',
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!image-recognition-record/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: 'deviceId：',
                            dataIndex: 'deviceId'
                        },
                        {
                            header: 'imageUrl：',
                            dataIndex: 'imageUrl'
                        },
                        {
                            header: 'content：',
                            dataIndex: 'content'
                        },
                        {
                            header: 'matchContent：',
                            dataIndex: 'matchContent'
                        },
                        {
                            header: 'resultStatus：',
                            dataIndex: 'resultStatus'
                        },
                        {
                            header: 'result：',
                            dataIndex: 'result'
                        },
                        {
                            header: 'deleted：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'deleted'
                        },
                        {
                            header: '创建者id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建者姓名：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更改者id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '更改者姓名：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!image-recognition-record/data/view',
                        save: 'jiakao-misc!image-recognition-record/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: 'deviceId：',
                            dataIndex: 'deviceId',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: 'deviceId'
                        },
                        {
                            header: 'imageUrl：',
                            dataIndex: 'imageUrl',
                            xtype: 'textarea',
                            maxlength: 512,
                            placeholder: 'imageUrl'
                        },
                        {
                            header: 'content：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: 'content'
                        },
                        {
                            header: 'matchContent：',
                            dataIndex: 'matchContent',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: 'matchContent'
                        },
                        {
                            header: 'resultStatus：',
                            dataIndex: 'resultStatus',
                            xtype: 'text',
                            placeholder: 'resultStatus'
                        },
                        {
                            header: 'result：',
                            dataIndex: 'result',
                            xtype: 'textarea',
                            maxlength: 512,
                            placeholder: 'result'
                        },
                        {
                            header: '状态',
                            dataIndex: 'status',
                            xtype: 'select',
                            placeholder: '请选择状态',
                            store:[{key:'', value:'请选择状态'}].concat(statusStore)
                        },
                        {
                            header: 'deleted：',
                            dataIndex: 'deleted',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        },
                        // {
                        //     header: '创建者id：',
                        //     dataIndex: 'createUserId',
                        //     xtype: 'text',
                        //     placeholder: '创建者id'
                        // },
                        // {
                        //     header: '创建者姓名：',
                        //     dataIndex: 'createUserName',
                        //     xtype: 'text',
                        //     maxlength: 32,
                        //     placeholder: '创建者姓名'
                        // },
                        // {
                        //     header: '更改者id：',
                        //     dataIndex: 'updateUserId',
                        //     xtype: 'text',
                        //     placeholder: '更改者id'
                        // },
                        // {
                        //     header: '更改者姓名：',
                        //     dataIndex: 'updateUserName',
                        //     xtype: 'textarea',
                        //     maxlength: 255,
                        //     placeholder: '更改者姓名'
                        // },
                        // {
                        //     header: '更改时间：',
                        //     dataIndex: 'updateTime',
                        //     xtype: 'date',
                        //     placeholder: '更改时间'
                        // }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!image-recognition-record/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'deviceId',
                    dataIndex: 'deviceId'
                },
                {
                    header: 'imageUrl',
                    dataIndex: 'imageUrl',
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    reportRender: function(data){
                        return data;
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('图片', { width: 800 }).done(function (dialog) {
                            $(dialog.body).html('<img src="' + lineData.imageUrl + '"></img>')
                        })
                    }
                },
                {
                    header: 'content',
                    dataIndex: 'content'
                },
                {
                    header: 'matchContent',
                    dataIndex: 'matchContent'
                },
                {
                    header: 'resultStatus',
                    dataIndex: 'resultStatus',
                    render: function(table, row, lineData){
                        return resultStatusMap[lineData.resultStatus]
                    }
                },
                {
                    header: 'result',
                    dataIndex: 'result',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.result + '</div><br/>')
                        })
                    }
                },
                {
                    header: '匹配类型',
                    dataIndex: 'matchType',
                    render: function(table, row, lineData){
                        return matchTypeMap[lineData.matchType]
                    }
                },
                {
                    header: '结果详情',
                    dataIndex: 'resultExtra'
                },
                {
                    header: '最高分',
                    dataIndex: 'maxScore'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function(data){
                        return statusMap[data];
                    }
                },
                {
                    header: 'deleted',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'deleted'
                },
                {
                    header: '创建者姓名',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '操作人',
                    dataIndex: 'operateUserName'
                },
                {
                    header: '操作时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'operateTime'
                },
                {
                    header: '更改者姓名',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '更改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }
            ]
        }, ['jiakao-misc!image-recognition-record/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});