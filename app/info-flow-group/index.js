/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'jiakao-misc!app/common/infoflow', "simple!core/plugin",], function (Template, Table, Utils, Widgets, Store, Form, Infoflow, Plugin) {
    var carMap = {
        'car': '小车'
    }
    var kemuMap = {
        2: '科目二'
    }
    var carType = []
    for (var key in carMap) {
        carType.push({ key: key, value: carMap[key] })
    }
    var kemuArr = []
    for (var key in kemuMap) {
        kemuArr.push({ key: key, value: kemuMap[key] })
    }
    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 900,
            store: 'jiakao-misc!info-flow-group/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler(form) {
                    var reg = /[1-9]\d*/
                    var sort = form.sort.value
                    var dataTypes = form.dataTypes && form.dataTypes.value
                    var newDataTypes = JSON.parse(dataTypes || "[]")
                    const newArr = newDataTypes.map(item => item.dataType);
                    const isRepeat = newArr.some((item, index, arr) => arr.indexOf(item) != index);
                    if (!reg.test(sort)) {
                        Widgets.dialog.alert('排序请输入正整数')
                        return
                    }
                    if (isRepeat) {
                        Widgets.dialog.alert('不能配置同类型的信息流');
                        return
                    }
                    return true

                }
            },
            renderAfter: function (config, dom, data) {
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '科目：',
                    dataIndex: 'course',
                    xtype: 'select',
                    check: 'required',
                    store: [{ key: '', value: '请选择科目' }].concat(kemuArr),
                    placeholder: '科目'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    check: 'required',
                    store: [{ key: '', value: '请选择车型' }].concat(carType),
                    placeholder: '车型'
                },
                {
                    header: '分组标签名称：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 4,
                    check: 'required',
                    placeholder: '请输入分组标签名称'
                },
                {
                    header: '排序：',
                    dataIndex: 'sort',
                    xtype: 'number',
                    min: 1,
                    check: 'required',
                    placeholder: 'sort'
                },
                {
                    header: '信息流类型：',
                    dataIndex: 'dataTypes',
                    xtype: Plugin('jiakao-misc!info-flow-group', {
                        dataIndex: 'dataTypes',
                        value: lineData.dataTypes,
                        tieziArray: Infoflow.infoflowType
                    }, function (plugin, value) {

                    })
                    // xtype: 'checkbox',
                    // check: 'required',
                    // store: Infoflow.infoflowType,
                },
                {
                    header: '信息流排序方式：',
                    dataIndex: 'fieldSort',
                    xtype: 'select',
                    store: [
                        { key: 'publish_time', value: '时间先后' },
                        { key: 'hot', value: '热度大小' },

                    ]
                },
                {
                    header: '广告位Id：',
                    dataIndex: 'adPlaceId',
                    xtype: 'text',
                    placeholder: '请输入广告位Id，多个以逗号分割'
                },

            ]
        }
        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Table({
            description: '分组标签管理',
            title: '分组标签管理',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [
                {
                    dataIndex: 'course',
                    xtype: 'select',
                    store: [{ key: '', value: '请选择科目' }].concat(kemuArr)
                },
                {
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{ key: '', value: '请选择车型' }].concat(carType)
                },
                {
                    dataIndex: 'id',
                    xtype: 'text',
                    placeholder: '分组标签id'
                },
                {
                    dataIndex: 'title',
                    xtype: 'text',
                    placeholder: '分组标签名称'
                }
            ],
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!info-flow-group/data/view',
                    columns: [
                        {
                            header: '标签id',
                            dataIndex: 'id',
                            width: 60
                        },
                        {
                            header: '科目',
                            dataIndex: 'course',
                            render: function (data) {
                                return kemuMap[data]
                            }
                        },
                        {
                            header: '车型',
                            dataIndex: 'carType',
                            render: function (data) {
                                return carMap[data]
                            }
                        },
                        {
                            header: '分组标签名称',
                            dataIndex: 'title'
                        },
                        {
                            header: '标签下包含的信息流类型',
                            dataIndex: 'dataTypes',
                            render: function (data) {
                                var dataArray = JSON.parse(data || '[]')
                                var showArray = []
                                dataArray && dataArray.forEach((res) => {
                                    showArray.push(Infoflow.infoflowMap[res.dataType])
                                })
                                return showArray.join(',')
                            }
                        },
                        {
                            header: '排序',
                            dataIndex: 'sort'
                        },
                        {
                            header: 'deleted：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'deleted'
                        },
                        {
                            header: '创建者id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建者姓名：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更改者id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '更改者姓名：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!info-flow-group/data/delete'
                }
            ],
            columns: [
                {
                    header: '标签id',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    header: '科目',
                    dataIndex: 'course',
                    render: function (data) {
                        return kemuMap[data]
                    }
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        return carMap[data]
                    }
                },
                {
                    header: '分组标签名称',
                    dataIndex: 'title'
                },
                {
                    header: '标签下包含的信息流类型',
                    dataIndex: 'dataTypes',
                    render: function (data) {
                        var dataArray = JSON.parse(data || '[]')
                        var showArray = []
                        dataArray && dataArray.forEach((res) => {
                            showArray.push(Infoflow.infoflowMap[res.dataType])
                        })
                        return showArray.join(',')
                    }
                },
                {
                    header: '排序',
                    dataIndex: 'sort'
                },
                {
                    header: '操作人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '操作时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!info-flow-group/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});