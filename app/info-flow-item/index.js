/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'jiakao-misc!app/common/infoflow', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Infoflow, Plugin) {
    var statusMap = {
        0: '已失效',
        1: '生效中',
        2: '待生效',
    }
    var statusArray = []
    for (var key in statusMap) {
        statusArray.push({ key: key, value: statusMap[key] })
    }
    var addEdit = function (table, lineData = {}, type) {
        var isEdit = !!lineData.id || type == 'edit'
        var defauleValue = isEdit ? lineData.hot : 0
        var imgTypeArray = []
        var imgTypeArray10 = []
        if (isEdit) {
            console.log(lineData.config)
            var jsonConfig = JSON.parse(lineData.config || "{}")
            if (lineData.dataType == 9) {
                imgTypeArray = jsonConfig.imgList || []
            }
            if (lineData.dataType == 10) {
                imgTypeArray10 = jsonConfig.imgList || []
            }
        }
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'jiakao-misc!info-flow-item/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler(form) {
                    var id = form.id && form.id.value
                    var groupId = form.groupId && form.groupId.value
                    var dataType = form.dataType && form.dataType.value
                    var dataId = form.dataId && form.dataId.value
                    var indexType = form.indexType && form.indexType.value
                    var forceIndex = form.forceIndex && form.forceIndex.value
                    var startTime = form.startTime && form.startTime.value
                    var endTime = form.endTime && form.endTime.value
                    var delaHot = $(form).item('delaHot-input')
                    var img0 = form.img0 && form.img0.value
                    var img1 = form.img1 && form.img1.value
                    console.log('img1', img1)
                    var img2 = form.img2 && form.img2.value
                    var img3 = form.img3 && form.img3.value
                    var url0 = form.url0 && form.url0.value
                    var url1 = form.url1 && form.url1.value
                    var url2 = form.url2 && form.url2.value
                    var url3 = form.url3 && form.url3.value


                    var reg = /^([1-9]|(1[0-5]))$/
                    if (indexType != 2 && dataType != 9 && !reg.test(forceIndex)) {
                        Widgets.dialog.alert('强插位置只能输入1~15的数字')
                        return
                    }
                    if (indexType != 2 && dataType == 9 && (forceIndex < 20 || forceIndex > 100)) {
                        Widgets.dialog.alert('强插位置可输入20~100的整数')
                        return
                    }
                    if (indexType != 2 && dataType != 9 && !new Date(startTime).getTime()) {
                        Widgets.dialog.alert('强插生效开始时间必填')
                        return

                    }
                    if (indexType != 2 && dataType != 9 && !new Date(endTime).getTime()) {
                        Widgets.dialog.alert('强插生效结束时间必填')
                        return

                    }
                    if (indexType != 2 && dataType != 9 && new Date(endTime).getTime() <= new Date(startTime).getTime()) {
                        Widgets.dialog.alert('结束时间必须大于开始时间')
                        return
                    }
                    if (groupId != 1 && indexType == 2) {
                        Widgets.dialog.alert('排序类型 不可选择【热度】')
                        return
                    }
                    var imgList = []
                    if (dataType == 9) {
                        indexType = 1
                        dataId = ''
                        startTime = ''
                        endTime = ''
                        imgList.push(
                            {
                                imgUrl: img1,
                                jumpUrl: url1
                            },
                            {
                                imgUrl: img2,
                                jumpUrl: url2
                            },
                            {
                                imgUrl: img3,
                                jumpUrl: url3
                            })


                    } else if (dataType == 10) {
                        indexType = 1
                        dataId = ''
                        imgList.push({
                            imgUrl: img0,
                            jumpUrl: url0
                        })
                    } else if (dataType == 8) {
                        dataId = ''
                    }
                    var config = {
                        dataId: dataId,
                        imgList: imgList
                    }

                    let params = {
                        id,
                        groupId,
                        dataType,
                        dataId: dataId,
                        indexType,
                        forceIndex: indexType == 2 ? '' : forceIndex,
                        startTime: indexType == 2 ? '' : startTime,
                        endTime: indexType == 2 ? '' : endTime,
                        deltaHot: indexType == 2 ? (delaHot.val() || '') : '',
                        cityCode: '000000',
                        config: JSON.stringify(config),

                    }
                    if (!isEdit) {
                        params.online = true

                    } else {
                        params.online = lineData.status == 1 ? true : false

                    }

                    return params

                },
                reverseParam: true
            },
            renderAfter: function (table, dom, data) {
                var dataType = dom.item('dataType')
                var delaHotValue = dom.item('delaHot-input')
                var dataId = dom.item('dataId-group')
                var hotConfig = dom.item('hotConfig-group')
                var startTime = dom.item('startTime-group')
                var endTime = dom.item('endTime-group')
                var count = dom.item('count')
                var indexType = dom.item('indexType-group')
                delaHotValue.on('input', function () {
                    var value = +$(this).val()
                    var indexOf = (value + '').indexOf('.')
                    if (indexOf !== -1) {
                        $(this).val($(this).val().substring(0, indexOf))
                    }
                    $(count).html(Number(defauleValue) + (value))
                })
                renderDom(lineData.dataType || '')
                var indexTypeOpt = function () {
                    let opt = dom.item('indexType').find('option[value=2]')
                    if ($(this).val() == 1) {
                        opt.attr('disabled', false)
                    } else {
                        opt.attr('disabled', true)
                    }
                }
                indexTypeOpt.bind(dom.item('groupId'))()
                dom.item('groupId').on('change', indexTypeOpt)
                dataType.on('change', function () {
                    renderDom($(this).val())
                })
                dom.item('forceIndex').on('input', function () {
                    var xiaoshudian = $(this).val().indexOf(".")
                    if (xiaoshudian !== -1) {
                        $(this).val($(this).val().substring(0, xiaoshudian))
                    }
                })
                dom.item('indexType').on('change', function () {
                    if ($(this).val() == 1) {
                        dom.item('forceIndex-group').css('display', 'block')
                        hotConfig.css('display', 'none')
                        startTime.css('display', 'block')
                        endTime.css('display', 'block')
                    } else {
                        dom.item('forceIndex-group').css('display', 'none')
                        hotConfig.css('display', 'block')
                        startTime.css('display', 'none')
                        endTime.css('display', 'none')
                    }
                })
                function renderHide() {
                    for (var i = 0; i <= 3; i++) {
                        dom.item('img' + i + '-group').css('display', 'none')
                        dom.item('url' + i + '-group').css('display', 'none')
                    }
                    dataId.css('display', 'none')
                    startTime.css('display', 'none')
                    endTime.css('display', 'none')
                    dom.item('forceIndex-group').css('display', 'none')
                    hotConfig.css('display', 'none')
                    indexType.css('display', 'none')
                }
                function renderType9() {
                    for (var i = 1; i <= 3; i++) {
                        dom.item('img' + i + '-group').css('display', 'block')
                        dom.item('url' + i + '-group').css('display', 'block')
                    }
                    dom.item('forceIndex-group').css('display', 'block')
                    dom.item('forceIndex').attr('placeholder', '请输入20~100的数字')
                }
                function renderType10() {
                    dom.item('forceIndex-group').css('display', 'block')
                    dom.item('forceIndex').attr('placeholder', '请输入1~15的数字')
                    dom.item('img0' + '-group').css('display', 'block')
                    dom.item('url0' + '-group').css('display', 'block')
                    startTime.css('display', 'block')
                    endTime.css('display', 'block')

                }
                function renderCommonType() {
                    if (dom.item('dataType').val() == 8) {
                        dataId.css('display', 'none')
                    } else {
                        dataId.css('display', 'block')
                    }
                    startTime.css('display', 'block')
                    endTime.css('display', 'block')
                    if (isEdit) {
                        indexType.css('display', 'block')
                    }
                    if (dom.item('indexType').val() == 2) {
                        hotConfig.css('display', 'block')
                        dom.item('forceIndex-group').css('display', 'none')
                        startTime.css('display', 'none')
                        endTime.css('display', 'none')
                    } else {
                        hotConfig.css('display', 'none')
                        dom.item('forceIndex-group').css('display', 'block')
                        startTime.css('display', 'block')
                        endTime.css('display', 'block')
                    }
                    dom.item('forceIndex').attr('placeholder', '请输入1~15的数字')
                }
                function renderDom(dataType) {
                    dom.item('dataId').val(lineData.dataId || '')
                    dom.item('forceIndex').val(lineData.forceIndex || '')
                    delaHotValue.val(lineData.deltaHot || '')
                    $(count).html(Number(defauleValue) + (lineData.deltaHot || 0))
                    if (lineData.startTime) {
                        dom.item('startTime').val(Utils.format.date((lineData.startTime), 'yyyy-MM-dd HH:mm:ss'))
                    } else {
                        dom.item('startTime').val('')
                    }
                    if (endTime) {
                        dom.item('endTime').val(Utils.format.date((lineData.endTime), 'yyyy-MM-dd HH:mm:ss'))
                    } else {
                        dom.item('endTime').val('')
                    }
                    renderHide()

                    if (dataType == 10) {
                        renderType10()
                    } else if (dataType == 9) {
                        renderType9()
                    } else {
                        renderCommonType()
                    }
                    if (isEdit && (dataType == 7 || dataType == 8)) {
                        dom.item('indexType').val(1)
                        dom.item('indexType').change()
                        dom.item('indexType').attr('disabled', 'disabled')
                    } else {
                        dom.item('indexType').removeAttr('disabled')
                    }
                }

            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '分组：',
                    dataIndex: 'groupId',
                    xtype: 'select',
                    check: 'required',
                    store: 'jiakao-misc!info-flow-group/data/list?limit=1000',
                    insert: [
                        { id: '', showGroup: '请选择分组' },
                    ],
                    index: {
                        key: 'id',
                        value: 'showGroup',
                    },
                    placeholder: '分组'
                },
                {
                    header: '信息流类型：',
                    dataIndex: 'dataType',
                    xtype: 'select',
                    store: Infoflow.infoflowType,
                    insert: [
                        { key: '', value: '请选择信息流类型' }
                    ],
                    placeholder: ''
                },
                {
                    header: '内容id',
                    dataIndex: 'dataId',
                    placeholder: '请输入对应的内容id',
                    xtype: 'text'
                },
                {
                    header: '图片：',
                    dataIndex: 'img0',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'img0',
                        uploadIndex: 'img0',
                        bucket: "exam-room",
                        isSingle: true,
                        value: imgTypeArray10[0] && imgTypeArray10[0].imgUrl,
                        placeholder: '请选择上传图片',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '跳转链接',
                    xtype: 'text',
                    dataIndex: 'url0',
                    value: imgTypeArray10[0] && imgTypeArray10[0].jumpUrl,
                    placeholder: '请输入url'
                },
                {
                    header: '图片1：',
                    dataIndex: 'img1',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'img1',
                        uploadIndex: 'img1',
                        bucket: "exam-room",
                        isSingle: true,
                        value: imgTypeArray[0] && imgTypeArray[0].imgUrl,
                        placeholder: '请选择上传图片',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '跳转链接1',
                    xtype: 'text',
                    dataIndex: 'url1',
                    value: imgTypeArray[0] && imgTypeArray[0].jumpUrl,
                    placeholder: '请输入url'
                },
                {
                    header: '图片2：',
                    dataIndex: 'img2',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'img2',
                        uploadIndex: 'img2',
                        bucket: "exam-room",
                        isSingle: true,
                        value: imgTypeArray[1] && imgTypeArray[1].imgUrl,
                        placeholder: '请选择上传图片',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '跳转链接2',
                    xtype: 'text',
                    value: imgTypeArray[1] && imgTypeArray[1].jumpUrl,
                    dataIndex: 'url2',
                    placeholder: '请输入url'
                },
                {
                    header: '图片3：',
                    dataIndex: 'img3',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'img3',
                        uploadIndex: 'img3',
                        bucket: "exam-room",
                        isSingle: true,
                        value: imgTypeArray[2] && imgTypeArray[2].imgUrl,
                        placeholder: '请选择上传图片',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '跳转链接3',
                    xtype: 'text',
                    value: imgTypeArray[2] && imgTypeArray[2].jumpUrl,
                    dataIndex: 'url3',
                    placeholder: '请输入url'
                },
                {
                    header: '排序类型',
                    dataIndex: 'indexType',
                    xtype: 'select',
                    store: [{ key: 1, value: '位置' }, { key: 2, value: '热度' }]
                },
                {
                    header: '强插位置：',
                    dataIndex: 'forceIndex',
                    xtype: 'number',
                    placeholder: '请输入1~15的数字',
                },
                {
                    header: '强插生效开始时间：',
                    dataIndex: 'startTime',
                    xtype: 'datetime',

                    placeholder: '强插生效开始时间：'
                },
                {
                    header: '强插生效结束时间：',
                    dataIndex: 'endTime',
                    xtype: 'datetime',

                    placeholder: '强插生效结束时间'
                },
                {
                    header: '热度：',
                    dataIndex: 'hotConfig',
                    xtype: function () {
                        return `<div>
                              <span style="margin-right:5px">${defauleValue}</span>
                              <span>+</span>
                              <input style="width:100px" type="number" data-item="delaHot-input"/>
                              <span style="margin-left:10px">=</span>
                              <span data-item="count"></span>
                          </div>`
                    },
                    placeholder: '热度'
                },
            ]
        }
        if (isEdit) {
            table.edit(lineData, config)
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Store([`jiakao-misc!info-flow-group/data/list?limit=1000`]).load().done((retData) => {
            const lineData = retData.data['info-flow-group'].data.list.data || [];
            renderTable(lineData)
        }).fail((err) => {
            renderTable([])
        })
        function renderTable(flowGroup) {
            Table({
                description: '推荐管理',
                title: '推荐管理',

                buttons: {
                    top: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            }
                        },
                        {
                            name: '添加',
                            class: 'primary',
                            click: function (table) {
                                addEdit(table)
                            }
                        },
                        {
                            name: '参数设置',
                            class: 'primary',
                            click: function (table) {
                                // 
                                Store([`jiakao-misc!info-flow-item/data/queryDivisor`]).load().done((retData) => {
                                    const value = retData.data['info-flow-item'].data.queryDivisor.data || {};
                                    renderTable(value)
                                }).fail((err) => {
                                    renderTable('')
                                })
                                function renderTable(value) {
                                    Table().add({
                                        title: '参数设置',
                                        width: 500,
                                        store: 'jiakao-misc!info-flow-item/data/divisor',
                                        success: function (obj, dialog) {
                                            dialog.close();
                                            table.render();
                                        },
                                        form: {
                                            submitHandler(form) {
                                                return true
                                            }
                                        },
                                        renderAfter: function (table, dom, data) {
                                            dom.item('divisor').on('input', function () {
                                                var xiaoshudian = $(this).val().indexOf(".")
                                                if (xiaoshudian !== -1) {
                                                    $(this).val($(this).val().substring(0, xiaoshudian + 3))
                                                }
                                            })
                                        },
                                        columns: [
                                            {
                                                header: '重力因子G：',
                                                dataIndex: 'divisor',
                                                xtype: 'number',
                                                value: value,
                                                placeholder: '重力因子G',
                                            },
                                        ]
                                    })
                                }

                            }
                        },
                        {
                            name: '内容权重设置',
                            class: 'default',
                            click: function (table) {
                                // 
                                Store([`jiakao-misc!info-flow-item/data/queryWeight`]).load().done((retData) => {
                                    const value = retData.data['info-flow-item'].data.queryWeight.data || {};
                                    renderTable(value)
                                }).fail((err) => {
                                    renderTable('')
                                })
                                function renderTable(value) {
                                    Table().add({
                                        title: '内容权重设置',
                                        width: 500,
                                        store: 'jiakao-misc!info-flow-item/data/weight',
                                        success: function (obj, dialog) {
                                            dialog.close();
                                            table.render();
                                        },
                                        form: {
                                            submitHandler(form) {
                                                return true
                                            }
                                        },
                                        columns: [
                                            {
                                                header: '帖子权重：',
                                                dataIndex: 'topicWeight',
                                                xtype: 'number',
                                                value: value.topicWeight,
                                                placeholder: '帖子权重',
                                            },
                                            {
                                                header: '考场权重：',
                                                dataIndex: 'examSceneWeight',
                                                xtype: 'number',
                                                value: value.examSceneWeight,
                                                placeholder: '考场权重',
                                            },
                                            {
                                                header: '项目视频权重：',
                                                dataIndex: 'projectVideoWeight',
                                                xtype: 'number',
                                                value: value.projectVideoWeight,
                                                placeholder: '项目视频权重',
                                            },
                                            {
                                                header: '教练带学权重：',
                                                dataIndex: 'coachCourseWeight',
                                                xtype: 'number',
                                                value: value.coachCourseWeight,
                                                placeholder: '教练带学权重',
                                            },
                                            {
                                                header: '短视频权重：',
                                                dataIndex: 'shortVideoWeight',
                                                xtype: 'number',
                                                value: value.shortVideoWeight,
                                                placeholder: '短视频权重',
                                            },
                                            {
                                                header: '直播权重：',
                                                dataIndex: 'liveWeight',
                                                xtype: 'number',
                                                value: value.liveWeight,
                                                placeholder: '直播权重',
                                            },
                                        ]
                                    })
                                }

                            }
                        }
                    ],
                    bottom: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            }
                        }
                    ]
                },
                search: [
                    {
                        dataIndex: 'groupId',
                        xtype: 'select',
                        store: 'jiakao-misc!info-flow-group/data/list?limit=1000',
                        value: flowGroup[0] && flowGroup[0].id || '',
                        // insert: [
                        //     { id: '', showGroup: '请选择分组' },
                        // ],
                        index: {
                            key: 'id',
                            value: 'showGroup',
                        },
                        placeholder: '分组'
                    },
                    {
                        dataIndex: 'dataType',
                        xtype: 'select',
                        store: [{ key: '', value: '请选择类型' }].concat(Infoflow.infoflowType),

                    },
                    {
                        dataIndex: 'status',
                        xtype: 'select',
                        store: [{ key: '', value: '请选择加权' }].concat(statusArray)
                    },
                    {
                        dataIndex: 'hidden',
                        xtype: 'select',
                        value: false,
                        store: [{ key: '', value: '请选择显隐' }, { key: true, value: '隐藏' }, { key: false, value: '显示' }],
                    }
                ],
                operations: [
                    {
                        name: '查看',
                        xtype: 'view',
                        width: 400,
                        class: 'success',
                        title: '查看',
                        render: function (name, arr, index) {
                            if (arr[index].id) {
                                return '查看'
                            }
                        },
                        store: 'jiakao-misc!info-flow-item/data/view',
                        columns: [
                            {
                                header: '#',
                                dataIndex: 'id'
                            },
                            {
                                header: '序号',
                                dataIndex: 'no',
                                width: 60
                            },
                            {
                                header: '信息流类型',
                                dataIndex: 'dataType',
                                render: function (data) {
                                    return Infoflow.infoflowMap[data]
                                }
                            },
                            {
                                header: '信息流id',
                                dataIndex: 'dataId'
                            },
                            {
                                header: '信息流标题',
                                dataIndex: 'config',
                                render: function (data) {
                                    var json = JSON.parse(data || '{}')
                                    return json.title
                                }
                            },
                            {
                                header: '强插位置',
                                dataIndex: 'forceIndex',
                                render: function (data) {
                                    return data || '-'
                                }
                            },
                            {
                                header: '开始时间',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'startTime'
                            },
                            {
                                header: '结束时间',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'endTime'
                            },
                            {
                                header: '人工加权',
                                dataIndex: 'status',
                                render: function (data) {
                                    return statusMap[data]
                                }
                            },
                            {
                                header: '显示状态',
                                dataIndex: 'hidden',
                                render: function (data) {
                                    if (data) {
                                        return '隐藏'
                                    } else {
                                        return '显示'
                                    }
                                }
                            },
                            {
                                header: '热度',
                                dataIndex: 'realHot'
                            },
                            {
                                header: '开始时间：',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'startTime'
                            },
                            {
                                header: '结束时间：',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'endTime'
                            },
                            {
                                header: '配置信息json：',
                                dataIndex: 'config'
                            },
                            {
                                header: 'deleted：',
                                render: function (data) {
                                    if (data) {
                                        return '是';
                                    } else {
                                        return '否';
                                    }
                                },
                                dataIndex: 'deleted'
                            },
                            {
                                header: '创建者id：',
                                dataIndex: 'createUserId'
                            },
                            {
                                header: '创建者姓名：',
                                dataIndex: 'createUserName'
                            },
                            {
                                header: '创建时间：',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'createTime'
                            },
                            {
                                header: '更改者id：',
                                dataIndex: 'updateUserId'
                            },
                            {
                                header: '更改者姓名：',
                                dataIndex: 'updateUserName'
                            },
                            {
                                header: '更改时间：',
                                render: function (data) {
                                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                },
                                dataIndex: 'updateTime'
                            }

                        ]
                    },
                    {
                        name: '编辑',
                        class: 'primary',
                        click: function (table, dom, lineData) {
                            addEdit(table, lineData, 'edit')
                        }
                    },
                    // {
                    //     name: '上线',
                    //     class: 'danger',
                    //     render: function (name, arr, index) {
                    //         if (arr[index].id) {
                    //             if (arr[index].status == 0) {
                    //                 return '上线';
                    //             } else {
                    //                 return '下线';
                    //             }
                    //         }

                    //     },
                    //     click: function (table, row, lineData) {
                    //         var names = lineData.status == 0 ? '上线' : '下线'
                    //         var online = lineData.status == 0 ? true : false
                    //         Widgets.dialog.confirm('确定' + names + '吗？', function (e, confirm) {
                    //             if (confirm) {
                    //                 Store(['jiakao-misc!info-flow-item/data/online']).load([{
                    //                     params: {
                    //                         id: lineData.id,
                    //                         online: online
                    //                     }
                    //                 }]).done(function () {
                    //                     table.render();
                    //                 }).fail(function (ret) {
                    //                     Widgets.dialog.alert(ret.message);
                    //                 })
                    //             }
                    //         })
                    //     }
                    // },
                    // {
                    //     name: '删除',
                    //     class: 'danger',
                    //     xtype: 'delete',
                    //     render: function (name, arr, index) {
                    //         if (arr[index].id) {
                    //             return '删除'
                    //         }
                    //     },
                    //     store: 'jiakao-misc!info-flow-item/data/delete'
                    // },
                    {
                        name: '隐藏',
                        class: 'warning',
                        render: function (name, arr, index) {
                            if (arr[index].hidden) {
                                return '显示';
                            } else {
                                return '隐藏';
                            }

                        },
                        click: function (table, row, lineData) {
                            Store(['jiakao-misc!info-flow-item/data/hidden']).save([{
                                params: {
                                    id: lineData.id,
                                    hidden: !lineData.hidden,
                                    groupId: lineData.groupId,
                                    dataType: lineData.dataType,
                                    dataId: lineData.dataId,
                                }
                            }]).done(function () {
                                table.render();
                            }).fail(function (ret) {
                                Widgets.dialog.alert(ret.message);
                            })
                        }
                    }
                ],
                columns: [
                    {
                        header: '序号',
                        dataIndex: 'no',
                        width: 60
                    },
                    {
                        header: '信息流类型',
                        dataIndex: 'dataType',
                        render: function (data) {
                            return Infoflow.infoflowMap[data]
                        }
                    },
                    {
                        header: '信息流id',
                        dataIndex: 'dataId'
                    },
                    {
                        header: '信息流标题',
                        dataIndex: 'config',
                        render: function (data, array, object) {
                            console.log(array, object)
                            var json = JSON.parse(data || '{}')
                            let html = ''
                            if (object.dataType == 9 || object.dataType == 10) {
                                var imgList = json.imgList || []
                                imgList && imgList.forEach((res) => {
                                    html += `<div style="width:330px;word-wrap:break-word;
word-break:break-all;" >
                                   <img style="width:100px;height:100px" src="${res.imgUrl}"/>
                                   <span>${res.jumpUrl}</span>
                                </div>`
                                })
                                if (object.dataId && (object.dataType == 1 || object.dataType == 7 || object.dataType == 5)) {
                                    return `<a>${html}</a>`
                                } else {
                                    return html
                                }


                            } else {

                                var newString = json.title && json.title.length > 50 ? json.title.substring(0, 50) + '...' : json.title
                                if (object.dataId && (object.dataType == 1 || object.dataType == 7 || object.dataType == 5)) {
                                    return `<a><div style="word-wrap:break-word;
word-break:break-all;">${newString || '-'}</div></a>`
                                } else {
                                    return `<div style="word-wrap:break-word;
word-break:break-all;">${newString || '-'}</div>`
                                }

                            }

                        },
                        click: function (table, dom, lineData) {
                            if (lineData.dataType == 7) {
                                var hashUrl = '#header=' + encodeURIComponent('saturn!main/aside6') + '&aside=' + encodeURIComponent('saturn!app/topic-vs/index/list')
                                window.open(window.j.root.saturn + '?dataId=' + lineData.dataId + hashUrl)
                            } else if (lineData.dataType == 1) {
                                var hashUrl = '#header=' + encodeURIComponent('saturn!main/aside') + '&aside=' + encodeURIComponent('saturn!app/topic/index/list')
                                window.open(window.j.root.saturn + '?dataId=' + lineData.dataId + hashUrl)
                            } else if (lineData.dataType == 5) {
                                var hashUrl = '#header=' + encodeURIComponent('saturn!main/aside1') + '&aside=' + encodeURIComponent('saturn!app/video/index/list')
                                window.open(window.j.root.shortVideo + '?dataId=' + lineData.dataId + hashUrl)
                            }

                        }
                    },
                    {
                        header: '排序类型',
                        dataIndex: 'indexType',
                        render: function (data) {
                            if (data == 2) {
                                return '热度'
                            } else if (data == 1) {
                                return '位置'
                            } else {
                                return ''
                            }
                        }
                    },
                    {
                        header: '强插位置',
                        dataIndex: 'forceIndex',
                        render: function (data) {
                            return data || '-'
                        }
                    },
                    {
                        header: '开始时间',
                        render: function (data) {
                            if (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            } else {
                                return '-'
                            }

                        },
                        dataIndex: 'startTime'
                    },
                    {
                        header: '结束时间',
                        render: function (data) {
                            if (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            } else {
                                return '-'
                            }
                        },
                        dataIndex: 'endTime'
                    },
                    {
                        header: '人工加权',
                        dataIndex: 'status',
                        render: function (data) {
                            return statusMap[data]
                        }
                    },
                    {
                        header: '显示状态',
                        dataIndex: 'hidden',
                        render: function (data) {
                            if (data) {
                                return '隐藏'
                            } else {
                                return '显示'
                            }
                        }
                    },
                    {
                        header: '热度',
                        dataIndex: 'realHot'
                    },
                    {
                        header: '操作人',
                        dataIndex: 'updateUserName'
                    },
                    {
                        header: '操作时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    }

                ]
            }, ['jiakao-misc!info-flow-item/data/list?status=1&hidden=false&groupId=' + (flowGroup[0] && flowGroup[0].id || '')], panel, null).render();
        }

    }

    return {
        list: list
    }

});