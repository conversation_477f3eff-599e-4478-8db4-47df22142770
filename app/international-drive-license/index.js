/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    var orderStatusMap = {
        0: '未支付',
        1: '已支付',
        2: '退款中',
        3: '已退款',
        10: '制作中',
        11: '已发货'
    }
    var orderStatusStore = [{
            key: '0',
            value: '未支付'
        }, {
            key: '1',
            value: '已支付'
        },
        {
            key: '2',
            value: '退款中'
        },
        {
            key: '3',
            value: '已退款'
        }, {
            key: "10",
            value: '制作中'
        }, {
            key: "11",
            value: '已发货'
        }
    ]
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!international-drive-license/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '驾照号：',
                    dataIndex: 'licenseNo',
                    xtype: 'text',
                    placeholder: '驾照号'
                },
                {
                    header: '住址：',
                    dataIndex: 'address',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '住址'
                },
                {
                    header: '中文姓名：',
                    dataIndex: 'userName',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '中文姓名'
                },
                {
                    header: '姓：',
                    dataIndex: 'firstName',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '姓'
                },
                {
                    header: '名：',
                    dataIndex: 'lastName',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '名'
                },
                {
                    header: '性别：',
                    dataIndex: 'gender',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '性别'
                },
                {
                    header: '出生日期：',
                    dataIndex: 'birthDay',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '出生日期'
                },
                {
                    header: '初次领证时间：',
                    dataIndex: 'issueDate',
                    xtype: 'date',
                    placeholder: '初次领证时间'
                },
                {
                    header: '准驾车型：',
                    dataIndex: 'vehicleType',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '准驾车型'
                },
                {
                    header: '有效开始时间：',
                    dataIndex: 'startDate',
                    xtype: 'date',
                    placeholder: '有效开始时间'
                },
                {
                    header: '有效截止时间：',
                    dataIndex: 'endDate',
                    xtype: 'date',
                    placeholder: '有效截止时间'
                },
                {
                    header: '有效期时长：',
                    dataIndex: 'endDateYears',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '有效期时长'
                },
                {
                    header: '发证地区：',
                    dataIndex: 'certificateAddress',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '发证地区'
                },
                {
                    header: '发证机构：',
                    dataIndex: 'certificateOrg',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '发证机构'
                },
                {
                    header: '收件人姓名：',
                    dataIndex: 'receiver',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '收件人姓名'
                },
                {
                    header: '手机号掩码：',
                    dataIndex: 'phoneMask',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '手机号掩码'
                },
                {
                    header: '手机号唯一码：',
                    dataIndex: 'phoneIdentifier',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '手机号唯一码'
                },
                {
                    header: '手机号加密数据：',
                    dataIndex: 'phoneEncrypted',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '手机号加密数据'
                },
                {
                    header: '收件所在地：',
                    dataIndex: 'receiveCity',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '收件所在地'
                },
                {
                    header: '收件详细地址：',
                    dataIndex: 'receiveAddress',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '收件详细地址'
                },
                {
                    header: '快递配送类型：',
                    dataIndex: 'receiveType',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '快递配送类型'
                },
                {
                    header: '订单号：',
                    dataIndex: 'orderNumber',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '订单号'
                },
                {
                    header: '订单状态：',
                    dataIndex: 'orderStatus',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '订单状态'
                },
                {
                    header: '创建人id：',
                    dataIndex: 'createUserId',
                    xtype: 'text',
                    placeholder: '创建人id'
                },
                {
                    header: 'createUserName：',
                    dataIndex: 'createUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'createUserName'
                },
                {
                    header: '更新时间：',
                    dataIndex: 'updateTime',
                    xtype: 'date',
                    placeholder: '更新时间'
                },
                {
                    header: '更新人id：',
                    dataIndex: 'updateUserId',
                    xtype: 'text',
                    placeholder: '更新人id'
                },
                {
                    header: 'updateUserName：',
                    dataIndex: 'updateUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'updateUserName'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '国际驾照列表',
            title: '国际驾照列表',
            search: [{
                dataIndex: 'orderNumber',
                placeholder: '订单号',
                xtype: 'text'

            }, {
                dataIndex: 'userName',
                placeholder: '用户名',
                xtype: 'text'


                },
                // {
                // dataIndex: 'phone',
                // placeholder: '手机号',
                // xtype: 'text'


                // },
                {
                dataIndex: 'orderStatus',
                xtype: 'select',
                store: [{key:'',value:'订单状态'},...orderStatusStore]


            }, {
                xtype: 'text',
                placeholder: '对接方',
                dataIndex: 'businessName'
            }, {
                dataIndex: 'handle',

                xtype: 'select',
                store: [{
                    key: '',
                    value: '处理状态'
                }, {
                    key: 'true',
                    value: '已处理'
                }, {
                    key: 'false',
                    value: '未处理'
                }]


            }],
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        },
                    },
                    {
                        name: '批量处理',
                        class: 'primary',
                        click: function (table, dom, config, selectedArr) {
                            //  console.log(selectedArr.join(','));
                            if (selectedArr.length > 0) {


                                Widgets.dialog.confirm('确认处理吗？', function (ev, status) {
                                    if (status) {
                                        Store(['jiakao-misc!international-drive-license/data/handleBatch?ids=' + selectedArr.join(',')], [{
                                            aliases: 'list'
                                        }]).save().done(function (store) {}).fail(function () {});
                                    } else {}
                                })
                            } else {
                                Widgets.dialog.alert('请选择数据')
                            }

                        },
                    },
                    // {
                    //     name: '添加',
                    //     class: 'primary',
                    //     click: add
                    // }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            selector: {
                dataIndex: 'id',



            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!international-drive-license/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '驾照号：',
                            dataIndex: 'licenseNo'
                        },
                        {
                            header: '住址：',
                            dataIndex: 'address'
                        },
                        {
                            header: '中文姓名：',
                            dataIndex: 'userName'
                        },
                        {
                            header: '姓：',
                            dataIndex: 'firstName'
                        },
                        {
                            header: '名：',
                            dataIndex: 'lastName'
                        },
                        {
                            header: '性别：',
                            dataIndex: 'gender'
                        },
                        {
                            header: '出生日期：',
                            dataIndex: 'birthDay'
                        },
                        {
                            header: '初次领证时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'issueDate'
                        },
                        {
                            header: '准驾车型：',
                            dataIndex: 'vehicleType'
                        },
                        {
                            header: '有效开始时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'startDate'
                        },
                        {
                            header: '有效截止时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'endDate'
                        },
                        {
                            header: '有效期时长：',
                            dataIndex: 'endDateYears'
                        },
                        {
                            header: '发证地区：',
                            dataIndex: 'certificateAddress'
                        },
                        {
                            header: '发证机构：',
                            dataIndex: 'certificateOrg'
                        },
                        {
                            header: '收件人姓名：',
                            dataIndex: 'receiver'
                        },
                        {
                            header: '手机号掩码：',
                            dataIndex: 'phoneMask'
                        },

                        {
                            header: '收件所在地：',
                            dataIndex: 'receiveCity'
                        },
                        {
                            header: '收件详细地址：',
                            dataIndex: 'receiveAddressMask'
                        },
                        {
                            header: '快递配送类型：',
                            dataIndex: 'receiveType'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber'
                        },
                        {
                            header: '支付流水号：',
                            dataIndex: 'payNo'
                        },
                        
                        {
                            header: '订单状态：',
                            dataIndex: 'orderStatus'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!international-drive-license/data/view',
                        save: 'jiakao-misc!international-drive-license/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '驾照号：',
                        
                            dataIndex: 'licenseNo',
                            xtype: 'text',
                            readonly: true,
                            placeholder: '驾照号'
                        },
                        {
                            header: '住址：',
                            dataIndex: 'address',
                            xtype: 'text',
                            maxlength: 128,
                            placeholder: '住址'
                        },
                        {
                            header: '中文姓名：',
                            dataIndex: 'userName',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '中文姓名'
                        },
                        {
                            header: '姓：',
                            dataIndex: 'firstName',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '姓'
                        },
                        {
                            header: '名：',
                            dataIndex: 'lastName',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '名'
                        },
                        {
                            header: '性别：',
                            dataIndex: 'gender',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '性别'
                        },
                        {
                            header: '出生日期：',
                            dataIndex: 'birthDay',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '出生日期'
                        },
                        {
                            header: '初次领证时间：',
                            dataIndex: 'issueDate',
                            xtype: 'date',
                            placeholder: '初次领证时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            }
                        },
                        {
                            header: '准驾车型：',
                            dataIndex: 'vehicleType',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '准驾车型'
                        },
                        {
                            header: '有效开始时间：',
                            dataIndex: 'startDate',
                            xtype: 'date',
                            placeholder: '有效开始时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            }
                        },
                        {
                            header: '有效截止时间：',
                            dataIndex: 'endDate',
                            xtype: 'date',
                            placeholder: '有效截止时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            }
                        },
                        {
                            header: '有效期时长：',
                            dataIndex: 'endDateYears',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '有效期时长'
                        },
                        {
                            header: '发证地区：',
                            dataIndex: 'certificateAddress',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '发证地区'
                        },
                        {
                            header: '发证机构：',
                            dataIndex: 'certificateOrg',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '发证机构'
                        },
                        {
                            header: '收件人姓名：',
                            dataIndex: 'receiver',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '收件人姓名'
                        },

                        {
                            header: '收件所在地：',
                            dataIndex: 'receiveCity',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '收件所在地'
                        },
                        {
                            header: '收件详细地址：',
                            dataIndex: 'receiveAddress',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '收件详细地址'
                        },
                        {
                            header: '快递配送类型：',
                            dataIndex: 'receiveType',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '快递配送类型'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '订单号'
                        },
                        {
                            header: '订单状态：',
                            dataIndex: 'orderStatus',
                            xtype: 'select',
                            store: orderStatusStore

                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!international-drive-license/data/delete'
                }, {
                    name: '查看电话',
                    class: 'primary',
                    click: function (table, rows, lineData) {
                        Store(['jiakao-misc!international-drive-license/data/viewPhone?id=' + lineData.id], [{
                            aliases: 'list'
                        }]).load().done(function (store) {
                            Widgets.dialog.alert(store.data.list.data)
                        }).fail(function () {});
                    }
                }

            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '驾照号',
                    dataIndex: 'licenseNo'
                },
                {
                    header: '住址',
                    dataIndex: 'addressMask'
                },
                {
                    header: '中文姓名',
                    dataIndex: 'userName'
                },
                {
                    header: '姓',
                    dataIndex: 'firstName'
                },
                {
                    header: '名',
                    dataIndex: 'lastName'
                },
                {
                    header: '性别',
                    dataIndex: 'gender'
                },
                {
                    header: '出生日期',
                    dataIndex: 'birthDay'
                },
                {
                    header: '驾照图片',
                    dataIndex: 'image',


                },
                {
                    header: '初次领证时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'issueDate'
                },
                {
                    header: '准驾车型',
                    dataIndex: 'vehicleType'
                },
                {
                    header: '对接方',
                    dataIndex: 'businessName'
                },
                {
                    header: '快递单号',
                    dataIndex: 'expressNo'
                },
                {
                    header: '识别类型',
                    dataIndex: 'recognizeType',
                    render: function (data) {
                        const type = {
                            auto: '自动识别',
                            skip: '未识别'
                        }
                        return type[data]
                    }
                },
                {
                    header: '有效开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'startDate'
                },
                {
                    header: '有效截止时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'endDate'
                },
                {
                    header: '有效期时长',
                    dataIndex: 'endDateYears'
                },
                {
                    header: '发证地区',
                    dataIndex: 'certificateAddress'
                },
                {
                    header: '发证机构',
                    dataIndex: 'certificateOrg'
                },
                {
                    header: '收件人姓名',
                    dataIndex: 'receiver'
                },
                {
                    header: '手机号掩码',
                    dataIndex: 'phoneMask'
                },
                //  {
                //      header: '手机号唯一码',
                //      dataIndex: 'phoneIdentifier'
                //  },
                //  {
                //      header: '手机号加密数据',
                //      dataIndex: 'phoneEncrypted'
                //  },
                {
                    header: '收件所在地',
                    dataIndex: 'receiveCity'
                },
                {
                    header: '收件详细地址',
                    dataIndex: 'receiveAddressMask'
                },
                {
                    header: '快递配送类型',
                    dataIndex: 'receiveType'
                },
                {
                    header: '订单号',
                    dataIndex: 'orderNumber'
                },
                {
                    header: '支付流水号',
                    dataIndex: 'payNo'
                },
                {
                    header: '错误日志',
                    dataIndex: 'errorLog'
                },
                {
                    header: '订单状态',
                    dataIndex: 'orderStatus',
                    render: function (data) {
                        return orderStatusMap[data]
                    }
                },
                {
                    dataIndex: 'handle',
                    header: '处理状态',
                    render: function (data) {
                        return data ? '已处理' : '未处理'
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!international-drive-license/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});