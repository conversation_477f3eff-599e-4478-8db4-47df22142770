/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {


    var typeArr = [{
        key: 0,
        value: '智慧驾校硬件'
    }];

    var typeNameMap = {
        0: '智慧驾校硬件'
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!intro-video/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '智慧驾校硬件：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: typeArr,
                    placeholder: '智慧驾校硬件'
                },
                {
                    header: '视频key：',
                    dataIndex: 'videoId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '视频key'
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '标题'
                },
                {
                    header: '封面图：',
                    dataIndex: 'cover',
                    xtype: Plugin(
                        "jiakao-misc!upload",
                        {
                            dataIndex: "cover",
                            uploadIndex: "cover",
                            bucket: "exam-room",
                            isSingle: true,
                            placeholder: "请选择上传封面图",
                            url: "simple-upload3://upload/file.htm",
                        },
                        function () {
                            console.log(arguments);
                        }
                    )
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'textarea',
                    maxlength: 512,
                    placeholder: '描述'
                },
                // {
                //     header: '视频详情json：',
                //     dataIndex: 'videoDetail',
                //     xtype: 'textarea',
                //     maxlength: 1024,
                //     placeholder: '视频详情json'
                // },
                {
                    header: '排序：',
                    dataIndex: 'sort',
                    xtype: 'number',
                    placeholder: '排序'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '介绍视频列表',
            title: '介绍视频列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!intro-video/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '智慧驾校硬件：',
                            dataIndex: 'type'
                        },
                        {
                            header: '视频key：',
                            dataIndex: 'videoId'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title'
                        },
                        {
                            header: '封面图：',
                            dataIndex: 'cover',
                            render: function (data) {
                                return '<img src="' + data + '" />';
                            }
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '视频详情json：',
                            dataIndex: 'videoDetail'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sort'
                        },
                        {
                            header: '创建人ID：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人ID：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!intro-video/data/view',
                        save: 'jiakao-misc!intro-video/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '智慧驾校硬件：',
                            dataIndex: 'type',
                            xtype: 'select',
                            store: typeArr,
                            placeholder: '智慧驾校硬件'
                        },
                        {
                            header: '视频key：',
                            dataIndex: 'videoId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '视频key'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '标题'
                        },
                        {
                            header: '封面图：',
                            dataIndex: 'cover',
                            xtype: Plugin(
                                "jiakao-misc!upload",
                                {
                                    dataIndex: "cover",
                                    uploadIndex: "cover",
                                    bucket: "exam-room",
                                    isSingle: true,
                                    placeholder: "请选择上传封面图",
                                    url: "simple-upload3://upload/file.htm",
                                },
                                function () {
                                    console.log(arguments);
                                }
                            )
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'textarea',
                            maxlength: 512,
                            placeholder: '描述'
                        },
                        // {
                        //     header: '视频详情json：',
                        //     dataIndex: 'videoDetail',
                        //     xtype: 'textarea',
                        //     maxlength: 1024,
                        //     placeholder: '视频详情json'
                        // },
                        {
                            header: '排序：',
                            dataIndex: 'sort',
                            xtype: 'number',
                            placeholder: '排序'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!intro-video/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '智慧驾校硬件',
                    dataIndex: 'type',
                    render: function (data) {
                        return typeNameMap[data];
                    }
                },
                {
                    header: '视频key',
                    dataIndex: 'videoId'
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '封面图',
                    dataIndex: 'cover',
                    render: function (data) {
                        return '<img style="width: 200px;" src="' + data + '" />';
                    }
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                // {
                //     header: '视频详情json',
                //     dataIndex: 'videoDetail'
                // },
                {
                    header: '排序',
                    dataIndex: 'sort'
                },
                {
                    header: '创建人：',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改人：',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!intro-video/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});