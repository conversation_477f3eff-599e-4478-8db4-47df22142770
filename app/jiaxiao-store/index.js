/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!plugin/select-district/district3', 'https://webapi.amap.com/maps?v=2.0&key=5d4e2bfebc376849b0fd27a963934135'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, District3, AMap) {
    var disableMap = {
        true: '禁用',
        false: '启用'
    }
    var zxCitys = ['120000', '110000', '500000', '310000']
    var getAllCity = function () {
        var list = District3.list
        var city = []

        for (var i = 0; i < list.length; ++i) {
            if (
                list[i].cities &&
                list[i].code != zxCitys[0] &&
                list[i].code != zxCitys[1] &&
                list[i].code != zxCitys[2] &&
                list[i].code != zxCitys[3]
            ) {
                var data = list[i].cities
                city.push({
                    key: list[i].code,
                    value: list[i].name,
                    search: list[i].name,
                })
                for (var j = 0; j < data.length; ++j) {
                    city.push({
                        key: data[j].code,
                        value: data[j].name,
                        search: data[j].name,
                    })
                }
            }
        }

        city.unshift({
            key: '310000',
            value: '上海',
            search: '上海',
        })
        city.unshift({
            key: '120000',
            value: '天津',
            search: '天津',
        })
        city.unshift({
            key: '500000',
            value: '重庆',
            search: '重庆',
        })
        city.unshift({
            key: '110000',
            value: '北京',
            search: '北京',
        })
        city.unshift({
            key: '000000',
            value: '全国',
            search: '全国',
        })
        return city
    }
    function loadMap(getCalback) {
        Widgets.dialog.html('选择经纬度', {
            width: 1200,
            height: 700,
            zIndex: 600
        }).done(function (dialog) {

            console.log()
            var dialogObject = dialog
            var ele = $(dialog.body)[0]
            $(ele).html(`
             <input style="width:250px;height:40px;position:absolute;top:0px;z-index:1004" placeholder="请输入地址" id="tipinput"  />
             <div id="panel" style="position: absolute;
            background-color: white;
            max-height: 70%;
            overflow-y: auto;
            top: 20px;
            right: 10px;
            width: 300px;z-index:1001"></div>
             <div id="showMap-store-jiaxiao" style="height:700px;z-index:1000"></div>
             
            `)
            window.AMap = AMap
            setTimeout(() => {
                const map = new AMap.Map('showMap-store-jiaxiao', {
                    resizeEnable: false,
                    zoom: 13, // 初始化地图层级
                });
                console.log($('#tipinput')[0])
                var autoOptions = {
                    input: $('#tipinput')[0]
                };
                AMap.plugin([
                    'AMap.ToolBar',
                ], function () {
                    // 在图面添加工具条控件, 工具条控件只有缩放功能
                    map.addControl(new AMap.ToolBar());
                });
                AMap.plugin(['AMap.AutoComplete', 'AMap.PlaceSearch'], function () {
                    var auto = new AMap.AutoComplete(autoOptions);
                    console.log('auto', auto)
                    var placeSearch = new AMap.PlaceSearch({
                        pageSize: 8,
                        pageIndex: 1,
                        city: "010",
                        citylimit: true,
                        map: map,
                        panel: "panel",
                        autoFitView: true
                    });
                    auto.on("select", select);
                    function select(e) {
                        placeSearch.setCity(e.poi.adcode);
                        placeSearch.search(e.poi.name);

                    }
                    placeSearch.on('markerClick', function (e) {
                        console.log('e', e)
                        getAddressName(e.
                            event.lnglat.lng, e.
                                event.lnglat.lat)
                    });

                });
                function getAddressName(lng, lat) {
                    var formattedAddress = ''
                    AMap.plugin('AMap.Geocoder', function () {
                        var geocoder = new AMap.Geocoder({
                            city: '010'
                        })
                        geocoder.getAddress([lng, lat], function (status, result) {
                            if (status === 'complete' && result.info === 'OK') {
                                formattedAddress = result.regeocode.formattedAddress
                                getCalback({
                                    lng: lng,
                                    lat: lat,
                                    address: formattedAddress
                                })
                                dialogObject.close();
                            } else {
                                formattedAddress = ''
                                getCalback({
                                    lng: lng,
                                    lat: lat,
                                    address: formattedAddress
                                })
                                dialogObject.close();
                            }
                        })

                    })

                }
                map.on('click', function (e) {
                    getAddressName(e.lnglat.getLng(), e.lnglat.getLat())

                })
            }, 500)
        })
    }
    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var storeImg = lineData.storeImg?.split(',')
        var storeFeatureImg = lineData.storeFeatureImg?.split(',')
        var storeImgArray = []
        var storeFeatureImgArray = []
        storeImg && storeImg.forEach((res) => {
            if (res) {
                storeImgArray.push({ url: res })
            }
        })
        storeFeatureImg && storeFeatureImg.forEach((res) => {
            if (res) {
                storeFeatureImgArray.push({ url: res })
            }
        })
        lineData.newstoreImg = JSON.stringify(storeImgArray)
        lineData.newstoreFeatureImg = JSON.stringify(storeFeatureImgArray)
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 800,
            store: 'jiakao-misc!jiaxiao-store/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form, fun) {
                    var storeFeatureImg = JSON.parse(form.newstoreFeatureImg.value || '[]')
                    var storeImg = JSON.parse(form.newstoreImg.value || '[]')
                    var latitude = form.latitude.value
                    var longitude = form.longitude.value
                    var arr = []
                    var arr1 = []
                    storeFeatureImg && storeFeatureImg.forEach((res) => {
                        arr.push(res.url)
                    })
                    storeImg && storeImg.forEach((res) => {
                        arr1.push(res.url)
                    })
                    if (!form.cityCode.value) {
                        Widgets.dialog.alert('请选择城市')
                        return
                    }
                    if (!latitude || latitude == 0) {
                        Widgets.dialog.alert('请选择经纬度')
                        return
                    }
                    if (arr1 && arr1.length <= 0) {
                        Widgets.dialog.alert('门店图必传')
                        return
                    }
                    if (arr && arr.length <= 0) {
                        Widgets.dialog.alert('门店特色图片必传')
                        return
                    }
                    if (!form.storeIntro.value) {
                        Widgets.dialog.alert('门店介绍必传')
                        return
                    }
                    return {
                        storeFeatureImg: arr.join(','),
                        storeImg: arr1.join(','),
                        latitude: latitude,
                        longitude: longitude,
                        disable:isEdit?lineData.disable:false
                    }
                }
            },
            renderAfter: function (table, dom) {
                var textGroup = dom.item('storeIntro-group');
                var option = {
                    imageScaleEnabled: true,
                    initialFrameHeight: 400, //初始化编辑器高度，默认320
                    initialFrameWidth: 668, //初始化编辑器宽度，默认1000
                    maximumWords: 10000,//可输入字数限制
                    clearFontFamily: false, //去掉所有的内嵌字体，使用编辑器默认的字体
                    clearFontSize: false, //去掉所有的内嵌字号，使用编辑器默认的字号
                    pasteplain: false, //是否默认为纯文本粘贴。false为不使用纯文本粘贴，true为使用纯文本粘贴
                    zIndex: 2018
                };
                var textareaId = new Date().getTime() + '_' + Math.random().toString(16).substring(2);
                var textarea = textGroup.find('[name=storeIntro]');
                textarea.attr('id', textareaId);
                setTimeout(function () {
                    var ueObj = UE.getEditor(textareaId, option);
                    setTimeout(function () {
                        var editorWrap = textGroup.find(".form-control").eq(0);
                        editorWrap.css({ "height": "auto", "border-radius": "0", "border": "0", "padding": "0" });
                        editorWrap.find(".edui-editor").css({ "width": "100%" });
                        editorWrap.find(".edui-editor-iframeholder").css({ "width": "100%" });
                    }, 200)
                }, 500)
                var getLngLat = dom.item('getLngLat')
                getLngLat.on('click', function (data) {
                    loadMap(function (data) {
                        dom.item('longitude').val(data.lng || '')
                        dom.item('latitude').val(data.lat || ''),
                            dom.item('storeAddress').val(data.address || '')
                    })
                })
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '城市code：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('simple!auto-prompt', {
                        store: getAllCity(),
                        placeholder: '选择城市',
                        dataIndex: 'cityCode',
                        isMulti: false,
                        defaultVal: false
                    }, function () {

                    }),
                    check: 'required',
                    placeholder: '城市code'
                },
                {
                    header: '经纬度：',
                    dataIndex: 'lnglat',
                    xtype: function () {
                        return '<a data-item="getLngLat">获取经纬度</a>'
                    },
                    check: 'required',
                    placeholder: ' 纬度'
                },
                {
                    header: ' 经度：',
                    dataIndex: 'longitude',
                    xtype: 'text',
                    check: 'required',
                    disabled: true
                },
                {
                    header: '维度：',
                    dataIndex: 'latitude',
                    xtype: 'text',
                    check: 'required',
                    disabled: true
                },
                {
                    header: '门店名称：',
                    dataIndex: 'storeName',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '门店名称'
                },
                {
                    header: '门店地址：',
                    dataIndex: 'storeAddress',
                    xtype: 'textarea',
                    check: 'required',
                    placeholder: '门店地址'
                },
                {
                    header: '可见距离(米)：',
                    dataIndex: 'visibleDistance',
                    xtype: 'text',
                    placeholder: '可见距离(米)'
                },
                {
                    header: '封面图：',
                    dataIndex: 'storeCover',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'storeCover',
                        uploadIndex: 'storeCover',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                    placeholder: '封面图'
                },
                {
                    header: '门店图：',
                    dataIndex: 'newstoreImg',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'newstoreImg',
                        uploadIndex: 'newstoreImg',
                        bucket: "exam-room",
                        isSingle: false,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                    placeholder: '门店图'
                },

                {
                    header: '门店介绍富文本：',
                    dataIndex: 'storeIntro',
                    xtype: 'textarea',
                    placeholder: '门店介绍富文本'
                },
                {
                    header: '门店特色图片：',
                    dataIndex: 'newstoreFeatureImg',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'newstoreFeatureImg',
                        uploadIndex: 'newstoreFeatureImg',
                        bucket: "exam-room",
                        isSingle: false,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                    placeholder: '门店特色图片'
                },
                {
                    header: '微信跳转地址：',
                    dataIndex: 'weixinForwardUrl',
                    xtype: 'textarea',
                    placeholder: ' 微信跳转地址'
                },
                {
                    header: '门店电话：',
                    dataIndex: 'storePhone',
                    xtype: 'text',
                    placeholder: '门店电话'
                },
                {
                    header: ' 驾校id：',
                    dataIndex: 'jiaxiaoId',
                    xtype: 'text',
                    placeholder: ' 驾校id'
                },
            ]
        }
        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Table({
            description: '驾校门店列表',
            title: '驾校门店列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [{
                dataIndex: 'storeName',
                xtype: 'text',
                placeholder: '门店名称'
            }],
            operations: [
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '启用',
                    class: 'warning',
                    render: function (name, arr, i) {
                        return arr[i].disable == true ? '启用' : '禁用';
                    },
                    click: function (table, dom, lineData) {
                        var disableName = lineData.disable == true ? '启用' : '禁用'
                        var disable = lineData.disable == true ? false : true
                        Widgets.dialog.confirm('确认' + disableName + '吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!jiaxiao-store/data/disable?id=' + lineData.id + '&disable=' + disable]).load().done(data => {
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!jiaxiao-store/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                }, {
                    header: '门店名称',
                    dataIndex: 'storeName',
                    render: function (data) {
                        return `<div style="  word-break: break-all;
                word-wrap: break-word;
                white-space: pre-wrap;">${data}</div>`
                    }
                },
                {
                    header: '门店地址',
                    dataIndex: 'storeAddress',
                    render: function (data) {
                        return `<div style="  word-break: break-all;
                word-wrap: break-word;
                white-space: pre-wrap;">${data}</div>`
                    }
                },
                {
                    header: '可见距离(米)',
                    dataIndex: 'visibleDistance',
                },
                {
                    header: '门店图',
                    dataIndex: 'storeImg',
                    render: function (data) {
                        if (data) {
                            return '<a>查看图片</a>'
                        }

                    },
                    click: function (table, dom, lineData) {
                        var strArray = lineData.storeImg.split(',');
                        var str = ''
                        try {
                            for (var i = 0; i < strArray.length; i++) {
                                str += '<img style="width: 300px;float:left;" src="' + strArray[i] + '"/>';
                            }
                        } catch (e) {
                            console.error(e);
                        }
                        Widgets.dialog.html('门店图', {
                            width: 600,
                        })
                            .done(function (dialog) {
                                $(dialog.body).html(str);
                            });

                    }
                },

                {
                    header: '城市名',
                    dataIndex: 'cityName'
                },
                {
                    header: '城市code',
                    dataIndex: 'cityCode'
                },
                {
                    header: '纬度',
                    dataIndex: 'latitude'
                },
                {
                    header: '经度',
                    dataIndex: 'longitude'
                },
                {
                    header: '封面图',
                    dataIndex: 'storeCover',
                    render: function (data) {
                        if (data) {
                            return '<a>查看图片</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('封面图', { width: 600 }).done(function (dialog) {
                            $(dialog.body).html('<img src="' + lineData.storeCover + '"></img>')
                        })
                    }
                },
                {
                    header: '门店介绍富文本',
                    dataIndex: 'storeIntro',
                    render: function (data) {
                        return `<div style="  word-break: break-all;
                word-wrap: break-word;
                white-space: pre-wrap;">${data}</div>`
                    }
                },
                {
                    header: '门店特色图片',
                    dataIndex: 'storeFeatureImg',
                    render: function (data) {
                        if (data) {
                            return '<a>查看图片</a>'
                        }

                    },
                    click: function (table, dom, lineData) {
                        var strArray = lineData.storeFeatureImg.split(',');
                        var str = ''
                        try {
                            for (var i = 0; i < strArray.length; i++) {
                                str += '<img style="width: 300px;float:left;" src="' + strArray[i] + '"/>';
                            }
                        } catch (e) {
                            console.error(e);
                        }
                        Widgets.dialog.html('门店特色图片', {
                            width: 600,
                        })
                            .done(function (dialog) {
                                $(dialog.body).html(str);
                            });

                    }
                },
                {
                    header: ' 微信跳转地址',
                    dataIndex: 'weixinForwardUrl',
                    render: function (data) {
                        if (data) {
                            return '<a>查看地址</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log('lineData.weixinForwardUrl', lineData.weixinForwardUrl)
                        Widgets.dialog.html('微信跳转地址', { width: 600 }).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.weixinForwardUrl + '</div>')
                        })
                    }
                },
                {
                    header: ' 驾校id',
                    dataIndex: 'jiaxiaoId'
                },
                {
                    header: '是否启用',
                    dataIndex: 'disable',
                    render: function (data) {
                        if (data) {
                            return '禁用'
                        } else {
                            return '启用'
                        }
                    }
                },
                {
                    header: ' 创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: ' 创建用户名称',
                    dataIndex: 'createUserName'
                },
                {
                    header: ' 更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: ' 更新用户名称',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!jiaxiao-store/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});