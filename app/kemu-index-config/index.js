/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!plugin/select-district/district3',
    'jiakao-misc!app/operation-config3/index',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, District3, OpConfig) {
    let imageDto = {

    }

    
    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

 
    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    function renderCodeFn(dom,data) {
        setTimeout(() => {
            const value = JSON.parse(data.value);

            dom.item('moreActionUrl').val(value.moreActionUrl);

            Plugin('jiakao-misc!group3', {
                dataIndex: 'itemList',
                target: dom.item('itemList-group').find('div[class=col-sm-8]'),
                value:JSON.stringify(value.itemList)
            }, function (plugin, value) {

            }).render();
           
        }, 200)
    }
 



    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id;

        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: `jiakao-misc!operation-config6/data/${isEdit ? 'update':'insert'}?bizType=home_page_difficult_point&code=home_page_difficult_point_lesson`,
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    let itemList = $('input[name=itemList]').val();
                    itemList = itemList && JSON.parse(itemList);
                    var moreActionUrl = $(form).find('#moreActionUrl').val();
                    moreActionUrl = moreActionUrl.replaceAll(' ', '');
                    const value = JSON.stringify({
                        itemList,
                        moreActionUrl:moreActionUrl || null
                    })

                    return {
                        value
                    };
                },
            },
            renderAfter: function (table, dom, data) {
                if (data.data) {
                    renderCodeFn(dom,data.data)
                }
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '名称',
                    dataIndex: 'name',
                    xtype:'text'
                },
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '场景code：',
                    dataIndex: 'sceneCode',
                    xtype: 'checkbox',
                    store: Constants.senceStore,
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store:carTypeArr,
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store: Constants.kemuStore.slice(1).concat([{key:'5',value:'拿本'}]),
                    placeholder: '科目'
                },
                {
                    header: '更多的跳转连接',
                    dataIndex: 'moreActionUrl',
                    xtype: 'text',
                    placeholder:'更多的跳转连接'
                },
                {
                    header: '课程配置',
                    dataIndex: 'itemList',
                    xtype: Plugin('jiakao-misc!group3', {
                        dataIndex: 'itemList'
                    }, function (plugin, value) {
        
                    })
                },
            
              
            ]
        }

        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }
    }

    var list = function (panel) {
        Table({
            description: '科目首页课程配置',
            title: '科目首页课程配置',
            search: [],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class:'warning',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!operation-config6/data/delete'
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        OpConfig.editPersonas(table, lineData)
                    },
                },
                {
                    class: 'danger',
                    render: function (name, arr, index) {
                        const status = arr[index].status
                        if (status == 0) {
                            return '测试发布';
                        } else if (status == 1) {
                            return '发布';
                        }else if (status == 2) {
                            return '下线';
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log(lineData, 'lineData');
                        const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1;
                        console.log(status, 'status');
                        let title = lineData.status == 1 ? '确定发布吗?' :   lineData.status == 2 ? '确定下线吗?' : '确定测试发布吗?'
                        Widgets.dialog.confirm(title, function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!operation-config6/data/update']).save([{
                                    params: {
                                        id: lineData.id,
                                        status
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
            ],
            columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '名称',
                        dataIndex: 'name'
                    },
                    {
                        header: '配置说明',
                        dataIndex: 'remark'
                    },
                    {
                        header: '配置key',
                        dataIndex: 'code'
                },
                       
                {
                    header: '场景',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.senceMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'patternCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.editionMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                if(data[i]=='5'){
                                    strArr.push('拿本')
                                }else{
                                    strArr.push(Constants.kemuMap[data[i]])
                                }
                                
                            }
                            return strArr.join(',');
                        }
                    }
                },
                
                    {
                        header: '配置内容',
                        dataIndex: 'value',
                        render: function () {
                            return `<a>点击查看</a>`
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('配置内容', {}).done(function (dialog) {
                                var data = lineData.value && JSON.stringify(JSON.parse(lineData.value), null, 4)
                                $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data, arr, i) {
                        return statusMap[data]
                    }
                },
                    {
                        header: '创建人',
                        dataIndex: 'createUserName'
                    },
                    {
                        header: '创建时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: '修改人',
                        dataIndex: 'updateUserName'
                    },
                    {
                        header: '修改时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    }

            ]
        }, ['jiakao-misc!operation-config6/data/list?bizType=home_page_difficult_point'], panel, null).render();
    }

    return {
        list: list
    }

});