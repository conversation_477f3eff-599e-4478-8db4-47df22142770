"use strict";

define([
  "simple!core/template",
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
  "simple!core/form",
  "jiakao-misc!app/common/constants",
  "simple!core/plugin",
  "jiakao-misc!plugin/select-district/district3",
  "jiakao-misc!app/operation-config3/index",
], function (
  Template,
  Table,
  Utils,
  Widgets,
  Store,
  Form,
  Constants,
  Plugin,
  District,
  OperationConfig
) {
  var addEdit = function (table, lineData = {}) {
    var isEdit = !!lineData.id;
    var config = {
      title: isEdit ? "编辑" : "添加",
      width: 900,
      store: "jiakao-misc!keyword-icon/data/" + (isEdit ? "update" : "insert"),
      success: function (obj, dialog) {
        dialog.close();
        table.render();
      },
      form: {
        submitHandler: function(form) {
          let url = $('input[name=url]').val();
          if (!url) {
            Widgets.dialog.alert('请选择图标');
            return false
          }
          return true
        }
      },
      columns: [
        {
            dataIndex: 'id',
            xtype: 'hidden',
        },
        {
          header: "名称：",
          dataIndex: "name",
          xtype: "text",
          maxlength: 32,
          check: "required",
          placeholder: " 请输入名称",
        },
        {
          header: "图标：",
          dataIndex: "url",
          xtype: Plugin(
            "jiakao-misc!upload",
            {
              dataIndex: "url",
              uploadIndex: "url",
              bucket: "exam-room",
              isSingle: true,
              accept: 'image/*',
              placeholder: "请选择上传文件",
              url: "simple-upload3://upload/file.htm",
            },
            function () {
              console.log(arguments);
            }
          ),
        },
        {
          header: "是否启用：",
          dataIndex: "status",
          xtype: 'radio',
          store: [
            {
                key: 1, value: '上线'
            },
            {
                key: 2, value: '下线'
            }
        ],
        },
      ],
    };
    if (isEdit) {
        Table().edit(lineData, config);
    } else {
        Table(config).add();
    }
  };

  var list = function (panel) {
    Table(
      {
        description: "关键词图标列表",
        title: "关键词图标列表",
        search: [],
        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "添加",
              class: "primary",
              click: function(table) {
                addEdit(table)
              },
            },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
          ],
        },
        operations: [
            {
                name:'编辑',
                class: 'warning',
                click: function(table, dom, lineData){
                    addEdit(table, lineData)
                }
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!keyword-icon/data/delete'
            }
        ],
        columns: [
          {
            header: "#",
            dataIndex: "id",
            width: 20,
          },
          {
            header: '名称',
            dataIndex: 'name',

          },
          {
            header: '图标',
            dataIndex: 'url',
            render: function (data) {
                return `<img src="${data}" style="width:50px">`;
            },
          },
          {
            header: '创建人',
            dataIndex: 'createUserName'
          },
          {
            header: '创建时间',
            dataIndex: 'createTime',
            render: function (data) {
                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
            },
          },
          {
            header: '更新人',
            dataIndex: 'updateUserName'
          },
          {
            header: '更新时间',
            dataIndex: 'updateTime',
            render: function (data) {
                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
            },
          },
          {
            header: '是否启用',
            dataIndex: 'status',
            render: function (data) {
                return data == 1 ? '上线' : '下线';
            },
          }
        ],
      },
      ["jiakao-misc!keyword-icon/data/list"],
      panel,
      null
    ).render();
  };
  return {
    list: list,
  };
});
