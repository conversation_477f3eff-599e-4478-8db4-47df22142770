/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var lessonTypeStore = [
        {
            key: 1,
            value: "父课程"
        }, {
            key: 2,
            value: "子课程"
        }
    ]

    var lessonTypeMap = Tools.getMapfromArray(lessonTypeStore);

    var relatedTypeStore = [
        {
            key: 1,
            value: "知识点"
        }, {
            key: 2,
            value: "试题标签"
        }
    ]

    var relatedTypeMap = Tools.getMapfromArray(relatedTypeStore);

    var patternMap = { 101: '普通模式', 102: '长辈模式' };
    var patternArr = Object.keys(patternMap).map((a) => ({
        key: a,
        value: patternMap[a],
    }));
    
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '关联类型：',
                dataIndex: 'relatedType',
                xtype: 'select',
                store: relatedTypeStore,
            },
            {
                header: '标签或知识点id：',
                dataIndex: 'knowledgeId',
                xtype: 'text',
                check: 'required',
                placeholder: '标签或知识点id'
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required',
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                check: 'required',
            },
            {
                header: '场景code：',
                dataIndex: 'sencesCode',
                xtype: 'checkbox',
                store: Constants.senceStore,
            },
            {
                header: "访问模式：",
                dataIndex: "patternCode",
                xtype: "checkbox",
                store: patternArr,
                placeholder: "访问模式",
            },
            {
                header: '课程类型：',
                dataIndex: 'lessonType',
                xtype: 'select',
                store: lessonTypeStore,
            },
            {
                header: '课程id：',
                dataIndex: 'lessonId',
                xtype: 'text',
                check: 'required',
                placeholder: '课程id'
            },
            {
                header: '排序值：',
                dataIndex: 'indexValue',
                xtype: 'text',
                check: 'required',
                placeholder: '排序值'
            },
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!knowledge-lesson-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: function (tab, dom) { 
                dom.item('relatedType').on('change', function () { 
                    let value = $(this).val()
                    if (value == 1) {
                        dom.item('lessonType').attr("disabled", false);
                    } else if (value == 2) {
                        dom.item('lessonType').attr("disabled", true);

                        dom.item('lessonType').val(1);
                    }
                })
            },
            form: {
                submitHandler: function (form) {
                    const relatedType =  $(form).find('[data-item="relatedType"]').val();
                    let lessonType;
                    if (relatedType == 2) {
                        lessonType = 1
                    }
                    return {
                        lessonType
                    }
                }  
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '课程知识点',
            title: '课程知识点',
            search: [{
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
            },{
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
            },{
                dataIndex: 'sencesCode',
                xtype: 'select',
                store: [{ key: '', value: '请选择场景code' }].concat(Constants.senceStore)
            },
            {
                header: '标签或知识点id：',
                dataIndex: 'knowledgeId',
                xtype: 'text',
                placeholder: '标签或知识点id'
            },{
                header: '课程id：',
                dataIndex: 'lessonId',
                xtype: 'text',
                placeholder: '课程id'
            },],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    },
                    {
                        name: '批量导入',
                        class: 'info',
                        click: function (table) {
                            Table({
                                title: '批量导入',
                                width: 500,
                                store: 'jiakao-misc!knowledge-lesson-config/data/importExcel',
                                columns: [
                                    {
                                        header: '文件',
                                        dataIndex: 'data',
                                        xtype: 'file',
                                        check: "required",
                                        placeholder: '请选择Excel文件'
                                    },
                                ],
                                success: function (obj, dialog, responseData, response) {
                                    if (response.success) {
                                        Widgets.dialog.alert('上传成功');
                                        dialog.close();
                                        table.render();
                                    } else {
                                        Widgets.dialog.alert(arguments[3].message);
                                        dialog.close();
                                    }
                                },
                            }).add();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!knowledge-lesson-config/data/view',
                        save: 'jiakao-misc!knowledge-lesson-config/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!knowledge-lesson-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '关联类型：',
                    dataIndex: 'relatedType',
                    render: function (data, arr, i) {
                        return relatedTypeMap[data]
                    }
                },
                {
                    header: '标签或知识点id',
                    dataIndex: 'knowledgeId',
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '场景code：',
                    dataIndex: 'sencesCode',
                    render: function (data, arr, i) {
                        return Constants.senceMap[data]
                    }
                },
                {
                    header: '课程类型：',
                    dataIndex: 'lessonType',
                    render: function (data, arr, i) {
                        return lessonTypeMap[data]
                    }
                },
                {
                    header: '课程id：',
                    dataIndex: 'lessonId',
                },
                {
                    header: '排序值：',
                    dataIndex: 'indexValue',
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!knowledge-lesson-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
