/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var list = function (panel) {
        Table({
            description: '同学会配置信息列表',
            title: '同学会配置信息列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: (table) => {
                            Store(['jiakao-misc!learning-union/data/autoCreate']).load().done(function () {
                                table.render()
                            }).fail(err => {
                                if (err.message != null) {
                                    Widgets.dialog.alert(err.message);
                                } else {
                                    Widgets.dialog.alert('接口请求失败...');

                                }
                            })
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!learning-union/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        return '小车'
                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu'
                }, 
                {
                    header: '同学会名称',
                    dataIndex: 'name'
                },
                {
                    header: 'tagId',
                    dataIndex: 'tagId'
                },
                {
                    header: '同学会开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'startTime'
                },
                {
                    header: '同学会结束时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'endTime'
                }
            ]
        }, ['jiakao-misc!learning-union/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});