/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var list = function (panel) {
        Table({
            description: '用户参与的同学会列表',
            title: '用户参与的同学会列表',
            search: [
                {
                    dataIndex: 'userId',
                    xtype: 'text',
                    placeholder: 'userId'
                },
                {
                    dataIndex: 'unionId',
                    xtype: 'text',
                    placeholder: 'unionId'
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },

                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [

            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'carType',
                    dataIndex: 'carType'
                },
                {
                    header: 'kemu',
                    dataIndex: 'kemu'
                },
                {
                    header: '同学会id',
                    dataIndex: 'unionId'
                },
                {
                    header: '用户id',
                    dataIndex: 'userId'
                },
                {
                    header: 'createTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: 'updateTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
            ]
        }, ['jiakao-misc!learning-user-union/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});