/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var statusStore = [
        {
            key: 1,
            value: '上架'
        },
        {
            key: 2,
            value: '下架'
        }
    ]
    var statusMap = Tools.getMapfromArray(statusStore);
    var giftTypeStore = [
        {
            key: 1,
            value: 'lesson'
        },
        {
            key: 2,
            value: '路线'
        }
    ]
    var giftTypeMap = Tools.getMapfromArray(giftTypeStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: ' 活动名称：',
                dataIndex: 'activityName',
                xtype: 'text',
                maxlength: 100,
                placeholder: ' 活动名称'
            },
            {
                header: '活动开始时间：',
                dataIndex: 'activityStartTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                check: 'required',
                placeholder: '活动开始时间'
            },
            {
                header: '活动结束时间：',
                dataIndex: 'activityEndTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                check: 'required',
                placeholder: '活动结束时间'
            },
            {
                header: '发布状态：',
                dataIndex: 'activityStatus',
                xtype: 'radio',
                check: 'required',
                store: statusStore
            },
            {
                header: '营销标识符：',
                dataIndex: 'identifierCode',
                xtype: 'text',
                maxlength: 64,
                check: 'required',
                placeholder: '营销标识符'
            },
            {
                header: '券方案标识码：',
                dataIndex: 'couponPlanCode',
                xtype: 'text',
                maxlength: 64,
                check: 'required',
                placeholder: '券方案标识码'
            },
            {
                header: '赠送的视频类型：',
                dataIndex: 'giftType',
                xtype: 'radio',
                check: 'required',
                store: giftTypeStore
            },
            {
                header: '课程或者线路视频对应的id：',
                dataIndex: 'giftBizId',
                xtype: 'text',
                check: 'required',
                placeholder: '课程或者线路视频对应的id'
            },
            {
                header: '赠送天数：',
                dataIndex: 'validDays',
                xtype: 'text',
                placeholder: '赠送天数'
            },
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!lesson-activity-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '活动管理',
            title: '活动管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!lesson-activity-config/data/view',
                        save: 'jiakao-misc!lesson-activity-config/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!lesson-activity-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: ' 活动名称',
                    dataIndex: 'activityName'
                },
                {
                    header: '活动开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'activityStartTime'
                },
                {
                    header: '活动结束时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'activityEndTime'
                },
                {
                    header: '上下架状态',
                    dataIndex: 'activityStatus',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '营销标识符',
                    dataIndex: 'identifierCode'
                },
                {
                    header: '券方案标识码',
                    dataIndex: 'couponPlanCode'
                },
                {
                    header: '赠送的视频类型',
                    dataIndex: 'giftType',
                    render: function (data) {
                        return giftTypeMap[data]
                    }
                },
                {
                    header: '课程或者线路视频对应的id',
                    dataIndex: 'giftBizId'
                },
                {
                    header: '赠送天数',
                    dataIndex: 'validDays'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!lesson-activity-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
