/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var albumCode, albumType

    var submitHandler = function (form) {
        var lessonComment = []
        $(form).find('.line').each(function () {
            let text = $(this).find('[data-item="conmment-text"]').val()
            lessonComment.push(text)
        })
        let input = lessonComment.some(text => {
            return text
        })
        if(input) {
            lessonComment = lessonComment.map(text => {
                return {comment: text}
            })
        } else {
            lessonComment = []
        }
        lessonComment = JSON.stringify(lessonComment)
        console.log(lessonComment);
        return {
            lessonComment: lessonComment
        };
    }
    function columns() {
        function cls(from) {
            if(albumType === 'recommend_lesson') {
                from.splice(2, 0, {
                    header: '课程评论：',
                    dataIndex: 'lessonComment',
                    xtype: Plugin('jiakao-misc!lesson-comment', {
                    }, function () {
                    }),
                })
            }
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '专辑编码：',
                dataIndex: 'albumCode',
                xtype: 'text',
                value: albumCode,
                readonly: true,
                maxlength: 32,
                placeholder: '专辑编码'
            },
            {
                header: '排序值,值越小越靠前：',
                dataIndex: 'sort',
                xtype: 'text',
                check: 'required',
                placeholder: '排序值,值越小越靠前'
            },
            {
                header: '课程id：',
                dataIndex: 'topLessonId',
                xtype: 'number',
                maxlength: 32,
                check: 'required',
                placeholder: '课程id'
            },
        ])
    }

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!lesson-album-relation/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        }).add();
    }

    var list = function(panel, routeData) {
        albumCode = routeData.albumCode
        albumType = routeData.albumType

        Table({
            description: '课程与专辑关联配置',
            title: '课程与专辑关联配置',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!lesson-album-relation/data/view',
                        save: 'jiakao-misc!lesson-album-relation/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!lesson-album-relation/data/delete'
                }
            ],
            columns: (function(){
                let columns = [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '专辑名称',
                        dataIndex: 'albumName'
                    },
                    {
                        header: '排序值,值越小越靠前',
                        dataIndex: 'sort'
                    },
                    {
                        header: '课程id',
                        dataIndex: 'topLessonId'
                    },
                    {
                        header: '课程标题',
                        dataIndex: 'lessonTitle'
                    },
                    {
                        header: '创建时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: '创建人',
                        dataIndex: 'createUserName'
                    },
                    {
                        header: '修改时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    },
                    {
                        header: '修改人',
                        dataIndex: 'updateUserName'
                    }
                ];
                if(albumType === 'recommend_lesson') {
                    columns.splice(2, 0, {
                        header: '课程评论',
                        dataIndex: 'lessonComment',
                        render: function (data) {
                            let ar = JSON.parse(data || '[]')
                            if(ar && ar.length) {
                                return ar.map((item, index) => (index+1) + '.' + item.comment).join(' ');
                            }
                        },
                    })
                }
                return columns
            })()
        }, ['jiakao-misc!lesson-album-relation/data/list?albumCode='+albumCode], panel, null).render();
    }

    return {
        list: list
    }

});