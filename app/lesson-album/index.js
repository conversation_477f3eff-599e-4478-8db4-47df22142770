/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var albumTypeStore = [{
            key: 'recommend_lesson',
            value: '驾考精华课'
        }, {
            key: 'excellent_lessson',
            value: '专项精品课'
        }
    ]
    var albumTypeMap = Tools.getMapfromArray(albumTypeStore);
    function columns(type) {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header:'语言',
                dataIndex: 'languageType',
                xtype: 'select',
                store: Constants.languageTypeStore
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required'
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                check: 'required'
            },
            {
                header: '场景code：',
                dataIndex: 'senceCode',
                xtype: 'select',
                store: Constants.senceStore,
                placeholder: '场景code'
            },
            {
                header: '访问模式：',
                dataIndex: 'editionCode',
                xtype: 'select',
                store:  Constants.editionStore,
                placeholder: '场景code'
            },
            {
                header: '专辑名称：',
                dataIndex: 'albumName',
                xtype: 'text',
                maxlength: 32,
                placeholder: '专辑名称'
            },
            {
                header: '专辑编码：',
                dataIndex: 'albumCode',
                xtype: 'text',
                maxlength: 32,
                check: 'required',
                readonly: type === 'edit',
                placeholder: '专辑编码'
            },
            {
                header: '排序值,值越小越靠前：',
                dataIndex: 'sort',
                xtype: 'text',
                check: 'required',
                placeholder: '排序值,值越小越靠前'
            },
            {
                header: '专辑描述：',
                dataIndex: 'albumDescription',
                xtype: 'textarea',
                maxlength: 256,
                check: 'required',
                placeholder: '专辑描述'
            },
            {
                header: '专辑类型：',
                dataIndex: 'albumType',
                xtype: 'select',
                store: albumTypeStore,
                check: 'required',
                placeholder: '专辑类型'
            },
        ])
    }

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!lesson-album/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '专辑配置',
            title: '专辑配置',
            search:[{
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
            },
            {
                header: '场景code：',
                dataIndex: 'senceCode',
                xtype: 'select',
                store: [{key:'',value:'请选择场景'}].concat(Constants.senceStore),
                placeholder: '场景code'
            },
            {
                dataIndex: 'languageType',
                xtype: 'select',
                store: [{ key: '', value: '请选择语言' }].concat(Constants.languageTypeStore)
            },          
            {
                header: '访问模式：',
                dataIndex: 'editionCode',
                xtype: 'select',
                store: [{key:'',value:'请选择访问模式'}].concat(Constants.editionStore),
                placeholder: '场景code'
            }],
            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!lesson-album/data/view',
                        save: 'jiakao-misc!lesson-album/data/update'
                    },
                    columns: columns('edit')
                },
                {
                    name: '关联配置',
                    class: 'primary',
                    click: function(table, dom, lineData) {
                        Widgets.dialog.html('课程与专辑关联配置', {
                            width: 800
                        }).done(function (dialog) {
                            require(['jiakao-misc!app/lesson-album-relation/index'], function (Item) {
                                Item.list(dialog.body, lineData)
                            })
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!lesson-album/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '场景code',
                    dataIndex: 'senceCode',
                    render: function (data, arr, i) {
                        return  Constants.senceMap[data]
                    }
                },
                {
                    header: '语言',
                    dataIndex: 'languageType',
                    render: function (data) {
                        return Constants.languageTypeMap[data]
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'editionCode',
                    render: function (data) {
                        return  Constants.editionMap[data]
                    }
                },
                {
                    header: '专辑名称',
                    dataIndex: 'albumName'
                },
                {
                    header: '专辑编码',
                    dataIndex: 'albumCode'
                },
                {
                    header: '排序值,值越小越靠前',
                    dataIndex: 'sort'
                },
                {
                    header: '专辑描述',
                    dataIndex: 'albumDescription'
                },
                {
                    header: '专辑类型',
                    dataIndex: 'albumType',
                    render: function (data, arr, i) {
                        return albumTypeMap[data]
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!lesson-album/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});