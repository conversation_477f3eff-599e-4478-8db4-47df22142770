/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
  "simple!core/template",
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
    "simple!core/form",
    'jiakao-misc!app/common/constants',
  "jiakao-misc!app/common/tools",
], function (Template, Table, Utils, Widgets, Store, Form, Constants,Tools) {
  var carTypeMap = {
    car: "小车",
    bus: "客车",
    truck: "货车",
  };
  var carTypeStore = Object.keys(carTypeMap).map((a) => ({
    key: a,
    value: carTypeMap[a],
  }));
  var bizStore = [
    {
      key: "",
      value: "请选择",
    },
    {
      key: "itemLessonCover",
      value: "子课程封面图",
    },
    {
      key: "itemLessonShareImg",
      value: "子课程分享图",
    },
    {
      key: "itemLessonIcon",
      value: "子课程列表图标",
    },
    {
      key: "vipRecommend",
      value: "vip课程推荐",
    },
    {
      key: "goodRecommend",
      value: "好课推荐",
      },
      {
        key: 'wrongRecommend',
        value:'错题推荐'
    }, {
        key: 'collectionRecommend',
        value:'收藏推荐'
    }];
    var patternMap = { 101: '普通模式', 102: '长辈模式' };
    var patternArr = Object.keys(patternMap).map((a) => ({
        key: a,
        value: patternMap[a],
      }));

    var bizMap = Tools.getMapfromArray(bizStore);
    
    var lessonTypeStore = [
        {
            key: 1,
            value: "精品课"
        }, {
            key: 2,
            value: "专项强化"
        }
    ]

    var lessonTypeMap = Tools.getMapfromArray(lessonTypeStore);
  var add = function (table) {
    Table({
      title: "添加",
      width: 500,
      store: "jiakao-misc!lesson-rank-config/data/insert",
      success: function (obj, dialog) {
        dialog.close();
        table.render();
      },
      renderAfter: function (tab, dom) { 
            dom.item('lessonType').on('change', function () {
                let value = $(this).val();
                console.log(value,'125');
                if (value == 1) {
                    dom.item('topLessonId-group').children('label')[0].innerHTML = '父课程id:'
                    $('#topLessonId').attr('placeholder','父课程id')
                } else if (value == 2) {
                    dom.item('topLessonId-group').children('label')[0].innerHTML = '专项强化合集id:'
                    $('#topLessonId').attr('placeholder','专项强化合集id')
                }
            })
        },
        columns: [
            {
                header: "课程类型",
                dataIndex: 'lessonType',
                xtype: 'select',
                store:lessonTypeStore,
                check: 'required',
                placeholder: "课程类型"
            },
            {
                header: "课程描述：",
                dataIndex: "description",
                xtype: "textarea",
                check: "required",
                placeholder: "课程描述：",
            },
        {
          header: "父课程id：",
          dataIndex: "topLessonId",
          xtype: "text",
          check: "required",
          placeholder: "父课程id",
            },
            {
                header: "汽车类型：",
                dataIndex: "carType",
                xtype: "select",
                store: Constants.carTypeStore,
                placeholder: "汽车类型",
              },
              {
                header: "科目：",
                dataIndex: "kemu",
                xtype: "select",
                store: Constants.kemuStore,
                placeholder: "科目",
              },
        {
          header: "场景code：",
          dataIndex: "senceCode",
          xtype: "checkbox",
          store: Constants.senceStore,
          maxlength: 32,
          placeholder: "场景code",
        },
        {
            header: "访问模式：",
            dataIndex: "patternCode",
            xtype: "checkbox",
            store: patternArr,
            placeholder: "访问模式",
        },
        {
          header: "排序值,值越小越靠前：",
          dataIndex: "sort",
          xtype: "text",
          check: "required",
          placeholder: "排序值,值越小越靠前",
        },
        {
          header: "业务类型key：",
          dataIndex: "bizKey",
          xtype: "select",
          store: bizStore,
          placeholder: "业务类型key",
        },
      ],
    }).add();
  };

  var list = function (panel) {
    Table(
      {
        description: "课程排名配置列表",
        title: "课程排名配置列表",
        search: [
          {
            dataIndex: "carType",
            xtype: "select",
            store: [{ key: "", value: "所有驾照类型" }].concat(carTypeStore),
          },
          {
            dataIndex: "kemu",
            xtype: "select",
            store: [
              {
                key: "",
                value: "选择科目",
              },
              {
                key: "1",
                value: "科目一",
              },
              {
                key: "2",
                value: "科目二",
              },
              {
                key: "3",
                value: "科目三",
              },
              {
                key: "4",
                value: "科目四",
              },
            ],
            },
            {
                header: "业务类型key：",
                dataIndex: "bizKey",
                xtype: "select",
                store: [bizStore.at(0)].concat(bizStore.slice(bizStore.length - 4)),
                placeholder: "业务类型key",
            },
            {
                header: "场景code：",
                dataIndex: "senceCode",
                xtype: "select",
                store: [{key:'',value:'请选择'}].concat(Constants.senceStore),
                maxlength: 32,
                placeholder: "场景code",
              },
            
        ],
        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "添加",
              class: "primary",
              click: add,
            },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
          ],
        },
        operations: [
          {
            name: "查看",
            xtype: "view",
            width: 400,
            class: "success",
            title: "查看",
            store: "jiakao-misc!lesson-rank-config/data/view",
            columns: [
              {
                header: "#",
                dataIndex: "id",
              },
              {
                header: "父课程id：",
                dataIndex: "topLessonId",
              },
              {
                header: "汽车类型：",
                  dataIndex: "carType",
                  render: function (data) {
                    return Constants.carTypeMap[data]
                }
              },
              {
                header: "科目：",
                dataIndex: "kemu",
              },
              {
                header: "场景code：",
                dataIndex: "senceCode",
                render: function (data) { 
                    console.log(Constants.senceMap,'Constants.senceMap');
                    var codes = data.split(",");
                    var text = [];
                    for (let v of codes) {
                      text.push(Constants.senceMap[v])
                      }
                  return text.toString()
              }
              },
              {
                header: "排序值,值越小越靠前：",
                dataIndex: "sort",
              },
              {
                header: "业务类型key：",
                dataIndex: "bizKey",
                render: function (data, arr, i) {
                  return data ? bizMap[data] : "";
                },
              },
             
              {
                header: "创建人",
                dataIndex: "createUserName",
              },
              {
                header: "创建时间",
                render: function (data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                },
                dataIndex: "createTime",
              },

              {
                header: "更新人",
                dataIndex: "updateUserName",
              },
              {
                header: "更新时间",
                render: function (data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                },
                dataIndex: "updateTime",
              },
            ],
          },
          {
            name: "编辑",
            xtype: "edit",
            width: 500,
            class: "warning",
            title: "编辑",
            success: function (obj, dialog, e) {
              dialog.close();
              obj.render();
              },
              renderAfter: function (tab, dom,data) { 
                const lessonType = data.data.lessonType;
                init(lessonType)
                dom.item('lessonType').on('change', function () {
                    let value = $(this).val();
                    init(value)
                })
                  
                function init(value){
                    if (value == 1) {
                        dom.item('topLessonId-group').children('label')[0].innerHTML = '父课程id:'
                        $('#topLessonId').attr('placeholder','父课程id')
                    } else if (value == 2) {
                        dom.item('topLessonId-group').children('label')[0].innerHTML = '专项强化合集id:'
                        $('#topLessonId').attr('placeholder','专项强化合集id')
                    }
                }
            },
            store: {
              load: "jiakao-misc!lesson-rank-config/data/view",
              save: "jiakao-misc!lesson-rank-config/data/update",
            },
            columns: [
              {
                dataIndex: "id",
                xtype: "hidden",
                },
                {
                    header: "课程类型",
                    dataIndex: 'lessonType',
                    xtype: 'select',
                    store:lessonTypeStore,
                    check: 'required',
                    placeholder: "课程类型"
                },
                {
                    header: "课程描述：",
                    dataIndex: "description",
                    xtype: "textarea",
                    check: "required",
                    placeholder: "课程描述：",
                },
              {
                header: "父课程id：",
                dataIndex: "topLessonId",
                xtype: "text",
                check: "required",
                placeholder: "父课程id",
                },
              {
                header: "汽车类型：",
                dataIndex: "carType",
                xtype: "select",
                store: Constants.carTypeStore,
                placeholder: "汽车类型",
              },
              {
                header: "科目：",
                dataIndex: "kemu",
                xtype: "select",
                store: Constants.kemuStore,
                placeholder: "科目",
              },
              {
                header: "场景code：",
                dataIndex: "senceCode",
                xtype: "checkbox",
                store: Constants.senceStore,
                maxlength: 32,
                placeholder: "场景code",
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    store: patternArr,
                    placeholder: "访问模式",
                },
              {
                header: "排序值,值越小越靠前：",
                dataIndex: "sort",
                xtype: "text",
                check: "required",
                placeholder: "排序值,值越小越靠前",
              },
              {
                header: "业务类型key：",
                dataIndex: "bizKey",
                xtype: "select",
                store: bizStore,
                placeholder: "业务类型key",
              },
            ],
          },
          {
            name: "删除",
            class: "danger",
            xtype: "delete",
            store: "jiakao-misc!lesson-rank-config/data/delete",
          },
        ],
        columns: [
          {
            header: "#",
            dataIndex: "id",
            width: 20,
          },
          {
            header: "标题",
            dataIndex: "title",
            },
            {
                header: "课程类型",
                dataIndex: 'lessonType',
                render: function (data) {
                    return lessonTypeMap[data]
                }
            },
          {
            header: "父课程或专项强化合集id",
            dataIndex: "topLessonId",
          },
          {
            header: "汽车类型",
            dataIndex: "carType",
            render: function (data) {
                return Constants.carTypeMap[data]
            }
          },
          {
            header: "科目",
            dataIndex: "kemu",
          },
          {
            header: "场景code",
            dataIndex: "senceCode",
            render: function (data) { 
                    var codes = data.split(",");
                    var text = [];
                    for (let v of codes) {
                        text.push(Constants.senceMap[v])
                    }
                    return text.toString()
                }
            },
            {
                header: "访问模式",
                dataIndex: "patternCode",
                render: function (data) { 
                    var codes = data?.split(",") || [];
                    var text = [];
                    for (let v of codes) {
                        text.push(patternMap[v])
                    }
                    return text.toString()
                }
            },
          {
            header: "排序值,值越小越靠前",
            dataIndex: "sort",
          },
          {
            header: "业务类型key",
            dataIndex: "bizKey",
            render: function (data, arr, i) {
              return data ? bizMap[data] : "";
            },
            },
          {
            header: "创建人",
            dataIndex: "createUserName",
          },
          {
            header: "创建时间",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
            dataIndex: "createTime",
          },

          {
            header: "更新人",
            dataIndex: "updateUserName",
          },
          {
            header: "更新时间",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
            dataIndex: "updateTime",
          },
        ],
      },
      ["jiakao-misc!lesson-rank-config/data/list"],
      panel,
      null
    ).render();
  };

  return {
    list: list,
  };
});
