/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var targetTypeStore = [
        {
            key: '1',
            value: '当前直播'
        }, {
            key: '2',
            value: '指定链接'
        }
    ]
    var targetTypeMap = Tools.getMapfromArray(targetTypeStore);

    var bannerTypeStore = [
        {
            key: '1',
            value: '图片'
        }, {
            key: '2',
            value: '视频'
        }
    ]
    var bannerTypeMap = Tools.getMapfromArray(bannerTypeStore);
    
    var carTypeStore = [
        {
            key: 'allType',
            value: '所有车型'
        },
        {
            
            key: 'car',
            value: '小车'
        },
        {
            key: 'truck',
            value: '货车'
        },
        {
            key: 'bus',
            value: '客车'
        },
        {
            key: 'moto',
            value: '摩托车'
        },
        {
            key: 'light_trailer',
            value: '轻型牵引挂车'
        }
    ]
    var carTypeMap = Tools.getMapfromArray(carTypeStore);

    var videoSourceStore = [
        {
            key: '1',
            value: '直播中的讲师视频'
        }, {
            key: '2',
            value: '指定视频'
        }
    ]
    var videoSourceMap = Tools.getMapfromArray(videoSourceStore);

    var radioStore = [
        {
            key: false,
            value: '否'
        }, {
            key: true,
            value: '是'
        }
    ]
    var radioMap = Tools.getMapfromArray(radioStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '车型：',
                check: 'required',
                dataIndex: 'carType',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: carTypeStore,
                    placeholder: '车型',
                    dataIndex: 'carType',
                    index: {
                        key: 'key',
                        value: 'value',
                        search: 'key'
                    },
                    isMulti: true,
                    defaultVal:  'allType'
                }, function (plugin, value) {
                })
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                check: 'required',
                store: Constants.kemuStore,
            },
            {
                header: 'banner名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 45,
                placeholder: 'banner名称'
            },
            {
                header: 'banner类型：',
                dataIndex: 'bannerType',
                xtype: 'select',
                check: 'required',
                store: bannerTypeStore
            },
            {
                header: '视频来源：',
                dataIndex: 'videoSource',
                xtype: 'select',
                check: 'required',
                store: videoSourceStore
            },
            {
                header: 'banner视频：',
                dataIndex: 'videoUrl',
                check: 'required',
                xtype: Plugin('jiakao-misc!upload2', {
                    dataIndex: 'videoUrl',
                    uploadIndex: 'videoUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: 'banner图片：',
                dataIndex: 'imgUrl',
                check: 'required',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'imgUrl',
                    uploadIndex: 'imgUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '跳转类型：',
                dataIndex: 'jumpType',
                xtype: 'select',
                check: 'required',
                store: targetTypeStore
            },
            {
                header: '跳转链接：',
                dataIndex: 'linkUrl',
                xtype: 'text',
                placeholder: '跳转链接'
            },
            {
                header: '是否付费：',
                dataIndex: 'isVip',
                xtype: 'radio',
                check: 'required',
                store: radioStore
            },
            {
                header: '是否展示付费标签：',
                dataIndex: 'displayTag',
                xtype: 'radio',
                check: 'required',
                store: radioStore,
                value: true
            },
            {
                header: '展示顺序：',
                dataIndex: 'orderValue',
                xtype: 'text',
                check: 'required',
                placeholder: '数字越大越靠前'
            },
            {
                header: '上架时间：',
                dataIndex: 'onTime',
                xtype: 'datetime',
                placeholder: '为空默认一直为上线状态',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                }
            },
            {
                header: '下架时间：',
                dataIndex: 'offTime',
                xtype: 'datetime',
                placeholder: '为空默认不下线',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                }
            },
        ])
    }

    var add = function(table) {
        Table({
            title: '添加',
            width: 800,
            store: 'jiakao-misc!live-banner/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '直播banner配置',
            title: '直播banner配置',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!live-banner/data/view',
                        save: 'jiakao-misc!live-banner/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '启用',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].status == 2 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认启用吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!live-banner/data/enable?id=' + lineData.id]).save().done(data => {
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '禁用',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].status == 1 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认禁用吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!live-banner/data/disable?id=' + lineData.id]).save().done(data => {
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!live-banner/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        let list = data.split(',')
                        list = list.map(item => carTypeMap[item])
                        return list.join(',')
                    }
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: 'banner名称：',
                    dataIndex: 'name'
                },
                {
                    header: 'banner类型：',
                    dataIndex: 'bannerType',
                    render: function (data, arr, i) {
                        return bannerTypeMap[data]
                    }
                },
                {
                    header: '视频来源：',
                    dataIndex: 'videoSource',
                    render: function (data, arr, i) {
                        return videoSourceMap[data]
                    }
                },
                {
                    header: 'banner视频',
                    dataIndex: 'videoUrl',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('视频', {width: 600}).done(function (dialog) {
                            $(dialog.body).html('<video width="600" src="' + lineData.videoUrl + '" controls></video>')
                        })
                    }
                },
                {
                    header: 'banner图片',
                    dataIndex: 'imgUrl',
                    width: 160,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.imgUrl}">`)
                    }
                },
                {
                    header: '跳转类型：',
                    dataIndex: 'jumpType',
                    render: function (data, arr, i) {
                        return targetTypeMap[data]
                    }
                },
                {
                    header: '跳转链接：',
                    dataIndex: 'linkUrl',
                    render: function (data) {
                        return '<div style="width: 160px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '是否付费：',
                    dataIndex: 'isVip',
                    render: function (data, arr, i) {
                        return radioMap[data]
                    }
                },
                {
                    header: '是否展示付费标签：',
                    dataIndex: 'displayTag',
                    render: function (data, arr, i) {
                        return radioMap[data]
                    }
                },
                {
                    header: '展示顺序：',
                    dataIndex: 'orderValue'
                },
                {
                    header: '上架时间：',
                    dataIndex: 'onTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                {
                    header: '下架时间：',
                    dataIndex: 'offTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                }
            ]
        }, ['jiakao-misc!live-banner/data/list'], panel, null).render();
    }

    return {
        list: list
    }
});
