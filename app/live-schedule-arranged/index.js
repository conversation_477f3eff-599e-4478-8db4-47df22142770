/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var list = function(panel) {
        Table({
            description: '直播日程表配置',
            title: '直播日程表配置',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '排课详情',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('live-schedule-calendar-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'live-schedule-calendar-' + lineData.id,
                                    name: '排课详情'
                                })
                            }
                            require(['jiakao-misc!app/live-schedule-calendar/index'], function (Item) {
                                var arranged = true
                                Item.list(nPanel, lineData, arranged)
                            })
                        });
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '日程名称：',
                    dataIndex: 'scheduleName'
                },
                {
                    header: '开始日期：',
                    dataIndex: 'startDate'
                },
                {
                    header: '结束日期：',
                    dataIndex: 'endDate'
                },
                {
                    header: '日程天数：',
                    dataIndex: 'dayCount'
                },
            ]
        }, ['jiakao-misc!live-schedule/data/listRecord'], panel, null).render();
    }

    return {
        list: list
    }
});
