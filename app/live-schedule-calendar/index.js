/**
 * Created by sunbin on 2015/4/20.
 */

 "use strict";
 define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var todayDate = new Date();
    var todayStr = Utils.format.date(todayDate, 'yyyy/MM/dd')
    var arranged = false
    //渲染时间轴
    var getStrObj = function (todayBeginTime, betweenTimes, dataObj, type) {
        //权限问题
        // var flagShow = type && (window.j.userInfo.roles.indexOf('管理员') > -1 || window.j.userInfo.roles.indexOf('项目管理员') > -1);
        var flagShow = type && !arranged;
        var strObj = '';
        var chooseColor = ['progress-bar-info', 'progress-bar-success', 'progress-bar-danger', 'progress-bar-primary', 'progress-bar-warning'];

        dataObj.liveScheduleItemDataList.map(function (o, index) {
            var endTime = +new Date(todayStr+' '+o.endTime)
            var beginTime = +new Date(todayStr+' '+o.beginTime)
            var c = chooseColor[type ? index % 5 : 2]
            var w = (endTime - beginTime) * 100 / betweenTimes.toFixed(3)
            var l = (beginTime - todayBeginTime) * 100 / betweenTimes.toFixed(3)
            // var t = Utils.format.date(o.beginTime, 'HH:mm:ss') + '至' + Utils.format.date(o.endTime, 'HH:mm:ss')
            var t = o.beginTime + '至' + o.endTime
            var n = arranged ? o.teacherName : 'NO.' + o.teacherRank
            strObj += '<div class="progress-bar ' + c + '" style="width: ' + w + '%;left: ' + l + '%">' +
                '<p>' + n + '</p>' +
                '<p>' + t + '</p>' +
                '<p>' + o.title + '</p>' +
                '<span class="glyphicon glyphicon-edit ' + (flagShow ? '' : 'hidden') + '" data-item="meeting-booking-edit" data-day-id="' + dataObj.dayId + '" data-id="' + o.id + '"  data-title="' + o.title + '"></span>' +
                '<span class="glyphicon glyphicon-remove ' + (flagShow ? '' : 'hidden') + '" data-item="meeting-booking-cancel" data-day-id="' + dataObj.dayId + '"  data-id="' + o.id + '" data-title=' + o.title + '></span>' +
                '</div>';
        });
        return strObj;
    };
    var showTime = function (beginTime) {
        var searchDate = todayDate.getFullYear() + '-' + (parseInt(todayDate.getMonth()) + 1 > 9 ? parseInt(todayDate.getMonth()) + 1 : ('0' + (parseInt(todayDate.getMonth()) + 1))) + '-' + (todayDate.getDate() > 9 ? todayDate.getDate() : '0' + todayDate.getDate());
        var todayBeginTime = todayDate.setHours(9, 0, 0, 0);
        var todayEndTime = todayDate.setHours(21, 0, 0, 0);
        var betweenTimes = todayEndTime - todayBeginTime;
        if (beginTime) {
            var time = beginTime + " 09:00:00";
            todayBeginTime = Date.parse(new Date(time.replace(/-/g, "/")));
        };
        return {
            searchDate: searchDate,
            betweenTimes: betweenTimes,
            todayBeginTime: todayBeginTime
        }
    };
    var operation = function (title, tableObj, scheduleId, dayId, dataObj, strObj, timeShaft, todayBeginTime) {
        var columnsObj = [
            {
                header: 'ID：',
                dataIndex: 'scheduleId',
                xtype: 'hidden',
                value: scheduleId
            },
            {
                header: 'ID：',
                dataIndex: 'dayId',
                xtype: 'hidden',
                value: dayId
            },
            {
                header: '已选择时间段：',
                dataIndex: '',
                xtype: function () {
                    return '<div class="progress">' + timeShaft + '</div>' +
                        '<div class="progress meeting-progress"  data-item="meeting-progress" style="background:#5cb85c;">' + strObj + '</div>';
                }
            },
            {
                header: '课程开始时间：',
                dataIndex: 'beginTime',
                xtype: function () {
                    var value = dataObj ? (dataObj.beginTime - todayBeginTime) / 60000 : '0'
                    var time = dataObj ? Utils.format.date(dataObj.beginTime, 'HH:mm:ss') : Utils.format.date(todayBeginTime, 'HH:mm:ss')
                    return '<p><input type="range" data-type="appointment-begin-time" data-item="time-range" min="0" max="720" step="10" value="' + value + '"/></p>' +
                        '<p>当前选择开始时间--<span data-item="appointment-begin-time">' + time +
                        '</span><input type="hidden" name="beginTime" value="' + time + '"/></p>';
                }
            },
            {
                header: '课程结束时间：',
                dataIndex: 'endTime',
                xtype: function () {
                    var value = dataObj ? (dataObj.endTime - todayBeginTime) / 60000 : '0'
                    var time = dataObj ? Utils.format.date(dataObj.endTime, 'HH:mm:ss') : Utils.format.date(todayBeginTime, 'HH:mm:ss')
                    return '<p><input type="range" data-type="appointment-end-time" data-item="time-range" min="0" max="720" step="10" value="' + value + '"/></p>' +
                        '<p>当前选择结束时间--<span data-item="appointment-end-time">' + time +
                        '</span><input type="hidden" name="endTime" value="' + time + '"/></p>';
                }
            },
            {
                header: '子课程模板：',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!top-lesson-item-tpl/data/templateList',
                    placeholder: '子课程模板',
                    dataIndex: 'lessonTemplateId',
                    index: {
                        key: 'id',
                        value: 'templateName',
                        search: 'id'
                    },
                    isMulti: false,
                }, function (plugin, value) {
                }),
                dataIndex: 'lessonTemplateId',
                value: dataObj && dataObj.lessonTemplateId
            },
        ];
        if (dataObj) {
            columnsObj.unshift({
                header: 'ID',
                dataIndex: 'id',
                xtype: 'hidden',
                value: dataObj.id
            });
        }

        Table({
            title: title,
            width: 1200,
            store: 'jiakao-misc!live-schedule/data/' + (dataObj ? 'updateItem' : 'insertItem'),
            success: function (obj, dialog) {
                dialog.close();
                tableObj.render();
            },
            columns: columnsObj,
            form: {
                submitHandler: function (form) {
                    var $form = $(form);

                    var beginTime = $form.find('[name=beginTime]').val();
                    var endTime = $form.find('[name=endTime]').val();

                    beginTime = +new Date(todayStr+' '+beginTime)
                    endTime = +new Date(todayStr+' '+endTime)
                    var beginStamp = new Date(beginTime).getTime();
                    var endStamp = new Date(endTime).getTime();

                    if (endStamp - beginStamp < 0) {
                        Simple.Dialog.toast('结束时间不能小于开始时间')
                        return false;
                    }

                    if(endStamp == beginStamp){
                        Simple.Dialog.toast('开始时间不能和结束时间一样')
                        return false;
                    }

                    // 10800000  最大3个小时
                    if (endStamp - beginStamp > 10800000) {
                        Simple.Dialog.toast('最多选择3个小时')
                        return false;
                    }

                    $form.find('[name=beginTime]').val(Utils.format.date(beginTime, 'HH:mm'));
                    $form.find('[name=endTime]').val(Utils.format.date(endTime, 'HH:mm'));
                    return true;
                }

            },
            renderAfter: function (table, dom, data) {
                //当是编辑时，隐藏控件
                if (dataObj) {
                    dom.item('startDate').prop('disabled', true);
                    dom.item('endDate').prop('disabled', true);
                }

                dom.parents('.modal-body').children().attr('data-item', 'meeting-container');
                //滑动过程中
                dom.item('time-range').on('input', function () {
                    //当编辑的时候则不与当前自己的时间进行比较
                    var dataType = $(this).attr('data-type');
                    var timeValue = $(this).val();
                    dom.item(dataType).text(Utils.format.date(todayBeginTime + timeValue * 60000, 'HH:mm:ss'));
                    dom.item(dataType).next().val(Utils.format.date(todayBeginTime + timeValue * 60000, 'HH:mm:ss'));
                });
                dom.item('time-range').on('change', function () {
                    //当编辑的时候则不与当前自己的时间进行比较
                    var dataType = $(this).attr('data-type');
                    var timeValue = $(this).val();
                    dom.item(dataType).text(Utils.format.date(todayBeginTime + timeValue * 60000, 'HH:mm:ss'));
                    dom.item(dataType).next().val(Utils.format.date(todayBeginTime + timeValue * 60000, 'HH:mm:ss'));
                });
                //当重填的时候
                dom.parents('.modal-body').next().find('button[type="reset"]').on('click', function () {
                    dom.item('appointment-begin-time').text(dataObj ? Utils.format.date(dataObj.beginTime, 'HH:mm:ss') : Utils.format.date(todayBeginTime, 'HH:mm:ss'));
                    dom.item('appointment-begin-time').next().val(dataObj ? Utils.format.date(dataObj.beginTime, 'HH:mm:ss') : Utils.format.date(todayBeginTime, 'HH:mm:ss'));
                    dom.item('appointment-end-time').text(dataObj ? Utils.format.date(dataObj.endTime, 'HH:mm:ss') : Utils.format.date(todayBeginTime, 'HH:mm:ss'));
                    dom.item('appointment-end-time').next().val(dataObj ? Utils.format.date(dataObj.endTime, 'HH:mm:ss') : Utils.format.date(todayBeginTime, 'HH:mm:ss'));
                });
            }
        }).add();
    };
    var list = function (panel, lineData, _arranged) {
        arranged = _arranged;
        var scheduleId = lineData.id
        //时间轴
        var timeShaft = '';
        for (var i = 9; i < 21; i++) {
            if (i == 21) {
                timeShaft += '<div class="progress-bar progress-bar-info timeShaft" style="width:' + (100 / 24).toFixed(3) + '%;"><span>' + i + ':00</span>-<span>' + i + ':30</span></div>'
            } else {
                timeShaft += '<div class="progress-bar progress-bar-info timeShaft" style="width: ' + (100 / 24).toFixed(3) + '%;"><span>' + i + ':00</span></div>' +
                    '<div class="progress-bar progress-bar-success timeShaft" style="width: ' + (100 / 24).toFixed(3) + '%;"><span>' + i + ':30</span></div>'
            }
        }
        Template('jiakao-misc!meeting-use/usePreview', panel, function (dom, data, item, mainObj) {
            var meetingContainer = item('meeting-container');
            Table({
                description: '直播日程表',
                title: '直播日程表',
                search: [
                ],
                buttons: {
                    top: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            }
                        },
                        !arranged && {
                            name: '添加日程',
                            class: 'primary',
                            click: function(obj) {
                                Store(['jiakao-misc!live-schedule/data/insertDay?scheduleId=' + lineData.id]).save([]
                                ).done(function (store, dataObj) {
                                    obj.render();
                                });
                            }
                        },
                        !arranged && {
                            name: '自动排课',
                            class: 'primary',
                            click: function(obj) {
                                Table({
                                    title: '自动排课',
                                    width: 500,
                                    store: 'jiakao-misc!live-schedule/data/arrangeSchedule',
                                    success: function (obj, dialog) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [{
                                        dataIndex: 'id',
                                        xtype: 'hidden',
                                        value: lineData.id
                                    },{
                                        header: '排课起始日期：',
                                        dataIndex: 'date',
                                        xtype: 'date',
                                        placeholder: '排课起始日期',
                                        render: function (data) {
                                            return Utils.format.date(data, 'yyyy-MM-dd');
                                        }
                                    },]
                                }).add();
                            }
                        }
                    ],
                    bottom: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            }
                        }
                    ]
                },
                operations: [
                    {
                        name: '添加',
                        class: 'success',
                        render: function() {
                            return !arranged ? '添加' : '';
                        },
                        click: function (table, lineDom, lineData, dom, data, index) {
                            console.log(lineData)
                            var todayTime = showTime(table.config.params.date);
                            var titleStr = '添加----绿色表示该时间段未被选择, 红色表示该时间段已被选择<b style="color:#FF0000">【当前滑动单位时间为十分钟】</b>';
                            var strObj = getStrObj(todayTime.todayBeginTime, todayTime.betweenTimes, lineData, '')
                            operation(
                                titleStr,
                                table,
                                scheduleId,
                                lineData.dayId,
                                '',
                                strObj,
                                timeShaft,
                                todayTime.todayBeginTime
                            );
                        }
                    },
                    {
                        name: '删除',
                        class: 'danger',
                        render: function() {
                            return !arranged ? '删除' : '';
                        },
                        store: 'jiakao-misc!live-schedule/data/delDay',
                        click: function (table, dom, lineData, panel, allData, index) {
                            Widgets.dialog.confirm('确认删除第'+(index+1)+'天吗', function (e, stat) {
                                if (stat) {
                                    Store(['jiakao-misc!live-schedule/data/delDay?id=' + lineData.dayId]).save().done(data => {
                                        console.log(data)
                                        table.render();
                                    }).fail();
                                }
                            })
                        }
                    },
                ],
                columns: [
                    {
                        header: '日程',
                        dataIndex: 'index',
                        render: function(data, allData, lineData, index) {
                            var n = arranged ? lineData.date : '第' + (index+1) + '天'
                            return n
                        }
                    },
                    {
                        header: '时间轴',
                        dataIndex: 'note',
                        render: function (data, allData, lineData) {
                            return '<div class="progress">' + timeShaft + '</div>' +
                                '<div class="progress meeting-progress" data-item="meeting-booking-progress"></div>';
                        }
                    }

                ]
            }, 
            ['jiakao-misc!live-schedule/data/'+(arranged?'listRecordSchedule':'getLiveCalendar')+'?id=' + lineData.id],
            meetingContainer,
            function (dom, data, item, obj) {
                var todayTime = showTime(obj.config.params.date);
                //渲染图片
                data.data.forEach(function (dataObj, i) {
                    item('meeting-booking-progress').eq(i).append(getStrObj(todayTime.todayBeginTime, todayTime.betweenTimes, dataObj, 1));
                });
                item('meeting-booking-edit').on('click', function () {
                    //编辑
                    var dayId = $(this).attr('data-day-id');
                    var itemId = $(this).attr('data-id');
                    var title = $(this).attr('data-title');
                    Store(['jiakao-misc!live-schedule/data/viewItem?id=' + itemId]).load([
                        { aliases: 'list' }
                    ]).done(function (store, dataObj) {
                        var item;
                        data.data.forEach(function (_item) {
                            if (dayId == _item.dayId) {
                                item = _item
                            }
                        });
                        dataObj.list.data.beginTime = +new Date(todayStr+' '+dataObj.list.data.beginTime)
                        dataObj.list.data.endTime = +new Date(todayStr+' '+dataObj.list.data.endTime)
                        operation(
                            '编辑<b style="color:#FF0000">' + title + '课程的时间</b>',
                            obj,
                            scheduleId,
                            dataObj.list.data.dayId,
                            dataObj.list.data,
                            getStrObj(todayTime.todayBeginTime, todayTime.betweenTimes, item, ''),
                            timeShaft,
                            todayTime.todayBeginTime
                        );

                    });
                });
                item('meeting-booking-cancel').on('click', function () {
                    //删除
                    var title = $(this).attr('data-title');
                    var itemId = $(this).attr('data-id');
                    Widgets.dialog.confirm('确定删除' + title + '课程吗?', function (e, status) {
                        if (status) {
                            Store(['jiakao-misc!live-schedule/data/delItem?id=' + itemId]).save().done(function () {
                                obj.render();
                            });
                        }
                    });
                });
            }).render();
        }).render();
    };
    return {
        list: list
    }
});
 