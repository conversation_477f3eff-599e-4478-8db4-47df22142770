/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '日程名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 45,
                placeholder: '日程名称'
            },
        ])
    }

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!live-schedule/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '直播日程表配置',
            title: '直播日程表配置',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!live-schedule/data/view',
                        save: 'jiakao-misc!live-schedule/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '管理日程表',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('live-schedule-calendar-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'live-schedule-calendar-' + lineData.id,
                                    name: '直播日程表'
                                })
                            }
                            require(['jiakao-misc!app/live-schedule-calendar/index'], function (Item) {
                                var arranged = false
                                Item.list(nPanel, lineData, arranged)
                            })
                        });
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!live-schedule/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '日程名称：',
                    dataIndex: 'name'
                },
                {
                    header: '日程天数：',
                    dataIndex: 'dayCount'
                },
            ]
        }, ['jiakao-misc!live-schedule/data/list'], panel, null).render();
    }

    return {
        list: list
    }
});
