/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var sceneStore = [
        {
            key: 1,
            value: '常规运营位'
        },
        {
            key: 2,
            value: '支付弹窗'
        },
        {
            key: 3,
            value: '推荐商品运营位'
        },
        {
            key: 4,
            value: '推荐商品弹窗'
        }
    ]
    var sceneMap = Tools.getMapfromArray(sceneStore);

    var radioStore = [
        {
            key: false,
            value: '否'
        }, {
            key: true,
            value: '是'
        }
    ]
    var radioMap = Tools.getMapfromArray(radioStore);

    var updateEnlargeInfo = function (table, line, lineData) {
        Widgets.dialog.html('售卖数量放大', {
            width: 400,
            buttons: [{
                name: '保存',
                xtype: 'success',
                click: function () {
                    var me = this;
                    var lower = +this.body.find("[data-item='lower']").val()
                    var upper = +this.body.find("[data-item='upper']").val()
                    var errorMsg = ''
                    // 2个值都为空或者0 允许提交
                    if(lower == 0 && upper == 0) {
                    }else if(lower < 0 || upper < 0) {
                        errorMsg = '非法输入！'
                    }else if(!upper) {
                        errorMsg = '后值应大于0'
                    }else if(lower >= upper) {
                        errorMsg = '后值应大于前值'
                    }
                    if(!errorMsg) {
                        Store(['jiakao-misc!live-stock-rule/data/updateEnlargeInfo'], [{
                            aliases: 'list',
                            params: {
                                id: lineData.id,
                                enlargeMin: lower,
                                enlargeMax: upper
                            }
                        }]).save().done(function (store) {
                            Widgets.dialog.alert('配置成功')
                            me.close();
                            table.render();
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        });
                    }else{
                        Widgets.dialog.alert(errorMsg)
                    }
                }
            }]
        }).done(function (dialog) {
            function lineDom(item) {
                item = item || {}
                return `<div style="padding-left: 10px;">
                            <div style="font-size: 18px;">每售出一单真实订单，已售数量增加</div>
                            <div style="padding: 5px 0;">
                                <input data-item="lower" type="text" value="${item.lower || ''}" style="width:60px;padding: 0;"> ~
                                <input data-item="upper" type="text" value="${item.upper || ''}" style="width:60px;padding: 0;"> 单
                            </div>
                            <p>区间内随机取数，库存扣完即止，不会出现负数</p>
                            
                </div>`
            }
            $(dialog.body).append(lineDom({
                lower: lineData.enlargeMin,
                upper: lineData.enlargeMax
            }))
        })
    }
    var updateAutoEecrConfig = function (table, line, lineData) {
        Widgets.dialog.html('虚拟自动售卖', {
            width: 400,
            buttons: [{
                name: '保存',
                xtype: 'success',
                click: function () {
                    var me = this;
                    var config = []
                    var errorMsg = ''
                    this.body.item('config').find('.line').each(function () {
                        var durationStart = +$(this).item('start').val()
                        var durationEnd = +$(this).item('end').val()
                        var autoDecrMin = +$(this).item('lower').val()
                        var autoDecrMax = +$(this).item('upper').val()
                        if(durationStart < 0 || durationEnd < 0 || autoDecrMin < 0 || autoDecrMax < 0) {
                            errorMsg = '非法输入！'
                        }else if(!durationEnd || !autoDecrMax) {
                            errorMsg = '后值应大于0'
                        }else if(durationStart >= durationEnd || autoDecrMin >= autoDecrMax) {
                            errorMsg = '后值应大于前值'
                        }
                        config.push({
                            durationStart,
                            durationEnd,
                            autoDecrMin,
                            autoDecrMax,
                        })
                    })
                    if(!errorMsg) {
                        Store(['jiakao-misc!live-stock-rule/data/updateAutoEecrConfig'], [{
                            aliases: 'list',
                            params: {
                                id: lineData.id,
                                autoDecrConfig: JSON.stringify(config)
                            }
                        }]).save().done(function (store) {
                            Widgets.dialog.alert('配置成功')
                            me.close();
                            table.render();
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        });
                    } else {
                        Widgets.dialog.alert(errorMsg)
                    }

                }
            }]
        }).done(function (dialog) {
            Plugin('jiakao-misc!stock-rule-config', {
                id: lineData.id,
                target: dialog.body
            }).render()
        })
    }

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                check: 'required'
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required'
            },
            {
                header: '规则描述：',
                dataIndex: 'desc',
                xtype: 'text',
                maxlength: 256,
                placeholder: '规则描述'
            },
            {
                header: '显示库存场景：',
                check: 'required',
                dataIndex: 'applyScene',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: sceneStore,
                    placeholder: '显示库存场景',
                    dataIndex: 'applyScene',
                    index: {
                        key: 'key',
                        value: 'value',
                        search: 'key'
                    },
                    isMulti: true,
                    defaultVal: false
                }, function (plugin, value) {
                })
            },
            {
                header: '是否生效：',
                dataIndex: 'enable',
                xtype: 'radio',
                check: 'required',
                store: radioStore
            },
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!live-stock-rule/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '优惠库存特殊规则配置',
            title: '优惠库存特殊规则配置',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!live-stock-rule/data/view',
                        save: 'jiakao-misc!live-stock-rule/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '售卖数量放大',
                    class: 'success',
                    click: updateEnlargeInfo
                },
                {
                    name: '虚拟自动售卖',
                    class: 'success',
                    click: updateAutoEecrConfig
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!live-stock-rule/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '规则描述',
                    dataIndex: 'desc',
                },
                {
                    header: '显示库存场景',
                    dataIndex: 'applyScene',
                    render: function (data) {
                        var list = data.split(',')
                        if(list && list.length){
                            var text = ''
                            list.forEach((item, i) => {
                                text += `${i+1},${sceneMap[item]}<br/>`;
                            })
                            return text;
                        }else{
                            return ''
                        }
                    },
                },
                {
                    header: '售卖数量放大',
                    render: function (data, allData, lineData) {
                        if(lineData.enlargeMax) {
                            return `每售出一单，已售数量增加${lineData.enlargeMin || 0}~${lineData.enlargeMax}单`;
                        }else{
                            return ''
                        }
                    },
                },
                {
                    header: '虚拟自动售卖',
                    dataIndex: 'autoDecrConfig',
                    render: function (data) {
                        var list = JSON.parse(data || '[]')
                        if(list && list.length){
                            var text = ''
                            list.forEach((item, i) => {
                                text += `${item.durationStart || 0}~${item.durationEnd}分钟，每隔30S自动扣减${item.autoDecrMin || 0}~${item.autoDecrMax}单；<br/>`;
                            })
                            return text;
                        }else{
                            return ''
                        }
                    },
                },
                {
                    header: '是否生效',
                    dataIndex: 'enable',
                    render: function (data, arr, i) {
                        return radioMap[data]
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!live-stock-rule/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
