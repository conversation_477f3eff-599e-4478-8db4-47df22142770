/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!maiche-rec/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '车系的id：',
                    dataIndex: 'seriesId',
                    xtype: 'text',
                    placeholder: '车系的id'
                },
                {
                    header: '车型描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '车型描述'
                },
                {
                    header: '性别：',
                    dataIndex: 'gender',
                    xtype: 'select',
                    store: [
                        {
                            key: '男', value: '男'
                        },
                        {
                            key: '女', value: '女'
                        }
                    ]
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'maiche-rec列表',
            title: 'maiche-rec列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!maiche-rec/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '车系的id：',
                            dataIndex: 'seriesId'
                        },
                        {
                            header: '车系名称：',
                            dataIndex: 'seriesName'
                        },
                        {
                            header: '车的价格：',
                            dataIndex: 'price'
                        },
                        {
                            header: '车型描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '车型图片：',
                            dataIndex: 'carImage',
                            render:function (data) {
                                if(data){
                                    return '<a><image style="width: 100px; height: auto;" src="'+ data+'"></a>'
                                }
                            },
                        },
                        {
                            header: '性别：',
                            dataIndex: 'gender'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },

                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },

                        {
                            header: '更新人',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!maiche-rec/data/view',
                        save: 'jiakao-misc!maiche-rec/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '车系的id：',
                            dataIndex: 'seriesId',
                            xtype: 'text',
                            placeholder: '车系的id'
                        },
                        {
                            header: '车型描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '车型描述'
                        },
                        {
                            header: '性别：',
                            dataIndex: 'gender',
                            xtype: 'select',
                            store: [
                                {
                                    key: '男', value: '男'
                                },
                                {
                                    key: '女', value: '女'
                                }
                            ]
                        },


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!maiche-rec/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '车系的id',
                    dataIndex: 'seriesId'
                },
                {
                    header: '车系名称',
                    dataIndex: 'seriesName'
                },
                {
                    header: '车的价格',
                    dataIndex: 'price'
                },
                {
                    header: '车型描述',
                    dataIndex: 'desc'
                },
                {
                    header: '车型图片',
                    dataIndex: 'carImage',
                    render:function (data) {
                        if(data){
                            return '<a><image style="width: 100px; height: auto;" src="'+ data+'"></a>'
                        }
                    },
                    click:function (table,row,lineData) {
                        Widgets.dialog.html('图片详情', {}).done(function (dialog) {
                            $(dialog.body).append('<img src="'+lineData.carImage+'" />')
                        })
                    }
                },
                {
                    header: '性别',
                    dataIndex: 'gender'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },

                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },

                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!maiche-rec/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
