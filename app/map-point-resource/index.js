/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 1000,
            store: 'jiakao-misc!map-point-resource/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '标题：',
                 dataIndex: 'title',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '标题'
             },
             {
                 header: '图片的名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '图片的名称'
             },
             {
                 header: '通关要点：',
                 dataIndex: 'desc',
                 xtype: 'richtext',
                 maxlength: 1024,
                 placeholder: '通关要点'
             },
             {
                 header: '语音内容：',
                 dataIndex: 'voiceContent',
                 xtype: 'textarea',
                 maxlength: 512,
                 placeholder: '语音内容'
             },
             {
                 header: '语音地址：',
                 dataIndex: 'voiceUrl',
                 xtype: 'textarea',
                 maxlength: 512,
                 placeholder: '语音地址'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             }

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '路线地图打点的资源列表',
            title: '路线地图打点的资源列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!map-point-resource/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '标题：',
                         dataIndex: 'title'
                     },
                     {
                         header: '图片的名称：',
                         dataIndex: 'name'
                     },
                     {
                         header: '通关要点：',
                         dataIndex: 'desc'
                     },
                     {
                         header: '语音内容：',
                         dataIndex: 'voiceContent'
                     },
                     {
                         header: '语音地址：',
                         dataIndex: 'voiceUrl'
                     },
                     {
                         header: 'createTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserId：',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: 'createUserName：',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: 'updateTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: 'updateUserId：',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: 'updateUserName：',
                         dataIndex: 'updateUserName'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 1000,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!map-point-resource/data/view',
                        save: 'jiakao-misc!map-point-resource/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '标题：',
                 dataIndex: 'title',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '标题'
             },
             {
                 header: '图片的名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '图片的名称'
             },
             {
                 header: '通关要点：',
                 dataIndex: 'desc',
                 xtype: 'richtext',
                 maxlength: 1024,
                 placeholder: '通关要点',
                 
             },
             {
                 header: '语音内容：',
                 dataIndex: 'voiceContent',
                 xtype: 'textarea',
                 maxlength: 512,
                 placeholder: '语音内容'
             },
             {
                 header: '语音地址：',
                 dataIndex: 'voiceUrl',
                 xtype: 'textarea',
                 maxlength: 512,
                 placeholder: '语音地址'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!map-point-resource/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '标题',
                         dataIndex: 'title'
                     },
                     {
                         header: '图片的名称',
                         dataIndex: 'name'
                     },
                     {
                         header: '通关要点',
                         dataIndex: 'desc',
                         render: function (data) {
                             if(data){
                                 return '<a>查看</a>'
                             }else{
                                 return ''
                             }
                             
                         },
                         click: function (table, row, lineData) {
                             Simple.Dialog.html('通关要点', {}).done(function (dialog) {
                                 dialog.body.append(lineData.desc)
                             })
                         }
                     },
                     {
                         header: '语音内容',
                         dataIndex: 'voiceContent'
                     },
                     {
                         header: '语音地址',
                         dataIndex: 'voiceUrl'
                     },
                     {
                         header: 'createTime',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserId',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: 'createUserName',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: 'updateTime',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: 'updateUserId',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: 'updateUserName',
                         dataIndex: 'updateUserName'
                     }

            ]
        }, ['jiakao-misc!map-point-resource/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});