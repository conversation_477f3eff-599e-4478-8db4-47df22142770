/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!must-exercise-detail/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '列表的id：',
                    dataIndex: 'listId',
                    xtype: 'text',
                    placeholder: '列表的id'
                },
                {
                    header: '图片：',
                    dataIndex: 'image',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '图片'
                },
                {
                    header: '详情内容：',
                    dataIndex: 'content',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '详情内容'
                },
                {
                    header: '关联试题id列表：',
                    dataIndex: 'questionList',
                    xtype: 'textarea',
                    rows: 20,
                    placeholder: '关联试题id列表'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '必刷题合集列表',
            title: '必刷题合集列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!must-exercise-detail/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '列表的id：',
                            dataIndex: 'listId'
                        },
                        {
                            header: '详情内容：',
                            dataIndex: 'content'
                        },
                        {
                            header: '关联试题id列表：',
                            dataIndex: 'questionList'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: '创建时间'
                        },
                        {
                            header: '创建人ID：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人ID：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!must-exercise-detail/data/view',
                        save: 'jiakao-misc!must-exercise-detail/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '列表的id：',
                            dataIndex: 'listId',
                            xtype: 'text',
                            placeholder: '列表的id'
                        },
                        {
                            header: '图片：',
                            dataIndex: 'image',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '图片'
                        },
                        {
                            header: '详情内容：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            maxlength: 1024,
                            rows: 15,
                            placeholder: '详情内容'
                        },
                        {
                            header: '关联试题id列表：',
                            dataIndex: 'questionList',
                            xtype: 'textarea',
                            rows: 20,
                            placeholder: '关联试题id列表'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!must-exercise-detail/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '列表的id',
                    dataIndex: 'listId'
                },
                {
                    header: '图片',
                    dataIndex: 'image',
                    render: function (data) {
                        return '<div><img style="width: 200px;" src="' + data + '"/></div>'
                    }
                },
                {
                    header: '详情内容',
                    dataIndex: 'content',
                    render: function (data) {
                        return "<a>查看内容</a>"
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert('<pre>' + Utils.format.json(lineData.content) + '</pre>')
                    }
                },
                {
                    header: '关联试题id列表',
                    dataIndex: 'questionList',
                    render: function (data) {
                        return '<div style="width: 300px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: '创建时间'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!must-exercise-detail/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
