/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([ 'simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form' ], function (Template, Table, Utils, Widgets, Store, Form) {

    var kemuMap = {
        1: '科目一',
        4: '科目四'
    }

    var kemuArr = [
        {
            key: 1,
            value: '科目一'
        },
        {
            key: 4,
            value: '科目四'
        }
    ]

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!must-exercise-list/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '标题'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'textarea',
                    rows: 3,
                    placeholder: '描述'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'radio',
                    store: kemuArr
                },
                {
                    header: '图片：',
                    dataIndex: 'image',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '图片'
                },
                {
                    header: '星级：',
                    dataIndex: 'star',
                    xtype: 'text',
                    placeholder: '星级'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '必刷题详情列表',
            title: '必刷题详情列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!must-exercise-list/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '科目',
                            dataIndex: 'kemu',
                            render: function (data) {
                                return kemuMap[data]
                            }
                        },
                        {
                            header: '图片：',
                            dataIndex: 'image'
                        },
                        {
                            header: '星级：',
                            dataIndex: 'star'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: '创建时间'
                        },
                        {
                            header: '创建人ID：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人ID：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!must-exercise-list/data/view',
                        save: 'jiakao-misc!must-exercise-list/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '标题'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'textarea',
                            rows: 3,
                            placeholder: '描述'
                        },
                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'radio',
                            store: kemuArr
                        },
                        {
                            header: '图片：',
                            dataIndex: 'image',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '图片'
                        },
                        {
                            header: '星级：',
                            dataIndex: 'star',
                            xtype: 'text',
                            placeholder: '星级'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!must-exercise-list/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data) {
                        return kemuMap[data]
                    }
                },
                {
                    header: '图片',
                    dataIndex: 'image',
                    render: function (data) {
                        return '<div><img style="width: 200px;" src="' + data + '"/></div>'
                    }
                },
                {
                    header: '星级',
                    dataIndex: 'star'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, [ 'jiakao-misc!must-exercise-list/data/list' ], panel, null).render();
    }

    return {
        list: list
    }

});
