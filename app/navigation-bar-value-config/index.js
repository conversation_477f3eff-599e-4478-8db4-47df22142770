/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

'use strict'

define([
	'simple!core/template',
	'simple!core/table',
	'simple!core/utils',
	'simple!core/widgets',
	'simple!core/store',
	'simple!core/form',
	'simple!core/plugin',
], function (Template, Table, Utils, Widgets, Store, Form, Plugin,) {
	var typeMap = {
		'saturn-home': '社区',
		'saturn-new': '最新',
		'live-video': '精品课',
		'live-stream': '直播',
		'toutiao-news': '资讯',
		'short-video': '短视频',
		'advert-drama': '短剧',
		'maiche': '买车',
		'maiche-newCar': '新车',
		'maiche-electric': '新能源',
		'maiche-usedCar': '二手车',
		'im': '消息',
		'web': '网页',
		'advert-feed': '商城'
	}
	var typeArray = []
	for (let key in typeMap) {
		typeArray.push({ key: key, value: typeMap[key] })
	}
	var uniqueKeyMap = {
		'container1':'container1',
		'container2':'container2'
	}
	var uniqueKeyArr = []
	for (let key in uniqueKeyMap) {
		uniqueKeyArr.push({ key: key, value: uniqueKeyMap[key] })
	}
	var isRepeatMethod = function (arr) {
		var hash = {};
		for (var i in arr) {
			if (hash[arr[i]])
				return true;
			hash[arr[i]] = true;
		}
		return false;
	}
	var addEdit = function (panel, parentObject, renderCallback, valueList = [], lineData = {}) {

		var isEdit = !!lineData.valueIndex
		var finalList = []

		var config = {
			title: isEdit ? '编辑' : '添加',
			width: 600,
			store: 'jiakao-misc!operation-config/data/update',
			success: function (obj, dialog) {
				dialog.close();
				renderCallback && renderCallback()
				list(panel, parentObject, renderCallback, finalList)
				
			},
			form: {
				submitHandler: function (form) {
					var newImgUrl = form.newImgUrl.value
					var objectImgUrl = JSON.parse(newImgUrl || '[]')
					var valueIndex = form.valueIndex.value
					var uniqueKey = form.uniqueKey.value
					var valueName = form.valueName.value
					var showMsgCenter = form.showMsgCenter.value
					var normalIcon = form.normalIcon.value
					var selectedIcon = form.selectedIcon.value
					var selectedGifIcon = form.selectedGifIcon.value
					var selectedChannelIndex = ''
					var allNewItemList = []
					objectImgUrl.forEach(function (item, order) {
						item.title = typeMap[item.type]
						if(item.sort){
							selectedChannelIndex = item.sort+''
						}
						if (item.type == 'web') {
							item.itemList?.forEach(function (res) {
								allNewItemList.push({
									type: item.type,
									title: res.title,
									data: {
										actionUrl: res.url
									}
								})
							})
						} else {
							allNewItemList.push({
								type: item.type,
								title: item.title,
							})
						}
					})
					var isNoSelect = allNewItemList.some(function (currentValue) {
						var isTag = false
						for (var key in currentValue) {
							if (!currentValue[key] || (key=='data'&&!currentValue[key]?.actionUrl)) {
								isTag = true
							}

						}
						return isTag
					})

					var isRep = false
					var repMap = {}
				
					objectImgUrl.forEach(function (res) {
						if (repMap[res.type] && res.type != 'web') {
							isRep = true
							return
						}
						repMap[res.type] = res.type
					})
					if (isNoSelect) {
						Widgets.dialog.alert('频道列表里面的所有选项必填');
						return
					}
					if (isRep) {
						Widgets.dialog.alert('除了网页，频道列表里面不能配置相同的值');
						return
					}
					var newParamsObeject = {
						uniqueKey,
						name:valueName,
						showMsgCenter: showMsgCenter === 'true',
						normalIcon,
						selectedIcon,
						selectedGifIcon,
						selectedChannelIndex: selectedChannelIndex + '',
						itemList: allNewItemList
					}
					
					var newvalueList = []
					if (isEdit) {
						valueList[valueIndex - 1] = {
							...newParamsObeject
						}
					} else {
						newvalueList = [newParamsObeject]
					}

					finalList = valueList.concat(newvalueList)
					var allIsArray = []
					var allUnikeyArr = []

					finalList && finalList.forEach(function (item) {
						allUnikeyArr.push(item.uniqueKey)
						item.itemList && item.itemList.forEach(function (son) {
							if (son.type != 'web') {
								allIsArray.push(son.type)
							}

						})
					})

					if (isRepeatMethod(allUnikeyArr)) {
						Widgets.dialog.alert('uniqueKey值不能与列表重复');
						return
					}
					if (isRepeatMethod(allIsArray)) {
						Widgets.dialog.alert('频道列表跟已有导航列表的频道重复，请重新选择');
						return
					}
					return {
						id: parentObject.id,
						value: JSON.stringify({ itemList: finalList })
					}
				},
				reverseParam: true
			},
			renderAfter: function (config, dom, data) {
				function renderConfig() {
					Plugin('jiakao-misc!navbar-group', {
						dataIndex: 'newImgUrl',
						target: dom.item('newImgUrl-group').find('div[class=newImg-div]'),
						value: JSON.stringify(lineData && lineData.itemList),
						typeArray: typeArray,
					}, function (plugin, value) {

					}).render();
				}
				renderConfig();
			},
			columns: [
				{
					dataIndex: 'valueIndex',
					xtype: 'hidden',
					render: function () {
						return lineData.valueIndex || ''
					}
				},
				{
					header: 'uniqueKey：',
					dataIndex: 'uniqueKey',
					xtype: 'select',
					check: 'required',
					store:[
						{ key: '', value:'请选择uniqueKey'},
						...uniqueKeyArr
					],
					placeholder: 'uniqueKey'
				},
				{
					header: '名称：',
					dataIndex: 'valueName',
					value:lineData.name||'',
					xtype: 'text',
					check: 'required',
					placeholder: '名称'
				},
				{
					header: '是否展示消息中心：',
					dataIndex: 'showMsgCenter',
					xtype: 'radio',
					store: [
						{
							key: false,
							value: '不显示'
						},
						{
							key: true,
							value: '显示'
						}
					]
				},
				{
					header: '未选中图标：',
					dataIndex: 'normalIcon',
					xtype: Plugin('jiakao-misc!upload', {
						dataIndex: 'normalIcon',
						uploadIndex: 'image',
						bucket: "jiakao-web",
						isSingle: true,
						check: 'required',
						placeholder: '请选择上传文件',
						url: 'simple-upload3://upload/file.htm'
					}, function () {

					}),
					check: 'required',
					placeholder: '未选中图标'
				},
				{
					header: '选中（动态图）：',
					dataIndex: 'selectedGifIcon',
					xtype: Plugin('jiakao-misc!upload', {
						dataIndex: 'selectedGifIcon',
						uploadIndex: 'image',
						bucket: "jiakao-web",
						isSingle: true,
						check: 'required',
						placeholder: '请选择上传文件',
						url: 'simple-upload3://upload/file.htm'
					}, function () {

					}),
					check: 'required',
					placeholder: '选中（动态图）'
				},
				{
					header: '选中（静态图）：',
					dataIndex: 'selectedIcon',
					xtype: Plugin('jiakao-misc!upload', {
						dataIndex: 'selectedIcon',
						uploadIndex: 'image',
						bucket: "jiakao-web",
						isSingle: true,
						check: 'required',
						placeholder: '请选择上传文件',
						url: 'simple-upload3://upload/file.htm'
					}, function () {

					}),
					check: 'required',
					placeholder: '选中（静态图）'
				},
				{
					header: '频道列表项：',
					dataIndex: 'newImgUrl',
					xtype: function () {
						return `<div class="newImg-div"></div>`
					},
				},
			]
		}
		if (isEdit) {
			
			lineData.itemList && lineData.itemList.forEach(function (item, index) {
				if (lineData.selectedChannelIndex==index){
					item.sort = lineData.selectedChannelIndex+''
				}else{
					item.sort = ''
				}
				
			})
			Table().edit({
				...lineData,
			}, config)
		} else {
			Table(config).add();
		}

	}
	var list = function (panel, parentObject,renderCallback, updateList) {
		var valueList = []
		if (updateList && updateList.length > 0) {
			valueList = updateList
		} else {
			valueList = JSON.parse(parentObject.value || '{"itemList": []}')
			valueList = valueList.itemList;
		}
		valueList && valueList.forEach((res, i) => {
			if (res) {
				res.valueIndex = i + 1
			} else {
				valueList.splice(i, 1)
			}

		})
		
		Table(
			{
				title: '配置项--导航栏配置Id: ' + parentObject.id,
				buttons: {
					top: [
						{
							name: '添加',
							class: 'primary',
							click: function (table) {
								addEdit(panel, parentObject, renderCallback,valueList)
							},
						},
					],
				},
				operations: [
					{
						name: '编辑',
						class: 'warning',
						click: function (table, dom, lineData) {
							addEdit(panel, parentObject, renderCallback,valueList, lineData)
						},
					},
					{
						name: '删除',
						class: 'danger',
						click: function (table, dom, lineData) {
							valueList.splice(lineData.valueIndex - 1, 1)
							Store(['jiakao-misc!operation-config/data/update'])
								.save(
									[
										{
											params: {
												id: parentObject.id,
												value: JSON.stringify({itemList:valueList})
											},
										},
									]
								)
								.done(retData => {
									renderCallback && renderCallback()
									if(valueList&&valueList.length<=0){
										parentObject.value = ''
									}
									list(panel, parentObject, renderCallback,valueList)

								})
								.fail(function (store) {
									Widgets.dialog.alert(store.message);
								});
						}
					}
				],
				columns: [
					{
						header: 'uniqueKey',
						dataIndex: 'uniqueKey',
						render:function(data){
							return uniqueKeyMap[data]
						}

					},
					{
						header: '名称',
						dataIndex: 'name',

					},
					{
						header: '默认选中频道',
						dataIndex: 'selectedChannelIndex',
						render: function (data,array,object) {
							console.log(arguments)
							let str = ''
							if (data) {
								object.itemList && object.itemList.forEach(function(res,index){
									
									if (data == index){
									
										str = res.title
									}
								})
								return str
							}

						}

					},
					{
						header: '未选中图标',
						dataIndex: 'normalIcon',
						render: function () {
							return `<a>点击查看</a>`
						},
						click: function (table, row, lineData) {
							Widgets.dialog.html('未选中图标', {}).done(function (dialog) {

								$(dialog.body).html(
									`<img src=${lineData.normalIcon}>`
								)
							})
						},
					},
					{
						header: '选中（动态图）',
						dataIndex: 'selectedGifIcon',
						render: function () {
							return `<a>点击查看</a>`
						},
						click: function (table, row, lineData) {
							Widgets.dialog.html('选中（动态图）', {}).done(function (dialog) {

								$(dialog.body).html(
									`<img src=${lineData.selectedGifIcon}>`
								)
							})
						},
					},
					{
						header: '选中（静态图）',
						dataIndex: 'selectedIcon',
						render: function () {
							return `<a>点击查看</a>`
						},
						click: function (table, row, lineData) {
							Widgets.dialog.html('选中（静态图）', {}).done(function (dialog) {

								$(dialog.body).html(
									`<img src=${lineData.selectedIcon}>`
								)
							})
						},
					},
					{
						header: '频道列表标题',
						dataIndex: 'itemList',
						render: function (data) {
							let strArr = []
							data && data.forEach(function (res) {
								strArr.push(res.title)
							})
							return strArr.join('，')

						},
					},
					{
						header: '频道列表配置项',
						dataIndex: 'itemList',
						render: function () {
							return `<a>点击查看</a>`
						},
						click: function (table, row, lineData) {
							var json = JSON.stringify(lineData.itemList)
							Widgets.dialog.html('配置项', {}).done(function (dialog) {
								var data = lineData.itemList && JSON.stringify(JSON.parse(json), null, 4)
								$(dialog.body).html(
									'<pre style="max-height: 200px; overflow: auto">' + (data || '无') + '</pre>'
								)
							})
						},
					},
				],
			},
			{ data: valueList },
			panel,
			null
		).render()
	}

	return {
		list: list,
	}
})
