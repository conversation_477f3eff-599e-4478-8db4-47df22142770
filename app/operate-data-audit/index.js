/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    var executeStatusMap = {
        1: '未执行',
        2: '执行成功',
        3: '执行失败'
    }
    var statusMap = {
        1: '未审核',
        2: '审核通过',
        3: '审核拒绝'
    }
    var viewStatusMap = {
        1: '只查我创建的',
        2: '只查我更新的',
        3: '查所有'
    }
    var dataTypeMap = {
        'route-video-item': 'route-video-item',
    }
    var methodNameMap = {
        'create': '新增',
        'insert': '新增',
        'add': '新增',
        'update': '编辑',
        'updateById': '编辑',
        'delete': '删除',
        'deleteById': '删除',
    }
    var handleArray = function (map) {
        let newArray = []
        for (let key in map) {
            newArray.push({ key: key, value: map[key], search: map[key] })
        }
        return newArray
    }
    var applyDialog = function (table, lineData = {}) {
        Table({
            title: '审核',
            width: 800,
            store: 'jiakao-misc!operate-data-audit/data/audit',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },

            renderAfter: function (tab, dom) {
                // 表格形式展示基本数据
                dom.item('biaoge-tips-group').html(
                    ` <table class="table table-striped table-bordered table-hover">
                         <tbody>
                         <tr data-item="id-group">
                             <td class="col-sm-3">数据对象</td>
                             <td class="">${dataTypeMap[lineData.dataType]}</td>
                              <td class="col-sm-2">操作类型</td>
                             <td class="">${methodNameMap[lineData.methodName]}</td>
                        </tr>
                         <tr data-item="id-group">
                             <td class="col-sm-3">数据ID</td>
                                  <td class="">${lineData.dataId}</td>
                              <td class="col-sm-2">状态</td>
                               <td class="">${statusMap[lineData.auditStatus]}</td>
                        </tr>
                         <tr data-item="id-group">
                             <td class="col-sm-3">操作原因</td>
                             <td class="">${lineData.reason}</td>
                              <td class="col-sm-2"></td>
                             <td class=""></td>
                        </tr>
                        <tr data-item="id-group">
                              <td class="col-sm-3">操作人</td>
                             <td class="">${lineData.createUserName}</td>
                              <td class="col-sm-2"></td>
                             <td class=""></td>
                        </tr>
                        </tbody>
                          
                         </table>
                         `
                )
                // 样式对齐
                dom.item('auditStatus-group').find('label[for="auditStatus"]').css('width', '100px')
                dom.item('comment-group').find('label[for="comment"]').css('width', '100px')
                dom.item('type-json-text-group').html('')
                CodeMirror.MergeView(dom.item('type-json-text-group')[0], {
                    value: JSON.stringify(JSON.parse(lineData.param), null, 2), // 右侧新文件
                    origLeft: JSON.stringify(JSON.parse(lineData.oldData || '{}'), null, 2),// 左侧老文件 
                    mode: 'application/json',
                    // mode: 'xml',
                    lineNumbers: true,
                    lineWrapping: true, // 是否默认换行
                    readOnly: true, // 禁止用户编辑编辑器内容
                    showDifferences: true,//展示更改的
                    autoRefresh: true,
                    matchBrackets: true, // 括号匹配
                    lint: true,
                    connect: null,
                    revertButtons: false, // 是否显示还原按钮
                    highlightDifferences: "highlight",//有差异的地方是否高亮
                    theme: '3024-day', // 主题
                    smartIndent: true // 智能缩进
                });
                setTimeout(() => {
                    //插入操作前内容，操作后内容页面
                    $($(dom.item('type-json-text-group')).find('.CodeMirror-merge-pane')[0]).prepend(`
                     <div style="width:100%;height:44px;border:1px solid #dedede;text-align:center;line-height:44px">操作前内容</div>
                    
                    `)
                    $($(dom.item('type-json-text-group')).find('.CodeMirror-merge-pane')[1]).prepend(`
                     <div style="width:100%;height:44px;border:1px solid #dedede;text-align:center;line-height:44px">操作后内容</div>
                    
                    `)
                }, 500)
                // 二次弹窗确认保存
                var dialogButton = $(dom).parents('.modal-content').find('button[type="submit"]')
                $(dialogButton).before(`
                        <div  data-item="get-dialog-from"   class="btn btn-success get-dialog-from">保存</div>
                        `)
                $(dialogButton).css('display', 'none');
                $(dom).parents('.modal-content').on('click', '.get-dialog-from', function () {
                    var status = dom.item('auditStatus').val()
                    var comment = dom.item('comment').val()
                    if (!status) {
                        Widgets.dialog.alert('请选择审核状态')
                        return
                    }
                    if (status == 3 && !comment) {
                        Widgets.dialog.alert('审核拒绝,原因必填')
                        return
                    }
                    var statusAtring = status == 2 ? '审核通过' : '审核拒绝'
                    Table({
                        title: '询问',
                        width: 400,
                        form: {
                            submitHandler: function (form, fun) {
                                console.log('$(dialogButton)', $(dialogButton))
                                $(dialogButton).click();
                                $(form).parent().find('.close').click()
                                return false
                            }
                        },
                        renderAfter: function (tab, dom) {
                            $(dom.item('show-group')).find('.control-label').css('width', '15%')
                            $(dom.item('showreason-group')).find('.control-label').css('width', '15%')
                        },
                        columns: [
                            {
                                header: '',
                                dataIndex: 'show',
                                xtype: function () {
                                    return `
                                    <div>审核结果：${statusAtring}（<span style="color:#ff0000;fontWeight:blod;">${statusAtring}，相关操作将被立即执行</span>）</div>`
                                },
                            },
                            {
                                header: '',
                                dataIndex: 'showreason',
                                xtype: function () {
                                    return `
                                    <div>审核说明：${comment}</div>`
                                },
                            },

                        ],
                    }).add()
                })
            },
            columns: [
                {

                    dataIndex: 'biaoge-tips',
                    xtype: 'text'
                },
                {

                    dataIndex: 'type-json-text',
                    xtype: 'text',
                },
                {

                    dataIndex: 'id',
                    xtype: 'hidden',
                    value: lineData.id
                },
                {
                    header: '审核结果：',
                    dataIndex: 'auditStatus',
                    xtype: 'select',
                    store: [
                        { key: '', value: '请选择审核结果' },
                        { key: 2, value: '审核通过' },
                        { key: 3, value: '审核拒绝' },
                    ],
                },
                {
                    header: '审核说明：',
                    dataIndex: 'comment',
                    xtype: 'textarea',
                    placeholder: '请填写审核说明（操作人可见，审核拒绝时必填）'
                },
            ]
        }).add();
    }
    var list = function (panel) {
        Table({
            description: 'operate-data-audit列表',
            title: 'operate-data-audit列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },

                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [
                {
                    dataIndex: 'dataType',
                    xtype: 'select',
                    store: [
                        { key: '', value: '请选择数据对象' },
                        ...handleArray(dataTypeMap)
                    ],
                    placeholder: '请输入数据对象'
                },
                {
                    dataIndex: 'dataId',
                    xtype: 'text',
                    placeholder: '请输入数据ID'
                },
                {
                    dataIndex: 'auditStatus',
                    xtype: 'select',
                    store: [
                        { key: '', value: '选择状态' },
                        ...handleArray(statusMap)
                    ]
                },
                {
                    dataIndex: 'queryRange',
                    xtype: 'select',
                    store: [
                        { key: '', value: '选择查看范围' },
                        ...handleArray(viewStatusMap)
                    ]
                },
                {
                    dataIndex: 'createTimeStart',
                    xtype: 'date',
                    placeholder: '创建开始时间'
                },
                {
                    dataIndex: 'createTimeEnd',
                    xtype: 'date',
                    placeholder: '创建结束时间'
                },
            ],
            operations: [
                {
                    name: '审核',
                    class: 'primary',
                    render: function (data, array, index) {
                        if (array[index].auditStatus == 1) {
                            return '审核'
                        }

                    },
                    click: function (table, dom, lineData) {
                        applyDialog(table, lineData)

                    }
                }

            ],
            line: {
                class: function (data, type) {
                    if (type == 'body') {
                        if (data.auditStatus == 1) {
                            return 'warning'
                        } else if (data.auditStatus == 2) {
                            return 'success'
                        } else if (data.auditStatus == 3) {
                            return 'danger'
                        }
                    }
                }
            },
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '数据对象',
                    dataIndex: 'dataType',
                    render: function (data) {
                        return dataTypeMap[data]
                    }
                },
                {
                    header: '数据ID',
                    dataIndex: 'dataId'
                },
                {
                    header: '操作类型',
                    dataIndex: 'methodName',
                    render: function (data) {
                        return methodNameMap[data]
                    },

                },
                {
                    header: '操作前的数据',
                    dataIndex: 'oldData',
                    render: function (data) {
                        return '<a>查看</a>'
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html('操作前的数据', '<pre>' + JSON.stringify(JSON.parse(lineData.oldData || "{}"), null, 4) + '</pre>');
                    }


                },
                {
                    header: '操作后的数据',
                    dataIndex: 'param',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, lineDom, lineData) {
                        Widgets.dialog.html('操作后的数据', '<pre>' + JSON.stringify(JSON.parse(lineData.param), null, 4) + '</pre>');
                    },
                },

                {
                    header: '状态',
                    dataIndex: 'auditStatus',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '操作原因',
                    dataIndex: 'reason'
                },

                {
                    header: '审核说明',
                    dataIndex: 'comment'
                },
                {
                    header: '审核人',
                    dataIndex: 'auditUserName'
                },
                {
                    header: ' 审核时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'auditTime'
                },
                {
                    header: '执行状态',
                    dataIndex: 'executeStatus',
                    render: function (data) {
                        return executeStatusMap[data]
                    }
                },
                {
                    header: '执行异常信息',
                    dataIndex: 'executeMessage'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '操作人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                // {
                //     header: '修改人名称',
                //     dataIndex: 'updateUserName'
                // },


            ]
        }, ['jiakao-misc!operate-data-audit/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});