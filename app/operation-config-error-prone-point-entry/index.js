/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/constants', 'jiakao-misc!app/common/tiku', 'jiakao-misc!app/common/tools', 'jiakao-misc!app/operation-config3/index',], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, TIKU, Tools, OpConfig) {
    let statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }
    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        const value = JSON.parse(lineData.value || '{}');
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 600,
            store: 'jiakao-misc!operation-config/data/' + (isEdit ? 'update' : 'insert?bizType=yicuo_knowledge_practice&code=yicuo_knowledge_practice_entrance'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var imageUrl = $(form).find('input[name="imageUrl"]').val();
                    var imageActionUrl = form.imageActionUrl.value
                    return {
                        name:'易错考点入口配置',
                        value:JSON.stringify({
                           imageUrl:imageUrl,
                           imageActionUrl:imageActionUrl
                        })
                    };
                },
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
               {
                    header: '配置说明',
                    dataIndex: 'remark',
                    xtype: 'textarea',
                    placeholder: '配置说明'
                },
                {
                    header: '图片跳转地址：',
                    dataIndex: 'imageActionUrl',
                    xtype: 'text',
                    check: 'required',
                    placeholder: 'banner跳转地址'
                },
                {
                    header: '图片：',
                    dataIndex: 'imageUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'imageUrl',
                        uploadIndex: 'imageUrl',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                 {
                    header: '场景code：',
                    dataIndex: 'sceneCode',
                    check: 'required',
                    xtype: 'checkbox',
                    store: Constants.senceStore,
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    check: 'required',
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    check: 'required',
                    xtype: 'checkbox',
                    store: [
                        ...Tools.getArrayFromMap(TIKU)
                    ],
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    check: 'required',
                    store: Constants.kemuStore.slice(1),
                    placeholder: '科目'
                },
            ]
        }
        if (isEdit) {
            Table().edit({
                ...lineData,
               imageActionUrl:value.imageActionUrl,
               imageUrl:value.imageUrl,
               value:value
            }, config);
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Table({
            description: '易错考点入口配置',
            title: '易错考点入口配置',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '开启',
                    width: 500,
                    class: 'primary',
                    render: function (text, data, index) {
                        if (data[index].status === 0) {
                            return '测试上线'
                        }
                        if (data[index].status === 1) {
                            return '上线'
                        }
                        if (data[index].status === 2) {
                            return '下线'
                        }
                    },
                    click: function (table, lineDom, lineData, dom, data, index) {
                        Store([`jiakao-misc!operation-config/data/update?id=${lineData.id}&status=${(lineData.status + 1) % 3} `]).save([{}]).done(function (store, data) {
                            table.render();
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        })
                    },
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!operation-config/data/delete'
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        OpConfig.editPersonas(table, lineData)
                    },
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '配置code',
                    dataIndex: 'code',
                    width:20,
                    render: function (data) {
                        return '<div style="width:100px;word-break: break-all">'+data+'</div>'
                    }
                },
                {
                    header: '图片',
                    dataIndex: 'value',
                    render: function (data) {
                        var value = JSON.parse(data||"{}");
                        if(value.imageUrl){
                            return '<a>查看</a>'
                        }
                        return value.imageUrl
                    },
                    click:function(table,dom,lineData){
                      var value = JSON.parse(lineData.value||"{}")
                      Widgets.dialog.html('图片', {}).done(function (dialog) {
                            $(dialog.body).html(`<img src=${value.imageUrl}>`)
                        });
                    }
                },
                {
                    header: '图片跳转地址',
                    dataIndex: 'value',
                    width:100,
                    render: function (data) {
                        var value = JSON.parse(data||"{}")
                         return '<div style="width:300px;word-break: break-all">'+value.imageActionUrl+'</div>'
                    }
                },
                 {
                    header: '场景',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.senceMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'patternCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.editionMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },

                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                if (data[i] == '5') {
                                    strArr.push('拿本')
                                } else {
                                    strArr.push(Constants.kemuMap[data[i]])
                                }

                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '创建时间 ',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间 ',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!operation-config/data/list?bizType=yicuo_knowledge_practice&codes=yicuo_knowledge_practice_entrance'], panel, null).render();
    }

    return {
        list: list
    }

});