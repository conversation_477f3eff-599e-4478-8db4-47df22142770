/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/constants', 'jiakao-misc!app/common/tiku', 'jiakao-misc!app/common/tools', 'jiakao-misc!app/operation-config3/index',], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, TIKU, Tools, OpConfig) {

    let codeMap = {
        'kemu_pass_rate_reminder_kemu_homepage': '科目首页',
        'kemu_pass_rate_reminder_exam_result_page': '考试结果页',
        'kemu_pass_rate_reminder_vip_page': 'vip页面'
    }

    let statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

    let cityList = (() => {
        let arr = [];
        Simple.DISTRICT.forEach(item => {
            item.cities.forEach(ele => {
                arr.push({
                    key: ele.code,
                    value: ele.name
                })
            })
        });

        return arr
    })()

    let cityMap = Tools.getMapfromArray(cityList)

    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        const value = JSON.parse(lineData.value || '{}');
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 600,
            store: 'jiakao-misc!operation-config/data/' + (isEdit ? 'update' : 'insert?bizType=yicuo_knowledge_practice&code=yicuo_knowledge_practice_detail'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var bannerUrl = $(form).find('input[name="bannerUrl"]').val();
                    var bannerActionUrl = form.bannerActionUrl.value
                    var newPoint = form.newPoint.value
                    var objectPoint = JSON.parse(newPoint||'[]')
					 var isNoSelect = objectPoint.some(function(currentValue){
                        var isTag = false
                        for(var key in currentValue){
                          if(!currentValue[key]){
                            isTag = true
                          }
                        
                        }
                        return isTag
                     })
                     if(isNoSelect){
                        Widgets.dialog.alert('知识点项里面的所有选项必填');
                        return 
                     }
                    return {
                        name:'易错考点专练详情配置',
                        value:JSON.stringify({
                           bannerUrl:bannerUrl,
                           bannerActionUrl:bannerActionUrl,
                           knowledgeList:objectPoint
                        })
                    };
                },
            },
            renderAfter: function (config, dom, data) {
                function renderConfig(){
                     Plugin('jiakao-misc!group5', {
                        dataIndex: 'newPoint',
                        target: dom.item('newPoint-group').find('div[class=newPoint-div]'),
                        value:value.knowledgeList&&value.knowledgeList,
                    }, function (plugin, value) {

                    }).render();
                }
                renderConfig()
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '场景code：',
                    dataIndex: 'sceneCode',
                    check: 'required',
                    xtype: 'checkbox',
                    store: Constants.senceStore,
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    check: 'required',
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    check: 'required',
                    xtype: 'checkbox',
                    store: [
                        ...Tools.getArrayFromMap(TIKU)
                    ],
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    check: 'required',
                    store: Constants.kemuStore.slice(1).concat([{ key: '5', value: '拿本' }]),
                    placeholder: '科目'
                },
                {
                    header: '配置说明',
                    dataIndex: 'remark',
                    xtype: 'textarea',
                    placeholder: '配置说明'
                },
                {
                    header: 'banner跳转地址：',
                    dataIndex: 'bannerActionUrl',
                    xtype: 'text',
                    check: 'required',
                    placeholder: 'banner跳转地址'
                },
                {
                    header: 'banner图片：',
                    dataIndex: 'bannerUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'bannerUrl',
                        uploadIndex: 'bannerUrl',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                 {
                    header: '知识点项：',
					dataIndex: 'newPoint',
					xtype: function(){
                        return `<div class="newPoint-div"></div>`
                    },
                },
            ]
        }
        if (isEdit) {
            Table().edit({
                ...lineData,
               bannerActionUrl:value.bannerActionUrl,
               bannerUrl:value.bannerUrl,
               value:value
            }, config);
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Table({
            description: '易错考点专练详情页配置',
            title: '易错考点专练详情页配置',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '开启',
                    width: 500,
                    class: 'primary',
                    render: function (text, data, index) {
                        if (data[index].status === 0) {
                            return '测试上线'
                        }
                        if (data[index].status === 1) {
                            return '上线'
                        }
                        if (data[index].status === 2) {
                            return '下线'
                        }
                    },
                    click: function (table, lineDom, lineData, dom, data, index) {
                        Store([`jiakao-misc!operation-config/data/update?id=${lineData.id}&status=${(lineData.status + 1) % 3} `]).save([{}]).done(function (store, data) {
                            table.render();
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        })
                    },
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!operation-config/data/delete'
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        OpConfig.editPersonas(table, lineData)
                    },
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '配置code',
                    dataIndex: 'code',
                    render: function (data) {
                        return data
                    }
                },
                {
                    header: '知识点配置内容',
                    dataIndex: 'value',
                    render: function () {
                        return '<a>查看详情</a>';
                    },
                    click: function (table, row, lineData) {
                        var value = JSON.parse(lineData.value)
                        Widgets.dialog.html('知识点配置内容', {}).done(function (dialog) {
                            $(dialog.body).html('<pre style="max-height: 600px; overflow: auto">' + JSON.stringify(value,null,4) + '</pre>')
                        });
                    }
                },
                {
                    header: '配置说明',
                    dataIndex: 'remark'
                },
                {
                    header: '场景',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.senceMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'patternCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.editionMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },

                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                if (data[i] == '5') {
                                    strArr.push('拿本')
                                } else {
                                    strArr.push(Constants.kemuMap[data[i]])
                                }

                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '创建时间 ',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间 ',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!operation-config/data/list?bizType=yicuo_knowledge_practice&codes=yicuo_knowledge_practice_detail'], panel, null).render();
    }

    return {
        list: list
    }

});