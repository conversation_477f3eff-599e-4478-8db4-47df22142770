/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

'use strict'

define([
	'simple!core/template',
	'simple!core/table',
	'simple!core/utils',
	'simple!core/widgets',
	'simple!core/store',
	'simple!core/form',
	'jiakao-misc!app/common/constants',
	'simple!core/plugin',
	'jiakao-misc!app/common/tiku',
	'jiakao-misc!plugin/select-district/district3',
	'jiakao-misc!app/operation-config3/index',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, District3, OpConfig) {
	var zxCitys = ['120000', '110000', '500000', '310000']

	var cityTypeMap = {
		userSelect: '用户选择城市匹配',
		gps: 'GPS匹配',
	}

	var cityArr = []
	for (const key in cityTypeMap) {
		cityArr.push({
			key,
			value: cityTypeMap[key],
		})
	}

	var carTypeArr = []

	for (var k in TIKU) {
		carTypeArr.push({
			key: k,
			value: TIKU[k],
		})
	}

	var getAllCity = function () {
		var list = District3.list
		var city = []

		for (var i = 0; i < list.length; ++i) {
			if (
				list[i].cities &&
				list[i].code != zxCitys[0] &&
				list[i].code != zxCitys[1] &&
				list[i].code != zxCitys[2] &&
				list[i].code != zxCitys[3]
			) {
				var data = list[i].cities
				city.push({
					key: list[i].code,
					value: list[i].name,
					search: list[i].name,
				})
				for (var j = 0; j < data.length; ++j) {
					city.push({
						key: data[j].code,
						value: data[j].name,
						search: data[j].name,
					})
				}
			}
		}

		city.unshift({
			key: '310000',
			value: '上海',
			search: '上海',
		})
		city.unshift({
			key: '120000',
			value: '天津',
			search: '天津',
		})
		city.unshift({
			key: '500000',
			value: '重庆',
			search: '重庆',
		})
		city.unshift({
			key: '110000',
			value: '北京',
			search: '北京',
		})
		city.unshift({
			key: '000000',
			value: '全国',
			search: '全国',
		})
		return city
	}

	Constants.kemuStore = Constants.kemuStore.map(item => ({
		key: item.key + '',
		value: item.value,
	}))

	console.log(Constants.kemuStore, 'Constants.kemuStore')

	var statusMap = {
		2: '发布',
		0: '下线',
		1: '测试发布',
	}

	var statusArr = []

	for (const key in statusMap) {
		statusArr.push({
			key,
			value: statusMap[key],
		})
	}
	var addEdit = function (table, codeMap,type, lineData = {}) {
		var isEdit = type!=='add'
        if(isEdit){
            var valueObject = JSON.parse(lineData.value||'{}')
            var bizRule = JSON.parse(lineData.bizRule||'{}')
            lineData.practiceType = bizRule.practiceType
        }
		var config = {
			title: isEdit ? '编辑' : '添加',
			width: 600,
			store: 'jiakao-misc!operation-config/data/' + (type=='edit' ? 'update' : 'insert'),
			success: function (obj, dialog) {
				dialog.close()
				table.render()
			},
			form: {
				submitHandler: function (form) {
                    var newImgUrl = form.newImgUrl.value
                    var objectImgUrl = JSON.parse(newImgUrl||'[]')
					var practiceType  = form.practiceType&&form.practiceType.value
                    var code = form.code.value;
					 var isNoSelect = objectImgUrl.some(function(currentValue){
                        var isTag = false
                        for(var key in currentValue){
                          if(!currentValue[key]){
                            isTag = true
                          }
                        
                        }
                        return isTag
                     })
                     if(isNoSelect){
                        Widgets.dialog.alert('配置项里面的所有选项必填');
                        return 
                     }
                   if(code=='practice_front_page_basic_consolidation'&&!practiceType){
                     Widgets.dialog.alert('练习前置页面类型必选')
                     return
                   }
                   var bizRuleMap = {}
                   if(code=='practice_front_page_basic_consolidation'){
                       bizRuleMap.bizRule = JSON.stringify({practiceType}) 
                   }
					return {
						value:JSON.stringify({itemList:objectImgUrl}),
						name: codeMap[code],
                        ...bizRuleMap
					}
				},
			},
			renderAfter: function (config, dom, data) {
                function renderConfig(type){
                    Plugin('jiakao-misc!group4', {
                        dataIndex: 'newImgUrl',
                        target: dom.item('newImgUrl-group').find('div[class=newImg-div]'),
                        value:JSON.stringify(valueObject&&valueObject.itemList),
                        isShowTitle:type=='simulation_exam_front_page_button'
                    }, function (plugin, value) {

                    }).render();
                    }
               
				renderItem(dom.item('code').val())
                renderConfig(dom.item('code').val());
				dom.item('code').on('change', function () {
					renderItem($(this).val())
                    renderConfig($(this).val())
				})
				function renderItem(code) {
					if (code == 'simulation_exam_front_page_button') {
						dom.item('configName-group').show()
						dom.item('practiceType-group').hide()
					} else if (code == 'practice_front_page_basic_consolidation') {
						dom.item('configName-group').hide()
						dom.item('practiceType-group').show()
					} else {
						dom.item('configName-group').hide()
						dom.item('practiceType-group').hide()
					}
				}
			},
			columns: [
				{
					dataIndex: 'id',
					xtype: 'hidden',
				},
                {
					dataIndex: 'bizType',
					xtype: 'hidden',
                    render:function(){
                        return 'exam_practice_front_page'
                    }
				},
				{
					header: '模块：',
					dataIndex: 'code',
					xtype: 'select',
					store: 'jiakao-misc!operation-config/data/codeList?bizType=exam_practice_front_page',
					index: [
						{
							key: 'key',
							value: 'value',
						},
					],
					check: 'required',
					insert: [
						{
							key: '',
							value: '请选择',
						},
					],
				},
				{
					header: '排序值：',
					dataIndex: 'sort',
					xtype: 'number',
					check: 'required',
					placeholder: '排序值',
				},
				{
					header: '配置说明：',
					dataIndex: 'remark',
					xtype: 'text',
					maxlength: 128,
					check: 'required',
					placeholder: '配置说明',
				},
				{
					header: '过滤城市:',
					dataIndex: 'cityCode',
					xtype: Plugin(
						'jiakao-misc!auto-prompt',
						{
							store: getAllCity(),
							dataIndex: 'cityCode',
							isMulti: true,
							defaultVal: false,
						},
						function (plugin, value) {}
					),
				},
				{
					header: '城市匹配模式：',
					dataIndex: 'cityType',
					xtype: 'select',
					store: cityArr,
					maxlength: 45,
					placeholder: '城市匹配模式',
				},
				{
					header: '车型：',
					dataIndex: 'carType',
					xtype: 'checkbox',
					store: carTypeArr,
					placeholder: '车型',
				},
				{
					header: '科目：',
					dataIndex: 'kemu',
					xtype: 'checkbox',
					store: Constants.kemuStore.slice(1),
					placeholder: '科目',
				},
				{
					header: '场景：',
					dataIndex: 'sceneCode',
					xtype: 'checkbox',
					store: Constants.senceStore,
				},
				{
					header: '访问模式：',
					dataIndex: 'patternCode',
					xtype: 'checkbox',
					store: Constants.editionStore,
					placeholder: '访问模式',
				},
				{
					header: '状态：',
					dataIndex: 'status',
					xtype: 'select',
					store: statusArr,
					value: 2,
					check: 'required',
					placeholder: '状态',
				},
                {
                    header: '配置项：',
					dataIndex: 'newImgUrl',
					xtype: function(){
                        return `<div class="newImg-div"></div>`
                    },
                },
				{
					header: '练习前置页面类型：',
					dataIndex: 'practiceType',
					xtype: 'select',
					store: [
						{ key: '', value: '请选择练习前置页面类型' },
						{ key: 1, value: '精简题库' },
						{ key: 2, value: '考前秘卷' },
					],
				},
			],
		}

		if (isEdit) {
			Table().edit(lineData, config)
		} else {
			Table(config).add()
		}
	}

	var list = function (panel) {
		Store(['jiakao-misc!operation-config/data/codeList?bizType=exam_practice_front_page'])
			.load()
			.done(retData => {
				console.log(retData, '125')
				const codeArr = retData.data['operation-config'].data.codeList.data
				let codeMap = {}
				codeArr.forEach(code => {
					codeMap[code.key] = code.value
				})
				Table(
					{
						description: '考试/练习前置页配置',
						title: '考试/练习前置页配置',
						search: [
							{
								dataIndex: 'codes',
								xtype: 'select',
								store: [
									{
										key: Object.keys(codeMap) + '',
										value: '全部',
									},
								].concat(codeArr),
							},
							{
								header: '车型：',
								dataIndex: 'carType',
								xtype: 'select',
								store: [{ key: '', value: '请选择车型' }, ...carTypeArr],
							},
							{
								header: '科目：',
								dataIndex: 'kemu',
								xtype: 'select',
								store: Constants.kemuStore,
								placeholder: '科目',
							},
							{
								header: '状态:',
								dataIndex: 'status',
								xtype: 'select',
								store: [{ key: '', value: '请选择状态' }, ...statusArr],
							},
						],
						buttons: {
							top: [
								{
									name: '刷新',
									class: 'info',
									click: function (obj) {
										obj.render()
									},
								},
								{
									name: '添加',
									class: 'primary',
									click: function (table) {
										addEdit(table, codeMap,'add')
									},
								},
							],
							bottom: [
								{
									name: '刷新',
									class: 'info',
									click: function (obj) {
										obj.render()
									},
								},
							],
						},
						operations: [
							{
								name: '查看',
								xtype: 'view',
								width: 400,
								class: 'success',
								title: '查看',
								store: 'jiakao-misc!operation-config/data/view',
								columns: [
									{
										header: '#',
										dataIndex: 'id',
									},
									{
										header: '名称：',
										dataIndex: 'name',
									},
									{
										header: '模块：',
										dataIndex: 'code',
									},
									{
										header: '配置内容：',
										dataIndex: 'value',
									},
									{
										header: '排序值：',
										dataIndex: 'sort',
									},
									{
										header: '配置说明：',
										dataIndex: 'remark',
									},
									{
										header: '过滤城市：',
										dataIndex: 'cityCode',
									},
									{
										header: '城市匹配模式：',
										dataIndex: 'cityType',
									},
									{
										header: '车型：',
										dataIndex: 'carType',
										render: function (data) {
											if (data) {
												data = data.split(',')
												var strArr = []
												for (var i = 0; i < data.length; i++) {
													strArr.push(TIKU[data[i]])
												}
												return strArr.join(',')
											}
										},
									},
									{
										header: '科目 1,2,3,4：',
										dataIndex: 'kemu',
										render: function (data, arr, i) {
											if (data) {
												data = data.split(',')
												var strArr = []
												for (var i = 0; i < data.length; i++) {
													strArr.push(Constants.kemuMap[data[i]])
												}
												return strArr.join(',')
											}
										},
									},
									{
										header: '场景',
										dataIndex: 'sceneCode',
										render: function (data, arr, i) {
											if (data) {
												data = data.split(',')
												var strArr = []
												for (var i = 0; i < data.length; i++) {
													strArr.push(Constants.senceMap[data[i]])
												}
												return strArr.join(',')
											}
										},
									},
									{
										header: '访问模式',
										dataIndex: 'patternCode',
										render: function (data, arr, i) {
											if (data) {
												data = data.split(',')
												var strArr = []
												for (var i = 0; i < data.length; i++) {
													strArr.push(Constants.editionMap[data[i]])
												}
												return strArr.join(',')
											}
										},
									},
									{
										header: '通用配置：',
										dataIndex: 'conditions',
									},
									{
										header: '状态：',
										dataIndex: 'status',
										render: function (data, arr, i) {
											return statusMap[data]
										},
									},
								],
							},
							{
								name: '投放策略',
								class: 'success',
								click: function (table, lineDom, lineData, dom, data, index) {
									Plugin('simple!product-filter', {
										store: 'jiakao-misc!operation-config/data/filter?id=' + lineData.id,
									})
										.render()
										.done(function () {
											// Store(['sirius!goods-session/data/updateAdverts']).load();
											table.render()
										})
								},
							},
							{
								name: '编辑',
								class: 'warning',
								click: function (table, dom, lineData) {
									addEdit(table, codeMap, 'edit',lineData)
								},
							},
							{
								name: '删除',
								class: 'danger',
								xtype: 'delete',
								store: 'jiakao-misc!operation-config/data/delete',
							},
							{
								name: '用户画像',
								class: 'success',
								click: function (table, dom, lineData) {
									OpConfig.editPersonas(table, lineData)
								},
							},
							{
								class: 'danger',
								render: function (name, arr, index) {
									const status = arr[index].status
									if (status == 0) {
										return '测试发布'
									} else if (status == 1) {
										return '发布'
									} else if (status == 2) {
										return '下线'
									}
								},
								click: function (table, row, lineData) {
									console.log(lineData, 'lineData')
									const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1
									console.log(status, 'status')
									let title =
										lineData.status == 1
											? '确定发布吗?'
											: lineData.status == 2
											? '确定下线吗?'
											: '确定测试发布吗?'
									Widgets.dialog.confirm(title, function (e, confirm) {
										if (confirm) {
											Store(['jiakao-misc!operation-config/data/update'])
												.save([
													{
														params: {
															id: lineData.id,
															status,
														},
													},
												])
												.done(function () {
													table.render()
												})
												.fail(function (ret) {
													Widgets.dialog.alert(ret.message)
												})
										}
									})
								},
							},
						    {
                                name:'复制',
                                class:'warning',
                                click:function(table,dom,lineData){
                                      delete lineData['id']
                                      addEdit(table, codeMap,'copy',lineData)
                                } 
                            }
						],
						columns: [
							{
								header: '#',
								dataIndex: 'id',
								width: 20,
							},
							{
								header: '名称',
								dataIndex: 'name',
                                 render:function(data){
                                    return `<div style="width:100px;word-break:break-all;white-space: pre-wrap">${data}</div>`
                                }
							},
							{
								header: '模块',
								dataIndex: 'code',
                                render:function(data){
                                    return `<div style="width:150px;word-break:break-all;white-space: pre-wrap">${data}</div>`
                                }
							},
							{
								header: '配置内容',
								dataIndex: 'value',
								render: function () {
									return `<a>点击查看</a>`
								},
								click: function (table, row, lineData) {
									Widgets.dialog.html('配置内容', {}).done(function (dialog) {
										var data = lineData.value && JSON.stringify(JSON.parse(lineData.value), null, 4)
										$(dialog.body).html(
											'<pre style="max-height: 200px; overflow: auto">' + data + '</pre>'
										)
									})
								},
							},
                            {
								header: 'bizRule扩展规则',
								dataIndex: 'bizRule',
								render: function () {
									return `<a>点击查看</a>`
								},
								click: function (table, row, lineData) {
									Widgets.dialog.html('bizRule扩展规则', {}).done(function (dialog) {
										var data = lineData.bizRule && JSON.stringify(JSON.parse(lineData.bizRule), null, 4)
										$(dialog.body).html(
											'<pre style="max-height: 200px; overflow: auto">' + (data||'无') + '</pre>'
										)
									})
								},
							},
							{
								header: '配置的图片',
								dataIndex: 'value',
								render: function () {
									return `<a>点击查看</a>`
								},
								click: function (table, row, lineData) {
									let valueObject = JSON.parse(lineData.value||'{}')
									Widgets.dialog.html('配置的图片', {}).done(function (dialog) {
                                        if(valueObject.itemList&&valueObject.itemList.length){
                                            let imgStr = ''
                                            valueObject.itemList.forEach((item)=>{
                                                imgStr+=`<div> 图片: <img src="${item.imageUrl}" style="width:200px"></div>`
                                            })
                                         $(dialog.body).html(imgStr)
                                        }else{
                                            $(dialog.body).html(
											`无`
										)
                                        }
										
									})
								},
							},
							{
								header: '排序值',
								dataIndex: 'sort',
								order: 'asc',
							},
							{
								header: '配置说明',
								dataIndex: 'remark',
							},
							{
								header: '过滤城市',
								dataIndex: 'cityName',
							},
							{
								header: '城市匹配模式',
								dataIndex: 'cityType',
								render: function (data) {
									return cityTypeMap[data]
								},
							},

							{
								header: '车型',
								dataIndex: 'carType',
								render: function (data) {
									if (data) {
										data = data.split(',')
										var strArr = []
										for (var i = 0; i < data.length; i++) {
											strArr.push(TIKU[data[i]])
										}
										return strArr.join(',')
									}
								},
							},
							{
								header: '科目',
								dataIndex: 'kemu',
								render: function (data, arr, i) {
									if (data) {
										data = data.split(',')
										var strArr = []
										for (var i = 0; i < data.length; i++) {
											strArr.push(Constants.kemuMap[data[i]])
										}
										return strArr.join(',')
									}
								},
							},
							{
								header: '场景',
								dataIndex: 'sceneCode',
								render: function (data, arr, i) {
									if (data) {
										data = data.split(',')
										var strArr = []
										for (var i = 0; i < data.length; i++) {
											strArr.push(Constants.senceMap[data[i]])
										}
										return strArr.join(',')
									}
								},
							},
							{
								header: '访问模式',
								dataIndex: 'patternCode',
								render: function (data, arr, i) {
									if (data) {
										data = data.split(',')
										var strArr = []
										for (var i = 0; i < data.length; i++) {
											strArr.push(Constants.editionMap[data[i]])
										}
										return strArr.join(',')
									}
								},
							},
							{
								header: '状态',
								dataIndex: 'status',
								render: function (data, arr, i) {
									return statusMap[data]
								},
							},
							{
								header: '创建人',
								dataIndex: 'createUserName',
							},
							{
								header: '创建时间',
								render: function (data) {
									return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
								},
								dataIndex: 'createTime',
							},
							{
								header: '修改人',
								dataIndex: 'updateUserName',
							},
							{
								header: '修改时间',
								render: function (data) {
									return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
								},
								dataIndex: 'updateTime',
							},
						],
					},
					['jiakao-misc!operation-config/data/list?codes=' + Object.keys(codeMap) + ''],
					panel,
					null
				).render()
			})
	}

	return {
		list: list,
	}
})
