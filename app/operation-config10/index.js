/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/constants', 'jiakao-misc!app/common/tiku', 'jiakao-misc!app/common/tools', 'jiakao-misc!app/operation-config3/index',], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, TIKU, Tools, OpConfig) {

    let codeMap = {
        'kemu_pass_rate_reminder_kemu_homepage': '科目首页',
        'kemu_pass_rate_reminder_exam_result_page': '考试结果页',
        'kemu_pass_rate_reminder_vip_page': 'vip页面'
    }

    let statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

    let cityList = (() => {
        let arr = [];
        Simple.DISTRICT.forEach(item => {
            item.cities.forEach(ele => {
                arr.push({
                    key: ele.code,
                    value: ele.name
                })
            })
        });

        return arr
    })()

    let cityMap = Tools.getMapfromArray(cityList)

    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'jiakao-misc!operation-config/data/' + (isEdit ? 'update' : 'insert?bizType=kemu_pass_rate_reminder'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var title = $(form).find('#title').val();
                    var btnImg = $(form).find('input[name="btnImg"]').val();
                    var reminder = $(form).find('#reminder').val();
                    var sourceDescription = $(form).find('#sourceDescription').val();
                    var sourceActionUrl = $('input[name="sourceActionUrl"]').val();
                    var disableCityCode = $('input[name="disableCityCode"]').val();
                    const bizCode = $(form).find('#code').val();

                    const value = JSON.stringify({
                        title, btnImg, reminder, sourceDescription, sourceActionUrl
                    })

                    const bizRule = JSON.stringify({
                        city_blacklist: disableCityCode
                    })

                    return {
                        value,
                        bizRule,
                        name: bizCode
                    };
                },
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    dataIndex: 'code',
                    xtype: 'select',
                    header: '配置code',
                    store: [
                        ...Tools.getArrayFromMap(codeMap)
                    ]
                },
                {
                    header: '场景code：',
                    dataIndex: 'sceneCode',
                    xtype: 'checkbox',
                    store: Constants.senceStore,
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store: [
                        ...Tools.getArrayFromMap(TIKU)
                    ],
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store: Constants.kemuStore.slice(1).concat([{ key: '5', value: '拿本' }]),
                    placeholder: '科目'
                },
                {
                    header: '配置说明',
                    dataIndex: 'remark',
                    xtype: 'textarea',
                    placeholder: '配置说明'
                },
                {
                    header: '城市黑名单：',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: cityList,
                        placeholder: '城市黑名单',
                        dataIndex: 'disableCityCode',
                        index: {
                            key: 'key',
                            value: 'value',
                            search: 'value'
                        },
                        isMulti: true,
                    }, function (plugin, value) {
                    }),
                    dataIndex: 'disableCityCode'
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    placeholder: '标题'
                },
                {
                    header: '按钮图片：',
                    dataIndex: 'btnImg',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'btnImg',
                        uploadIndex: 'btnImg',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '提醒文案：',
                    dataIndex: 'reminder',
                    xtype: 'text',
                    placeholder: '提醒文案'
                },
                {
                    header: '数据来源描述：',
                    dataIndex: 'sourceDescription',
                    xtype: 'text',
                    placeholder: '数据来源描述'
                },
                {
                    header: '数据来源跳转地址：',
                    dataIndex: 'sourceActionUrl',
                    xtype: 'text',
                    placeholder: '数据来源跳转地址'
                }
            ]
        }
        if (isEdit) {
            const value = JSON.parse(lineData.value || '{}');
            const bizRule = JSON.parse(lineData.bizRule || '{}');
        
            Table().edit({
                ...lineData,
                title: value.title,
                btnImg: value.btnImg,
                reminder: value.reminder,
                sourceDescription: value.sourceDescription,
                sourceActionUrl: value.sourceActionUrl,
                disableCityCode: bizRule.city_blacklist
            }, config);
        } else {
            Table(config).add();
        }

    }

    var list = function (panel) {
        Table({
            description: '科目合格率考试提醒列表',
            title: '科目合格率考试提醒列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '开启',
                    width: 500,
                    class: 'primary',
                    render: function (text, data, index) {
                        if (data[index].status === 0) {
                            return '测试上线'
                        }
                        if (data[index].status === 1) {
                            return '上线'
                        }
                        if (data[index].status === 2) {
                            return '下线'
                        }
                    },
                    click: function (table, lineDom, lineData, dom, data, index) {
                        Store([`jiakao-misc!operation-config/data/update?id=${lineData.id}&status=${(lineData.status + 1) % 3} `]).save([{}]).done(function (store, data) {
                            table.render();
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        })
                    },
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!operation-config/data/delete'
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        OpConfig.editPersonas(table, lineData)
                    },
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '配置code',
                    dataIndex: 'code',
                    render: function (data) {
                        return codeMap[data]
                    }
                },
                {
                    header: '配置内容',
                    dataIndex: 'value',
                    render: function () {
                        return '<a>查看详情</a>';
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('资源内容', {}).done(function (dialog) {
                            $(dialog.body).html('<pre style="max-height: 600px; overflow: auto">' + lineData.value + '</pre>')
                        });
                    }
                },
                {
                    header: '配置说明',
                    dataIndex: 'remark'
                },
                {
                    header: '场景',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.senceMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'patternCode',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(Constants.editionMap[data[i]])
                            }
                            return strArr.join(',');
                        }
                    }
                },

                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                if (data[i] == '5') {
                                    strArr.push('拿本')
                                } else {
                                    strArr.push(Constants.kemuMap[data[i]])
                                }

                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '通用配置',
                    dataIndex: 'conditions'
                },
                {
                    header: '业务特定规则',
                    dataIndex: 'bizRule',
                },
                {
                    header: '禁用城市',
                    dataIndex: 'disableCityCode',
                    render: function (value, data, lineData, index) {
                        const bizRule = (JSON.parse(lineData.bizRule || '{}'));
                        const disableCityCode = bizRule.city_blacklist ? bizRule.city_blacklist.split(',') : []

                        return disableCityCode.map(item => cityMap[item]).join(',')
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '创建时间 ',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间 ',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!operation-config/data/list?bizType=kemu_pass_rate_reminder'], panel, null).render();
    }

    return {
        list: list
    }

});