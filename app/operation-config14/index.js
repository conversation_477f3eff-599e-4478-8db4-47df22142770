/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!plugin/select-district/district3',
], function (Template, Table, Utils, Widgets, Store, Form,Constants,Plugin,TIKU,District3) {
    var carTypeArr = [];
    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }
    Constants.kemuStore = Constants.kemuStore.map(item => ({
        key: item.key + '',
        value:item.value
    }))

    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

    var statusArr = [];
    for (const key in statusMap) {
        statusArr.push({
            key,
            value:statusMap[key]
        })
    }

    var popupTypeMap = {
        1: '速记口诀弹窗',
        2: '答题技巧弹窗',
        3: '精简题库弹窗',
        4: '真实考场模拟弹窗'
    };
 
    var popupTypeArr = [];
    for (const key in popupTypeMap) {
        popupTypeArr.push({
            key,
            value:popupTypeMap[key]
        })
    }

    var practiceTypeMap = {
        1: '精简题库',
        2: '考前秘卷',
        3: '普通练习',
    };
 
    var practiceTypeArr = [];
    for (const key in practiceTypeMap) {
        practiceTypeArr.push({
            key,
            value:practiceTypeMap[key]
        })
    }
    var answerModeMap = {
        1: '考试',
        2: '练习'
    };
    var answerModeArr = [];
    for (const key in answerModeMap) {
        answerModeArr.push({
            key,
            value:answerModeMap[key]
        })
    }
    function loadPersonas(id) {
        return new Promise(resolve => {
            Store([`jiakao-misc!operation-config/data/personas`]).load([{
                aliases: 'result',
                params: {
                    id
                }
            }]).then(data => {
                resolve(data.data.result)
            })
        })
    }

	var addEdit = function (table, codeMap, type, lineData = {}) {
		var isEdit = type!=='add'
        var bizRuleObject = {}
        var valueObject = {}
        if(isEdit){
            valueObject = JSON.parse(lineData.value || lineData.multiValue || '{}')
            bizRuleObject = JSON.parse(lineData.bizRule||'{}')
            if(valueObject.select){
                valueObject = valueObject.traffic.map((item,index) => {
                    return {percent: item.percent,...item.value , ...bizRuleObject[index]}
                })
            } else {
                valueObject = {...valueObject,...bizRuleObject}
            }
 
            
        }
		var config = {
			title: isEdit ? '编辑' : '添加',
			width: 700,
			store: 'jiakao-misc!operation-config/data/' + (type=='edit' ? 'update' : 'insert') + '?bizType=strong_guidance_jump_homepage',
			success: function (obj, dialog) {
				dialog.close()
				table.render()
			},
			form: {
				submitHandler: function (form) {
                    const configurationValueArr = JSON.parse(form.configuration.value)
                    let isRequest = true
                    const config = (() => {
                        let field = null;
                        let traffic = {};
                        let bizRule = {};
                        let percentSum = 0;
                        bizRule = configurationValueArr.map(item => {
                            return {
                                practiceType: item.practiceType,
                            }
                        })
                        if(configurationValueArr.length > 1){
                            percentSum = configurationValueArr.reduce((sum,item) => sum + (+item.percent),0)
                            traffic = {
                                select: 'TRAFFIC',
                                traffic: configurationValueArr.map(item => {
                                    return {
                                        percent: item.percent,
                                        value: {
                                            actionUrl: item.actionUrl,
                                            actionShowType: item.actionShowType,
                                            popupType: item.popupType,
                                            popupName: item.popupName
                                        }
                                    }
                                })
                            }
                            field = 'multiValue'

                        } else {
                            field = 'value'
                            traffic = {
                                buttonTitle: configurationValueArr[0].buttonTitle,
                                actionUrl: configurationValueArr[0].actionUrl,
                                actionShowType: configurationValueArr[0].actionShowType,
                                popupType: configurationValueArr[0].popupType,
                                popupName: configurationValueArr[0].popupName,
                                imageUrl: configurationValueArr[0].imageUrl
                            }
                            
                        }
                        
                        return {
                            field,
                            traffic,
                            bizRule,
                            percentSum
                        }
                    })()
                    var code = $(form).find('#code').val();
                    if(!isRequest){
                        Widgets.dialog.alert(`fragment名称必填`);
                        return
                    }
                    if(configurationValueArr.length > 1 && config.percentSum !== 100){
                        Widgets.dialog.alert(`流量比例必须相加必须为100`);
                        return
                    }
                    const clearObj = config.field === 'value' ? {multiValue: ''} : {value: ''}
                    return {
                        ...clearObj,
                        [config.field]: JSON.stringify(config.traffic), 
                        code: code,
                        name: codeMap[code],
                        bizRule: JSON.stringify(config.bizRule)
                    };
				},
			},
			renderAfter: function (config, dom, data) {
                let options = []
                var renderData = data?.data || {}
                let codeValue = renderData.code || ''
                
                // let actionShowTypeValue = renderData.actionShowType
                //let actionShowType = dom.item('actionShowType-group')
                // let buttonTitle = dom.item('buttonTitle-group')
                // let actionUrl = dom.item('actionUrl-group')
                // let popupType = dom.item('popupType-group')
                // let imageUrl = dom.item('imageUrl-group')
                // let practiceType = dom.item('practiceType-group')
                // let answerMode = dom.item('answerMode-group')
                // let fragmentName = dom.item('fragmentName-group')
                const actionChangeFun = (value,option) => {
                    const options = option;
                    console.log('codeValue',value,codeValue);
                    
                    if (codeValue === 'exam_result_upgrade' || codeValue === 'exam_result_card'){
                        if (value == 'popup') {
                            option.push({
                                header: "弹窗类型：",
                                dataIndex: "popupType",
                                xtype: "select",
                                store: [
                                  {
                                    key: "",
                                    value: "请选择",
                                  },
                                  ...popupTypeArr,
                                ],
                                placeholder: "弹窗类型",
                              })

                        } else if(value === 'jump'){
                            // actionUrl.show();
                            // popupType.hide();
                            option.push({
                                header: "协议url：",
                                dataIndex: "actionUrl",
                                xtype: "text",
                                placeholder: "协议url"
                            })
                        }
                    }else if(value === 'popup' || value === 'jump'){
                        option.push({
                            header: "协议url：",
                            dataIndex: "actionUrl",
                            xtype: "text",
                            placeholder: "协议url"
                        })
                        
                    }
                    // if (value === 'popup') {
                    //     // actionUrl.hide();
                    //     // popupType.show();
                    //     options.push({
                    //         header: "弹窗类型：",
                    //         dataIndex: "popupType",
                    //         xtype: "select",
                    //         store: [
                    //           {
                    //             key: "",
                    //             value: "请选择弹窗类型",
                    //           },
                    //           ...popupTypeArr,
                    //         ],
                    //         placeholder: "弹窗类型",
                    //       })

                    // } else if(value === 'jump'){
                    //     // actionUrl.show();
                    //     // popupType.hide();
                    //     options.push({
                    //         header: "协议url：",
                    //         dataIndex: "actionUrl",
                    //         xtype: "text",
                    //         placeholder: "协议url"
                    //     })
                    // }
                    return options
                }
                /**
                 * 展示类型切换时的动态进行配置
                 * @param {*} value 展示类型的值
                 * @param {*} options 配置项
                 * @param {*} data 当前数据源对象
                 * @returns 返回处理好的 配置项和数据源对象
                 */
                const actionShowTypeChangeFn = (value,options,data) => {
                    const option = options;
                    const dataObj = {}
                    for(const key in data){
                        if(key !== 'actionUrl' && key !== 'popupType'){
                            dataObj[key] = data[key]
                        }
                    }
                    const popupTypeIndex = option.findIndex(item => item.dataIndex === 'popupType');
                    const actionUrlIndex = option.findIndex(item => item.dataIndex === 'actionUrl');
                    if(popupTypeIndex !== -1){
                        option.splice(popupTypeIndex,1)
                    }
                    if(actionUrlIndex !== -1){
                        option.splice(actionUrlIndex,1)
                    }
                    console.log(value);
                    if (codeValue === 'exam_result_upgrade' || codeValue === 'exam_result_card'){
                        if (value === 'popup') {
                            option.push({
                                header: "弹窗类型：",
                                dataIndex: "popupType",
                                xtype: "select",
                                store: [
                                  {
                                    key: "",
                                    value: "请选择",
                                  },
                                  ...popupTypeArr,
                                ],
                                placeholder: "弹窗类型",
                              })
                            dataObj.popupType = ''
                        } else if(value === 'jump') {
                            option.push({
                                header: "协议url：",
                                dataIndex: "actionUrl",
                                xtype: "text",
                                placeholder: "协议url"
                            })
                            dataObj.actionUrl = ''
                        }
                    }else if(value === 'popup' || value === 'jump'){
                        option.push({
                            header: "协议url：",
                            dataIndex: "actionUrl",
                            xtype: "text",
                            placeholder: "协议url"
                        })
                        dataObj.actionUrl = ''

                    }
                    return {
                        option,
                        dataObj
                    }
                }
                /**
                 * 用于配置key选择时来确定配置项的配置对象
                 * @param {*} code 配置key
                 * @param {*} actionShowType  
                 * @returns 
                 */
                // const changeFun = (code,actionShowType) => { 
                //     let options = [
                //         {
                //             header: '流量比例(百分比)',
                //             dataIndex: 'percent',
                //             xtype: 'text',
                //             disabled: Array.isArray(valueObject) ? false : true,
                //             placeholder: '流量比例(百分比)',
                //             addRender: (data,options) => {
                //                 if(options.length > 1){
                //                     data.disabled = false
                //                 }
                //             },
                //             removeRender: (data,options) => {
                //                 if(options.length <= 1){
                //                     data.disabled = true
                //                 }
                //             }
                //         }
                //     ]
                    
                //     if(code === 'exam_result_upgrade'){
                //         options.push({
                //             header: "展示类型：",
                //             dataIndex: "actionShowType",
                //             store: [
                //                 {
                //                     key: '',
                //                     value: '请选择展示类型'
                //                 },
                //               {
                //                 key: "jump",
                //                 value: "跳转",
                //               },
                //               {
                //                 key: "popup",
                //                 value: "弹窗",
                //               },
                //             ],
                //             xtype: "select",
                //             placeholder: "展示类型：",
                //             render: (data,options,valueArr) => {
                //                return actionShowTypeChangeFn(data,options,valueArr);
                //             }
                //           },{
                //             header: "fragment名称",
                //             dataIndex: "fragmentName",
                //             xtype: "text",
                //             placeholder: "fragment名称",
                //           },{
                //             header: "按钮标题：",
                //             dataIndex: "buttonTitle",
                //             xtype: "text",
                //             placeholder: "按钮标题",
                //           })
                //     } else if(code === 'exam_result_card' || code === 'practice_result_accumulate_answer' || code === 'exam_practice_result_comment_guide'){
                //         options.push({
                //             header: "图片:",
                //             dataIndex: "imageUrl",
                //             xtype: "fileUpload",
                //         },{
                //             header: "展示类型：",
                //             dataIndex: "actionShowType",
                //             store: [
                //                 {
                //                     key: '',
                //                     value: '请选择展示类型'
                //                 },
                //               {
                //                 key: "jump",
                //                 value: "跳转",
                //               },
                //               {
                //                 key: "popup",
                //                 value: "弹窗",
                //               },
                //             ],
                //             xtype: "select",
                //             placeholder: "展示类型：",
                //             render: (data,options,valueArr) => {
                //                 return actionShowTypeChangeFn(data,options,valueArr);
                //             }
                //           },{
                //             header: "fragment名称",
                //             dataIndex: "fragmentName",
                //             xtype: "text",
                //             placeholder: "fragment名称",
                //         })
                        
                        
                //     }
                //     options = actionChangeFun(actionShowType,options);
                //     return options;
                // }
                const changeFun = (code,actionShowType) => {
                    let options = [
                        {
                            header: '流量比例(百分比)',
                            dataIndex: 'percent',
                            xtype: 'text',
                            disabled: Array.isArray(valueObject) ? false : true,
                            placeholder: '流量比例(百分比)',
                            addRender: (data,options) => {
                                if(options.length > 1){
                                    data.disabled = false
                                }
                            },
                            removeRender: (data,options) => {
                                if(options.length <= 1){
                                    data.disabled = true
                                }
                            }
                        }
                    ]
                    // actionShowType.hide();
                    // buttonTitle.hide();
                    // actionUrl.hide();
                    // popupType.hide();
                    // imageUrl.hide();
                    // practiceType.hide();
                    // answerMode.hide();
                    // fragmentName.hide();
                    options = actionChangeFun(actionShowType,options);
                    // imageUrl.show();
                    // answerMode.show();
                    // fragmentName.show();
                    // actionShowType.show();
                    options.push({
                        header: "展示类型：",
                        dataIndex: "actionShowType",
                        store: [
                            {
                                key: '',
                                value: '请选择展示类型'
                            },
                            {
                            key: "jump",
                            value: "跳转",
                            },
                            {
                            key: "popup",
                            value: "弹窗",
                            },
                        ],
                        xtype: "select",
                        placeholder: "展示类型：",
                        render: (data,options,valueArr) => {
                            return actionShowTypeChangeFn(data,options,valueArr);
                        }
                        
                        })
                    return options;
                }

                if(valueObject && Array.isArray(valueObject)){
                    options = valueObject.map(item =>{
                        return changeFun(data.data ? data.data.code : '',item.actionShowType)
                    })
                } else if(typeof valueObject === 'object' && valueObject !== null  ){
                    options = changeFun(data.data ? data.data.code : '',valueObject.actionShowType) 
                } else {
                    options = changeFun(data.data ? data.data.code : '','') 
                }
                dom.item('code').on('change', function() {
                    let code = $(this).val();
                    codeValue = code
                    options = changeFun(code,'')
                    renderConfig()
                });  
                function renderConfig() {
					Plugin('jiakao-misc!group8', {
                        dataIndex: 'configuration',
						target: dom.item('configuration-group').find('div[class=value-div]'),
						value: isEdit ? valueObject : [{}],
                        options
					}, function (plugin, value) {
					}).render();

				}
                renderConfig()
			},
			columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '配置key：',
                    dataIndex: 'code',
                    xtype: 'select',
                    store: 'jiakao-misc!operation-config/data/codeList?bizType=strong_guidance_jump_homepage',
                    index: [{
                        key: 'key',
                        value: 'value'
                    }],
                    insert: [{
                        key: '',
                        value: '请选择'
                    }],
                    disabled: isEdit
                },
                {
                    header: '排序值,值越小越靠前：',
                    dataIndex: 'sort',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '排序值,值越小越靠前'
                },
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '场景code：',
                    dataIndex: 'sceneCode',
                    xtype: 'checkbox',
                    store: Constants.senceStore,
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store:carTypeArr,
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store: Constants.kemuStore.slice(1),
                    placeholder: '科目'
                },
                {
                    header: '',
                    dataIndex: 'configuration',
                    xtype: function () {
						return `<div class="value-div"></div>`
					},
                },
            ],
		}

		if (isEdit) {
			Table().edit({
                ...lineData,
                value: valueObject,
                ...valueObject,
                practiceType: bizRuleObject.practiceType,
            }, config)
		} else {
			Table(config).add()
		}
	}
    var limitSCount = function (table, lineData = {}) {
        var config = {
            title: '展示限制次数',
            width: 600,
            store: 'jiakao-misc!operation-config/data/update',
            success: function (obj, dialog) {
                dialog.close()
                table.render()
            },
            form: {
                submitHandler: function (form) {
                    var allCountLimit = form.allCountLimit.value
                    var dailyCountLimit = form.dailyCountLimit.value
                    var intervalCountLimit = form.intervalCountLimit.value
                    var newInstallLimit = form.newInstallLimit.value
                    var countLimitIdType = form.countLimitIdType.value
                    var bizRuleMap = JSON.parse(lineData.bizRule || '{}')
                    var config = {
                        allCountLimit,
                        dailyCountLimit,
                        intervalCountLimit,
                        newInstallLimit,
                        countLimitIdType
                    }
                    var newObject
                    if(Array.isArray(bizRuleMap)){
                        newObject = config
                    } else {
                        newObject = Object.assign(bizRuleMap, config)
                    }
                    return {
                        bizRule: JSON.stringify(newObject)
                    }
                },
            },
            renderAfter: function (config, dom, data) {
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden',
                },
                {
                    dataIndex: 'bizType',
                    xtype: 'hidden',
                    value:'strong_guidance_jump_homepage'
                },
                {
                    header: '总展示次数限制：',
                    dataIndex: 'allCountLimit',
                    xtype: 'number',
                    min: 0,
                    value: 0,
                    placeholder: '总展示次数限制',
                },
                {
                    header: '每日展示次数限制：',
                    dataIndex: 'dailyCountLimit',
                    xtype: 'number',
                    min: 0,
                    value: 0,
                    placeholder: '每日展示次数限制',
                },
                {
                    header: '展示间隔：',
                    dataIndex: 'intervalCountLimit',
                    xtype: 'number',
                    min: 0,
                    value:0,
                    placeholder: '展示间隔',
                },
                {
                    header: '距离新安装的间隔天数：',
                    dataIndex: 'newInstallLimit',
                    xtype: 'number',
                    min: 0,
                    value:0,
                    placeholder: '距离新安装的间隔天数',
                },
                {
                    header: '次数限制对应的标识类型：',
                    dataIndex: 'countLimitIdType',
                    xtype: 'select',
                    check: 'required',
                    store: [
                        { key: 'deviceId', value: '设备' },
                        { key: 'userId', value: '账号' },
                       
                    ],
                },
            ],
        }
        var bizRule = JSON.parse(lineData.bizRule||'')
        Table().edit({
            ...lineData,
            allCountLimit: bizRule.allCountLimit,
            dailyCountLimit: bizRule.dailyCountLimit,
            intervalCountLimit: bizRule.intervalCountLimit,
            newInstallLimit: bizRule.newInstallLimit,
            countLimitIdType: bizRule.countLimitIdType,
        }, config)
    }

	var editPersonas = async function (table, lineData) {
        console.log(table);
        console.log(lineData);
        const personasData = await loadPersonas(lineData.id)
        var valueObject = JSON.parse(personasData.data||'{}')
		var config = {
			title: '用户画像',
			width: 700,
			store: 'jiakao-misc!operation-config/data/personas?id='+lineData.id,
			success: function (obj, dialog) {
				dialog.close()
				table.render()
			},
			form: {
				submitHandler: function (form) {
                    var bindJiaxiao = $(form).item('bindJiaxiao').val();
                    var commentJiaxiao = $(form).item('commentJiaxiao').val();
                    var permissionCodes = $(form).item('permissionCodes').val();
                    var examDistanceDays = $(form).item('examDistanceDays').val();
                    examDistanceDays = JSON.parse(examDistanceDays || '{}')
                    var answeredQuestions = $(form).item('answeredQuestions').val();
                    answeredQuestions = JSON.parse(answeredQuestions || '{}')
                    var correctRate = $(form).item('correctRate').val();
                    correctRate = JSON.parse(correctRate || '{}')
                    var lastExamScore = $(form).item('lastExamScore').val();
                    lastExamScore = JSON.parse(lastExamScore || '{}')
                    var examTimes = $(form).item('examTimes').val();
                    examTimes = JSON.parse(examTimes || '{}')
                    var passTimes = $(form).item('passTimes').val();
                    passTimes = JSON.parse(passTimes || '{}')
                    var paymentAttempts = $(form).item('paymentAttempts').val();
                    paymentAttempts = JSON.parse(paymentAttempts || '{}')
                    var userGroupJ = $(form).item('userGroupJ').val();
                    var userGroupJy = $(form).item('userGroupJy').val();
                    var canQuestionnaire = $(form).item('canQuestionnaire').val();
                    var questionnaireId = $(form).item('questionnaireId').val();
                    var ageGroup = $(form).item('ageGroup').val();
                    var educationLevel = $(form).item('educationLevel').val();
                    var reversePermission = '';
                    $(form).item('reversePermission').filter(':checked').each(function () {
                        reversePermission = $(this).val()
                    })
                    

                    let so = [{
                        name: '距离预约考试天数',
                        value: examDistanceDays,
                    },{
                        name: '已做题数量',
                        value: answeredQuestions,
                    },{
                        name: '做题正确率',
                        value: correctRate,
                    },{
                        name: '最近1次考试成绩',
                        value: lastExamScore,
                    },{
                        name: '考试次数',
                        value: examTimes,
                    },{
                        name: '合格次数',
                        value: passTimes,
                    },{
                        name: '去支付次数',
                        value: paymentAttempts,
                    }]

                    for (let ke of so) {
                        let {name, value} = ke

                        if ((value.from && value.from % 1 != 0) || (value.to && value.to % 1 != 0)) {
                            Widgets.dialog.alert(`${name}: 请填入大于0的整数`);
                            return;
                        }

                        if (name === '做题正确率') {
                            if ((value.from  && (value.from > 100 || value.from < 0)) || (value.to && (value.to > 100 || value.to < 0))) {
                                Widgets.dialog.alert(`${name}: 数值范围0-100`);
                                return;
                            }
                        }

                        if (value.from && value.to && +value.from >= +value.to) {
                            Widgets.dialog.alert(`${name}: 左边值需小于右边值`);
                            return;
                        }
                    }

                    const value = JSON.stringify({
                        bindJiaxiao,
                        commentJiaxiao,
                        permissionCodes,
                        examDistanceDays,
                        answeredQuestions,
                        correctRate,
                        lastExamScore,
                        examTimes,
                        passTimes,
                        paymentAttempts,
                        userGroupJ,
                        userGroupJy,
                        canQuestionnaire,
                        questionnaireId: questionnaireId.trim(),
                        ageGroup: ageGroup.trim(),
                        educationLevel: educationLevel.trim(),
                        reversePermission: reversePermission||'false'
                    })

                    return {
                        value: value
                    };
				},
			},
			renderAfter: function (config, dom, data) {
			},
			columns: [
                {
                    header: '权限反选：',
                    dataIndex: 'reversePermission',
                    xtype: 'checkbox',
                    store:[
                        {key:'true',value:''}
                    ],
                    placeholder: '权限反选'
                },
                {
                    header: 'VIP身份权益码：',
                    dataIndex: 'permissionCodes',
                    xtype: 'text',
                    placeholder: 'VIP身份权益码'
                },
                {
                    header: '距离预约考试天数：',
                    dataIndex: 'examDistanceDays',
                    xtype: Plugin('jiakao-misc!section2', {
                        dataIndex: 'examDistanceDays',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '已做题数量：',
                    dataIndex: 'answeredQuestions',
                    xtype: Plugin('jiakao-misc!section2', {
                        dataIndex: 'answeredQuestions',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '做题正确率：',
                    dataIndex: 'correctRate',
                    xtype: Plugin('jiakao-misc!section2', {
                        dataIndex: 'correctRate',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '最近1次考试成绩：',
                    dataIndex: 'lastExamScore',
                    xtype: Plugin('jiakao-misc!section2', {
                        dataIndex: 'lastExamScore',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '考试次数：',
                    dataIndex: 'examTimes',
                    xtype: Plugin('jiakao-misc!section2', {
                        dataIndex: 'examTimes',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '合格次数：',
                    dataIndex: 'passTimes',
                    xtype: Plugin('jiakao-misc!section2', {
                        dataIndex: 'passTimes',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '去支付次数：',
                    dataIndex: 'paymentAttempts',
                    xtype: Plugin('jiakao-misc!section2', {
                        dataIndex: 'paymentAttempts',
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '绑定驾校：',
                    dataIndex: 'bindJiaxiao',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择'
                        }, {
                            key: 1,
                            value: '已绑定'
                        }, {
                            key: 0,
                            value: '未绑定'
                        }
                    ]
                },
                {
                    header: '评价驾校：',
                    dataIndex: 'commentJiaxiao',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择'
                    },{
                        key:1,
                        value:'已评价'
                    }, {
                        key: 0,
                        value: '未评价'
                    }]
                },
                {
                    header: '符合J标签的用户群体：',
                    dataIndex: 'userGroupJ',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择符合J标签的用户群体'
                        }, {
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '符合Jy标签的用户群体：',
                    dataIndex: 'userGroupJy',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择符合Jy标签的用户群体'
                        }, {
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '是否能够参与填写问卷：',
                    dataIndex: 'canQuestionnaire',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '是否能够参与填写问卷'
                        }, {
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '问卷id：',
                    dataIndex: 'questionnaireId',
                    xtype: 'text',
                    placeholder: '问卷id'
                },
                {
                    header: '年龄：',
                    dataIndex: 'ageGroup',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择'
                        },
                        {
                            key: "18-24",
                            value: "18-24岁"
                        },
                        {
                            key: "25-30",
                            value: "25-30岁"
                        },
                        {
                            key: "31-40",
                            value: "31-40岁"
                        },
                        {
                            key: "41-50",
                            value: "41-50岁"
                        },
                        {
                            key: "51-100",
                            value: "50岁以上"
                        },
                        {
                            key: "unknown",
                            value: "保密"
                        }
                    ],
                    placeholder: '年龄'
                },
                {
                    header: '学历：',
                    dataIndex: 'educationLevel',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择'
                        },
                        {
                            key: "university",
                            value: "本科及以上"
                        },
                        {
                            key: "junior_college",
                            value: "大专"
                        },
                        {
                            key: "high_school",
                            value: "高中/中专"
                        },
                        {
                            key: "middle_school",
                            value: "初中"
                        },
                        {
                            key: "primary",
                            value: "小学及以下"
                        },
                        {
                            key: "unknown",
                            value: "保密"
                        }
                    ],
                    placeholder: '学历'
                },
            ],
		}

		Table().edit(valueObject, config)
	}

    var list = function (panel) {
        Store(['jiakao-misc!operation-config/data/codeList?bizType=strong_guidance_jump_homepage']).load().done((retData) => { 
            const codeArr = retData.data['operation-config'].data.codeList.data;
            let codeMap = {};
            codeArr.forEach((code) => {
                codeMap[code.key] = code.value
            })
            Table({
                description: '启动全局推荐',
                title: '启动全局推荐',
                search: [{
                    dataIndex: 'codes',
                    xtype: 'select',
                    store: [{
                        key: Object.keys(codeMap)+'',
                        value: '全部'
                    }].concat(codeArr),
                }, {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [{
                        key:'',
                        value:'请选择车型'
                    }, ...carTypeArr],
                }, {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore,
                    placeholder: '科目'
                }, {
                    header:'状态:',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{
                        key:'',
                        value:'请选择状态'
                    }, ...statusArr],
                }],
                buttons: {
                    top: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function(obj) {
                                obj.render();
                            }
                        },
                        {
                            name: '添加',
                            class: 'primary',
                            click: function (table) {
                                addEdit(table, codeMap, 'add')
                            }
                        }
                    ],
                    bottom: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function(obj) {
                                obj.render();
                            }
                        }
                    ]
                },
                operations: [
                    {
                        name: '查看',
                        xtype: 'view',
                        width: 400,
                        class: 'success',
                        title: '查看',
                        store: 'jiakao-misc!operation-config/data/view',
                        columns: [
                            {
                                header: '#',
                                dataIndex: 'id'
                            },
                            {
                                header: '名称：',
                                dataIndex: 'name'
                            },
                            {
                                header: '配置说明：',
                                dataIndex: 'remark'
                            },
                            {
                                header: '配置key：',
                                dataIndex: 'code'
                            },
                            {
                                header: '配置内容：',
                                dataIndex: 'value'
                            },
                            {
                                header: '排序值：',
                                dataIndex: 'sort'
                            },
                            {
                                header: '场景',
                                dataIndex: 'sceneCode',
                                render: function (data, arr, i) {
                                    if (data) {
                                        data = data.split(',');
                                        var strArr = [];
                                        for (var i = 0; i < data.length; i++) {
                                            strArr.push(Constants.senceMap[data[i]])
                                        }
                                        return strArr.join(',');
                                    }
                                }
                            },
                            {
                                header: '访问模式',
                                dataIndex: 'patternCode',
                                render: function (data, arr, i) {
                                    if (data) {
                                        data = data.split(',');
                                        var strArr = [];
                                        for (var i = 0; i < data.length; i++) {
                                            strArr.push(Constants.editionMap[data[i]])
                                        }
                                        return strArr.join(',');
                                    }
                                }
                            },
                            {
                                header: '车型：',
                                dataIndex: 'carType',
                                render: function (data) {
                                    if (data) {
                                        data = data.split(',');
                                        var strArr = [];
                                        for (var i = 0; i < data.length; i++) {
                                            strArr.push(TIKU[data[i]])
                                        }
                                        return strArr.join(',');
                                    }
                                }
                            },
                            {
                                header: '科目 1,2,3,4：',
                                dataIndex: 'kemu',
                                render: function (data, arr, i) {
                                    if (data) {
                                        data = data.split(',');
                                        var strArr = [];
                                        for (var i = 0; i < data.length; i++) {
                                            strArr.push(Constants.kemuMap[data[i]])
                                        }
                                        return strArr.join(',');
                                    }
                                }
                            },
                            {
                                header: '状态：',
                                dataIndex: 'status',
                                render: function (data, arr, i) {
                                    return statusMap[data]
                                }
                            }
                        ]
                    },
                    {
                        name: '投放策略',
                        class: 'success',
                        click: function (table, lineDom, lineData, dom, data, index) {
                            Plugin('simple!product-filter', {
                                store: 'jiakao-misc!operation-config/data/filter?id=' + lineData.id
                            }).render().done(function () {
                                table.render();
                            });
                        }
                    },
                    {
                        name: '编辑',
                        class: 'warning',
                        click: function (table, dom, lineData) {
                            console.log('lineData',lineData);
                            
                            addEdit(table, codeMap, 'edit', lineData)
                        },
                    },
                    {
                        name: '删除',
                        class: 'danger',
                        xtype: 'delete',
                        store: 'jiakao-misc!operation-config/data/delete'
                    },
                    {
                        name: '用户画像',
                        class: 'success',
                        click: function (table, dom, lineData) {
                            editPersonas(table, lineData)
                        },
                    },
                    {
                        name: '展示次数限制',
                        class: 'success',
                        click: function (table, dom, lineData) {
                            limitSCount(table, lineData)
                        },
                    },
                    {
                        class: 'danger',
                        render: function (name, arr, index) {
                            const status = arr[index].status
                            if (status == 0) {
                                return '测试发布';
                            } else if (status == 1) {
                                return '发布';
                            }else if (status == 2) {
                                return '下线';
                            }
                        },
                        click: function (table, row, lineData) {
                            const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1;
                            let title = lineData.status == 1 ? '确定发布吗?' : lineData.status == 2 ? '确定下线吗?' : '确定测试发布吗?'
                            Widgets.dialog.confirm(title, function (e, confirm) {
                                if (confirm) {
                                    Store(['jiakao-misc!operation-config/data/update']).save([{
                                        params: {
                                            id: lineData.id,
                                            status
                                        }
                                    }]).done(function () {
                                        table.render();
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message);
                                    })
                                }
                            })
                        }
                    },
                    {
                        name:'复制',
                        class:'warning',
                        click:function(table,dom,lineData){
                            delete lineData['id']
                            addEdit(table, codeMap, 'copy', lineData)
                        } 
                    }
                ],
                columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '名称',
                        dataIndex: 'name'
                    },
                    {
                        header: '配置说明',
                        dataIndex: 'remark'
                    },
                    {
                        header: '配置key',
                        dataIndex: 'code'
                    },
                    {
                        header: '配置内容',
                        dataIndex: 'value',
                        render: function () {
                            return `<a>点击查看</a>`
                        },
                        click: function (table, row, lineData) {
                            console.log(lineData.multiValue);
                            
                            Widgets.dialog.html('配置内容', {}).done(function (dialog) {
                                var data =  JSON.stringify(JSON.parse(lineData.value || lineData.multiValue || '{}' ), null, 4)
                                $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                    },
                    {
                        header: '排序值：',
                        dataIndex: 'sort'
                    },
                    {
                        header: '场景',
                        dataIndex: 'sceneCode',
                        render: function (data, arr, i) {
                            if (data) {
                                data = data.split(',');
                                var strArr = [];
                                for (var i = 0; i < data.length; i++) {
                                    strArr.push(Constants.senceMap[data[i]])
                                }
                                return strArr.join(',');
                            }
                        }
                    },
                    {
                        header: '访问模式',
                        dataIndex: 'patternCode',
                        render: function (data, arr, i) {
                            if (data) {
                                data = data.split(',');
                                var strArr = [];
                                for (var i = 0; i < data.length; i++) {
                                    strArr.push(Constants.editionMap[data[i]])
                                }
                                return strArr.join(',');
                            }
                        }
                    },
                    {
                        header: '车型',
                        dataIndex: 'carType',
                        render: function (data) {
                            if (data) {
                                data = data.split(',');
                                var strArr = [];
                                for (var i = 0; i < data.length; i++) {
                                    strArr.push(TIKU[data[i]])
                                }
                                return strArr.join(',');
                            }
                        }
                    },
                    {
                        header: '科目',
                        dataIndex: 'kemu',
                        render: function (data, arr, i) {
                            if (data) {
                                data = data.split(',');
                                var strArr = [];
                                for (var i = 0; i < data.length; i++) {
                                    strArr.push(Constants.kemuMap[data[i]])
                                }
                                return strArr.join(',');
                            }
                        }
                    },
                    {
                        header: '身份权益码列表',
                        dataIndex: 'bizRule',
                        render: function (data) {
                            const retData = JSON.parse(data);
                            const permissionCodes = retData?.permissionCodes
                            return permissionCodes
                        }
                    },
                    {
                        header: '状态',
                        dataIndex: 'status',
                        render: function (data, arr, i) {
                            return statusMap[data]
                        }
                    },
                    {
                        header: '创建人',
                        dataIndex: 'createUserName'
                    },
                    {
                        header: '创建时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: '修改人',
                        dataIndex: 'updateUserName'
                    },
                    {
                        header: '修改时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    }
                ]
            }, ['jiakao-misc!operation-config/data/list?codes=' + Object.keys(codeMap)+''], panel, null).render();
        })
    }

    return {
        list: list,
        editPersonas: editPersonas
    }
});