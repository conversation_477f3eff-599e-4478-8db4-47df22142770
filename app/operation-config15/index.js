/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

'use strict'

define([
	'simple!core/template',
	'simple!core/table',
	'simple!core/utils',
	'simple!core/widgets',
	'simple!core/store',
	'simple!core/form',
	'jiakao-misc!app/common/constants',
	'simple!core/plugin',
	'jiakao-misc!app/common/tiku',
	'simple!app/layout/main',
	'jiakao-misc!app/operation-config3/index',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, Layout, OpConfig) {
	var carTypeArr = []

	for (var k in TIKU) {
		carTypeArr.push({
			key: k,
			value: TIKU[k],
		})
	}

	Constants.kemuStore = Constants.kemuStore.map(item => ({
		key: item.key + '',
		value: item.value,
	}))

	var statusMap = {
		2: '发布',
		0: '下线',
		1: '测试发布',
	}

	var statusArr = []

	for (const key in statusMap) {
		statusArr.push({
			key,
			value: statusMap[key],
		})
	}
	var videoTypeMap = {
		1:'项目视频',
		2:'路线视频'
	}
	var videoTypeArray = []
	for (const key in videoTypeMap) {
		videoTypeArray.push({
			key,
			value: videoTypeMap[key],
		})
	}
	var BIZTYPE = 'practice_page_background'
	var addEdit = function (table, codeMap,type, lineData = {}) {
		var isEdit = type!=='add'
        if(isEdit){
            var valueObject = JSON.parse(lineData.value||'{}')
			var bizRuleObject = JSON.parse(lineData.bizRule || '{}')
        }
		var config = {
			title: isEdit ? '编辑' : '添加',
			width: 700,
			store: 'jiakao-misc!operation-config/data/' + (type=='edit' ? 'update' : 'insert'),
			success: function (obj, dialog) {
				dialog.close()
				table.render()
			},
			form: {
				submitHandler: function (form) {
					// 因为已经有code，所以这里用别名code9
					var code9 = form.code9.value;
					var name = form.name.value;
					var previewImage = form.previewImage.value;
					var bgImage = form.bgImage.value;
					var pellucidity = form.pellucidity.value;
					var useDefault = form.useDefault.value;
					var needAuthority = form.needAuthority.value;
					if (useDefault === 'true' && needAuthority === 'true') {
						Widgets.dialog.alert('【是否默认】和【是否鉴权】不能同时为是')
						return
					}

					return {
						name: codeMap[code],
						value: JSON.stringify({
							code: code9,
							name,
							previewImage,
							bgImage,
							pellucidity,
							useDefault,
							needAuthority
						 })
					}
				},
			},
			columns: [
				{
					dataIndex: 'id',
					xtype: 'hidden',
				},
                {
					dataIndex: 'bizType',
					xtype: 'hidden',
                    render:function(){
						return BIZTYPE
                    }
				},
				{
					header: '模块：',
					dataIndex: 'code',
					xtype: 'select',
					store: 'jiakao-misc!operation-config/data/codeList?bizType=' + BIZTYPE,
					index: [
						{
							key: 'key',
							value: 'value',
						},
					],
					check: 'required',
					insert: [
						{
							key: '',
							value: '请选择',
						},
					],
					disabled: isEdit
				},
				{
					header: '排序值：',
					dataIndex: 'sort',
					xtype: 'number',
					check: 'required',
					placeholder: '排序值',
				},
				{
					header: '配置说明：',
					dataIndex: 'remark',
					xtype: 'text',
					maxlength: 128,
					placeholder: '配置说明',
				},
				{
					header: '状态：',
					dataIndex: 'status',
					xtype: 'select',
					store: statusArr,
					value: 0,
					check: 'required',
					placeholder: '状态',
                },
				{
					header: 'code：',
					dataIndex: 'code9',
					xtype: 'text',
				
					placeholder: 'code',
                    check: 'required',
				},
				{
					header: '名称：',
					dataIndex: 'name',
					xtype: 'text',
				
					placeholder: '名称',
                    check: 'required',
				},
                {
                    header: '预览图：',
                    dataIndex: 'previewImage',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'previewImage',
                        uploadIndex: 'previewImage',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '背景图：',
                    dataIndex: 'bgImage',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'bgImage',
                        uploadIndex: 'bgImage',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
				{
					header: '透明度：',
					dataIndex: 'pellucidity',
					xtype: 'number',
					value: 0.5,
					placeholder: '透明度',
					min: 0.01,
					max: 1,
					step: 0.01,
                    check: 'required',
				},
                {
                    header: '是否默认：',
                    dataIndex: 'useDefault',
                    xtype: 'select',
					value: false,
                    store: [{
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }
                    ],
                    check: 'required',
                },
                {
                    header: '是否鉴权：',
                    dataIndex: 'needAuthority',
                    xtype: 'select',
					value: false,
                    store: [{
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }
                    ],
                    check: 'required',
                },
			],
		}

		if (isEdit) {
			Table().edit({
				...lineData,
				code9: valueObject?.code,
				name: valueObject?.name,
				previewImage: valueObject?.previewImage,
				bgImage: valueObject?.bgImage,
				pellucidity: valueObject?.pellucidity,
				useDefault: valueObject?.useDefault,
				needAuthority: valueObject?.needAuthority
			}, config)
		} else {
			Table(config).add()
		}
	}

	var list = function (panel) {
		Store(['jiakao-misc!operation-config/data/codeList?bizType=' + BIZTYPE])
			.load()
			.done(retData => {
				console.log(retData, '125')
				const codeArr = retData.data['operation-config'].data.codeList.data
				let codeMap = {}
				codeArr.forEach(code => {
					codeMap[code.key] = code.value
				})
				Table(
					{
						description: '答题背景配置',
						title: '答题背景配置',
						search: [
							{
								dataIndex: 'codes',
								xtype: 'select',
								store: [
									{
										key: Object.keys(codeMap) + '',
										value: '全部',
									},
								].concat(codeArr),
							},
							{
								header: '车型：',
								dataIndex: 'carType',
								xtype: 'select',
								store: [{ key: '', value: '请选择车型' }, ...carTypeArr],
							},
							{
								header: '科目：',
								dataIndex: 'kemu',
								xtype: 'select',
								store: Constants.kemuStore,
								placeholder: '科目',
							},
							{
								header: '状态:',
								dataIndex: 'status',
								xtype: 'select',
								store: [{ key: '', value: '请选择状态' }, ...statusArr],
							},
						],
						buttons: {
							top: [
								{
									name: '刷新',
									class: 'info',
									click: function (obj) {
										obj.render()
									},
								},
								{
									name: '添加',
									class: 'primary',
									click: function (table) {
										addEdit(table, codeMap,'add')
									},
								},
							],
							bottom: [
								{
									name: '刷新',
									class: 'info',
									click: function (obj) {
										obj.render()
									},
								},
							],
						},
						operations: [
							{
								name: '查看',
								xtype: 'view',
								width: 400,
								class: 'success',
								title: '查看',
								store: 'jiakao-misc!operation-config/data/view',
								columns: [
									{
										header: '#',
										dataIndex: 'id',
									},
									{
										header: '名称：',
										dataIndex: 'name',
									},
									{
										header: '模块：',
										dataIndex: 'code',
									},
									{
										header: '配置内容：',
										dataIndex: 'value',
									},
									{
										header: '排序值：',
										dataIndex: 'sort',
									},
									{
										header: '配置说明：',
										dataIndex: 'remark',
									},
									{
										header: '通用配置：',
										dataIndex: 'conditions',
									},
									{
										header: '状态：',
										dataIndex: 'status',
										render: function (data, arr, i) {
											return statusMap[data]
										},
									},
								],
							},
							{
								name: '投放策略',
								class: 'success',
								click: function (table, lineDom, lineData, dom, data, index) {
									Plugin('simple!product-filter', {
										store: 'jiakao-misc!operation-config/data/filter?id=' + lineData.id,
									})
										.render()
										.done(function () {
											// Store(['sirius!goods-session/data/updateAdverts']).load();
											table.render()
										})
								},
							},
							{
								name: '编辑',
								class: 'warning',
								click: function (table, dom, lineData) {
									addEdit(table, codeMap, 'edit',lineData)
								},
							},
							{
                              name:'配置项',
							  class:'primary',
							  click:function(table,dom,lineData){
								  var panelId = 'navigation-bar-value-config' + lineData.id;
								  var viewPanel = Layout.panel(panelId);
								  if (viewPanel.length == 0) {
									  viewPanel = Layout.panel({
										  id: panelId,
										  name: '配置项'
									  });
								  }
								  require(['jiakao-misc!app/navigation-bar-value-config/index'], function (demo) {
									  demo.list(viewPanel, lineData,function(){
										table.render()
									  })

								  }) 
							  }
							},
							{
								name: '删除',
								class: 'danger',
								xtype: 'delete',
								store: 'jiakao-misc!operation-config/data/delete',
							},
							{
								name: '用户画像',
								class: 'success',
								click: function (table, dom, lineData) {
									OpConfig.editPersonas(table, lineData)
								},
							},
							{
								class: 'danger',
								render: function (name, arr, index) {
									const status = arr[index].status
									if (status == 0) {
										return '测试发布'
									} else if (status == 1) {
										return '发布'
									} else if (status == 2) {
										return '下线'
									}
								},
								click: function (table, row, lineData) {
									console.log(lineData, 'lineData')
									const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1
									console.log(status, 'status')
									let title =
										lineData.status == 1
											? '确定发布吗?'
											: lineData.status == 2
											? '确定下线吗?'
											: '确定测试发布吗?'
									Widgets.dialog.confirm(title, function (e, confirm) {
										if (confirm) {
											Store(['jiakao-misc!operation-config/data/update'])
												.save([
													{
														params: {
															id: lineData.id,
															status,
														},
													},
												])
												.done(function () {
													table.render()
												})
												.fail(function (ret) {
													Widgets.dialog.alert(ret.message)
												})
										}
									})
								},
							},
						    {
                                name:'复制',
                                class:'warning',
                                click:function(table,dom,lineData){
                                      delete lineData['id']
                                      addEdit(table, codeMap,'copy',lineData)
                                } 
                            }
						],
						columns: [
							{
								header: '#',
								dataIndex: 'id',
								width: 20,
							},
							{
								header: 'code',
								dataIndex: 'value',
								render:function(data){
									let valueObject = JSON.parse(data || '{}')
									return valueObject.code
                                }
							},
							{
								header: '名称',
								dataIndex: 'value',
								render:function(data){
									let valueObject = JSON.parse(data || '{}')
									return valueObject.name
                                }
							},
							{
								header: '预览图',
								dataIndex: 'value',
								render: function (data) {
									let valueObject = JSON.parse(data || '{}')
									if (valueObject.previewImage) {
										return `<a>查看</a>`
									}
								},
								click: function (table, row, lineData) {
									let valueObject = JSON.parse(lineData.value || '{}')
									Widgets.dialog.html('预览图', {}).done(function (dialog) {
										let imgStr = `<div><img src="${valueObject.previewImage}"></div>`
										$(dialog.body).html(imgStr)
									})
								},
							},
							{
								header: '背景图',
								dataIndex: 'value',
								render: function (data) {
									let valueObject = JSON.parse(data || '{}')
									if (valueObject.bgImage) {
										return `<a>查看</a>`
									}
								},
								click: function (table, row, lineData) {
									let valueObject = JSON.parse(lineData.value || '{}')
									Widgets.dialog.html('背景图', {}).done(function (dialog) {
										let imgStr = `<div><img src="${valueObject.bgImage}"></div>`
										$(dialog.body).html(imgStr)
									})
								},
							},
							{
								header: '遮罩透明度',
								dataIndex: 'value',
								render:function(data){
									let valueObject = JSON.parse(data || '{}')
									return valueObject.pellucidity
                                }
							},
							{
								header: '是否默认',
								dataIndex: 'value',
								render:function(data){
									let valueObject = JSON.parse(data || '{}')
									if (valueObject.useDefault === 'true') {
										return '是';
									} else if (valueObject.needAuthority === 'false') {
										return '否';
									}
                                }
							},
							{
								header: '是否鉴权',
								dataIndex: 'value',
								render:function(data){
									let valueObject = JSON.parse(data || '{}')
									if (valueObject.needAuthority === 'true') {
										return '是';
									} else if (valueObject.needAuthority === 'false') {
										return '否';
									}
                                }
							},
							{
								header: '模块',
								dataIndex: 'code',
                                render:function(data){
                                    return `<div style="width:150px;word-break:break-all;white-space: pre-wrap">${data}</div>`
                                }
							},
							{
								header: '配置内容',
								dataIndex: 'value',
								render: function () {
									return `<a>点击查看</a>`
								},
								click: function (table, row, lineData) {
									Widgets.dialog.html('配置内容', {}).done(function (dialog) {
										var data = lineData.value && JSON.stringify(JSON.parse(lineData.value), null, 4)
										$(dialog.body).html(
											'<pre style="max-height: 200px; overflow: auto">' + data + '</pre>'
										)
									})
								},
							},
                            {
								header: 'bizRule扩展规则',
								dataIndex: 'bizRule',
								render: function () {
									return `<a>点击查看</a>`
								},
								click: function (table, row, lineData) {
									Widgets.dialog.html('bizRule扩展规则', {}).done(function (dialog) {
										var data = lineData.bizRule && JSON.stringify(JSON.parse(lineData.bizRule), null, 4)
										$(dialog.body).html(
											'<pre style="max-height: 200px; overflow: auto">' + (data||'无') + '</pre>'
										)
									})
								},
							},
						
							{
								header: '排序值',
								dataIndex: 'sort',
								order: 'asc',
							},
							{
								header: '配置说明',
								dataIndex: 'remark',
							},

							{
								header: '状态',
								dataIndex: 'status',
								render: function (data, arr, i) {
									return statusMap[data]
								},
							},
							{
								header: '创建人',
								dataIndex: 'createUserName',
							},
							{
								header: '创建时间',
								render: function (data) {
									return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
								},
								dataIndex: 'createTime',
							},
							{
								header: '修改人',
								dataIndex: 'updateUserName',
							},
							{
								header: '修改时间',
								render: function (data) {
									return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
								},
								dataIndex: 'updateTime',
							},
						],
					},
					['jiakao-misc!operation-config/data/list?codes=' + Object.keys(codeMap) + ''],
					panel,
					null
				).render()
			})
	}

	return {
		list: list,
	}
})
