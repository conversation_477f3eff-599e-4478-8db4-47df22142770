/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!plugin/select-district/district3',
    'jiakao-misc!app/operation-config3/index',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, District3, OpConfig) {
    var zxCitys = ['120000', '110000', '500000', '310000']

    var cityTypeMap = {
        userSelect : '用户选择城市匹配',
        gps :'GPS匹配' 
    };

    var cityArr = []
    for (const key in cityTypeMap) {
        cityArr.push({
            key,
            value:cityTypeMap[key]
        })
    }

    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    var getAllCity = function () {
        var list = District3.list;
        var city = [];

        for (var i = 0; i < list.length; ++i) {
            if (list[i].cities && list[i].code != zxCitys[0] && list[i].code != zxCitys[1] && list[i].code != zxCitys[2] && list[i].code != zxCitys[3]) {
                var data = list[i].cities;
                city.push({
                    key: list[i].code,
                    value: list[i].name,
                    search: list[i].name
                })
                for (var j = 0; j < data.length; ++j) {
                    city.push({
                        key: data[j].code,
                        value: data[j].name,
                        search: data[j].name
                    })
                }

            }

        }

        city.unshift({
            key: "310000",
            value: "上海",
            search: '上海'
        })
        city.unshift({
            key: "120000",
            value: "天津",
            search: '天津'
        })
        city.unshift({
            key: "500000",
            value: "重庆",
            search: '重庆'
        })
        city.unshift({
            key: "110000",
            value: "北京",
            search: '北京'
        })
        city.unshift({
            key: "000000",
            value: "全国",
            search: '全国'
        })
        return city;
    }

    Constants.kemuStore = Constants.kemuStore.map(item => ({
        key: item.key + '',
        value:item.value
    }))

    console.log(Constants.kemuStore,'Constants.kemuStore');

    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

    var statusArr = [];

    for (const key in statusMap) {
        statusArr.push({
            key,
            value:statusMap[key]
        })
    }
  
    var add = function (table, codeMap) {
        console.log(codeMap,'codeMap');
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!operation-config/data/insert?sort=1',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var aiUserId = $(form).find('#aiUserId').val();
                    var image = $('input[name="image"]').val();
                    var leftText = $(form).find('#leftText').val();
                    var rightText = $(form).find('#rightText').val();
                    var btnText = $(form).find('#btnText').val();
                    var btnActionUrl = $(form).find('#btnActionUrl').val();
                    var code = $(form).find('#code').val();
                    
                    var groupId = $(form).find('#groupId').val();
                    
                    const value = JSON.stringify({
                        aiUserId,
                        image,
                        leftText,
                        rightText,
                        btnText,
                        btnActionUrl,
                        groupId
                    })
                    
                    
                    return {
                        value,
                        name: codeMap[code],
                        
                    };
                },
            },
            columns: [
             {
                 header: '配置key：',
                 dataIndex: 'code',
                 xtype: 'select',
                 store: 'jiakao-misc!operation-config/data/codeList?bizType=ai_companion_main_entrance',
                 index: [{
                    key: 'key',
                    value: 'value'
                    }],
                    insert: [{
                            key: '',
                            value: '请选择'
                    }]
             },
             {
                 header: '配置说明：',
                 dataIndex: 'remark',
                 xtype: 'text',
                 maxlength: 128,
                 placeholder: '配置说明'
             },
             {
                header: '过滤城市code:',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: getAllCity(),
                    dataIndex: 'cityCode',
                    isMulti: true,
                    defaultVal: false
                }, function (plugin, value) {
                }),
            },
             {
                 header: '城市的类型：',
                 dataIndex: 'cityType',
                 xtype: 'select',
                 store:cityArr,
                 maxlength: 45,
                 placeholder: '城市的类型'
             },
             {
                 header: '车型：',
                 dataIndex: 'carType',
                 xtype: 'checkbox',
                 store:carTypeArr,
                 placeholder: '车型'
             },
             {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'checkbox',
                store: Constants.kemuStore.slice(1),
                placeholder: '科目'
            }, 
			{
				header: '场景：',
				dataIndex: 'sceneCode',
				xtype: 'checkbox',
				store: Constants.senceStore,
			},
			{
				header: '访问模式：',
				dataIndex: 'patternCode',
				xtype: 'checkbox',
				store: Constants.editionStore,
				placeholder: '访问模式',
			},
             {
                 header: '状态：',
                 dataIndex: 'status',
                 xtype: 'select',
                 store: statusArr,
                 value:2,
                 check: 'required',
                 placeholder: '状态'
                },
                {
                    header: 'IM用户id：',
                    dataIndex: 'aiUserId',
                    xtype: 'text',
                    placeholder: 'IM账号的用户id'
                },
              
                {
                    header: '图片:',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '左边文案：',
                    dataIndex: 'leftText',
                    xtype: 'text',
                    placeholder: '左边文案'
                },
                {
                    header: '右边文案：',
                    dataIndex: 'rightText',
                    xtype: 'text',
                    placeholder: '右边文案'
                },
                {
                    header: '按钮文案：',
                    dataIndex: 'btnText',
                    xtype: 'text',
                    placeholder: '按钮文案'
                },
                {
                    header: '按钮跳转链接：',
                    dataIndex: 'btnActionUrl',
                    xtype: 'text',
                    placeholder: '按钮跳转链接'
                }
                

            ]
        }).add();
    }

    var list = function (panel) {
        Store(['jiakao-misc!operation-config/data/codeList?bizType=ai_companion_main_entrance']).load().done((retData) => { 
            console.log(retData, '125');
            const codeArr = retData.data['operation-config'].data.codeList.data;
            console.log('codeArr', codeArr);
            
            let codeMap = {};
            codeArr.forEach((code) => {
                codeMap[code.key] = code.value
            })
            Table({
                description: 'AI陪练的主入口配置',
                title: 'AI陪练的主入口配置',
                search: [{
                    dataIndex: 'codes',
                    xtype: 'select',
                    store: [{
                        key: Object.keys(codeMap)+'',
                        value: '全部'
                    }].concat(codeArr),
                }],
                buttons: {
                    top: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function(obj) {
                                obj.render();
                            }
                        },
                        {
                            name: '添加',
                            class: 'primary',
                            click: function (table) {
                                add(table,codeMap)
                            }
                        }
                    ],
                    bottom: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function(obj) {
                                obj.render();
                            }
                        }
                    ]
                },
                operations: [
                    {
                        name: '查看',
                        xtype: 'view',
                        width: 400,
                        class: 'success',
                        title: '查看',
                        store: 'jiakao-misc!operation-config/data/view',
                        columns: [
                            {
                                header: '#',
                                dataIndex: 'id'
                            },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                            },
                            {
                                header: '配置说明：',
                                dataIndex: 'remark'
                            },
                        {
                            header: '配置key：',
                            dataIndex: 'code'
                        },
                        {
                            header: '配置内容：',
                            dataIndex: 'value'
                        },
                        {
                            header: '排序值：',
                            dataIndex: 'sort'
                        },
                      
                        {
                            header: '过滤城市code：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '城市的类型：',
                            dataIndex: 'cityType'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType',
                            render: function (data) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(TIKU[data[i]])
                                    }
                                    return strArr.join(',');
                                }
        
                            }
                        },
                        {
                            header: '科目 1,2,3,4：',
                            dataIndex: 'kemu',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.kemuMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        
                        
                        {
                            header: '通用配置：',
                            dataIndex: 'conditions'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            render: function (data, arr, i) {
                                return statusMap[data]
                            }
                        }]
                    },
                    {
                        name: '投放策略',
                        class: 'success',
                        click: function (table, lineDom, lineData, dom, data, index) {
                            Plugin('simple!product-filter', {
                                store: 'jiakao-misc!operation-config/data/filter?id=' + lineData.id
                            }).render().done(function () {
                                table.render();
                            });
                        }
                    },
                    {
                        name: '编辑',
                        xtype: 'edit',
                        width: 500,
                        class: 'warning',
                        title: '编辑',
                        success: function(obj, dialog, e) {
                            dialog.close();
                            obj.render();
                        },
                        renderAfter: function (table,dom,data) {
                            setTimeout(() => {
                                const value = JSON.parse(data.data.value);
                                
                                $('input[name="aiUserId"]').val(value.aiUserId);
                                dom.item('clickUrl').val(value.clickUrl);
                                $('input[name="image"]').val(value.image);
                                
                                dom.item('leftText').val(value.leftText);
                                dom.item('rightText').val(value.rightText);
                                dom.item('btnText').val(value.btnText);
                                dom.item('btnActionUrl').val(value.btnActionUrl);
                                // dom.item('valueTarget').val(data.data.value);
                        },500)
                        },
                        form: {
                            submitHandler: function (form) {
                                var aiUserId = $(form).find('#aiUserId').val();
                                var image = $('input[name="image"]').val();
                                var leftText = $(form).find('#leftText').val();
                                var rightText = $(form).find('#rightText').val();
                                var btnText = $(form).find('#btnText').val();
                                var btnActionUrl = $(form).find('#btnActionUrl').val();
                                var code = $(form).find('#code').val();
                                
                                var groupId = $(form).find('#groupId').val();
                                
                                const value = JSON.stringify({
                                    aiUserId,
                                    image,
                                    leftText,
                                    rightText,
                                    btnText,
                                    btnActionUrl,
                                    groupId
                                })
                                
                                
                                return {
                                    value,
                                    name: codeMap[code],
                                    
                                };
                            },
                        },
                        store: {
                            load: 'jiakao-misc!operation-config/data/view',
                            save: 'jiakao-misc!operation-config/data/update?sort=1'
                        },
                        columns: [
                            {
                                dataIndex: 'id',
                                xtype: 'hidden'
                            },
                {
                    header: '配置key：',
                    dataIndex: 'code',
                    xtype: 'select',
                    store: 'jiakao-misc!operation-config/data/codeList?bizType=ai_companion_main_entrance',
                    index: [{
                        key: 'key',
                        value: 'value'
                    }],
                },               
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '过滤城市code:',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: getAllCity(),

                        dataIndex: 'cityCode',
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                },
                {
                    header: '城市的类型：',
                    dataIndex: 'cityType',
                    xtype: 'select',
                    store:cityArr,
                    placeholder: '城市的类型'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store:carTypeArr,
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store: Constants.kemuStore.slice(1),
                    placeholder: '科目'
                },
                {
				header: '场景：',
				dataIndex: 'sceneCode',
				xtype: 'checkbox',
				store: Constants.senceStore,
			},
			{
				header: '访问模式：',
				dataIndex: 'patternCode',
				xtype: 'checkbox',
				store: Constants.editionStore,
				placeholder: '访问模式',
			},
                
                {
                    header: '状态：',
                    dataIndex: 'status',
                    xtype: 'select',
                    store:statusArr,
                    check: 'required',
                    placeholder: '状态'
                },
                {
                    header: 'IM用户id：',
                    dataIndex: 'aiUserId',
                    xtype: 'text',
                    placeholder: 'IM账号的用户id'
                },
              
                {
                    header: '图片:',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "jiakao-web",
                        isSingle: true,
                        valuePath:'value.image',
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '左边文案：',
                    dataIndex: 'leftText',
                    xtype: 'text',
                    placeholder: '左边文案'
                },
                {
                    header: '右边文案：',
                    dataIndex: 'rightText',
                    xtype: 'text',
                    placeholder: '右边文案'
                },
                {
                    header: '按钮文案：',
                    dataIndex: 'btnText',
                    xtype: 'text',
                    placeholder: '按钮文案'
                },
                {
                    header: '按钮跳转链接：',
                    dataIndex: 'btnActionUrl',
                    xtype: 'text',
                    placeholder: '按钮跳转链接'
                }
                
                            
                           
                        ]
                    },
                    {
                        name: '删除',
                        class: 'danger',
                        xtype: 'delete',
                        store: 'jiakao-misc!operation-config/data/delete'
                    },
                    {
                        name: '用户画像',
                        class: 'success',
                        click: function (table, dom, lineData) {
                            OpConfig.editPersonas(table, lineData)
                        },
                    },
                    {
                        class: 'danger',
                        render: function (name, arr, index) {
                            const status = arr[index].status
                            if (status == 0) {
                                return '测试发布';
                            } else if (status == 1) {
                                return '发布';
                            }else if (status == 2) {
                                return '下线';
                            }
                        },
                        click: function (table, row, lineData) {
                            console.log(lineData, 'lineData');
                            const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1;
                            console.log(status, 'status');
                            let title = lineData.status == 1 ? '确定发布吗?' :   lineData.status == 2 ? '确定下线吗?' : '确定测试发布吗?'
                            Widgets.dialog.confirm(title, function (e, confirm) {
                                if (confirm) {
                                    Store(['jiakao-misc!operation-config/data/update']).save([{
                                        params: {
                                            id: lineData.id,
                                            status
                                        }
                                    }]).done(function () {
                                        table.render();
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message);
                                    })
                                }
                            })
                        }
                    },
                    {
                        name: '复制',
                        xtype: 'edit',
                        width: 500,
                        class: 'warning',
                        title: '编辑',
                        success: function(obj, dialog, e) {
                            dialog.close();
                            obj.render();
                        },
                        renderAfter: function (table,dom,data) {
                            setTimeout(() => {
                                const value = JSON.parse(data.data.value);
                                
                                $('input[name="aiUserId"]').val(value.aiUserId);
                                dom.item('clickUrl').val(value.clickUrl);
                                $('input[name="image"]').val(value.image);
                                
                                dom.item('leftText').val(value.leftText);
                                dom.item('rightText').val(value.rightText);
                                dom.item('btnText').val(value.btnText);
                                dom.item('btnActionUrl').val(value.btnActionUrl);
                                
                        },500)
                        },
                        form: {
                            submitHandler: function (form) {
                                var aiUserId = $(form).find('#aiUserId').val();
                                var image = $('input[name="image"]').val();
                                var leftText = $(form).find('#leftText').val();
                                var rightText = $(form).find('#rightText').val();
                                var btnText = $(form).find('#btnText').val();
                                var btnActionUrl = $(form).find('#btnActionUrl').val();
                                var code = $(form).find('#code').val();
                                
                                var groupId = $(form).find('#groupId').val();
                                
                                const value = JSON.stringify({
                                    aiUserId,
                                    image,
                                    leftText,
                                    rightText,
                                    btnText,
                                    btnActionUrl,
                                    groupId
                                })
                                
                                
                                return {
                                    value,
                                    name: codeMap[code],
                                    
                                };
                            },
                            reverseParams:true
                        },
                        store: {
                            load: 'jiakao-misc!operation-config/data/view',
                            save: 'jiakao-misc!operation-config/data/insert'
                        },
                        columns: [
                        
                //  {
                //      header: '名称：',
                //      dataIndex: 'name',
                //      xtype: 'text',
                //      maxlength: 128,
                //      check: 'required',
                //      placeholder: '名称'
                //  },
                {
                    header: '配置key：',
                    dataIndex: 'code',
                    xtype: 'select',
                    store: 'jiakao-misc!operation-config/data/codeList?bizType=ai_companion_main_entrance',
                    index: [{
                        key: 'key',
                        value: 'value'
                    }]
                },
                
                {
                    header: '排序值：',
                    dataIndex: 'sort',
                    xtype: 'number',
                    value: 1,
                    disabled:true,
                    check: 'required',
                    placeholder: '排序值'
                },
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '过滤城市code:',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: getAllCity(),

                        dataIndex: 'cityCode',
                        // index: {
                        //
                        //     key: 'code',
                        //     value: 'name',
                        //     search: 'name'
                        // },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                },
                {
                    header: '城市的类型：',
                    dataIndex: 'cityType',
                    xtype: 'select',
                    store:cityArr,
                    placeholder: '城市的类型'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store:carTypeArr,
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store: Constants.kemuStore.slice(1),
                    placeholder: '科目'
                },
               {
				header: '场景：',
				dataIndex: 'sceneCode',
				xtype: 'checkbox',
				store: Constants.senceStore,
			},
			{
				header: '访问模式：',
				dataIndex: 'patternCode',
				xtype: 'checkbox',
				store: Constants.editionStore,
				placeholder: '访问模式',
			},
                
                {
                    header: '通用配置：',
                    dataIndex: 'conditions',
                    xtype: 'textarea',
                    placeholder: '通用配置'
                },
                {
                    header: '状态：',
                    dataIndex: 'status',
                    xtype: 'select',
                    store:statusArr,
                    check: 'required',
                    placeholder: '状态'
                },
                
                           
                {
                    header: 'IM用户id：',
                    dataIndex: 'aiUserId',
                    xtype: 'text',
                    placeholder: 'IM账号的用户id'
                },
              
                {
                    header: '图片:',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "jiakao-web",
                        isSingle: true,
                        valuePath:'value.image',
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '左边文案：',
                    dataIndex: 'leftText',
                    xtype: 'text',
                    placeholder: '左边文案'
                },
                {
                    header: '右边文案：',
                    dataIndex: 'rightText',
                    xtype: 'text',
                    placeholder: '右边文案'
                },
                {
                    header: '按钮文案：',
                    dataIndex: 'btnText',
                    xtype: 'text',
                    placeholder: '按钮文案'
                },
                {
                    header: '按钮跳转链接：',
                    dataIndex: 'btnActionUrl',
                    xtype: 'text',
                    placeholder: '按钮跳转链接'
                }
                        ]
                    },

                    // {
                    //     name: '复制2',
                    //     click: function (table, row, lineData) {
                    //         delete lineData.id;
                    //         Store(['jiakao-misc!operation-config/data/insert']).save([{
                    //             params: lineData
                    //         }]).done(function () {
                    //             table.render();
                    //         }).fail(function (ret) {
                    //             Widgets.dialog.alert(ret.message);
                    //         })
                    //     }
                    // }
                ],
                columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                        {
                            header: '名称',
                            dataIndex: 'name'
                        },
                        {
                            header: '配置说明',
                            dataIndex: 'remark'
                        },
                        {
                            header: '配置key',
                            dataIndex: 'code'
                        },
                        {
                            header: '配置内容',
                            dataIndex: 'value',
                            render: function () {
                                return `<a>点击查看</a>`
                            },
                            click: function (table, row, lineData) {
                                Widgets.dialog.html('配置内容', {}).done(function (dialog) {
                                    var data = lineData.value && JSON.stringify(JSON.parse(lineData.value), null, 4)
                                    $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                                })
                            }
                    },
                    {
                        header: '配置的图片',
                        dataIndex: 'value',
                        render: function () {
                            return `<a>点击查看</a>`
                        },
                        click: function (table, row, lineData) {
                            const value = JSON.parse(lineData.value);

                            Widgets.dialog.html('配置的图片', {}).done(function (dialog) {
                                $(dialog.body).html(`图片: <img src="${value.image}" style="width:200px">`)
                            })
                        }
                    },
                        {
                            header: '排序值',
                            dataIndex: 'sort'
                        },
                       
                        {
                            header: '过滤城市code',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '城市的类型',
                            dataIndex: 'cityType',
                            render: function (data) {
                                return cityTypeMap[data]
                            }
                        },
                        
                        {
                            header: '车型',
                            dataIndex: 'carType',
                            render: function (data) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(TIKU[data[i]])
                                    }
                                    return strArr.join(',');
                                }
        
                            }
                        },
                        {
                            header: '科目',
                            dataIndex: 'kemu',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.kemuMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        {
								header: '场景',
								dataIndex: 'sceneCode',
								render: function (data, arr, i) {
									if (data) {
										data = data.split(',')
										var strArr = []
										for (var i = 0; i < data.length; i++) {
											strArr.push(Constants.senceMap[data[i]])
										}
										return strArr.join(',')
									}
								},
							},
							{
								header: '访问模式',
								dataIndex: 'patternCode',
								render: function (data, arr, i) {
									if (data) {
										data = data.split(',')
										var strArr = []
										for (var i = 0; i < data.length; i++) {
											strArr.push(Constants.editionMap[data[i]])
										}
										return strArr.join(',')
									}
								},
							},
                      
                        {
                            header: '通用配置',
                            dataIndex: 'conditions',
                            render: function () {
                                return `<a>点击查看</a>`
                            },
                            click: function (table, row, lineData) {
                                Widgets.dialog.html('通用配置', {}).done(function (dialog) {
                                    var data = lineData.conditions && JSON.stringify(JSON.parse(lineData.conditions), null, 4)
                                    $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                                })
                            }
                        },
                        {
                            header: '状态',
                            dataIndex: 'status',
                            render: function (data, arr, i) {
                                return statusMap[data]
                            }
                        },
                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '修改时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                ]
            }, ['jiakao-misc!operation-config/data/list?codes=' + Object.keys(codeMap)+''], panel, null).render();
        })
    }

    return {
        list: list
    }

});