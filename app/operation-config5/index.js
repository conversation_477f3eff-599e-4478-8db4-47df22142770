/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!plugin/select-district/district3',
    'jiakao-misc!app/operation-config3/index',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, District3, OpConfig) {

    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }
 

    Constants.kemuStore = Constants.kemuStore.map(item => ({
        key: item.key + '',
        value:item.value
    }))

    console.log(Constants.kemuStore,'Constants.kemuStore');

    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

    var statusArr = [];

    for (const key in statusMap) {
        statusArr.push({
            key,
            value:statusMap[key]
        })
    }
 

 

    function renderCodeFn(dom, data) {
        if (data) {
            setTimeout(() => {
                const value = JSON.parse(data.data.value);
                dom.item('actionUrl').val(value.actionUrl);
                dom.item('title').val(value.title);
            }, 200)
        }
    }
    var add = function (table, codeMap) {
        console.log(codeMap,'codeMap');
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!operation-config/data/insert?bizType=pc_live',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var actionUrl = $(form).find('#actionUrl').val();
                    var title = $(form).find('#title').val();
                    var code = $(form).find('#code').val();
                    var actionShowType = $(form).find('#actionShowType').val();
                    var imageUrl = $('input[name="imageUrl"]').val();
                    const value = JSON.stringify({
                        title,  actionUrl, actionShowType,   imageUrl
                    })


                    
                    return {
                        value,
                        name: codeMap[code]
                    };
                },
            },
            columns: [
             {
                 header: '配置key：',
                 dataIndex: 'code',
                 xtype: 'select',
                 store: 'jiakao-misc!operation-config/data/codeList?bizType=pc_live',
                 index: [{
                    key: 'key',
                    value: 'value'
                    }],
                    insert: [{
                            key: '',
                            value: '请选择'
                    }]
             },
         
             {
                header: '场景code：',
                dataIndex: 'sceneCode',
                xtype: 'checkbox',
                store: Constants.senceStore,
            },
            {
                header: "访问模式：",
                dataIndex: "patternCode",
                xtype: "checkbox",
                store: Constants.editionStore,
                placeholder: "访问模式",
            },
             {
                 header: '车型：',
                 dataIndex: 'carType',
                 xtype: 'checkbox',
                 store:carTypeArr,
                 placeholder: '车型'
             },
             {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'checkbox',
                store: Constants.kemuStore.slice(1).concat([{key:'5',value:'拿本'}]),
                placeholder: '科目'
                }, 
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '排序值：',
                    dataIndex: 'sort',
                    xtype: 'number',
                    check: 'required',
                    placeholder: '排序值'
                },
            
            {
                header: '标题：',
                dataIndex: 'title',
                xtype: 'text',
                placeholder: '标题'
                },
                {
                    header: '图片地址：',
                    dataIndex: 'imageUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'imageUrl',
                        uploadIndex: 'imageUrl',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '跳转链接：',
                    dataIndex: 'actionUrl',
                    xtype: 'text',
                    placeholder: '跳转链接'
                },
            
            ]
        }).add();
    }

    var list = function (panel) {
        Store(['jiakao-misc!operation-config/data/codeList?bizType=pc_live']).load().done((retData) => { 
            console.log(retData, '125');
            const codeArr = retData.data['operation-config'].data.codeList.data;
            let codeMap = {};
            codeArr.forEach((code) => {
                codeMap[code.key] = code.value
            })
            Table({
                description: ' pc直播运营位配置',
                title: ' pc直播运营位配置',
                search: [{
                    dataIndex: 'codes',
                    xtype: 'select',
                    store: [{
                        key: Object.keys(codeMap)+'',
                        value: '全部'
                    }].concat(codeArr),
                }, {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store:[{key:'',value:'请选择车型'},...carTypeArr],
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore.concat([{ key: '5', value: '拿本' }]),
                    placeholder: '科目'
                    }, {
                    header:'状态:',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{key:'',value:'请选择状态'},...statusArr],
                }],
                buttons: {
                    top: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function(obj) {
                                obj.render();
                            }
                        },
                        {
                            name: '添加',
                            class: 'primary',
                            click: function (table) {
                                add(table,codeMap)
                            }
                        }
                    ],
                    bottom: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function(obj) {
                                obj.render();
                            }
                        }
                    ]
                },
                operations: [
                    {
                        name: '查看',
                        xtype: 'view',
                        width: 400,
                        class: 'success',
                        title: '查看',
                        store: 'jiakao-misc!operation-config/data/view',
                        columns: [
                            {
                                header: '#',
                                dataIndex: 'id'
                            },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                            },
                            {
                                header: '配置说明：',
                                dataIndex: 'remark'
                            },
                        {
                            header: '配置key：',
                            dataIndex: 'code'
                        },
                        {
                            header: '配置内容：',
                            dataIndex: 'value'
                        },
                       
                        {
                            header: '场景',
                            dataIndex: 'sceneCode',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.senceMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        {
                            header: '访问模式',
                            dataIndex: 'patternCode',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.editionMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType',
                            render: function (data) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(TIKU[data[i]])
                                    }
                                    return strArr.join(',');
                                }
        
                            }
                        },
                        {
                            header: '科目 1,2,3,4,5：',
                            dataIndex: 'kemu',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        if(data[i]=='5'){
                                            strArr.push('拿本')
                                        }else{
                                            strArr.push(Constants.kemuMap[data[i]])
                                        }
                                        
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        
                     
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            render: function (data, arr, i) {
                                return statusMap[data]
                            }
                        }]
                    },
                    {
                        name: '编辑',
                        xtype: 'edit',
                        width: 500,
                        class: 'warning',
                        title: '编辑',
                        success: function(obj, dialog, e) {
                            dialog.close();
                            obj.render();
                        },
                        renderAfter: function (table, dom, data) { 
                            renderCodeFn(dom,data)
                        },
                        form: {
                            submitHandler: function (form) {
                                var actionUrl = $(form).find('#actionUrl').val();
                                var title = $(form).find('#title').val();
                                var code = $(form).find('#code').val();
                                var actionShowType = $(form).find('#actionShowType').val();
                                var imageUrl = $('input[name="imageUrl"]').val();
                                
                                const value = JSON.stringify({
                                    title,  actionUrl, actionShowType,  imageUrl
                                })
            
                          
                                
                                return {
                                    value,
                                    name: codeMap[code]
                                };
                            },
                        },
                        store: {
                            load: 'jiakao-misc!operation-config/data/view',
                            save: 'jiakao-misc!operation-config/data/update'
                        },
                        columns: [
                            {
                                dataIndex: 'id',
                                xtype: 'hidden'
                            },
                            {
                                header: '配置key：',
                                dataIndex: 'code',
                                xtype: 'select',
                                store: 'jiakao-misc!operation-config/data/codeList?bizType=pc_live',
                                index: [{
                                    key: 'key',
                                    value: 'value'
                                }],
                            },
                            
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '场景code：',
                    dataIndex: 'sceneCode',
                    xtype: 'checkbox',
                    store: Constants.senceStore,
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store:carTypeArr,
                    placeholder: '车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store: Constants.kemuStore.slice(1).concat([{ key: '5', value: '拿本' }]),
                    placeholder: '科目'
                },
                {
                    header: '排序值：',
                    dataIndex: 'sort',
                    xtype: 'number',
                    check: 'required',
                    placeholder: '排序值'
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    placeholder: '标题'
                            },

                            {
                                header: '图片地址：',
                                dataIndex: 'imageUrl',
                                xtype: Plugin('jiakao-misc!upload', {
                                    dataIndex: 'imageUrl',
                                    uploadIndex: 'imageUrl',
                                    bucket: "exam-room",
                                    isSingle: true,
                                    valuePath:'value.imageUrl',
                                    placeholder: '请选择上传文件',
                                    check: 'required',
                                    url: 'simple-upload3://upload/file.htm'
                                }, function () {
                                    console.log(arguments)
                                })
                            },
                            {
                                header: '跳转链接：',
                                dataIndex: 'actionUrl',
                                xtype: 'text',
                                placeholder: '跳转链接'
                            },
         
                        ]
                    },
                    {
                        name: '删除',
                        class: 'danger',
                        xtype: 'delete',
                        store: 'jiakao-misc!operation-config/data/delete'
                    },
                    {
                        name: '用户画像',
                        class: 'success',
                        click: function (table, dom, lineData) {
                            OpConfig.editPersonas(table, lineData)
                        },
                    },
                    {
                        class: 'danger',
                        render: function (name, arr, index) {
                            const status = arr[index].status
                            if (status == 0) {
                                return '测试发布';
                            } else if (status == 1) {
                                return '发布';
                            }else if (status == 2) {
                                return '下线';
                            }
                        },
                        click: function (table, row, lineData) {
                            console.log(lineData, 'lineData');
                            const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1;
                            console.log(status, 'status');
                            let title = lineData.status == 1 ? '确定发布吗?' :   lineData.status == 2 ? '确定下线吗?' : '确定测试发布吗?'
                            Widgets.dialog.confirm(title, function (e, confirm) {
                                if (confirm) {
                                    Store(['jiakao-misc!operation-config/data/update']).save([{
                                        params: {
                                            id: lineData.id,
                                            status
                                        }
                                    }]).done(function () {
                                        table.render();
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message);
                                    })
                                }
                            })
                        }
                    },
                    {
                        name: '复制',
                        xtype: 'edit',
                        width: 500,
                        class: 'warning',
                        title: '编辑',
                        success: function (obj, dialog, e) {
                            dialog.close();
                            obj.render();
                        },
                        renderAfter: function (table, dom, data) { 
                            renderCodeFn(dom,data)
                        },
                        form: {
                            submitHandler: function (form) {
                                var actionUrl = $(form).find('#actionUrl').val();
                                var title = $(form).find('#title').val();
                                var code = $(form).find('#code').val();
                            
                                var actionShowType = $(form).find('#actionShowType').val(); 
                                var imageUrl = $('input[name="imageUrl"]').val();
                                const value = JSON.stringify({
                                    title,  actionUrl, actionShowType,   imageUrl
                                })
            
                          
                                
                                return {
                                    value,
                                    name: codeMap[code]
                                };
                            },
                            reverseParams:true
                        },
                        store: {
                            load: 'jiakao-misc!operation-config/data/view',
                            save: 'jiakao-misc!operation-config/data/insert'
                        },
                        columns: [
                            {
                                header: '配置key：',
                                dataIndex: 'code',
                                xtype: 'select',
                                store: 'jiakao-misc!operation-config/data/codeList?bizType=pc_live',
                                index: [{
                                   key: 'key',
                                   value: 'value'
                                   }],
                                   insert: [{
                                           key: '',
                                           value: '请选择'
                                   }]
                            },
                                           
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '场景code：',
                    dataIndex: 'sceneCode',
                    xtype: 'checkbox',
                    store: Constants.senceStore,
                },
                {
                    header: "访问模式：",
                    dataIndex: "patternCode",
                    xtype: "checkbox",
                    store: Constants.editionStore,
                    placeholder: "访问模式",
                },
                            {
                                header: '车型：',
                                dataIndex: 'carType',
                                xtype: 'checkbox',
                                store:carTypeArr,
                                placeholder: '车型'
                            },
                            {
                               header: '科目：',
                               dataIndex: 'kemu',
                               xtype: 'checkbox',
                                store: Constants.kemuStore.slice(1).concat([{ key: '5', value: '拿本' }]),
                               placeholder: '科目'
                               }, 
                        
                           
                           {
                               header: '标题：',
                               dataIndex: 'title',
                               xtype: 'text',
                               placeholder: '标题'
                            },
                            {
                                header: '图片地址：',
                                dataIndex: 'imageUrl',
                                xtype: Plugin('jiakao-misc!upload', {
                                    dataIndex: 'imageUrl',
                                    uploadIndex: 'imageUrl',
                                    bucket: "exam-room",
                                    isSingle: true,
                                    valuePath:'value.imageUrl',
                                    placeholder: '请选择上传文件',
                                    check: 'required',
                                    url: 'simple-upload3://upload/file.htm'
                                }, function () {
                                    console.log(arguments)
                                })
                            },
                            {
                                header: '跳转链接：',
                                dataIndex: 'actionUrl',
                                xtype: 'text',
                                placeholder: '跳转链接'
                            },
                         
                        ]
                    },
                ],
                columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                      
                    {
                        header: '标题',
                        dataIndex: 'value',
                        render: function (data) {
                            if (data) {
                                return JSON.parse(data).title
                            }
                        },
                    },
                    {
                        header: '名称',
                        dataIndex: 'name'
                },
                    {
                        header: '图片地址',
                        dataIndex: 'value',
                        render: function (data) {
                            if (data) {
                                const url = JSON.parse(data).imageUrl;
                                return '<img style="width:100px;" src="' + url + '" />';
                            }
                        },
                    },
                      
                        {
                            header: '配置key',
                            dataIndex: 'code'
                        },
                        {
                            header: '配置内容',
                            dataIndex: 'value',
                            render: function () {
                                return `<a>点击查看</a>`
                            },
                            click: function (table, row, lineData) {
                                Widgets.dialog.html('配置内容', {}).done(function (dialog) {
                                    var data = lineData.value && JSON.stringify(JSON.parse(lineData.value), null, 4)
                                    $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                                })
                            }
                    },
                    {
                        header: '场景',
                        dataIndex: 'sceneCode',
                        render: function (data, arr, i) {
                            if (data) {
                                data = data.split(',');
                                var strArr = [];
                                for (var i = 0; i < data.length; i++) {
                                    strArr.push(Constants.senceMap[data[i]])
                                }
                                return strArr.join(',');
                            }
                        }
                    },
                    {
                        header: '访问模式',
                        dataIndex: 'patternCode',
                        render: function (data, arr, i) {
                            if (data) {
                                data = data.split(',');
                                var strArr = [];
                                for (var i = 0; i < data.length; i++) {
                                    strArr.push(Constants.editionMap[data[i]])
                                }
                                return strArr.join(',');
                            }
                        }
                    },
                       
                        {
                            header: '车型',
                            dataIndex: 'carType',
                            render: function (data) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(TIKU[data[i]])
                                    }
                                    return strArr.join(',');
                                }
        
                            }
                        },
                        {
                            header: '科目',
                            dataIndex: 'kemu',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        if(data[i]=='5'){
                                            strArr.push('拿本')
                                        }else{
                                            strArr.push(Constants.kemuMap[data[i]])
                                        }
                                       
                                    }
                                    return strArr.join(',');
                                }
                            }
                    },
                     
                    {
                        header: '排序值',
                        dataIndex: 'sort',
                        order: 'asc',
                    },
                   
                        {
                            header: '状态',
                            dataIndex: 'status',
                            render: function (data, arr, i) {
                                return statusMap[data]
                            }
                    },
                    {
                        header: '配置说明',
                        dataIndex: 'remark'
                    },
                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '修改人',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '修改时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                ]
            }, ['jiakao-misc!operation-config/data/list?bizType=pc_live'], panel, null).render();
        })
            
    }

    return {
        list: list
    }

});