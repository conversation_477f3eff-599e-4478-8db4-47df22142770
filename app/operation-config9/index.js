/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!plugin/select-district/district3',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, District3) {

    var carTypeArr = [];
    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    Constants.kemuStore = Constants.kemuStore.map(item => ({
        key: item.key + '',
        value: item.value
    }));

    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }
    var statusArr = [];
    for (const key in statusMap) {
        statusArr.push({
            key,
            value: statusMap[key]
        })
    }

    const propertyMap = {
        'positive': '积极',
        'passive': '消极',
        'neutral': '中性',
    };
    const propertyStore = [{ key: '', value: '选择性质' }].concat(Object.keys(propertyMap).map(key => {
        return {
            key: key,
            value: propertyMap[key]
        };
    }));

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!operation-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                reverseParam: true,
                submitHandler: function (form) {
                    var name = $(form).find('#name').val();
                    var icon = $('input[name="icon"]').val();
                    var code = $(form).find('#code').val();
                    var likeName = $(form).find('#likeName').val();
                    var property = $(form).find('#property').val();
                    const value = JSON.stringify({
                        name, icon, code, likeName, property
                    });

                    return {
                        value,
                        bizType: 'learning_union_state',
                        code: 'learning_union_state_mood',
                        remark: 'remark'
                    };
                },
            },
            columns: [
                {
                    header: '状态名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '状态名称'
                },
                {
                    header: '图标：',
                    dataIndex: 'icon',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'icon',
                        uploadIndex: 'icon',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        check: 'required',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: 'code：',
                    dataIndex: 'code',
                    xtype: 'text',
                    check: 'required',
                    placeholder: 'code'
                },
                {
                    header: '激励文案：',
                    dataIndex: 'likeName',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '激励文案'
                },
                {
                    header: '适用车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store: carTypeArr,
                    placeholder: '车型'
                },
                {
                    header: '适用科目：',
                    dataIndex: 'kemu',
                    xtype: 'checkbox',
                    store: Constants.kemuStore.slice(1).concat([{ key: '5', value: '拿本' }]),
                    placeholder: '科目'
                },
                {
                    header: '排序：',
                    dataIndex: 'sort',
                    xtype: 'number',
                    check: 'required',
                    placeholder: '排序（值越大越靠后）'
                },
                {
                    header: '性质：',
                    dataIndex: 'property',
                    xtype: 'select',
                    store: propertyStore,
                    check: 'required',
                },
                {
                    header: '状态：',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{ key: '', value: '选择状态' }].concat(statusArr),
                    check: 'required',
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '驾考状态配置',
            title: '驾考状态配置',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            add(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    renderAfter: function (table, dom, data) {
                        if (data) {
                            setTimeout(() => {
                                const value = JSON.parse(data.data.value);
                                dom.item('name').val(value.name);
                                dom.item('code').val(value.code);
                                dom.item('likeName').val(value.likeName);
                                dom.item('property').val(value.property);
                            }, 200)
                        }
                    },
                    form: {
                        reverseParam: true,
                        submitHandler: function (form) {
                            var name = $(form).find('#name').val();
                            var icon = $('input[name="icon"]').val();
                            var code = $(form).find('#code').val();
                            var likeName = $(form).find('#likeName').val();
                            var property = $(form).find('#property').val();
                            const value = JSON.stringify({
                                name, icon, code, likeName, property
                            });

                            return {
                                value,
                                bizType: 'learning_union_state',
                                code: 'learning_union_state_mood',
                                remark: 'remark'
                            };
                        },
                    },
                    store: {
                        load: 'jiakao-misc!operation-config/data/view',
                        save: 'jiakao-misc!operation-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '状态名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '状态名称'
                        },
                        {
                            header: '图标：',
                            dataIndex: 'icon',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'icon',
                                uploadIndex: 'icon',
                                valuePath: 'value.icon',
                                bucket: "exam-room",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                check: 'required',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: 'code：',
                            dataIndex: 'code',
                            xtype: 'text',
                            check: 'required',
                            placeholder: 'code'
                        },
                        {
                            header: '激励文案：',
                            dataIndex: 'likeName',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '激励文案'
                        },
                        {
                            header: '适用车型：',
                            dataIndex: 'carType',
                            xtype: 'checkbox',
                            store: carTypeArr,
                            placeholder: '车型'
                        },
                        {
                            header: '适用科目：',
                            dataIndex: 'kemu',
                            xtype: 'checkbox',
                            store: Constants.kemuStore.slice(1).concat([{ key: '5', value: '拿本' }]),
                            placeholder: '科目'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sort',
                            xtype: 'number',
                            check: 'required',
                            placeholder: '排序（值越大越靠后）'
                        },
                        {
                            header: '性质：',
                            dataIndex: 'property',
                            xtype: 'select',
                            store: propertyStore,
                            check: 'required',
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            xtype: 'select',
                            store: [{ key: '', value: '选择状态' }].concat(statusArr),
                            check: 'required',
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!operation-config/data/delete'
                },
                {
                    class: 'danger',
                    render: function (name, arr, index) {
                        const status = arr[index].status
                        if (status == 0) {
                            return '测试发布';
                        } else if (status == 1) {
                            return '发布';
                        } else if (status == 2) {
                            return '下线';
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log(lineData, 'lineData');
                        const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1;
                        console.log(status, 'status');
                        let title = lineData.status == 1 ? '确定发布吗?' : lineData.status == 2 ? '确定下线吗?' : '确定测试发布吗?'
                        Widgets.dialog.confirm(title, function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!operation-config/data/update']).save([{
                                    params: {
                                        id: lineData.id,
                                        status
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '状态名称',
                    dataIndex: 'value',
                    render: function (data) {
                        if (data) {
                            return JSON.parse(data).name
                        }
                    },
                },
                {
                    header: '图标',
                    dataIndex: 'value',
                    render: function (data) {
                        if (data) {
                            const url = JSON.parse(data).icon;
                            return '<img style="width:100px;" src="' + url + '" />';
                        }
                    },
                },
                {
                    header: 'code',
                    dataIndex: 'value',
                    render: function (data) {
                        if (data) {
                            return JSON.parse(data).code
                        }
                    },
                },
                {
                    header: '激励文案',
                    dataIndex: 'value',
                    render: function (data) {
                        if (data) {
                            return JSON.parse(data).likeName
                        }
                    },
                },
                {
                    header: '适用车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '适用科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                if (data[i] == '5') {
                                    strArr.push('拿本')
                                } else {
                                    strArr.push(Constants.kemuMap[data[i]])
                                }

                            }
                            return strArr.join(',');
                        }
                    }
                },
                {
                    header: '排序',
                    dataIndex: 'sort',
                    order: 'asc',
                },
                {
                    header: '性质',
                    dataIndex: 'value',
                    render: function (data) {
                        if (data) {
                            return propertyMap[JSON.parse(data).property]
                        }
                    },
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data, arr, i) {
                        return statusMap[data]
                    }
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }
            ]
        }, ['jiakao-misc!operation-config/data/list?bizType=learning_union_state&codes=learning_union_state_mood'], panel, null).render();
    }

    return {
        list: list
    }

});