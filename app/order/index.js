/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

    
define(['simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants,Tools) {

    var payType = {
        1: '支付宝',
        2: '微信',
        3: '苹果'
    }
    var payStatus = {
        0: '支付中',
        1: '已支付',
        2: '退款中',
        3: '退款成功',
        4: '退款失败',
    }

    var carTypeStore = [
        {
            
            key: 'car',
            value: '小车'
        },
        {
            key: 'truck',
            value: '货车'
        },
        {
            key: 'bus',
            value: '客车'
        },
        {
            key: 'moto',
            value: '摩托车'
        },
        {
            key: 'light_trailer',
            value: '轻型牵引挂车'
        }
    ]
    var carTypeMap = Tools.getMapfromArray(carTypeStore);


    var objStatus = function (obj, tips) {
        var arr = [];
        if (tips) {
            arr.push({
                key: '',
                value: tips
            })
        }
        Object.keys(obj).map(function (key, item) {
            var temp = {};
            temp.key = key;
            temp.value = obj[key];
            arr.push(temp)
        })
        return arr;
    }


    var breakWord = function (data) {
        return "<div style='width: 100px;word-wrap: break-word'>" + data + "</div>"
    }
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!order/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '用户的mucangId：',
                dataIndex: 'userId',
                xtype: 'text',
                maxlength: 64,
                placeholder: '用户的mucangId'
            },
            {
                header: '业务相关的id：',
                dataIndex: 'bizId',
                xtype: 'text',
                maxlength: 64,
                placeholder: '业务相关的id'
            },
            {
                header: '业务类型：',
                dataIndex: 'bizType',
                xtype: 'text',
                maxlength: 32,
                placeholder: '业务类型'
            },
            {
                header: '设备Id：',
                dataIndex: 'deviceId',
                xtype: 'text',
                maxlength: 64,
                placeholder: '设备Id'
            },
            {
                header: '平台：',
                dataIndex: 'platform',
                xtype: 'text',
                maxlength: 16,
                placeholder: '平台'
            },
            {
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: 'text',
                maxlength: 16,
                placeholder: '城市编码'
            },
            {
                header: '城市名称：',
                dataIndex: 'cityName',
                xtype: 'text',
                maxlength: 64,
                placeholder: '城市名称'
            },
            {
                header: '订单号：',
                dataIndex: 'orderNumber',
                xtype: 'text',
                maxlength: 128,
                placeholder: '订单号'
            },
            {
                header: '支付方式(1支付宝, 2微信, 3苹果)：',
                dataIndex: 'payType',
                xtype: 'text',
                placeholder: '支付方式(1支付宝, 2微信, 3苹果)'
            },
            {
                header: '支付账号：',
                dataIndex: 'payAccount',
                xtype: 'text',
                maxlength: 64,
                placeholder: '支付账号'
            },
            {
                header: '支付金额，单位分：',
                dataIndex: 'money',
                xtype: 'text',
                placeholder: '支付金额，单位分'
            },
            {
                header: '用户设备信息：',
                dataIndex: 'clientInfo',
                xtype: 'textarea',
                maxlength: 1024,
                placeholder: '用户设备信息'
            },
            {
                header: '支付状态，0支付中,1已支付：',
                dataIndex: 'status',
                xtype: 'text',
                placeholder: '支付状态，0支付中,1已支付'
            },
            {
                header: '订单的来源：',
                dataIndex: 'from',
                xtype: 'text',
                maxlength: 32,
                placeholder: '订单的来源'
            },
            {
                header: 'updateTime：',
                dataIndex: 'updateTime',
                xtype: 'date',
                placeholder: 'updateTime'
            }

            ]
        }).add();
    }

    var widgetTable = function (data, panel) {
        Table({
            columns: [{
                header: 'deviceId',
                dataIndex: 'deviceId'
            },
            {
                header: '品牌',
                dataIndex: 'manufacturer'
            },

            {
                header: '型号 ',
                dataIndex: 'device'
            },
            {
                header: '登录时间',
                dataIndex: 'visitTime',
                render: function (data, arrData, lineData, index) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss')
                }
            }
            ]
        }, {
            data: data
        }, panel, function (target, config, item, table) {
            target.item("download-data").remove()
        }).render()
    }
    
    var delDevice = function (table) {
        Table({
            title: '清除用户设备',
            width: 600,
            store: 'jiakao-misc!order/data/deleteDeviceList',
            success: function (obj, dialog, data) {
                Widgets.dialog.alert('清除成功！');
                dialog.close()
            },
            columns: [{
                header: '用户的mucangId：',
                dataIndex: 'userId',
                xtype: 'text',
                maxlength: 64,
                placeholder: '用户的mucangId'
            }],
        }).add();
    }

    var delDevice = function (table) {
        Table({
            title: '清除用户设备',
            width: 600,
            store: 'jiakao-misc!order/data/deleteDeviceList',
            success: function (obj, dialog, data) {
                Widgets.dialog.alert('清除成功！');
                dialog.close()
            },
            columns: [{
                header: '用户的mucangId：',
                dataIndex: 'userId',
                xtype: 'text',
                maxlength: 64,
                placeholder: '用户的mucangId'
            }],
        }).add();
    }

    var bindUser = function (lineData) {
        Table({
            title: '添加绑定',
            width: 600,
            store: 'jiakao-misc!order/data/bindUser',
            success: function (obj, dialog, data) {
                Widgets.dialog.alert('绑定成功！');
                dialog.close()
            },
            columns: [
                {
                    header: '订单号：',
                    dataIndex: 'orderNumber',
                    xtype: 'hidden',
                    value: lineData.orderNumber
                },
                {
                    header: 'userId',
                    dataIndex: 'userId',
                    xtype: 'text',
                },
                {
                    header: '用户手机号：',
                    dataIndex: 'userPhone',
                    xtype: 'text',
                },
            ],
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '通用的购买订单列表',
            title: '通用的购买订单列表',
            search: [
                {
                    dataIndex: 'userId',
                    xtype: 'text',
                    placeholder: 'mucangId'
                },
                {
                    xtype: 'text',
                    dataIndex: 'deviceId',
                    placeholder: '设备Id'
                },
                {
                    dataIndex: 'phone',
                    xtype: 'text',
                    placeholder: '手机号'
                },
                {
                    dataIndex: 'orderNumber',
                    xtype: 'text',
                    placeholder: '订单号'
                },
                {
                    dataIndex: 'platform',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择平台'
                    },
                    {
                        key: 'android',
                        value: 'android'
                    },
                    {
                        key: 'iphone',
                        value: 'iphone'
                    }
                    ]
                },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: objStatus(payStatus, '请选择支付状态')
                },
                {
                    dataIndex: 'bizType',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '选择类型'
                    }, {
                        key: '名师精品课',
                        value: '名师精品课'
                    }, {
                        key: '考场路线视频',
                        value: '考场路线视频'
                    }, {
                        key: '国际驾照快递',
                        value: '国际驾照快递'
                    }, {
                        key: '66学车节',
                        value: '66学车节'
                    }]
                },
                {
                    dataIndex: 'cityName',
                    xtype: 'text',
                    placeholder: '城市'
                },
                {
                    dataIndex: 'goodsCity',
                    xtype: 'text',
                    placeholder: '商品城市'
                },
                {
                    dataIndex: 'startTime',
                    xtype: 'date',
                    placeholder: '开始时间'
                },
                {
                    dataIndex: 'endTime',
                    xtype: 'date',
                    placeholder: '结束时间'
                }
            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },{
                    name: '添加',
                    class: 'primary',
                    click: add
                },{
                    name: '清除用户设备',
                    class: 'danger',
                    click: delDevice
                }],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    render: function (name, arr, index) {
                        if (arr[index].id) {
                            return '查看'
                        }
                    },
                    store: 'jiakao-misc!order/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户的mucangId：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '业务相关的id：',
                            dataIndex: 'bizId'
                        },
                        {
                            header: '业务类型：',
                            dataIndex: 'bizType'
                        },
                        {
                            header: '设备Id：',
                            dataIndex: 'deviceId'
                        },
                        {
                            header: '平台：',
                            dataIndex: 'platform'
                        },
                        {
                            header: '城市编码：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '城市名称：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber'
                        },
                        {
                            header: '支付方式(1支付宝, 2微信, 3苹果)：',
                            dataIndex: 'payType'
                        },
                        {
                            header: '支付账号：',
                            dataIndex: 'payAccount'
                        },
                        {
                            header: '支付金额，单位分：',
                            dataIndex: 'money'
                        },
                        {
                            header: '用户设备信息：',
                            dataIndex: 'clientInfo'
                        },
                        {
                            header: '支付状态，0支付中,1已支付：',
                            dataIndex: 'status'
                        },
                        {
                            header: '订单的来源：',
                            dataIndex: 'from'
                        },
                        {
                            header: '有效天数',
                            dataIndex: 'validDay'
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    render: function (name, arr, index) {
                        if (arr[index].id) {
                            return '编辑'
                        }
                    },
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!order/data/view',
                        save: 'jiakao-misc!order/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    },
                    {
                        header: '用户的mucangId：',
                        dataIndex: 'userId',
                        xtype: 'text',
                        maxlength: 64,
                        placeholder: '用户的mucangId'
                    },
                    {
                        header: '业务相关的id：',
                        dataIndex: 'bizId',
                        xtype: 'text',
                        maxlength: 64,
                        placeholder: '业务相关的id'
                    },
                    {
                        header: '业务类型：',
                        dataIndex: 'bizType',
                        xtype: 'text',
                        maxlength: 32,
                        placeholder: '业务类型'
                    },
                    {
                        header: '设备Id：',
                        dataIndex: 'deviceId',
                        xtype: 'text',
                        maxlength: 64,
                        placeholder: '设备Id'
                    },
                    {
                        header: '平台：',
                        dataIndex: 'platform',
                        xtype: 'text',
                        maxlength: 16,
                        placeholder: '平台'
                    },
                    {
                        header: '城市编码：',
                        dataIndex: 'cityCode',
                        xtype: 'text',
                        maxlength: 16,
                        placeholder: '城市编码'
                    },
                    {
                        header: '城市名称：',
                        dataIndex: 'cityName',
                        xtype: 'text',
                        maxlength: 64,
                        placeholder: '城市名称'
                    },
                    {
                        header: '订单号：',
                        dataIndex: 'orderNumber',
                        xtype: 'text',
                        maxlength: 128,
                        placeholder: '订单号'
                    },
                    {
                        header: '支付方式(1支付宝, 2微信, 3苹果)：',
                        dataIndex: 'payType',
                        xtype: 'text',
                        placeholder: '支付方式(1支付宝, 2微信, 3苹果)'
                    },
                    {
                        header: '支付账号：',
                        dataIndex: 'payAccount',
                        xtype: 'text',
                        maxlength: 64,
                        placeholder: '支付账号'
                    },
                    {
                        header: '支付金额，单位分：',
                        dataIndex: 'money',
                        xtype: 'text',
                        placeholder: '支付金额，单位分'
                    },
                    {
                        header: '用户设备信息：',
                        dataIndex: 'clientInfo',
                        xtype: 'textarea',
                        maxlength: 1024,
                        placeholder: '用户设备信息'
                    },
                    {
                        header: '支付状态，0支付中,1已支付：',
                        dataIndex: 'status',
                        xtype: 'text',
                        placeholder: '支付状态，0支付中,1已支付'
                    },
                    {
                        header: '订单的来源：',
                        dataIndex: 'from',
                        xtype: 'text',
                        maxlength: 32,
                        placeholder: '订单的来源'
                    },
                    {
                        header: 'updateTime：',
                        dataIndex: 'updateTime',
                        xtype: 'date',
                        placeholder: 'updateTime'
                    }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    render: function (name, arr, index) {
                        if (arr[index].id) {
                            return '删除'
                        }
                    },
                    store: 'jiakao-misc!order/data/delete'
                },
                {
                    name: '修改商品',
                    class: 'primary',
                    render: function (name, arr, index) {
                        if (arr[index].id) {
                            return '修改商品'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Table({
                            title: '添加',
                            width: 500,
                            store: 'jiakao-misc!order/data/updateBizId',
                            success: function (obj, dialog) {
                                dialog.close();
                                table.render();
                            },
                            columns: [{
                                dataIndex: 'id',
                                xtype: 'hidden',
                                value: lineData.id
                            }, {
                                header: '业务相关id',
                                dataIndex: 'bizId',
                                xtype: 'text',
                            }


                            ]
                        }).add();
                    }
                },
                {
                    name: '退款',
                    class: 'danger',
                    render: function (name, arr, index) {
                        if (arr[index].id && (arr[index].status == 1 || arr[index].status == 4)) {
                            return '退款';
                        }
                    },
                    click: function (table, lineDom, lineData) {
                        Widgets.dialog.confirm('确定要退款吗?', function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!order/data/refund?id=' + lineData.id]).save().done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                });
                            }

                        });
                    },
                },
                {
                    name: '查看登录设备',
                    class: 'info',
                    render: function (name, arr, index) {
                        if (arr[index].id) {
                            return '查看登录设备'
                        }
                    },
                    click: function (table, row, lineData) {
                        var dialogBody = '';
                        Store(['jiakao-misc!order/data/deviceList?userId=' + lineData.userId]).load([{
                            aliases: 'list'
                        }]).done(function (store, obj) {
                            Widgets.dialog.html('查看用户登录设备', {
                                width: 800,
                                // buttons: [
                                //     {
                                //         name: '清空用户登录设备',
                                //         xtype: 'primary',
                                //         click: function () {
                                //             Store(['jiakao-misc!goods-user/data/getDelDeviceId?id='+lineData.id]).load([{
                                //                 aliases: 'list'
                                //             }]).done(function (store) {
                                //                 widgetTable([],dialogBody)
                                //             })
                                //         }
                                //     }
                                // ]
                            }).done(function (dialog) {
                                dialogBody = dialog.body;
                                widgetTable(obj.list.data, dialog.body)
                            })

                        }).fail(function (ret) {
                            Widgets.dialog.alert(ret.message);
                        })
                    }
                },
                {
                    name: '清除用户设备',
                    class: 'danger',
                    render: function (name, arr, index) {
                        if (arr[index].id) {
                            return '清除用户设备'
                        }
                    },
                    click: function (table, lineDom, lineData) {
                        Widgets.dialog.confirm('确定要清除用户设备吗?', function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!order/data/deleteDeviceList?userId=' + lineData.userId]).save().done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                });
                            }

                        });
                    },
                },
                {
                    name: '手动设置已支付',
                    class: 'primary',
                    render: function (name, arr, index) {
                        if (arr[index].id && arr[index].status == 0) {
                            return '手动设置已支付';
                        }
                    },
                    click: function (table, lineDom, lineData) {
                        Widgets.dialog.confirm('确定要设置已支付吗?', function (e, confirm) {
                            if(confirm){
                                Store(['jiakao-misc!order/data/manualSetPaid?orderNumber=' + lineData.orderNumber]).save().done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                });
                            }
                            
                        });
                    },
                },
                {
                    name: '绑定用户',
                    class: 'info',
                    render: function (text,tableData,index) {
                        if (tableData[index].id && !tableData[index].userId && tableData[index].status == 1){
                            return text
                        }
                        return ''
                    },
                    click: function (table, lineDom, lineData) {
                        bindUser(lineData);
                    },
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户的mucangId',
                    dataIndex: 'userId',
                    render: breakWord
                },
                {
                    header: '业务相关的id',
                    dataIndex: 'bizId',
                    render: function (data) {
                        if (data) {
                            return `<a>${data}</a>`
                        }
                    },
                    click: function (table, row, lineData) {
                        const bizId = lineData.bizId;
                        if (lineData.bizIdType === 'top_lesson_id') {
                            Store(['jiakao-misc!top-lesson-group/data/view?id=' + bizId]).load().done(({ data }) => {
                                console.log(data, 'data');
                                const lineData = data['top-lesson-group'].data.view.data
                                Table().view(lineData, {
                                    columns: [
                                        {
                                            header: '#',
                                            dataIndex: 'id'
                                        },
                                        {
                                            header: '科目：',
                                            dataIndex: 'kemu',
                                            render: function (data) {
                                                return Constants.kemuMap[data]
                                            }
                                        },
                                        {
                                            header: '车型',
                                            dataIndex: 'carType',
                                            render: function (data, arr, i) {
                                                let list = data.split(',')
                                                list = list.map(item => carTypeMap[item])
                                                return list.join(',')
                                            }
                                        },
                                        {
                                            header: '标题',
                                            dataIndex: 'title'
                                        },
                                        {
                                            header: '课程类型',
                                            dataIndex: 'type',
                                            render: function (data) {
                                                return data == 2 ? '直播' : '视频'
                                            }
                                        },
                                        {
                                            header: '状态',
                                            dataIndex: 'status',
                                            render: function (data) {
                                                return data == 2 ? '上线' : '下线'
                                            }
                                        },
                                        {
                                            header: '版本',
                                            dataIndex: 'editionName'
                                        },
                                        {
                                            header: '创建时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            },
                                            dataIndex: 'createTime'
                                        },
                                        {
                                            header: '创建人',
                                            dataIndex: 'createUserName'
                                        },
                                        {
                                            header: '修改时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            },
                                            dataIndex: 'updateTime'
                                        },
                                        {
                                            header: '修改人',
                                            dataIndex: 'updateUserName'
                                        }
                                    ]
                                })
                            })
                        } else if (lineData.bizIdType === "route_place_id") {
                            Store(['jiakao-misc!route-video-meta/data/view?id=' + bizId]).load().done(({ data }) => {
                                console.log(data, 'datadata');
                                const lineData = data['route-video-meta'].data.view.data
                                Table().view(lineData, {
                                    columns: [
                                        {
                                            header: '#',
                                            dataIndex: 'id'
                                        },
                                        {
                                            header: '考场名称：',
                                            dataIndex: 'name'
                                        },
                                        {
                                            header: '考场的城市：',
                                            dataIndex: 'cityName'
                                        },
                                        {
                                            header: '创建时间：',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            },
                                            dataIndex: 'createTime'
                                        },
                        
                                        {
                                            header: '创建人：',
                                            dataIndex: 'createUserName'
                                        },
                                        {
                                            header: '更新时间：',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            },
                                            dataIndex: 'updateTime'
                                        },
                                        {
                                            header: '更新人：',
                                            dataIndex: 'updateUserName'
                                        }
                                    ]
                                })
                            })
                        }
                    }
                },
                {
                    header: '业务ID的类型',
                    dataIndex: 'bizIdType'
                },
                {
                    header: '业务类型',
                    dataIndex: 'bizType'
                },
                {
                    header: '设备Id',
                    dataIndex: 'deviceId'
                },
                {
                    header: '平台',
                    dataIndex: 'platform'
                },
                {
                    header: '城市编码',
                    dataIndex: 'cityCode'
                },
                {
                    header: '城市名称',
                    dataIndex: 'cityName'
                },
                {
                    header: '订单号',
                    dataIndex: 'orderNumber',
                    render: breakWord
                },
                {
                    header: '商品城市',
                    dataIndex: 'goodsCity',
                },
                {
                    header: '支付方式',
                    dataIndex: 'payType',
                    render: function (data) {
                        return payType[data]
                    }
                },
                // {
                //     header: '支付账号',
                //     dataIndex: 'payAccount'
                // },
                {
                    header: '支付金额，单位分',
                    dataIndex: 'money'
                },
                {
                    header: '用户设备信息',
                    dataIndex: 'clientInfo',
                    render: function (data) {
                        if (data) {
                            return "<a>查看信息</a>"
                        }
                    },
                    click: function (table, row, lineData) {
                        if (lineData.clientInfo) {
                            // console.log(lineData)
                            var clientInfo = JSON.parse(lineData.clientInfo);
                            Widgets.dialog.html('题目列表', '<pre>' + Utils.format.json(JSON.stringify(clientInfo)) + '</pre>')
                        }
                    }
                },
                {
                    header: '支付状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return payStatus[data]

                    }
                },
                {
                    header: '订单的来源',
                    dataIndex: 'from'
                },
                {
                    header: '页面名称',
                    dataIndex: 'page'
                },
                {
                    header: '有效天数',
                    dataIndex: 'validDay'
                },
                {
                    header: '到期日期',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd')
                    },
                    dataIndex: 'expireTime'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!order/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
