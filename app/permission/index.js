/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var statusMap = {
        0: '支付中',
        1: '已支付',
        2: '退款中',
        3: '退款成功',
        4: '退款失败',
        5: '权限已使用'
    };

    var statusArr = [];

    for (var key in statusMap) {
        statusArr.push({ key,value:statusMap[key]})
    }

    

    var list = function(panel) {
        Table({
            description: '用户权限列表',
            title: '用户权限列表',
            search: [
                {
                    dataIndex: 'userId',
                    xtype: 'text',
                    placeholder: '请输入用户ID',
                    value:hashParams.mucangId? hashParams.mucangId:1
                },
            ],
            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!permission/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '用户id：',
                         dataIndex: 'userId'
                     },
                     {
                         header: '业务的类型：',
                         dataIndex: 'bizType'
                     },
                     {
                         header: '权限内容：',
                         dataIndex: 'permission'
                     },
                     {
                         header: '权限的类型：',
                         dataIndex: 'permissionType'
                     },
                     {
                         header: '订单号：',
                         dataIndex: 'orderNumber'
                     },
                     {
                         header: '对应的订单状态：',
                         dataIndex: 'status',
                         render: function (data) {
                             return statusMap[data]
                         }
                     },
                     {
                         header: '过期时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'expireTime'
                     },
                     {
                         header: '权限开始时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'validTime'
                     },
                     {
                         header: 'createTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserName：',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: 'updateTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                    

                    ]
                },
              
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!permission/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '用户id',
                         dataIndex: 'userId'
                     },
                     {
                         header: '业务的类型',
                         dataIndex: 'bizType'
                     },
                     {
                         header: '权限内容',
                         dataIndex: 'permission'
                     },
                     {
                         header: '权限的类型',
                         dataIndex: 'permissionType'
                     },
                     {
                         header: '订单号',
                         dataIndex: 'orderNumber'
                     },
                     {
                         header: '对应的订单状态',
                         dataIndex: 'status',
                         render: function (data) {
                             return statusMap[data]
                         }
                     },
                     {
                         header: '过期时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'expireTime'
                     },
                     {
                         header: '权限开始时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'validTime'
                     },
            ]
        }, [`jiakao-misc!permission/data/list?userId=${hashParams.mucangId? hashParams.mucangId:1}`], panel, null).render();
    }

    return {
        list: list
    }

});