/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!plugin/select-district/district3',
    'jiakao-misc!app/operation-config3/index',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, District3, OpConfig) {

    
    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

 
    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    function renderCodeFn(dom,data) {
        setTimeout(() => {
            const value = JSON.parse(data.value);

            dom.item('actionUrl').val(value.actionUrl);
            dom.item('msgActionUrl').val(value.msgActionUrl);
            dom.item('title').val(value.title);
            dom.item('highlight').val(value.highlight);
            Plugin('jiakao-misc!pinxuan-group', {
                dataIndex: 'itemList',
                target: dom.item('itemList-group').find('div[class=col-sm-8]'),
                value:value.itemList ? JSON.stringify(value.itemList) : JSON.stringify([{ title: '',subTitle:'', actionUrl: '',sort:'' }]) 
            }, function (plugin, value) {

            }).render();


         
           
        }, 200)
    }
 
    function isAllEmpty(obj) {
        let len = 0;

        for (var k in obj) {
            if (obj[k] == '') {
                len++;
            }
        }


        return len == Object.keys(obj).length;
    }



    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id;

        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: `jiakao-misc!operation-config/data/${isEdit ? 'update':'insert'}?bizType=naben_tab_publicity&code=naben_tab_publicity`,
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    let itemList = $('input[name=itemList]').val();
                    itemList = itemList && JSON.parse(itemList);
                    
                    itemList = isAllEmpty(itemList[0]) ? null : itemList;
                    
                    if (itemList !== null && itemList.length !== 3) {
                        Widgets.dialog.alert('子项列表请上传三条数据')
                        return
                    }


                    const title = $(form).find('#title').val();
                    const highlight = $(form).find('#highlight').val();
                    const actionUrl = $(form).find('#actionUrl').val();
                    const msgActionUrl = $(form).find('#msgActionUrl').val();
                    const mainImage =  $('input[name="mainImage"]').val();
                    const value = JSON.stringify({
                        title,
                        highlight,
                        actionUrl,
                        mainImage,
                        msgActionUrl,
                        itemList,
                    })

                    return {
                        value
                    };
                },
            },
            renderAfter: function (table, dom, data) {
                if (data.data) {
                    renderCodeFn(dom,data.data)
                }
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    dataIndex: 'code',
                    xtype: 'hidden',
                    value: 'naben_tab_publicity'
                },
                {
                    header: '动态跳转链接:',
                    dataIndex: 'msgActionUrl',
                    xtype: 'text',
                    placeholder:'动态跳转链接'
                },
                {
                    header: '名称:',
                    dataIndex: 'name',
                    xtype: 'text',
                    check: 'required',
                    placeholder:'名称'
                },
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    check: 'required',
                    placeholder: '配置说明'
                },
                {
                    header: '标题:',
                    dataIndex: 'title',
                    xtype: 'text',
                    check: 'required',
                    placeholder:'标题'
                },
                {
                    header: '高亮部分:',
                    dataIndex: 'highlight',
                    xtype: 'text',
                    check: 'required',
                    placeholder:'高亮部分'
                },
                {
                    header: '主图：',
                    dataIndex: 'mainImage',
                    xtype: Plugin(
                        "jiakao-misc!upload",
                        {
                            dataIndex: "mainImage",
                            uploadIndex: "mainImage",
                            bucket: "exam-room",
                            valuePath:'value.mainImage',
                            isSingle: true,
                            placeholder: "主图",
                            url: "simple-upload3://upload/file.htm",
                        }, function (me, b, imageUrl, wh) {
                            
                        }
                    )
                },
                {
                    header: '主图跳转链接:',
                    dataIndex: 'actionUrl',
                    xtype: 'text',
                    check: 'required',
                    placeholder:'主图跳转链接'
                },
             
                {
                    header: '子项列表:',
                    dataIndex: 'itemList',
                    xtype: Plugin('jiakao-misc!pinxuan-group', {
                        dataIndex: 'itemList'
                    }, function (plugin, value) {
        
                    })
                },
              
            ]
        }

        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }
    }

    var list = function (panel) {
        Table({
            description: '品宣卡片配置',
            title: '品宣卡片配置',
            search: [],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class:'warning',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!operation-config/data/delete'
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        OpConfig.editPersonas(table, lineData)
                    },
                },
                {
                    class: 'danger',
                    render: function (name, arr, index) {
                        const status = arr[index].status
                        if (status == 0) {
                            return '测试发布';
                        } else if (status == 1) {
                            return '发布';
                        }else if (status == 2) {
                            return '下线';
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log(lineData, 'lineData');
                        const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1;
                        console.log(status, 'status');
                        let title = lineData.status == 1 ? '确定发布吗?' :   lineData.status == 2 ? '确定下线吗?' : '确定测试发布吗?'
                        Widgets.dialog.confirm(title, function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!operation-config/data/update']).save([{
                                    params: {
                                        id: lineData.id,
                                        status
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
            ],
            columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '名称',
                        dataIndex: 'name'
                    },
                    {
                        header: '配置说明',
                        dataIndex: 'remark'
                    },
                    {
                        header: '配置key',
                        dataIndex: 'code'
                },
                       
                    {
                        header: '配置内容',
                        dataIndex: 'value',
                        render: function () {
                            return `<a>点击查看</a>`
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('配置内容', {}).done(function (dialog) {
                                var data = lineData.value && JSON.stringify(JSON.parse(lineData.value), null, 4)
                                $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data, arr, i) {
                        return statusMap[data]
                    }
                },
                    {
                        header: '创建人',
                        dataIndex: 'createUserName'
                    },
                    {
                        header: '创建时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: '修改人',
                        dataIndex: 'updateUserName'
                    },
                    {
                        header: '修改时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    }

            ]
        }, ['jiakao-misc!operation-config/data/list?bizType=naben_tab_publicity'], panel, null).render();
    }

    return {
        list: list
    }

});