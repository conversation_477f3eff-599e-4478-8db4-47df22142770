/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var storeBaseUrl = 'jiakao-misc!crawler-answer/data/';
    var stateMap = {
        0: '未审核',
        1: '审核通过',
        2: '审核不通过'

    }

    var list = function (panel, questionId, question) {
        Table({
            title: '提问：' + question,
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '批量通过',
                        class: 'primary',
                        click: function (table, dom, config, selectedArr) {
                            //  console.log(selectedArr.join(','));
                            if (selectedArr.length > 0) {


                                Widgets.dialog.confirm('确认处理吗？', function (ev, status) {
                                    if (status) {
                                        Store([storeBaseUrl + 'audit?ids=' + selectedArr.join(',')+'&pass=true'], [{
                                            aliases: 'list'
                                        }]).save().done(function (store) {}).fail(function () {});
                                    } else {}
                                })
                            } else {
                                Widgets.dialog.alert('请选择数据')
                            }

                        },
                    }, 
                    {
                        name: '审核结束',
                        class: 'warning',
                        click: function (obj) {
                            Widgets.dialog.confirm('确认框', function (ev, status) {
                                if (status) {
                                    Store(['jiakao-misc!crawler-question/data/publish?id=' + questionId], [{
                                        aliases: 'list',
                                    }]).load().done(function (store) {
                                        Widgets.dialog.alert('审核结束成功')
                                    }).fail(function (res) {
                                        Widgets.dialog.alert(res.message)
                                    });
                                } else {}
                            })
                        }
                    },

                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            selector: {
                dataIndex: 'id',
            },
            operations: [{
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    render: function (name, arr, i) {
                        return arr[i].state == 0 ? name : ''
                    },
                    renderAfter: function (table, dom) {
                        dom.find('#answer').css('height', '300px')
                    },
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: storeBaseUrl + 'view',
                        save: storeBaseUrl + 'update'
                    },

                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },

                        {
                            header: '采集问题',
                            dataIndex: 'question',
                            xtype: 'text',

                        },
                        {
                            header: '采集答案',
                            dataIndex: 'answer',
                            xtype: 'textarea',
                        },
                    ]
                },
                {
                    name: '审核通过',
                    class: 'success',
                    click: function (table, rows, lineData) {
                        Widgets.dialog.confirm('确认审核通过吗？', function (ev, status) {
                            if (status) {
                                Store([storeBaseUrl + 'audit?id=' + lineData.id + '&pass=true'], [{
                                    aliases: 'list'
                                }]).save().done(function (store) {
                                    table.render();
                                }).fail(function () {});
                            } else {}
                        })


                    },
                    render: function (name, arr, i) {
                        return arr[i].state == 0 ? name : ''
                    }
                },

                {
                    name: '审核不通过',
                    class: 'danger',
                    click: function (table, rows, lineData) {
                        Widgets.dialog.confirm('确认审核不通过吗？', function (ev, status) {
                            if (status) {
                                Store([storeBaseUrl + 'audit?id=' + lineData.id + '&pass=false'], [{
                                    aliases: 'list'
                                }]).save().done(function (store) {
                                    table.render();
                                }).fail(function () {});
                            } else {}
                        })

                    },
                    render: function (name, arr, i) {
                        return arr[i].state == 0 ? name : ''
                    }

                },

            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '来源',
                    dataIndex: 'source'
                },
                {
                    header: '采集问题',
                    dataIndex: 'question',
                    render: function (data) {
                        return '<div style="width:300px; word-break:break-all;">' + data + '</div>'
                    },
                },
                {
                    header: '采集答案',
                    dataIndex: 'answer',
                    render: function (data) {
                        return '<pre>' + data + '</pre>'
                    },

                },
                {
                    header: '采集地址',
                    dataIndex: 'sourceUrl',
                    render: function (data) {
                        return '<div style="width:500px; word-break:break-all;">' + data + '</div>'
                    },
                },
                {
                    header: '审核状态',
                    dataIndex: 'state',
                    render: function (data) {
                        return stateMap[data]
                    }
                },
                {
                    header: '采集时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },



            ]
        }, [storeBaseUrl + 'list?questionId=' + questionId], panel, null).render();
    }

    return {
        list: list
    }

});