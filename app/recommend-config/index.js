/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tiku',
    'simple!core/plugin',
], function (Template, Table, Utils, Widgets, Store, Form,Constants,TIKU,Plugin) {

    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }


    var bizDataTypeMap = {
        'lesson': '课程',
        'live': '直播间',
        'image_text': '自定义图文'
    };


    var bizDataTypeArr = []
    for (const key in bizDataTypeMap) {
        bizDataTypeArr.push({
            key,
            value:bizDataTypeMap[key]
        })
    }


    var recommendTypeMap = {
        "practice_result": "练习结果页",
        "exam_result": "考试结果页",
        "practice_page": "做题页第一条推荐数据"
    }

    var recommendTypeArr = []
    for (const key in recommendTypeMap) {
        recommendTypeArr.push({
            key,
            value:recommendTypeMap[key]
        })
    }


    Constants.kemuStore = Constants.kemuStore.map(item => ({
        key: item.key + '',
        value:item.value
    }))


    function renderCodeFn(dom, data) {
        function typeShow(value) {
            if (value == 'lesson') {
                dom.item('title-group').hide();
                dom.item('content-group').hide();
                dom.item('btnText-group').hide();
                dom.item('url-group').hide();

                dom.item('lessonId-group').show();
            } else if (value == 'live') {
                dom.item('title-group').hide();
                dom.item('content-group').hide();
                dom.item('btnText-group').hide();
                dom.item('url-group').hide();

                dom.item('lessonId-group').hide();
            } else if (value == 'image_text') {
                dom.item('title-group').show();
                dom.item('content-group').show();
                dom.item('btnText-group').show();
                dom.item('url-group').show();

                dom.item('lessonId-group').hide();
            }
        }

        if (data) {
            setTimeout(() => {
                const value = data.data.bizData && JSON.parse(data.data.bizData);
                dom.item('title').val(value.title);
                dom.item('content').val(value.content);
                dom.item('btnText').val(value.btnText);
                dom.item('url').val(value.url);
                dom.item('lessonId').val(value.lessonId);
                var bizDataType = data.data.bizDataType;
                typeShow(bizDataType)


                var rule = data.data.rule && JSON.parse(data.data.rule);
                dom.item('permissionCodes').val(rule && rule.permissionCodes);
            }, 200)
        }
        dom.item('title-group').hide();
        dom.item('content-group').hide();
        dom.item('btnText-group').hide();
        dom.item('url-group').hide();
        dom.item('lessonId-group').hide();


        dom.item('bizDataType').on('change', function(){
            let value = $(this).val();
            typeShow(value)
        })
    }

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!recommend-config/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var title = $(form).find('#title').val();
                    var content = $(form).find('#content').val();
                    var btnText = $(form).find('#btnText').val();
                    var url = $(form).find('#url').val();
                    var lessonId = $(form).find('#lessonId').val();

                    var bizDataType = $(form).find('#bizDataType').val();

                    var bizData;
                    if (bizDataType == 'lesson') {
                       
                        bizData = JSON.stringify({
                            lessonId
                        })
                    } else if (bizDataType == 'live') {
                        bizData = ''
                    } else if (bizDataType == 'image_text') {
                        bizData = JSON.stringify({
                            title,
                            content,
                            btnText,
                            url
                        })
                    }


 

                    // var examScoreRangeMin = $("input[name='examScoreRange']").next().children().children('input')[0].value;
                    // if (examScoreRangeMin == '') {
                    //     Widgets.dialog.alert('请输入考试分数区间最小值')
                    //     return
                    // }

                    // var practiceCountRangeMin = $("input[name='practiceCountRange']").next().children().children('input')[0].value;
                    // if (practiceCountRangeMin == '') {
                    //     Widgets.dialog.alert('请输入做题数的区间最小值')
                    //     return
                    // }



                    // rule
                    var examScoreRange = $("input[name='examScoreRange']").val();
                    var practiceCountRange = $("input[name='practiceCountRange']").val();
                    var permissionCodes = $(form).find('#permissionCodes').val().replaceAll('，',',')

                    console.log(examScoreRange,practiceCountRange,'examScoreRange');
                    var rule = JSON.stringify({
                        examScoreRange,permissionCodes,practiceCountRange
                    })
                    return {
                        bizData,
                        rule
                    };
                }  
            },
            renderAfter: function (config, dom, data) {
                renderCodeFn(dom);
            },
            columns: [
                {
                    header: '名称',
                    dataIndex: 'name',
                    xtype: 'text',
                    placeholder:'名称'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store:carTypeArr,
                    placeholder: '车型'
                },
                {
                   header: '科目：',
                   dataIndex: 'kemu',
                   xtype: 'checkbox',
                   store: Constants.kemuStore.slice(1),
                   placeholder: '科目'
               },
                {
                   header: '场景：',
                   dataIndex: 'sceneCode',
                   xtype: 'checkbox',
                   store: Constants.senceStore,
               },
               {
                   header: "访问模式：",
                   dataIndex: "patternCode",
                   xtype: "checkbox",
                   store: Constants.editionStore,
                   placeholder: "访问模式",
                },
               
                {
                    header: '考试分数区间：',
                    dataIndex: 'examScoreRange',
                    xtype: Plugin('jiakao-misc!section', {
                        dataIndex: 'examScoreRange'
                    }, function (plugin, value) {
        
                    })
                },
                {
                    header: '身份权益码列表',
                    dataIndex: 'permissionCodes',
                    xtype: 'text',
                    placeholder:'身份权益码列表,逗号隔开'
                },
                {
                    header: '做题数的区间：',
                    dataIndex: 'practiceCountRange',
                    xtype: Plugin('jiakao-misc!section', {
                        dataIndex: 'practiceCountRange'
                    }, function (plugin, value) {
        
                    })
                },



             {
                 header: '推荐的类型：',
                 dataIndex: 'recommendType',
                 xtype: 'select',
                 store: [{key:'',value:'请选择'},...recommendTypeArr],
             },
             {
                 header: '业务内容对应的类型：',
                 dataIndex: 'bizDataType',
                 xtype: 'select',
                 store:[{key:'',value:'请选择'},...bizDataTypeArr],
                 maxlength: 32,
                 check: 'required',
             },
           
                {
                    header: '标题',
                    dataIndex: 'title',
                    xtype: 'text',
                    placeholder:'标题'
                },
                {
                    header: '文案',
                    dataIndex: 'content',
                    xtype: 'text',
                    placeholder:'文案'
                },
                {
                    header: '按钮文案',
                    dataIndex: 'btnText',
                    xtype: 'text',
                    placeholder:'按钮文案'
                },
                {
                    header: '跳转链接',
                    dataIndex: 'url',
                    xtype: 'text',
                    placeholder:'跳转链接'
                },
                {
                    header: '课程ID',
                    dataIndex: 'lessonId',
                    xtype: 'text',
                    placeholder:'课程ID'
                },
                
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '推荐数据表列表',
            title: '推荐数据表列表',
            search: [
                {
                    header:'推荐的类型',
                    xtype: 'select',
                    dataIndex: 'recommendType',
                    store: [{key:'',value:'请选择'},...recommendTypeArr]
                }
            ],
            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!recommend-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '科目',
                            dataIndex: 'kemu',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.kemuMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        {
                            header: '场景',
                            dataIndex: 'sceneCode',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.senceMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        {
                            header: '访问模式',
                            dataIndex: 'patternCode',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.editionMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                     {
                         header: '推荐的类型：',
                         dataIndex: 'recommendType',
                         render: function (data) {
                             return recommendTypeMap[data]
                         }
                     },
                     {
                         header: '业务内容对应的类型：',
                         dataIndex: 'bizDataType',
                         render: function (data) {
                             return bizDataTypeMap[data]
                         }
                     },
                     {
                         header: '业务内容：',
                         dataIndex: 'bizData',
                     },
                     {
                         header: '匹配规则：',
                         dataIndex: 'rule'
                     },
                   
                     {
                         header: '创建人：',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '创建时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '更新人：',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '更新时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form) {
                            var title = $(form).find('#title').val();
                            var content = $(form).find('#content').val();
                            var btnText = $(form).find('#btnText').val();
                            var url = $(form).find('#url').val();
                            var lessonId = $(form).find('#lessonId').val();
        
                            var bizDataType = $(form).find('#bizDataType').val();
        
                            var bizData;
                            if (bizDataType == 'lesson') {
                                bizData = JSON.stringify({
                                    lessonId
                                })
                            } else if (bizDataType == 'live') {
                                bizData = ''
                            } else if (bizDataType == 'image_text') {
                                bizData = JSON.stringify({
                                    title,
                                    content,
                                    btnText,
                                    url
                                })
                            }
        

                            // var examScoreRangeMin = $("input[name='examScoreRange']").next().children().children('input')[0].value;
                            // if (examScoreRangeMin == '' ) {
                            //     Widgets.dialog.alert('请输入考试分数区间最小值')
                            //     return
                            // }
        
                            // var practiceCountRangeMin = $("input[name='practiceCountRange']").next().children().children('input')[0].value;
                            // if (practiceCountRangeMin == '') {
                            //     Widgets.dialog.alert('请输入做题数的区间最小值')
                            //     return
                            // }
        
        
        
                            // rule
                            var examScoreRange = $("input[name='examScoreRange']").val();
                            var practiceCountRange = $("input[name='practiceCountRange']").val();
                            var permissionCodes = $(form).find('#permissionCodes').val().replaceAll('，',',')
        
                            console.log(examScoreRange,practiceCountRange,'examScoreRange');
                            var rule = JSON.stringify({
                                examScoreRange,permissionCodes,practiceCountRange
                            })


                            return {
                                bizData,
                                rule
                            };
                        }  
                    },
                    renderAfter: function (config, dom, data) {
                        renderCodeFn(dom,data);
                    },
                    store: {
                        load: 'jiakao-misc!recommend-config/data/view',
                        save: 'jiakao-misc!recommend-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称',
                            dataIndex: 'name',
                            xtype: 'text',
                            placeholder:'名称'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType',
                            xtype: 'checkbox',
                            store:carTypeArr,
                            placeholder: '车型'
                        },
                        {
                           header: '科目：',
                           dataIndex: 'kemu',
                           xtype: 'checkbox',
                           store: Constants.kemuStore.slice(1),
                           placeholder: '科目'
                       },
                        {
                           header: '场景：',
                           dataIndex: 'sceneCode',
                           xtype: 'checkbox',
                           store: Constants.senceStore,
                       },
                       {
                           header: "访问模式：",
                           dataIndex: "patternCode",
                           xtype: "checkbox",
                           store: Constants.editionStore,
                           placeholder: "访问模式",
                        },
                       
                        {
                            header: '考试分数区间：',
                            dataIndex: 'examScoreRange',
                            xtype: Plugin('jiakao-misc!section', {
                                dataIndex: 'examScoreRange',
                                valuePath:'rule.examScoreRange'
                            }, function (plugin, value) {
                
                            })
                        },
                        {
                            header: '身份权益码列表',
                            dataIndex: 'permissionCodes',
                            xtype: 'text',
                            placeholder:'身份权益码列表,逗号隔开'
                        },
                        {
                            header: '做题数的区间：',
                            dataIndex: 'practiceCountRange',
                            xtype: Plugin('jiakao-misc!section', {
                                dataIndex: 'practiceCountRange',
                                valuePath:'rule.practiceCountRange'
                            }, function (plugin, value) {
                
                            })
                        },


             {
                 header: '推荐的类型：',
                 dataIndex: 'recommendType',
                 xtype: 'select',
                 store: [{key:'',value:'请选择'},...recommendTypeArr],
                 placeholder:'推荐的类型'
             },
             {
                 header: '业务内容对应的类型：',
                 dataIndex: 'bizDataType',
                 xtype: 'select',
                 store:[{key:'',value:'请选择'},...bizDataTypeArr],
                 maxlength: 32,
                 check: 'required',
                 placeholder: '业务内容对应的类型'
             },
            //  {
            //      header: '匹配规则：',
            //      dataIndex: 'rule',
            //      xtype: 'textarea',
            //      maxlength: 1024,
            //      placeholder: '匹配规则'
            //  },
             {
                header: '标题',
                dataIndex: 'title',
                xtype: 'text',
                placeholder:'标题'
            },
            {
                header: '文案',
                dataIndex: 'content',
                xtype: 'text',
                placeholder:'文案'
            },
            {
                header: '按钮文案',
                dataIndex: 'btnText',
                xtype: 'text',
                placeholder:'按钮文案'
            },
            {
                header: '跳转链接',
                dataIndex: 'url',
                xtype: 'text',
                placeholder:'跳转链接'
            },
            {
                header: '课程ID',
                dataIndex: 'lessonId',
                xtype: 'text',
                placeholder:'课程ID'
            }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!recommend-config/data/delete'
                },
                {
                    name: '投放策略',
                    class: 'success',
                    click: function (table, lineDom, lineData, dom, data, index) {
                        Plugin('simple!product-filter', {
                            store: 'jiakao-misc!recommend-config/data/filter?id=' + lineData.id
                        }).render().done(function () {
                            // Store(['sirius!goods-session/data/updateAdverts']).load();
                            table.render();
                        });
                    }
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name',
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                    {
                            header: '科目',
                            dataIndex: 'kemu',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.kemuMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        {
                            header: '场景',
                            dataIndex: 'sceneCode',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.senceMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                        {
                            header: '访问模式',
                            dataIndex: 'patternCode',
                            render: function (data, arr, i) {
                                if (data) {
                                    data = data.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(Constants.editionMap[data[i]])
                                    }
                                    return strArr.join(',');
                                }
                            }
                        },
                     {
                         header: '推荐的类型',
                         dataIndex: 'recommendType',
                         render: function (data) {
                             return recommendTypeMap[data]
                         }
                     },
                     {
                         header: '业务内容对应的类型',
                         dataIndex: 'bizDataType',
                         render: function (data) {
                            return bizDataTypeMap[data]
                         }
                     },
                     {
                         header: '业务内容',
                         dataIndex: 'bizData',
                         render: function (data) {
                            return `<a>点击查看</a>`  
                         },
                         click: function (table, row, lineData) {
                            Widgets.dialog.html('业务内容', {}).done(function (dialog) {
                                var data = lineData.bizData && JSON.stringify(JSON.parse(lineData.bizData), null, 4)
                                 $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                     },
                     {
                         header: '匹配规则',
                         dataIndex: 'rule',
                         render: function (data) {
                            return `<a>点击查看</a>`  
                         },
                         click: function (table, row, lineData) {
                             Widgets.dialog.html('匹配规则', {}).done(function (dialog) {
                                var data =lineData.rule && JSON.stringify(JSON.parse(lineData.rule), null, 4)
                                 $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                     },
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '更新人',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '更新时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     }

            ]
        }, ['jiakao-misc!recommend-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});