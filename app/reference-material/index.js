/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!reference-material/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '名称：',
                    dataIndex: 'materialName',
                    xtype: 'text',
                    maxlength: 100,
                    placeholder: '名称'
                },
                {
                    header: '排序：',
                    dataIndex: 'displayOrder',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '排序'
                },
                {
                    header: '来源：',
                    dataIndex: 'source',
                    xtype: 'text',
                    maxlength: 100,
                    placeholder: '来源'
                },
                {
                    header: '资料地址：',
                    dataIndex: 'materialUrl',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '资料地址'
                },
                {
                    header: '发布时间：',
                    dataIndex: 'publishTime',
                    xtype: 'date',
                    placeholder: '发布时间'
                },
                {
                    header: '实施时间：',
                    dataIndex: 'enforceTime',
                    xtype: 'date',
                    placeholder: '实施时间'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '参考资料',
            title: '参考资料',
            search: [
                {
                    dataIndex: 'materialName',
                    xtype: 'text',
                    placeholder: '请输入名称'
                },
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [

                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!reference-material/data/view',
                        save: 'jiakao-misc!reference-material/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'materialName',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '名称'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'displayOrder',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '排序'
                        },
                        {
                            header: '来源：',
                            dataIndex: 'source',
                            xtype: 'text',
                            check: 'required',
                            maxlength: 100,
                            placeholder: '来源'
                        },
                        {
                            header: '资料地址：',
                            dataIndex: 'materialUrl',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '资料地址'
                        },
                        {
                            header: '发布时间：',
                            dataIndex: 'publishTime',
                            xtype: 'date',
                            placeholder: '发布时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                        },
                        {
                            header: '实施时间：',
                            dataIndex: 'enforceTime',
                            xtype: 'date',
                            placeholder: '实施时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!reference-material/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'materialName'
                },
                {
                    header: '排序',
                    dataIndex: 'displayOrder'
                },
                {
                    header: '来源',
                    dataIndex: 'source'
                },
                {
                    header: '资料地址',
                    dataIndex: 'materialUrl',
                    render: function () {
                        return "<a>查看</a>"
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html(lineData.materialUrl);
                    }
                },
                {
                    header: '发布时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'publishTime'
                },
                {
                    header: '实施时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'enforceTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                // {
                //     header: 'deleted',
                //     render: function (data) {
                //         if (data) {
                //             return '是';
                //         } else {
                //             return '否';
                //         }
                //     },
                //     dataIndex: 'deleted'
                // }

            ]
        }, ['jiakao-misc!reference-material/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});