/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!related-function/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '名称：',
                    dataIndex: 'functionName',
                    xtype: 'text',
                    maxlength: 100,
                    placeholder: '名称'
                },
                {
                    header: '排序：',
                    dataIndex: 'displayOrder',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '排序'
                },
                {
                    header: '跳转地址：',
                    dataIndex: 'functionUrl',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '跳转地址'
                },
                
                

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '相关功能列表',
            title: '相关功能列表',
            search: [
                {
                    dataIndex: 'functionName',
                    xtype: 'text',
                    placeholder: '请输入名称'
                },
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [

                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!related-function/data/view',
                        save: 'jiakao-misc!related-function/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'functionName',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '名称'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'displayOrder',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '排序'
                        },
                        {
                            header: '跳转地址：',
                            dataIndex: 'functionUrl',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '跳转地址：'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!related-function/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'functionName'
                },
                {
                    header: '排序',
                    dataIndex: 'displayOrder'
                },
                {
                    header: '跳转地址',
                    dataIndex: 'functionUrl',
                    render: function () {
                        return "<a>查看</a>"
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html(lineData.functionUrl);
                    }
                },
                {
                    header: '创建人名称',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                // {
                //     header: 'deleted',
                //     render: function (data) {
                //         if (data) {
                //             return '是';
                //         } else {
                //             return '否';
                //         }
                //     },
                //     dataIndex: 'deleted'
                // }

            ]
        }, ['jiakao-misc!related-function/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});