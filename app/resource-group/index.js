/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!resource-group/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: 'key：',
                    dataIndex: 'code',
                    xtype: 'text',
                    maxlength: 16,
                    check: 'required',
                    placeholder: 'key'
                },
                {
                    header: '名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 16,
                    check: 'required',
                    placeholder: '名称'
                },
                {
                    header: '备注：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '备注'
                }
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '资源组管理表列表',
            title: '资源组管理表列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!resource-group/data/view',
                        save: 'jiakao-misc!resource-group/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 16,
                 check: 'required',
                 placeholder: '名称'
             },
             {
                 header: '编码code：',
                 dataIndex: 'code',
                 xtype: 'text',
                 maxlength: 16,
                 check: 'required',
                 placeholder: '编码code'
             },
             {
                 header: '备注：',
                 dataIndex: 'remark',
                 xtype: 'text',
                 maxlength: 128,
                 placeholder: '备注'
             },
                    ]
                },
                {
                    name: "内容管理",
                    class: 'info',
                    click: function (table, row, lineData) {
                        var nameStr = '资源详情管理';
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('resource-item-' + lineData.code);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'resource-item-' + lineData.code,
                                    name: nameStr + '-' + lineData.code
                                })
                            }

                            require(['jiakao-misc!app/resource-item/index'], function (Item) {
                                Item.list(nPanel, null, lineData.code)
                            })
                        });
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!resource-group/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'key',
                    dataIndex: 'code'
                },
                     {
                         header: '名称',
                         dataIndex: 'name'
                     },
                   
                     {
                         header: '备注',
                         dataIndex: 'remark'
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                  
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '更新时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                   
                     {
                         header: '更新人',
                         dataIndex: 'updateUserName'
                     },

            ]
        }, ['jiakao-misc!resource-group/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});