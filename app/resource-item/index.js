/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
'simple!core/plugin',
], function (Template, Table, Utils, Widgets, Store, Form,Plugin) {

    var retTypeMap = {
        image: '图片',
        richText: '富文本',
        text: '字符串',
        url: '链接'
    };

    
    var retTypeArr = [];

    for (const key in retTypeMap) {
        retTypeArr.push({
            key,
            value:retTypeMap[key]
        })
    }

    function init(dom,data) {
        if (data) {
            setTimeout(() => {
                const resType = data.data.resType;
                const resValue = data.data.resValue && JSON.parse(data.data.resValue);
                if (resType == 'image') {
                    dom.item('image-group').show();
                    dom.item('richText-group').hide();
                    dom.item('content-group').hide();
                    dom.item('url-group').hide();
                } else if (resType == 'richText') {
                    dom.item('image-group').hide();
                    dom.item('richText-group').show();
                    dom.item('content-group').hide();
                    dom.item('url-group').hide();



                } else if (resType == 'text') {
                    dom.item('image-group').hide();
                    dom.item('richText-group').hide();
                    dom.item('content-group').show();
                    dom.item('url-group').hide();
                    $('input[name="content"]').val(resValue?.content);

                } else if (resType == 'url') {
                    dom.item('image-group').hide();
                    dom.item('richText-group').hide();
                    dom.item('content-group').hide();
                    dom.item('url-group').show();
                    console.log(resValue,'resValue.url');
                    $('input[name="url"]').val(resValue?.url);
                }
            }, 200)
        }
        dom.item('image-group').hide();
        dom.item('richText-group').hide();
        dom.item('content-group').hide();
        dom.item('url-group').hide();


        dom.item('resType').on('change', function(){
            let value = $(this).val();
            if (value == 'image') {
                dom.item('image-group').show();
                dom.item('richText-group').hide();
                dom.item('content-group').hide();
                dom.item('url-group').hide();
                
            } else if (value == 'richText') {
                dom.item('image-group').hide();
                dom.item('richText-group').show();
                dom.item('content-group').hide();
                dom.item('url-group').hide();
            } else if (value == 'text') {
                dom.item('image-group').hide();
                dom.item('richText-group').hide();
                dom.item('content-group').show();
                dom.item('url-group').hide();
             
            } else if (value == 'url') {
                dom.item('image-group').hide();
                dom.item('richText-group').hide();
                dom.item('content-group').hide();
                dom.item('url-group').show();
             
            }
        })
    }

    var addEdit = function (table, groupCode = '', lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: lineData ?  '编辑' : '添加',
            width: 1000,
            store: `jiakao-misc!resource-item/data/${isEdit ? 'update' : 'insert'}?groupCode=${groupCode}`,
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    // console.log(arguments,'1121');
                    var image = $('input[name="image"]').val();
                    var content = $('input[name="content"]').val();
                    var url = $('input[name="url"]').val();
                    var richText = $(form).find('textarea')[0].value;

                    let retData = {
                        image,
                        richText,
                        content,
                        url
                    }

                    for (const key in retData) {
                        if (!retData[key]) {
                            delete retData[key]
                        }
                    }

                    const resValue = JSON.stringify(
                        retData
                    )

                    return {
                        resValue
                    }
                }
            },
            renderAfter: function (table,dom,data) {
                isEdit ?  init(dom,data) :init(dom)
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: 'key：',
                    dataIndex: 'code',
                    xtype: 'text',
                    maxlength: 16,
                    check: 'required',
                    placeholder: 'key'
                },
                {
                    header: '分组编码:',
                    dataIndex: 'groupCode',
                    xtype: 'text',
                    value:groupCode,
                    placeholder: '分组编码'
                },
             {
                 header: '名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 16,
                 check: 'required',
                 placeholder: '名称'
             },
             {
                header: '类型：',
                dataIndex: 'resType',
                xtype: 'select',
                store:[{key:'',value:'请选择'},...retTypeArr],
                check: 'required',
                placeholder: '类型'
                },
                
                {
                    header: '图片地址：',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "exam-room",
                        isSingle: true,
                        valuePath:'resValue.image',
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                    
                },


                {
                    header: '富文本：',
                    dataIndex: 'richText',
                    xtype: 'richtext',
                    value:isEdit ?  (JSON.parse(lineData.resValue)).richText : '',
                    placeholder: '富文本'
                },

                {
                    header: '字符串：',
                    dataIndex: 'content',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '字符串'
                },
                {
                    header: '链接地址：',
                    dataIndex: 'url',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '链接地址'
                },
             {
                 header: '排序：',
                 dataIndex: 'sort',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '排序'
             },
            ]
        }
        console.log(lineData,'lineData');
        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }
    }
    var list = function (panel, tab, groupCode = '') {
        Table({
            description: '资源子项目管理表列表',
            title: '资源子项目管理表列表',
            search: [
                {
                    header: '分组编码',
                    dataIndex: 'groupCode',
                    xtype: 'text',
                    placeholder:'分组编码'
                }
            ],
            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) { 
                            addEdit(table,groupCode)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    click: function (table, dom, lineData) {
                        console.log(12515);
                        Store(['jiakao-misc!resource-item/data/view?id=' + lineData.id]).load().done(data => { 
                            console.log(data);
                            addEdit(table, lineData.groupCode ,data.data['resource-item'].data.view.data)
                        })
                    },
                     renderAfter: function (table,dom,data) {
                        init(dom,data)
                    },
                    // form: {
                    //     submitHandler: function (form) {
                    //         var image = $('input[name="image"]').val();
                    //         var content = $('input[name="content"]').val();
                    //         var url = $('input[name="url"]').val();
                    //         var richText = $(form).find('textarea')[0].value;
        
                    //         let retData = {
                    //             image,
                    //             richText,
                    //             content,
                    //             url
                    //         }
        
                    //         for (const key in retData) {
                    //             if (!retData[key]) {
                    //                 delete retData[key]
                    //             }
                    //         }
        
                    //         const resValue = JSON.stringify(
                    //             retData
                    //         )
        
                    //         return {
                    //             resValue
                    //         }
                    //     }
                    // },
                   
                    // success: function(obj, dialog, e) {
                    //     dialog.close();
                    //     obj.render();
                    // },
                    // store: {
                    //     load: 'jiakao-misc!resource-item/data/view',
                    //     save: 'jiakao-misc!resource-item/data/update'
                    // },
                    // columns: [
                    //     {
                    //         dataIndex: 'id',
                    //         xtype: 'hidden'
                    //     },
                    //     {
                    //         header: 'key：',
                    //         dataIndex: 'code',
                    //         xtype: 'text',
                    //         maxlength: 16,
                    //         check: 'required',
                    //         placeholder: 'key'
                    //     },
                    // {
                    //     header: '名称：',
                    //     dataIndex: 'name',
                    //     xtype: 'text',
                    //     maxlength: 16,
                    //     check: 'required',
                    //     placeholder: '名称'
                    // },
                 
                    // {
                    //     header: '分组编码：',
                    //     dataIndex: 'groupCode',
                    //     xtype: 'hidden',
                    // },
                    
                    // {
                    //     header: '类型：',
                    //     dataIndex: 'resType',
                    //     xtype: 'select',
                    //     store:[{key:'',value:'请选择'},...retTypeArr],
                    //     check: 'required',
                    //     placeholder: '类型'
                    //     },

                    //     {
                    //         header: '图标地址:',
                    //         dataIndex: 'image',
                    //         xtype: Plugin('jiakao-misc!upload', {
                    //             dataIndex: 'image',
                    //             uploadIndex: 'image',
                    //             bucket: "jiakao-web",
                    //             isSingle: true,
                    //             valuePath:'resValue.image',
                    //             placeholder: '请选择上传文件',
                    //             url: 'simple-upload3://upload/file.htm'
                    //         }, function () {
                    //             console.log(arguments)
                    //         })
                    //     },
        
        
                    //     {
                    //         header: '富文本：',
                    //         dataIndex: 'richText',
                    //         xtype: 'richtext',
                    //         value:1124,
                    //         placeholder: '富文本'
                    //     },
        
                    //     {
                    //         header: '字符串：',
                    //         dataIndex: 'content',
                    //         xtype: 'text',
                    //         maxlength: 16,
                    //         placeholder: '字符串'
                    //     },
                    //     {
                    //         header: '链接地址：',
                    //         dataIndex: 'url',
                    //         xtype: 'text',
                    //         maxlength: 16,
                    //         placeholder: '链接地址'
                    //     },
                    
                    
                    //     {
                    //         header: '排序：',
                    //         dataIndex: 'sort',
                    //         xtype: 'text',
                    //         check: 'required',
                    //         placeholder: '排序'
                    //     },]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!resource-item/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'key',
                    dataIndex: 'code'
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '分组编码',
                    dataIndex: 'groupCode'
                },
                {
                    header: '备注',
                    dataIndex: 'remark'
                },
                {
                    header: '排序',
                    dataIndex: 'sort'
                },
                {
                    header: '类型',
                    dataIndex: 'resType'
                },
                {
                    header: '资源内容',
                    dataIndex: 'resValue',
                    render: function () {
                        return '<a>查看详情</a>';
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('资源内容', {}).done(function (dialog) {
                            var data = lineData.resValue && JSON.parse(lineData.resValue);
                            data = data.image ? ('<img src="' + data.image + '" />') : (data.richText || data.content || data.url);
                            $(dialog.body).html('<pre style="max-height: 600px; overflow: auto">' + data + '</pre>')
                        });
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
            
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!resource-item/data/list?groupCode=' + groupCode], panel, null).render();
    }

    return {
        list: list
    }
});