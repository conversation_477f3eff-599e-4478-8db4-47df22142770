/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
*/

"use strict";

define(['simple!core/utils'], function (Utils) {
    var redirects;

    function loadRedirects(callback) {
        if (redirects) {
            callback();
        } else {
            var redirectUrl = `${window.j.root['jiakao-misc']}/redirect.txt`;
            require(['simple_text!' + redirectUrl], function (redirect) {
                redirects = redirect.split('\n');
                callback();
            });
        }
    }

    function getRedirect(name, callback) {
        loadRedirects(function () {
            if (redirects.indexOf(name) !== -1) {
                name = name.replace(/^app\//, 'dist/');
            }
            callback(name);
        });
    }



    return {
        load: function (name, require, load, config) {
            name = name.replace(/\.vue(\.(ts|js))?$/, '');
            getRedirect(name, function (name) {
                require([Utils.require.rewrite(name, window.j.root['jiakao-misc'])], function () {
                    load.apply(this, arguments);
                });
            });
                
        }
    }  
});