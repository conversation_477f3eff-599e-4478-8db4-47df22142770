/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!route-coach/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '教练名称：',
                    dataIndex: 'coachName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '教练名称'
                },
                {
                    header: '教练所在城市：',
                    xtype: Plugin('jiakao-misc!select-district2', {
                        name: 'cityCode',
                        dataIndex: 'cityCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    header: '教练头像：',
                    dataIndex: 'avatar',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'avatar',
                        uploadIndex: 'avatar',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                },
                {
                    header: '驾校名称：',
                    dataIndex: 'jiaxiaoName',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '驾校名称'
                },
                {
                    header: '教练推荐语：',
                    dataIndex: 'description',
                    xtype: 'textarea',
                    maxlength: 512,
                    placeholder: '教练推荐语'
                },
                {
                    header: '推荐使用人数：',
                    dataIndex: 'referrals',
                    xtype: 'text',
                    placeholder: '推荐使用人数'
                }
            ]
        }).add();
    }
    var addCity = function (table) {
        Table({
            title: '添加',
            width: 600,
            store: 'jiakao-misc!route-coach/data/addCity',
            success: function (obj, dialog, arr, data) {
                dialog.close();
                // getCityConfig()
            },
            columns: [{
                header: '考场的城市：',
                xtype: Plugin('jiakao-misc!select-district1', {
                    name: 'cityCode',
                    dataIndex: 'cityCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }]
                    }
                }, function (plugin, code) {

                })
            }

            ]
        }).add();
    }
    var delCity = function (table) {
        Table({
            title: '添加',
            width: 600,
            store: 'jiakao-misc!route-coach/data/delCity',
            success: function (obj, dialog, arr, data) {
                dialog.close();
                getCityConfig()
            },
            columns: [{
                header: '考场的城市：',
                xtype: Plugin('jiakao-misc!select-district1', {
                    name: 'cityCode',
                    hideArea: true,
                    dataIndex: 'cityCode',
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                    }
                }, function (plugin, code) {

                }),
            }

            ]
        }).add();
    }

    var getCityConfig = function () {
        Widgets.dialog.html('已选城市').done(function (dialog) {
            Table({
                description: '',
                title: '',
                buttons: {
                    top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                        {
                            name: '添加城市',
                            class: 'primary',
                            click: addCity
                        }

                    ],
                    bottom: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }]
                },
                columns: [{
                    header: '城市名称',
                    dataIndex: 'name'
                }],
                operations: [{
                    name: '上线',
                    class: 'success',
                    render: function (name, arr, index) {
                        console.log(arguments)
                        if (arr[index].value === 'true') {
                            return '';
                        }
                        return name;
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确定上线吗？', function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!route-video-meta/data/updateCity']).save([{
                                    params: {
                                        id: lineData.id,
                                        value: 'true'
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
                    {
                        name: '下线',
                        class: 'warning',
                        render: function (name, arr, index) {
                            if (arr[index].value === 'false') {
                                return '';
                            }
                            return name;
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.confirm('确定上线吗？', function (e, confirm) {
                                if (confirm) {
                                    Store(['jiakao-misc!route-video-meta/data/updateCity']).save([{
                                        params: {
                                            id: lineData.id,
                                            value: 'false'
                                        }
                                    }]).done(function () {
                                        table.render();
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message);
                                    })
                                }
                            })
                        }
                    },
                    {
                        name: '删除',
                        class: 'danger',
                        click: function (table, row, lineData) {
                            Widgets.dialog.confirm('确定删除吗？', function (e, confirm) {
                                if (confirm) {
                                    Store(['jiakao-misc!route-video-meta/data/delCity']).save([{
                                        params: {
                                            cityCode: lineData.key
                                        }
                                    }]).done(function () {
                                        table.render();
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message);
                                    })
                                }
                            })
                        }
                    }
                ]
            }, ['jiakao-misc!route-coach/data/getCityConfig'], dialog.body, function () {
                $(dialog.body).item('download-data').remove()
            }).render();
        })
    }
    var list = function (panel, configData) {
        var cityCode = configData.cityCode || '';
        var cityName = configData.cityName || '请选择市'

        Table({
            description: '城市教练信息列表',
            title: '城市教练信息列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!route-coach/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '教练名称：',
                            dataIndex: 'coachName'
                        },
                        {
                            header: '教练所在城市：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '教练头像：',
                            dataIndex: 'avatar'
                        },
                        {
                            header: '驾校名称：',
                            dataIndex: 'jiaxiaoName'
                        },
                        {
                            header: '教练推荐语：',
                            dataIndex: 'description'
                        },
                        {
                            header: '推荐使用人数：',
                            dataIndex: 'referrals'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 700,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!route-coach/data/view',
                        save: 'jiakao-misc!route-coach/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '教练名称：',
                            dataIndex: 'coachName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '教练名称'
                        },
                        {
                            header: '教练所在城市：',
                            xtype: Plugin('jiakao-misc!select-district2', {
                                name: 'cityCode',
                                dataIndex: 'cityCode',
                                insert: {
                                    province: [{
                                        code: '',
                                        name: '请选择省份'
                                    }],
                                    city: [{
                                        code: '',
                                        name: '选择城市'
                                    }]
                                }
                            }, function (plugin, code) {
                                console.log('----->', code);
                            }),
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '教练头像：',
                            dataIndex: 'avatar',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'avatar',
                                uploadIndex: 'avatar',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            }),
                        },
                        {
                            header: '驾校名称：',
                            dataIndex: 'jiaxiaoName',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '驾校名称'
                        },
                        {
                            header: '教练推荐语：',
                            dataIndex: 'description',
                            xtype: 'textarea',
                            maxlength: 512,
                            placeholder: '教练推荐语'
                        },
                        {
                            header: '推荐使用人数：',
                            dataIndex: 'referrals',
                            xtype: 'text',
                            placeholder: '推荐使用人数'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!route-coach/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '教练名称',
                    dataIndex: 'coachName'
                },
                {
                    header: '教练所在城市',
                    dataIndex: 'cityName'
                },
                {
                    header: '教练头像',
                    dataIndex: 'avatar',
                    render: Simple.render.image()
                },
                {
                    header: '驾校名称',
                    dataIndex: 'jiaxiaoName'
                },
                {
                    header: '教练推荐语',
                    dataIndex: 'description'
                },
                {
                    header: '推荐使用人数',
                    dataIndex: 'referrals'
                }
            ]
        }, ['jiakao-misc!route-coach/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});