/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form','simple!core/plugin'], function(Template, Table, Utils, Widgets, Store, Form,Plugin) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!route-exam-voice/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '图标',
                    dataIndex: 'icon',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'icon',
                        uploadIndex: 'icon',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
             {
                 header: '图标名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 32,
                 check: 'required',
                 placeholder: '图标名称'
             },
             {
                 header: '内容：',
                 dataIndex: 'content',
                 xtype: 'textarea',
                 maxlength: 1024,
                 check: 'required',
                 placeholder: '内容'
             },
             {
                 header: '内容语音地址：',
                 dataIndex: 'voiceUrl',
                 xtype: 'textarea',
                 maxlength: 255,
                 placeholder: '内容语音地址'
             },
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '科三路考语音',
            title: '科三路考语音',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!route-exam-voice/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '图标：',
                         dataIndex: 'icon',
                         render: function (data) {
                            if (data) {
                                return `<img  src="${data}" style="width: 200px">`;
                            } else {
                                return ''
                            }
                         }
                     },
                     {
                         header: '图标名称：',
                         dataIndex: 'name'
                     },
                     {
                         header: '内容：',
                         dataIndex: 'content'
                     },
                     {
                         header: '内容语音地址：',
                         dataIndex: 'voiceUrl'
                     },
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!route-exam-voice/data/view',
                        save: 'jiakao-misc!route-exam-voice/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '图标',
                            dataIndex: 'icon',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'icon',
                                uploadIndex: 'icon',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '图标名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '图标名称'
                        },
                        {
                            header: '内容：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            maxlength: 1024,
                            check: 'required',
                            placeholder: '内容'
                        },
                        {
                            header: '内容语音地址：',
                            dataIndex: 'voiceUrl',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '内容语音地址'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!route-exam-voice/data/delete'
                },
                {
                    name: '文字转语音',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        Store(['jiakao-misc!route-exam-voice/data/textTospeech?id=' + lineData.id]).save().done(data => {
                            table.render();
                        }).fail();
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '图标',
                         dataIndex: 'icon',
                         render: function (data) {
                            if (data) {
                                return `<img  src="${data}" style="width: 200px">`;
                            } else {
                                return ''
                            }
                         }
                     },
                     {
                         header: '图标名称',
                         dataIndex: 'name'
                     },
                     {
                         header: '内容',
                         dataIndex: 'content'
                     },
                     {
                         header: '内容语音地址',
                         dataIndex: 'voiceUrl'
                     },

            ]
        }, ['jiakao-misc!route-exam-voice/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});