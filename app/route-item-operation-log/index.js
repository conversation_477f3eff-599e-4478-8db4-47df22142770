/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var typeMap = {
        1: '路线视频',
        2: '教练视频'
    };

    var opTypeMap = {
        1: '删除'
    };

    var list = function (panel) {
        Table({
            description: '删除记录',
            title: '删除记录',

            search: [
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district2', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    header: '操作人姓名',
                    xtype: 'text',
                    dataIndex: 'createUserName',
                    placeholder: '操作人姓名'
                },
                {
                    dataIndex: 'createTimeStart',
                    xtype: 'datetime',
                    placeholder: '开始时间'
                },
                {
                    dataIndex: 'createTimeEnd',
                    xtype: 'datetime',
                    placeholder: '结束时间'
                },
            ],

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            columns: [
                {
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '省',
                    dataIndex: 'provinceName'
                },
                {
                    header: '市',
                    dataIndex: 'cityName'
                },
                {
                    header: '区县',
                    dataIndex: 'areaName'
                },
                {
                    header: '考场ID',
                    dataIndex: 'metaId'
                },
                {
                    header: '考场名称',
                    dataIndex: 'metaName',
                },
                {
                    header: '路线id',
                    dataIndex: 'itemId'
                },
                {
                    header: '路线名称',
                    dataIndex: 'itemName'
                },
                {
                    header: '数据类型',
                    dataIndex: 'type',
                    render: function (data) {
                        return typeMap[data];
                    }
                },
                {
                    header: ' 操作类型',
                    dataIndex: 'operationType',
                    render: function (data) {
                        return opTypeMap[data];
                    }
                },
                {
                    header: ' 删除原因',
                    dataIndex: 'reason'
                },
                {
                    header: '删除时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '操作人',
                    dataIndex: 'createUserName'
                }

            ]
        }, ['jiakao-misc!route-item-operation-log/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});