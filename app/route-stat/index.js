/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    /** 平台;0:安卓,1:iOS,2:H5,其他或不传全部 */
    var platformStore = [{
        key: '',
        value: '全部设备'
    }, {
        key: 0,
        value: '安卓'
    }, {
        key: 1,
        value: 'iOS'
    }, {
        key: 2,
        value: 'H5'
    }]

    var list = function (panel, configData) {

        Table({
            description: '科三视频每日分成数据统计',
            title: '科三视频每日分成数据统计',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [
                {
                    dataIndex: 'coachId',
                    xtype: 'text',
                    placeholder: '教练ID'
                },
                {
                    dataIndex: 'routeMetaId',
                    xtype: 'text',
                    placeholder: '考场ID'
                },
                {
                    dataIndex: 'routeMetaName',
                    xtype: 'text',
                    placeholder: '考场名称'
                },
                {
                    dataIndex: 'cityName',
                    xtype: 'text',
                    placeholder: '城市名称'
                },
                {
                    dataIndex: 'platform',
                    xtype: 'select',
                    store: platformStore
                },
                {
                    dataIndex: 'startDate',
                    xtype: 'date',
                    placeholder: '创建订单时间开始'
                },
                {
                    dataIndex: 'endDate',
                    xtype: 'date',
                    placeholder: '创建订单时间结束'
                }
            ],
            columns: [
                {
                    header: '日期',
                    dataIndex: 'date'
                },
                {
                    header: '购买订单数',
                    dataIndex: 'videoOrderCount',
                },
                {
                    header: '购买订单数-科三速成包',
                    dataIndex:'videoOrderCountOfKe3Package'
                },
                {
                    header: '购买订单数-考场视频',
                    dataIndex:'videoOrderCountOfSingVideo'
                },
                {
                    header: '购买订单数-科三VIP',
                    dataIndex:'videoOrderCountOfKe3VIP'
                },
                {
                    header: '分成订单数',
                    dataIndex: 'orderCount'
                },
                {
                    header: '分成订单数-科三速成包',
                    dataIndex: 'orderCountOfKe3Package'
                },
                {
                    header: '分成订单数-考场视频',
                    dataIndex: 'orderCountOfSingVideo'
                }, 
                {
                    header: '分成订单数-科三VIP',
                    dataIndex: 'orderCountOfKe3VIP'
                }, 
                {
                    header: '分成金额（分）',
                    dataIndex: 'shareAmount'
                },
                {
                    header: '分成金额（分）-科三速成包',
                    dataIndex: 'shareAmountOfKe3Package'
                },
                {
                    header: '分成金额（分）-考场视频',
                    dataIndex: 'shareAmountOfSingVideo'
                },
                {
                    header: '分成金额（分）-科三VIP',
                    dataIndex: 'shareAmountOfKe3VIP'
                },
                {
                    header: '退款金额（分）',
                    dataIndex: 'refundAmount'
                },
                {
                    header: '退款金额（分）-科三速成包',
                    dataIndex: 'refundAmountOfKe3Package'
                },
                {
                    header: '退款金额（分）-考场视频',
                    dataIndex: 'refundAmountOfSingVideo'
                },
                {
                    header: '退款金额（分）-科三VIP',
                    dataIndex: 'refundAmountOfKe3VIP'
                },
            ]
        }, ['jiakao-misc!route-stat/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});