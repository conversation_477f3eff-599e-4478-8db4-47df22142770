/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
  "simple!core/template",
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
  "simple!core/form",
  "simple!core/plugin",
  "./download.js",
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Download) {
  var statusMap = {
    "-1": "已删除",
    0: "待审核",
    1: "未通过",
    2: "已通过",
    3: "已上架",
    4: "已下架",
  };

  var statusStore = [
    {
      key: 0,
      value: "待审核",
      search: "",
    },
    {
      key: 1,
      value: "未通过",
      search: "",
    },
    {
      key: 2,
      value: "已通过",
      search: "",
    },
    {
      key: 3,
      value: "已上架",
      search: "",
    },
    {
      key: 4,
      value: "已下架",
      search: "",
    },
  ];

  var uploadSourceMap = {
    0: "未知",
    1: "iOS",
    2: "安卓",
    3: "网页",
  };

  var checkSourceMap = {
    0: "内部",
    1: "外包",
  };

  var checkSourceStore = [
    {
      key: "",
      value: "剪辑方",
    },
    {
      key: 0,
      value: "内部",
    },
    {
      key: 1,
      value: "外包",
    },
  ];

  var extractStatusMap = {
    "": "提取状态",
    false: "未提取",
    true: "已提取",
  };

  var updateTypeMap = {
    0: "新增路线",
    1: "更新路线",
  };

  var idsSelected = [];
  var list = function (panel) {
    let importantCoachList;

    var getImportantList = () => {
      return Store(["jiakao-misc!route-video-audit/data/importantCoachList"])
        .load()
        .done((retData) => {
          importantCoachList =
            retData.data["route-video-audit"].data.importantCoachList.data
              .itemList;
        });
    };
    const refreshTable = () =>
      getImportantList()
        .done(() => {
          Table(
            {
              description: "教练视频审核",
              title: "教练视频审核",
              selector: {
                dataIndex: "id",
                checked: function (lineData) {
                  // 因 dom 取值类型为 string，所以 idsSelected 和 lineData.id 类型需相等都得是 string
                  return idsSelected.indexOf(lineData.id + "") > -1;
                },
                change: function (table, tableDom, tableData, checkResult) {
                  idsSelected = checkResult;
                  // console.log('最终选中的所有ID为：', idsSelected);
                },
              },
              renderAfter: (config, dom, data) => {
                console.log(arguments, 111);
              },
              searchReset: {
                class: 'danger'
              },
              search: [
                {
                  header: "城市等级",
                  dataIndex: "cityLevel",
                  xtype: Plugin(
                    "simple!auto-prompt2",
                    {
                      store: [
                        {
                          key: 0,
                          value: "一般",
                        },
                        {
                          key: 1,
                          value: "中",
                        },
                        {
                          key: 2,
                          value: "高",
                        },
                        {
                          key: 3,
                          value: "极高",
                        },
                      ],
                      dataIndex: "cityLevel",
                      isMulti: true,
                      defaultVal: false,
                      placeholder: "请选择城市等级",
                    },
                    function (plugin, value) {}
                  ),
                },
                {
                  header: "城市编码：",
                  dataIndex: "cityCode",
                  xtype: Plugin(
                    "jiakao-misc!select-district2",
                    {
                      name: "cityCode",
                      areaName: "areaCode",
                      hideArea: true,
                      insert: {
                        province: [
                          {
                            code: "",
                            name: "请选择省份",
                          },
                        ],
                        city: [
                          {
                            code: "",
                            name: "请选择市",
                          },
                        ],
                        area: [
                          {
                            code: "",
                            name: "请选择区域",
                          },
                        ],
                      },
                    },
                    function (plugin, code) {}
                  ),
                },
                {
                  dataIndex: "id",
                  xtype: "text",
                  placeholder: "视频ID",
                },
                {
                  dataIndex: "coachId",
                  xtype: "text",
                  placeholder: "教练ID",
                },
                {
                  dataIndex: "routeMetaName",
                  xtype: "text",
                  placeholder: "考场名称",
                },
                {
                  dataIndex: "routeMetaId",
                  xtype: "text",
                  placeholder: "驾考考场ID",
                },
                {
                  dataIndex: "cityName",
                  xtype: "text",
                  placeholder: "城市名称",
                },
                // {
                //     dataIndex: 'checkSource',
                //     xtype: 'select',
                //     store: checkSourceStore
                // },
                // {
                //     dataIndex: 'enableNewTemplate',
                //     xtype: 'select',
                //     store: [{
                //         key: '',
                //         value: '所有模板'
                //     }, {
                //         key: true,
                //         value: '启用新模板'
                //     }, {
                //         key: false,
                //         value: '没有启用新模板'
                //     }]
                // },
                {
                  dataIndex: "status",
                  xtype: Plugin("simple!auto-prompt2", {
                    store: statusStore,
                    dataIndex: "status",
                    index: {
                      key: "key",
                      value: "value",
                      search: "value",
                    },
                    params: false,
                    isMulti: true,
                    placeholder: "审核状态",
                  }),
                },
                {
                  dataIndex: "timeType",
                  xtype: "select",
                  store: [
                    {
                      key: 0,
                      value: "上传时间",
                    },
                    {
                      key: 1,
                      value: "上架时间",
                    },
                    {
                      key: 2,
                      value: "下架时间",
                    },
                    {
                      key: 3,
                      value: "加急时间",
                    },
                  ],
                  placeholder: "类型",
                },
                {
                  dataIndex: "startTime",
                  xtype: "datetime",
                  placeholder: "开始时间",
                },
                {
                  dataIndex: "endTime",
                  xtype: "datetime",
                  placeholder: "结束时间",
                },
                {
                  dataIndex: "hasTrackInfo",
                  xtype: "select",
                  store: [
                    {
                      key: "",
                      value: "是否有轨迹信息",
                    },
                    {
                      key: true,
                      value: "有轨迹信息",
                    },
                    {
                      key: false,
                      value: "没有轨迹信息",
                    },
                  ],
                },
                {
                  dataIndex: "urgentStatus",
                  xtype: "select",
                  store: [
                    {
                      key: "",
                      value: "是否加急状态",
                    },
                    {
                      key: true,
                      value: "已加急",
                    },
                    {
                      key: false,
                      value: "未加急",
                    },
                  ],
                },
                {
                  dataIndex: "updateType",
                  xtype: "select",
                  store: [
                    {
                      key: "",
                      value: "全部更新",
                    },
                    {
                      key: 0,
                      value: "新增路线",
                    },
                    {
                      key: 1,
                      value: "更新路线",
                    },
                  ],
                },
                {
                  dataIndex: "extractStatus",
                  xtype: "select",
                  store: [
                    {
                      key: "",
                      value: "全部提取状态",
                    },
                    {
                      key: false,
                      value: "未提取",
                    },
                    {
                      key: true,
                      value: "已提取",
                    },
                  ],
                },
                {
                  dataIndex: "importantCoach",
                  xtype: "select",
                  store: [
                    {
                      key: "",
                      value: "全部教练",
                    },
                    {
                      key: true,
                      value: "重点教练",
                    },
                    {
                      key: false,
                      value: "非重点教练",
                    },
                  ],
                },
              ],
              buttons: {
                top: [
                  {
                    name: "刷新",
                    class: "info",
                    click: function (obj) {
                      obj.render();
                    },
                  },
                  {
                    name: "重点教练列表",
                    class: "info",
                    click: function (obj) {
                      console.log(obj);
                      var config = {
                        width: 400,
                        height: 400,
                        close() {
                          refreshTable();
                        },
                      };
                      Simple.Dialog.html("重点教练列表", config).done(function (
                        dialog
                      ) {
                        const refreshList = () => {
                          getImportantList().done(() => {
                            refreshTable();
                          });
                        };
                        const refreshTable = () => {
                          Table(
                            {
                              description: "重点教练列表",
                              title: "重点教练列表",
                              buttons: {
                                top: [
                                  {
                                    name: "刷新",
                                    class: "info",
                                    click: function (obj) {
                                      refreshList(obj);
                                    },
                                  },
                                  {
                                    name: "更新",
                                    class: "info",
                                    click: function () {
                                      Table({
                                        title: "更新",
                                        width: 600,
                                        store:
                                          "jiakao-misc!route-video-audit/data/updateImportantCoach",
                                        success: function (obj, dialog) {
                                          dialog.close();
                                          refreshList();
                                        },
                                        columns: [
                                          {
                                            header: "教练ID：",
                                            dataIndex: "coachIds",
                                            xtype: "text",
                                            value: importantCoachList.join(","),
                                            placeholder: "教练ID",
                                          },
                                        ],
                                      }).add();
                                    },
                                  },
                                ],
                              },
                              columns: [
                                {
                                  header: "教练ID",
                                  dataIndex: "id",
                                },
                              ],
                            },
                            {
                              data: importantCoachList.map((id) => ({ id })),
                            },
                            dialog.body
                          ).render();
                        };
                        refreshTable();
                      });
                      //Table().view();
                      // Table({

                      // })
                    },
                  },
                  {
                    name: "批量加急",
                    class: "info",
                    click: function (obj) {
                      if (idsSelected.length <= 0) {
                        Simple.Dialog.toast("请选择id");
                        return;
                      }
                      Store([
                        "jiakao-misc!route-video-audit/data/doUrgent?ids=" +
                          idsSelected.join(","),
                      ])
                        .save()
                        .done(function () {
                          Simple.Dialog.toast("加急成功");
                          idsSelected = []
                          obj.render();
                          
                        })
                        .fail((err) => {
                          console.log(err);
                        });
                    },
                  },
                  {
                    name: "批量提取",
                    class: "info",
                    click: function (obj) {
                      if (idsSelected.length <= 0) {
                        Simple.Dialog.toast("请选择id");
                        return;
                      }
                      Store([
                        "jiakao-misc!route-video-audit/data/updateExtractStatus?ids=" +
                          idsSelected.join(",") +
                          "&status=true",
                      ])
                        .save()
                        .done(function () {
                          Simple.Dialog.toast("提取成功");
                          idsSelected = []
                          obj.render();
                         
                        })
                        .fail((err) => {
                          console.log(err);
                        });
                    },
                  },
                ],
                bottom: [
                  {
                    name: "刷新",
                    class: "info",
                    click: function (obj) {
                      obj.render();
                    },
                  },
                ],
              },
              operations: [
                {
                  name: "审核视频",
                  xtype: "edit",
                  width: 600,
                  class: "primary",
                  title: "审核视频",
                  success: function (obj, dialog, e) {
                    dialog.close();
                    obj.render();
                  },
                  renderAfter: function (table, dom, lineData) {
                    console.log(lineData);

                    let data = lineData.data;
                    setTimeout(() => {
                      console.log(
                        $(".modal-title").html(
                          `审核视频  <p style="color:red;display:inline"> (${data.routeMetaName}-${data.routeName})</p> `
                        )
                      );
                    }, 200);
                    $(".modal-title").html();
                    dom
                      .item("pass")
                      .on("change", function () {
                        if (dom.item("pass")[0].checked) {
                          dom.item("rejectReason").parent().parent().hide();
                        } else {
                          dom.item("rejectReason").parent().parent().show();
                        }
                      })
                      .trigger("change");

                    if (!lineData.data.routeId) {
                      return;
                    }

                    const docker = $('<div style="margin: 0 50px;"></div>');
                    dom.find("[data-item=tableSlot-group]").append(docker);
                    Table(
                      {
                        buttons: {
                          top: [
                            {
                              name: "刷新",
                              class: "info",
                              click: function (obj) {
                                console.log(obj);
                                obj.render();
                              },
                            },
                          ],
                        },
                        columns: [
                          {
                            header: "教练上传视频ID",
                            dataIndex: "id",
                          },
                          {
                            header: "上传时间",
                            dataIndex: "createTime",
                            render: function (data) {
                              return Utils.format.date(
                                data,
                                "yyyy-MM-dd HH:mm:ss"
                              );
                            },
                          },
                          {
                            header: "教练ID",
                            dataIndex: "coachId",
                          },
                          {
                            header: "当前状态",
                            dataIndex: "status",
                            render: function (data) {
                              return statusMap[data];
                            },
                          },
                          {
                            header: "上架时间",
                            dataIndex: "onlineTime",
                            render: function (data) {
                              return Utils.format.date(
                                data,
                                "yyyy-MM-dd HH:mm:ss"
                              );
                            },
                          },
                        ],
                      },
                      [
                        "jiakao-misc!route-video-audit/data/list?dmRouteId=" +
                          lineData.data.routeId,
                      ],
                      docker,
                      null
                    ).render();
                  },
                  store: "jiakao-misc!route-video-audit/data/audit",
                  columns: [
                    {
                      dataIndex: "id",
                      xtype: "hidden",
                    },
                    {
                      dataIndex: "tableSlot",
                      xtype: "static",
                    },
                    {
                      header: "是否通过",
                      dataIndex: "pass",
                      xtype: "radio",
                      store: [
                        {
                          key: true,
                          value: "通过",
                        },
                        {
                          key: false,
                          value: "不通过",
                        },
                      ],
                    },
                    {
                      header: "不通过原因",
                      dataIndex: "rejectReason",
                      xtype: "textarea",
                    },
                  ],
                },
                {
                  name: "删除",
                  class: "danger",
                  click(table, row, lineData) {
                    Table().edit(lineData, {
                      title:
                        "确认删除 “" +
                        table.config.columns[0].header +
                        "” 为 “" +
                        lineData[table.config.columns[0].dataIndex] +
                        "” 的信息吗？",
                      width: 500,
                      store: "jiakao-misc!route-video-audit/data/delete",
                      success: function (obj, dialog, e) {
                        dialog.close();
                        table.render();
                      },
                      columns: [
                        {
                          dataIndex: "id",
                          xtype: "hidden",
                        },
                        {
                          header: "删除原因",
                          dataIndex: "reason",
                          xtype: "text",
                          check: "required",
                          placeholder: "请填写删除原因",
                        },
                      ],
                    });
                  },
                },
                // {
                //     name: '分配剪辑方',
                //     class: 'info',
                //     xtype: 'edit',
                //     title: '分配剪辑方',
                //     success: function (obj, dialog, e) {
                //         dialog.close();
                //         obj.render();
                //     },
                //     store: 'jiakao-misc!route-video-audit/data/editCheckSource',
                //     columns: [
                //         {
                //             dataIndex: 'id',
                //             xtype: 'hidden'
                //         },
                //         {
                //             header: '剪辑方',
                //             dataIndex: 'checkSource',
                //             xtype: 'radio',
                //             store: [{
                //                 key: 0,
                //                 value: '内部'
                //             },
                //             {
                //                 key: 1,
                //                 value: '外包'
                //             }
                //             ]
                //         }
                //     ]
                // },
                // {
                //     name: '启用新模板',
                //     width: 400,
                //     class: 'primary',
                //     render: function (name, arr, index) {
                //         if (arr[index].enableNewTemplate) {
                //             return ''
                //         }
                //         return name;
                //     },
                //     click: function (table, row, lineData) {
                //         Store(['jiakao-misc!route-video-audit/data/enableNewTemplate?enableNewTemplate=true&id=' + lineData.id])
                //             .save().done(function () {
                //                 table.render();
                //             }).fail(err => {
                //                 console.log(err)
                //             })
                //     }
                // },
                // {
                //     name: '取消新模板',
                //     width: 400,
                //     class: 'danger',
                //     render: function (name, arr, index) {
                //         if (!arr[index].enableNewTemplate) {
                //             return ''
                //         }
                //         return name;
                //     },
                //     click: function (table, row, lineData) {
                //         Store(['jiakao-misc!route-video-audit/data/enableNewTemplate?enableNewTemplate=false&id=' + lineData.id])
                //             .save().done(function () {
                //                 table.render();
                //             }).fail(err => {
                //                 console.log(err)
                //             })
                //     }
                // },
                {
                  name: "加急",
                  class: "primary",
                  click: function (table, row, lineData) {
                    Store([
                      "jiakao-misc!route-video-audit/data/doUrgent?ids=" +
                        lineData.id,
                    ])
                      .save()
                      .done(function () {
                        table.render();
                        Simple.Dialog.toast("加急成功");
                      })
                      .fail((err) => {
                        console.log(err);
                      });
                  },
                },
                {
                  name: "",
                  class: "primary",
                  render: function (name, arr, index) {
                    return arr[index].extractStatus ? "取消提取" : "提取视频";
                  },
                  click: function (table, row, lineData) {
                    Store([
                      "jiakao-misc!route-video-audit/data/updateExtractStatus?ids=" +
                        lineData.id +
                        "&status=" +
                        !lineData.extractStatus,
                    ])
                      .save()
                      .done(function () {
                        table.render();
                        Simple.Dialog.toast("修改成功");
                      })
                      .fail((err) => {
                        console.log(err);
                      });
                  },
                },
                // {
                //     name: '下载视频',
                //     width: 400,
                //     class: 'warning',
                //     click: function (table, row ,lineData) {
                //         lineData.videoUrl.split(",").forEach(function (url) {
                //             Download(url, '教练ID' + lineData.coachId + '的路线视频')
                //         })
                //     }
                // },
              ],
              columns: [
                {
                  header: "教练上传视频ID",
                  dataIndex: "id",
                  width: 50,
                },
                {
                  header: "城市",
                  dataIndex: "cityName",
                },
                {
                  header: "区县",
                  dataIndex: "countyName",
                },
                {
                  header: "驾考考场ID",
                  dataIndex: "routeMetaId",
                },
                {
                  header: "考场名称",
                  dataIndex: "routeMetaName",
                },
                {
                  header: "驾考路线ID",
                  dataIndex: "routeId",
                },
                {
                  header: "终端场地id",
                  dataIndex: "dmExamSiteId"
                },
                {
                  header: "终端路线id",
                  dataIndex: "dmRouteId"
                },
                {
                  header: "路线名称",
                  dataIndex: "routeName",
                },
                {
                  header: "适合车型",
                  dataIndex: "routeCarType",
                  render: function (data) {
                    return data == 1
                      ? "手动"
                      : data == 2
                      ? "自动"
                      : data == 3
                      ? "手自动一体"
                      : "";
                  },
                },
                {
                  header: "城市等级",
                  dataIndex: "cityLevel",
                  render: function (data) {
                    return data == 0
                      ? "一般"
                      : data == 1
                      ? "中"
                      : data == 2
                      ? "高"
                      : data == 3
                      ? "极高"
                      : "";
                  },
                },
                {
                  header: "车辆型号",
                  dataIndex: "vehicleModel",
                },
                {
                  header: '轨迹信息',
                  dataIndex: 'trackInfo',
                  render: function (data, dataArr, lineData) {
                      if (lineData.trackInfo) {
                          return `<a>点击查看</a>`
                      }
                      return ''
                  },
                  click: function (table, row, lineData) {
                      require(['jiakao-misc!app/route-video-item/editMap'], function (ModeVs) {
  
                          Widgets.dialog.html('轨迹信息', {
                              width: '1000px'
                          }).done(function (dialog) {
  
                              var data = JSON.parse(lineData.trackInfo);
  
                              var spotInfoData = lineData.spotInfo ? JSON.parse(lineData.spotInfo) : [];
  
                              const lineArr = data && data.map(item => [item.lng, item.lat])
  
                              const mapDom = document.createElement('div');
                              mapDom.style.height = '800px';
  
                              var map = new AMap.Map(mapDom, {
                                  resizeEnable: true,
                                  center: lineArr[Math.floor(lineArr.length / 2)],
                                  zoom: 15
                              });
                              let lnglats = [];
                              let PromiseList = [];
                              for (let index = 0; index < lineArr.length / 1000; index++) {
                                  PromiseList.push(new Promise(resolve => {
                                      AMap.convertFrom(lineArr.slice(1000 * index, 1000 * (index + 1)), 'gps', function (status, result) {
                                          if (result.info === 'ok') {
                                              resolve(result.locations);
                                          }
                                      })
                                  }))
                              }
  
                              Promise.all(PromiseList).then(([...res]) => {
                                 
                                  res.forEach(item => {
                                      lnglats.push(...item)
                                  })
  
                                  new AMap.Polyline({
                                      map: map,
                                      path: lnglats,
                                      showDir: true,
                                      strokeColor: "#28F",  //线颜色
                                      strokeWeight: 6,      //线宽
                                  });
                              })
  
  
                              AMap.convertFrom(spotInfoData.map(item => [item.spot.lng, item.spot.lat]), 'gps', function (status, result) {
                                  if (result.info === 'ok') {
                                      var lnglats = result.locations; // 转换后的高德坐标 Array.
  
                                      lnglats.forEach((item, i) => {
                                          const marker = new AMap.Marker({
                                              icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                              position: item,
                                              label: {
                                                  content: spotInfoData[i].spot.stepAliasName + '-' + (i + 1),
                                                  direction: 'right'
                                              }
                                          });
                                          marker.setMap(map);
                                      })
                                  }
                              })
  
  
  
                              $(dialog.body).html(mapDom);
  
                          })
  
                      })
  
                  }
              },
                {
                  header: "点位信息",
                  dataIndex: "spotInfo",
                  render: function () {
                    return `<a>点击查看</a>`;
                  },
                  click: function (table, row, lineData) {
                    require([
                      "jiakao-misc!app/route-video-item/editMap",
                    ], function (ModeVs) {
                      if (!lineData.spotInfo) {
                        Widgets.dialog.alert("暂无数据");
                        return;
                      }

                      Widgets.dialog
                        .html("点位信息", {
                          width: "1000px",
                        })
                        .done(function (dialog) {
                          var data = JSON.parse(lineData.spotInfo);

                          const lineArr =
                            data &&
                            data.map(({ spot }) => [
                              spot.lng,
                              spot.lat,
                              spot.stepAliasName,
                            ]);

                          const mapDom = document.createElement("div");
                          mapDom.style.height = "800px";

                          var map = new AMap.Map(mapDom, {
                            resizeEnable: true,
                            center: lineArr[Math.floor(lineArr.length / 2)],
                            zoom: 15,
                          });

                          AMap.convertFrom(
                            lineArr,
                            "gps",
                            function (status, result) {
                              if (result.info === "ok") {
                                var lnglats = result.locations; // 转换后的高德坐标 Array.<LngLat>
                                var polyline = new AMap.Polyline({
                                  map: map,
                                  path: lnglats,
                                  showDir: true,
                                  strokeColor: "#28F", //线颜色
                                  strokeWeight: 6, //线宽
                                });
                              }

                              // result.locations.forEach((item,i) => {
                              //     var text = new AMap.Text({
                              //         text:data[i].spot.stepAliasName || '',
                              //         anchor:'center', // 设置文本标记锚点
                              //         draggable:true,
                              //         cursor:'pointer',
                              //         angle: 10,
                              //         offset: new AMap.Pixel(50, 10),
                              //         style:{
                              //             'padding': '.05rem 0.5rem',
                              //             'width': '8rem',
                              //             'border-width': 0,
                              //             'box-shadow': '0 2px 6px 0 rgba(114, 124, 245, .5)',
                              //             'text-align': 'center',
                              //             'font-size': '12px',
                              //             'color': 'blue'
                              //         },
                              //         position: [item.lng,item.lat]
                              //     });
                              //     text.setMap(map);
                              // });
                            }
                          );

                          $(dialog.body).html(mapDom);
                        });
                    });

                    // Widgets.dialog.html('点位信息', {}).done(function (dialog) {
                    //     var data = lineData.spotInfo && JSON.stringify(JSON.parse(lineData.spotInfo), null, 4)
                    //     $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                    // })
                  },
                },
                {
                  header: "是否有轨迹信息",
                  dataIndex: "trackInfo",
                  render: function (data) {
                    return data ? "是" : "否";
                  },
                },
                {
                  header: "是否有海拔信息",
                  dataIndex: "hasAltitude",
                  render: function (data) {
                    return data ? "是" : "否";
                  },
                },
                {
                  header: "灯光题图片",
                  dataIndex: "emulatorQuestionUrl",
                  render: function (data) {
                    if (data) {
                      return "<a>查看图片</a>";
                    }
                  },
                  click: function (table, row, lineData) {
                    Widgets.dialog
                      .html("灯光题图片", { width: 1200 })
                      .done(function (dialog) {
                        $(dialog.body).html(
                          '<img src="' +
                            lineData.emulatorQuestionUrl +
                            '"></img>'
                        );
                      });
                  },
                },
                {
                  header: "描述",
                  render: function (data) {
                    return (
                      '<div style="width: 200px; word-break: break-all">' +
                      (data || "") +
                      "</div>"
                    );
                  },
                  dataIndex: "routeDescription",
                },
                {
                  header: "考场封面",
                  render: function (data) {
                    if (data) {
                      return "<a>查看图片</a>";
                    }
                  },
                  dataIndex: "examFieldCover",
                  click: function (table, row, lineData) {
                    Widgets.dialog
                      .html("考场封面", { width: 1500 })
                      .done(function (dialog) {
                        $(dialog.body).html(
                          '<img src="' + lineData.examFieldCover + '"></img>'
                        );
                      });
                  },
                },
                {
                  header: "路线地图",
                  dataIndex: "routeImageUrl",
                  render: function (data) {
                    if (data) {
                      return "<a>查看图片</a>";
                    }
                  },
                  click: function (table, row, lineData) {
                    Widgets.dialog
                      .html("路线地图", { width: 1500 })
                      .done(function (dialog) {
                        $(dialog.body).html(
                          '<img src="' + lineData.routeImageUrl + '"></img>'
                        );
                      });
                  },
                },
                {
                  header: "路线视频",
                  dataIndex: "videoUrl",
                  render: function (data) {
                    if (data) {
                      return "<a>点击查看</a>";
                    }
                  },
                  click: function (table, row, lineData) {
                    Widgets.dialog
                      .html(
                        "路线视频(" +
                          lineData.routeMetaName +
                          "-" +
                          lineData.routeName +
                          ")",
                        {}
                      )
                      .done(function (dialog) {
                        var html = lineData.videoUrl
                          .split(",")
                          .map(function (url) {
                            return (
                              '<a href="' +
                              url +
                              '" target="_blank">' +
                              url +
                              "</a>" +
                              '<br><video src="' +
                              url +
                              '" width="400" controls></video>'
                            );
                          });
                        $(dialog.body).html(
                          '<div style="max-height: 600px; overflow: auto;">' +
                            html.join("<br/>") +
                            "</div>"
                        );
                      });
                  },
                },
                {
                  header: "教练ID",
                  dataIndex: "coachId",
                  render: function (data) {
                    console.log(data);
                    if (data && importantCoachList.includes(data)) {
                      return `<span style="color: red;">${data}</span>`;
                    } else {
                      return data;
                    }
                  },
                },
                {
                  header: "更新状态",
                  dataIndex: "updateType",
                  render: function (data) {
                    return updateTypeMap[data];
                  },
                },
                {
                  header: "提取状态",
                  dataIndex: "extractStatus",
                  render: function (data) {
                    return extractStatusMap[data];
                  },
                },
                {
                  header: "上传时间",
                  dataIndex: "createTime",
                  order: "",
                  render: function (data) {
                    return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                  },
                },
                {
                  header: "加急时间",
                  dataIndex: "urgentTime",
                  render: function (data) {
                    return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                  },
                },
                {
                  header: "上架时间",
                  dataIndex: "onlineTime",
                  render: function (data) {
                    return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                  },
                },

                {
                  header: "下架时间",
                  dataIndex: "offlineTime",
                  render: function (data) {
                    return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                  },
                },
                {
                  header: "上传来源",
                  dataIndex: "uploadSource",
                  render(v) {
                    return uploadSourceMap[v];
                  },
                },
                {
                  header: "审核状态",
                  dataIndex: "status",
                  render: function (data) {
                    return statusMap[data];
                  },
                },
                // {
                //     header: '剪辑方',
                //     dataIndex: 'checkSource',
                //     render: function (data) {
                //         return checkSourceMap[data]
                //     }
                // },
                // {
                //     header: '是否启用新模板',
                //     render: function (data) {
                //         if (data) {
                //             return '是';
                //         } else {
                //             return '否';
                //         }
                //     },
                //     dataIndex: 'enableNewTemplate'
                // },
                {
                  header: "审核时间",
                  dataIndex: "auditTime",
                  render: function (data) {
                    return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                  },
                  order: "",
                },
                {
                  header: "更新人",
                  dataIndex: "updateUserName",
                },
              ],
            },
            ["jiakao-misc!route-video-audit/data/list"],
            panel,
            () => {
            }
          ).render();
        })
        .fail((err) => {
          if (err.message != null) {
            Widgets.dialog.alert(err.message);
          } else {
            Widgets.dialog.alert("接口请求失败...");
          }
        });

    refreshTable();
  };

  return {
    list: list,
  };
});
