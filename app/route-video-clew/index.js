/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!route-video-clew/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '姓名：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '姓名'
                },
                {
                    header: '电话：',
                    dataIndex: 'phone',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '电话'
                },
                {
                    header: 'QQ：',
                    dataIndex: 'qq',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: 'QQ'
                },
                {
                    header: '城市：',
                    dataIndex: 'cityName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '城市'
                },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '考场路线线索收集列表',
            title: '考场路线线索收集列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!route-video-clew/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '姓名：',
                            dataIndex: 'name'
                        },
                        {
                            header: '电话：',
                            dataIndex: 'phone'
                        },
                        {
                            header: 'QQ：',
                            dataIndex: 'qq'
                        },
                        {
                            header: '城市：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },

                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!route-video-clew/data/view',
                        save: 'jiakao-misc!route-video-clew/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '姓名：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '姓名'
                        },
                        {
                            header: '电话：',
                            dataIndex: 'phone',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '电话'
                        },
                        {
                            header: 'QQ：',
                            dataIndex: 'qq',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: 'QQ'
                        },
                        {
                            header: '城市：',
                            dataIndex: 'cityName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '城市'
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!route-video-clew/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '姓名',
                    dataIndex: 'name'
                },
                {
                    header: '电话',
                    dataIndex: 'phone'
                },
                {
                    header: 'QQ',
                    dataIndex: 'qq'
                },
                {
                    header: '城市',
                    dataIndex: 'cityName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },

                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!route-video-clew/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
