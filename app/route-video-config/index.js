/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/plugin', 'jiakao-misc!plugin/select-district/district'], function (Template, Table, Utils, Widgets, Store, Plugin, District) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!route-video-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '考场的城市：',
                xtype: Plugin('jiakao-misc!select-district', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
                dataIndex: 'cityCode'
            },
            {

                header: '是否开启入口：',
                dataIndex: 'showEntrance',
                xtype: 'hidden',
                value: false
            }, {
                header: '允许上传视频的教练',
                dataIndex: 'allowCoachIds',
                xtype: 'text',
                placeholder: '允许上传视频的教练'
            }, {
                header: '激励视频：',
                dataIndex: 'adFree',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                }, {
                    key: false,
                    value: '否'
                }],
                value: false
            },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '考场路线视频-城市列表',
            title: '考场路线视频-城市列表',
            search: [{
                header: '城市等级',
                dataIndex: 'cityLevel',
                xtype: Plugin('simple!auto-prompt2', {
                    store: [{
                        key: 0,
                        value: '一般'
                    }, {
                        key: 1,
                        value: '中'
                    }, {
                        key: 2,
                        value: '高'
                    }, {
                        key: 3,
                        value: '极高'
                    }],
                    dataIndex: 'cityLevel',
                    isMulti: true,
                    defaultVal: false,
                    placeholder: '请选择城市等级'
                }, function (plugin, value) {
                }),
            }, {
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
            }, {
                dataIndex: 'provName',
                xtype: 'text',
                placeholder: '输入省份'
            },
            {
                dataIndex: 'name',
                xtype: 'text',
                placeholder: '输入城市'
            },
            {
                dataIndex: 'showEntrance',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '选择是否开启入口'
                }, {
                    key: true,
                    value: '是'
                }, {
                    key: false,
                    value: '否'
                }]

            },
            {
                dataIndex: 'videoConfirm',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '选择审核状态'
                }, {
                    key: true,
                    value: '已审核'
                }, {
                    key: false,
                    value: '未审核'
                }]

            },
            {
                dataIndex: 'videoConfirmUser',
                xtype: 'text',
                placeholder: '选择审核人',



            },
            {
                dataIndex: 'showEntranceStartTime',
                xtype: 'date',
                placeholder: '上线时间开始',

            }, {
                dataIndex: 'showEntranceEndTime',
                xtype: 'date',
                placeholder: '上线时间结束',

            },



            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                },
                {
                    name: '修改活动配置',
                    class: 'primary',
                    click: function (obj) {
                        Table(

                            {
                                title: '修改活动配置',
                                width: 400,
                                store: 'jiakao-misc!route-video-config/data/updateActivityConfig',
                                success: function (obj, dialog) {
                                    dialog.close();
                                    obj.render();
                                },
                                renderAfter: function (config, dom, data) {
                                    var contentDom = dom.item('activityConfig')
                                    $(contentDom).css("height", '350')
                                },
                                columns: [
                                    {
                                        header: '活动配置',
                                        dataIndex: 'activityConfig',
                                        xtype: 'textarea',
                                        check: 'required',
                                        placeholder: '活动配置',
                                    }
                                ]

                            }).add()
                    }
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                name: '操作',
                class: 'success',
                nodes: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!route-video-config/data/view',
                    columns: [{
                        header: '#',
                        dataIndex: 'id'
                    },
                    {
                        header: '名称：',
                        dataIndex: 'name'
                    },
                    {
                        header: '城市编码：',
                        dataIndex: 'cityCode'
                    },
                    {
                        header: '城市等级',
                        dataIndex: 'cityLevel',
                        render: function (data) {
                            return data == 0 ? '一般' : data == 1 ? '中' : data == 2 ? '高' : data == 3 ? '极高' : ''
                        }
                    },

                    {
                        header: '是否开启入口：',
                        render: function (data) {
                            if (data) {
                                return '是';
                            } else {
                                return '否';
                            }
                        },
                        dataIndex: 'showEntrance'
                    },
                    {
                        header: '打包价格，单位分：',
                        dataIndex: 'packPrice'
                    },
                    {
                        header: 'createTime：',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: 'createUserId：',
                        dataIndex: 'createUserId'
                    },
                    {
                        header: 'createUserName：',
                        dataIndex: 'createUserName'
                    },
                    {
                        header: 'updateTime：',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    },
                    {
                        header: 'updateUserId：',
                        dataIndex: 'updateUserId'
                    },
                    {
                        header: 'updateUserName：',
                        dataIndex: 'updateUserName'
                    }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 760,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!route-video-config/data/view',
                        save: 'jiakao-misc!route-video-config/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    },
                    {

                        header: '是否开启入口：',
                        dataIndex: 'showEntrance',
                        xtype: 'radio',
                        store: [{
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                        ]
                    },
                    {
                        header: '激励视频：',
                        dataIndex: 'adFree',
                        xtype: 'radio',
                        store: [{
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }]
                    },
                    {
                        header: '允许上传视频的教练',
                        dataIndex: 'allowCoachIds',
                        xtype: 'text',
                        placeholder: '允许上传视频的教练'
                    },
                    {
                        header: '打包价格（半年）：',
                        dataIndex: 'packPrice',
                        xtype: 'number',
                        suffix: '分',
                        placeholder: '打包价格',
                        render: function (data) {
                            if (data == null) {
                                return 6000
                            } else {
                                return data
                            }
                        }
                    },
                    {
                        header: '打包价格（一年）：',
                        dataIndex: 'oneYearPrice',
                        xtype: 'number',
                        suffix: '分',
                        placeholder: '打包价格',
                        render: function (data) {
                            if (data == null) {
                                return 12000
                            } else {
                                return data
                            }
                        }
                    },
                    {
                        header: '苹果打包价格（半年）：',
                        dataIndex: 'applePackPrice',
                        xtype: 'number',
                        suffix: '分',
                        placeholder: '苹果打包价格',
                        render: function (data) {
                            if (data == null) {
                                return 6000
                            } else {
                                return data
                            }
                        }
                    },
                    {
                        header: '苹果价格ID（半年）：',
                        dataIndex: 'applePriceId',
                        xtype: 'text',
                        placeholder: '苹果价格ID'
                    },
                    {
                        header: '苹果打包价格（一年）：',
                        dataIndex: 'oneYearApplePrice',
                        xtype: 'number',
                        suffix: '分',
                        placeholder: '苹果打包价格',
                        render: function (data) {
                            if (data == null) {
                                return 12000
                            } else {
                                return data
                            }
                        }
                    },
                    {
                        header: '苹果价格ID（一年）：',
                        dataIndex: 'oneYearApplePriceId',
                        xtype: 'text',
                        placeholder: '苹果价格ID'
                    },
                    {
                        header: '活动配置：',
                        dataIndex: 'activity',
                        xtype: 'textarea',
                        placeholder: '活动配置',
                        render: function (data) {
                            if (data == null) {
                                return '{"day365": {"oneYearActivity": 6000} }';
                            } else {
                                return data
                            }
                        }
                    },
                    {
                        header: '是否开放上传：',
                        dataIndex: 'supportCoachUpload',
                        xtype: 'radio',
                        store: [{
                            key: true,
                            value: '是'
                        }, {
                            key: false,
                            value: '否'
                        }]
                    },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!route-video-config/data/delete'
                },

                ]
            },
            {
                name: '上传考场',
                class: 'primary',
                click: function (table, row, lineData) {
                    var areaName;
                    Table({
                        title: '上传考场',
                        width: 600,
                        store: 'jiakao-misc!route-video-config/data/upload',
                        renderAfter: function (config, dom, data) {




                            dom.find('select#areaCode').on('change', function () {
                                areaName = dom.find('select#areaCode').find("option:selected").text();


                            }).change();


                        },
                        form: {
                            submitHandler: function (form, fun) {



                                return {

                                    areaName: areaName
                                };


                            }
                        },

                        success: function (obj, dialog) {

                            dialog.close();
                            table.render();
                        },
                        columns: [{
                            header: '考场的城市：',
                            xtype: 'hidden',
                            dataIndex: 'cityCode',
                            value: lineData.cityCode
                        },
                        {
                            header: '考场的城市：',
                            xtype: 'select',
                            dataIndex: 'areaCode',
                            store: District.getAreasOfCity(lineData.cityCode),
                            index: {
                                key: 'code',
                                value: 'name'
                            },

                        },
                        {
                            header: '考场的区中文：',
                            xtype: 'hidden',
                            dataIndex: 'areaName',



                        },
                        {

                            header: '选择文件：',
                            dataIndex: 'data',
                            xtype: 'file'
                        }
                        ]
                    }).add();
                }
            },
            {
                name: '确认审核',
                width: 400,
                class: 'info',

                render: function (name, arr, index) {

                    if (arr[index].videoConfirm) {
                        return ''
                    }
                    return name;
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.confirm('确定审核吗?', function (ev, status) {
                        if (status) {
                            Store(['jiakao-misc!route-video-config/data/verify?confirm=true&id=' + lineData.id]).load().done(function () {
                                table.render();
                            }).fail(err => {
                                console.log(err)
                            })

                        }
                    })



                }
            },
            {
                name: '取消审核',
                width: 400,
                class: 'danger',
                render: function (name, arr, index) {

                    if (!arr[index].videoConfirm) {
                        return ''
                    }
                    return name;
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.confirm('确定取消审核吗?', function (ev, status) {
                        if (status) {
                            Store(['jiakao-misc!route-video-config/data/verify?confirm=false&id=' + lineData.id]).load().done(function () {
                                table.render();
                            }).fail(err => {
                                console.log(err)
                            })
                        }
                    })


                }



            },
            {
                name: '启用新模板',
                width: 400,
                class: 'info',
                render: function (name, arr, index) {
                    if (arr[index].enableNewTemplate) {
                        return ''
                    }
                    return name;
                },
                click: function (table, row, lineData) {
                    Store(['jiakao-misc!route-video-config/data/enableNewTemplate?enableNewTemplate=true&id=' + lineData.id])
                    .save().done(function () {
                        table.render();
                    }).fail(err => {
                        console.log(err)
                    })
                }
            },
            {
                name: '取消新模板',
                width: 400,
                class: 'danger',
                render: function (name, arr, index) {
                    if (!arr[index].enableNewTemplate) {
                        return ''
                    }
                    return name;
                },
                click: function (table, row, lineData) {
                    Store(['jiakao-misc!route-video-config/data/enableNewTemplate?enableNewTemplate=false&id=' + lineData.id])
                    .save().done(function () {
                        table.render();
                    }).fail(err => {
                        console.log(err)
                    })
                }
            },
            {
                name: '数据报表',
                class: '',
                click: function (table, dom, lineData) {
                    console.log(arguments);
                    Widgets.dialog.html('数据报表', {
                        //弹窗宽度
                        width: 500,
                        height: 500,
                        //自定义按钮，可空
                        buttons: [{
                            name: '关闭',
                            xtype: 'primary',
                            click: function () {

                                this.close();
                            }
                        }]
                    }).done(function (dialog) {
                        Template('jiakao-misc!cityreport/main', dialog.body, function (dom, data) {
                            console.log(data);
                        }, ['jiakao-misc!route-video-config/data/listItemByCity?cityCode=' + lineData.cityCode], {
                            'cityName': lineData.name
                        }).render([{
                            aliases: 'list'
                        }])
                    })
                }

            },
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '名称',
                dataIndex: 'name',
                render: function (data) {
                    if (data) {
                        return '<a>' + data + '</a>'
                    }
                },
                click: function (table, row, lineData) {
                    require(['simple!app/layout/main'], function (Main) {
                        var nPanel = Main.panel('route-video-meta-' + lineData.name);
                        if (nPanel.length == 0) {
                            nPanel = Main.panel({
                                id: 'route-video-meta-' + lineData.name,
                                name: lineData.name + '-路线'
                            })
                        }
                        require(['jiakao-misc!app/route-video-meta/index'], function (Meta) {
                            Meta.list(nPanel, lineData)
                        })
                    });
                }
            },
            {
                header: '城市编码',
                dataIndex: 'cityCode'
            },
            {
                header: '城市等级',
                dataIndex: 'cityLevel',
                render: function (data) {
                    return data == 0 ? '一般' : data == 1 ? '中' : data == 2 ? '高' : data == 3 ? '极高' : ''
                }
            },

            {
                header: '考场数量',
                dataIndex: 'metaCount'
            },
            {
                header: '是否开启入口',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'showEntrance'
            },
            {
                header: '打包价格(半年)',
                dataIndex: 'packPrice'
            },
            {
                header: '打包价格(一年)',
                dataIndex: 'oneYearPrice'
            },
            {
                header: '苹果打包价格（半年）',
                dataIndex: 'applePackPrice'
            },
            {
                header: '苹果价格ID（半年）',
                dataIndex: 'applePriceId'
            },
            {
                header: '苹果打包价格（一年）',
                dataIndex: 'oneYearApplePrice'
            },
            {
                header: '苹果价格ID（一年）',
                dataIndex: 'oneYearApplePriceId'
            },
            {
                header: '审核状态',
                dataIndex: 'videoConfirm',
                render: function (data) {

                    return data ? '已审核' : '未审核'

                }

            },
            {
                header: '视频审核人',
                dataIndex: 'videoConfirmUser',
            },
            {
                header: '活动配置：',
                dataIndex: 'activity'
            },
            {
                header: '激励视频：',
                dataIndex: 'adFree',
                render: function (data) {
                    return data ? '是' : '否'
                }

            },
            {
                header: '是否开放上传：',
                dataIndex: 'supportCoachUpload',
                render: function (data) {
                    return data ? '是' : '否'
                }
            },
            {
                header: '是否启用新模板：',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else if(data === false) {
                        return '否';
                    }
                },
                dataIndex: 'enableNewTemplate'
            },
            {
                header: '允许上传视频的教练',
                dataIndex: 'allowCoachIds'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            },
            {
                header: '上线时间',
                dataIndex: 'showEntranceTime',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                }
            },

            ]
        }, ['jiakao-misc!route-video-config/data/list'], panel, function (target, config, item, table) {
            Store(['jiakao-misc!route-video-config/data/list?showEntrance=true'], [{
                aliases: 'list'
            }]).load().done(function (data) {
                item('description').append("<p style='float:right;margin-top:10px;color:black;'><span style='margin-left:-50px;'>已上线城市数量 :</span> " + data.data.list.paging.total + "</p>")
            }).fail(function (err) {
                console.log(err);
            })

        }).render();
    }

    return {
        list: list
    }

});
