/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    // 状态，1:支付成功，2:退款成功，3:退款失败，其他或不传返回全部
    var statusMap = {
        1: '<span style="color: green;">支付成功</span>',
        2: '<span style="color: sandybrown;">退款成功</span>',
        3: '<span style="color: red;">退款失败</span>'
    }

    var list = function (panel) {
        Table({
            description: '教练路线视频的分成流水',
            title: '教练路线视频的分成流水',
            search: [{
                dataIndex: 'coachId',
                xtype: 'text',
                placeholder: '教练ID'
            },
            {
                dataIndex: 'routeMetaName',
                xtype: 'text',
                placeholder: '考场名称'
            },
            {
                dataIndex: 'routeMetaId',
                xtype: 'text',
                placeholder: '考场ID'
            },
            {
                dataIndex: 'cityName',
                xtype: 'text',
                placeholder: '城市名称'
            },
            {
                dataIndex: 'orderNumber',
                xtype: 'text',
                placeholder: '订单号'
            },
            {
                dataIndex: 'startTime',
                xtype: 'date',
                placeholder: '开始日期'
            },
            {
                dataIndex: 'endTime',
                xtype: 'date',
                placeholder: '结束日期'
            }
            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [],
            columns: [
                {
                    header: '订单时间',
                    dataIndex: 'createTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                },
                {
                    header: '订单号',
                    dataIndex: 'orderNumber',
                    reportRender: function (data) {
                        return 'No.' + data;
                    }
                },
                {
                    header: '手机系统',
                    dataIndex: 'platform'
                },
                {
                    header: '支付金额（分）',
                    dataIndex: 'payAmount',
                    render: function (data, rows, lineData) {
                        console.log(arguments);
                        switch (lineData.status) {
                            case 1:
                                return data;
                            case 2:
                                return -data;
                            case 3:
                                return 0;
                        }
                    }
                },
                {
                    header: '考场ID',
                    dataIndex: 'routeMetaId'
                },
                {
                    header: '商品类型',
                    dataIndex:'goodsType'
                },
                {
                    header: '路线ID',
                    dataIndex: 'routeVideoId'
                },
                {
                    header: '教练ID',
                    dataIndex: 'coachId'
                },
                {
                    header: '分成金额（分）',
                    dataIndex: 'amount',
                    render: function (data, rows, lineData) {
                        console.log(arguments);
                        switch (lineData.status) {
                            case 1:
                                return data;
                            case 2:
                                return -data;
                            case 3:
                                return 0;
                        }
                    }
                },
                {
                    header: '订单状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data];
                    }
                }
            ]
        }, ['jiakao-misc!route-video-income/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});