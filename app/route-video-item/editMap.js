/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['https://webapi.amap.com/maps?v=1.4.15&key=c76d3a11492cf4e271fdcfce1ef62221&plugin=AMap.PolyEditor&plugin=AMap.PlaceSearch', 'https://cache.amap.com/lbs/static/addToolbar.js', 'https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js', 'https://cache.amap.com/lbs/static/PlaceSearchRender.js'], function () {

    var edit = function ($panel,params) {
        Simple.Plugin('jiakao-misc!map-edit',{
            id: params.id,
            selectKaochang: params.selectKaochang,
            target: $panel,
            
        }).render();

    }

    return {
        edit: edit
    }

});
