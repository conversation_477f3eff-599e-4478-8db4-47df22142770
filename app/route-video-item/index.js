/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var statusMap = {
        0: '上传中',
        1: '转码中',
        2: '视频加密中',
        3: '上传完成'
    }
    var mapStatusMap = {
        1: '没地图',
        2: '有地图没上线',
        3: '有地图且上线'
    }
    var mapStatusStore = [{
        key: '',
        value: '地图状态'
    },
    {
        key: '1',
        value: '没地图'
    },
    {
        key: '2',
        value: '有地图没上线'
    },
    {
        key: '3',
        value: '有地图且上线'
    },
    ]

    var add = function (table, metaId) {
        Store(['jiakao-misc!route-video-meta/data/listAll']).load([{
            aliases: 'list'
        }]).done(function (store, data) {
            var listData = data.list.data;

            var columns = [{
                header: '视频名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 32,
                placeholder: '视频名称'
            },
            {
                header: '描述：',
                dataIndex: 'desc',
                xtype: 'textarea',
                placeholder: '描述'
            },
            {
                header: '标签：',
                dataIndex: 'tagName',
                xtype: 'text',
                placeholder: '标签'
            },
            {
                header: '封面图：',
                dataIndex: 'videoImage',
                xtype: Simple.Plugin('simple!jiakao-upload', {
                    useSecureUpload: true,
                    bucket: 'jiakao-image',
                    prefix: 'jiakao-image',
                    // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                    getAppSpaceId: 'jiakao-misc!common/data/getAppSpaceId',
                    imageSrc: {
                        // 必填。
                        dataIndex: 'encode',
                    },
                    parse: function (value) {
                        return [
                            {
                                encode: value
                            }
                        ]
                    }
                })
            },
            {
                header: '考场路线地图id：',
                dataIndex: 'mapId',
                xtype: 'text',
                placeholder: '考场路线地图id'
            },
            {
                header: '排序：',
                dataIndex: 'order',
                xtype: 'text',
            },
            {
                header: '适合车型',
                dataIndex: 'carGear',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '适合车型'
                }, {
                    key: 1,
                    value: '手动挡'
                },
                {
                    key: 2,
                    value: '自动挡'
                }, {
                    key: 3,
                    value: '自动挡+手动挡'
                }
                ],
                placeholder: '适合车型',
                value: 3,
                check: 'required'
            },
            {
                header: '试看视频id：',
                xtype: 'text',
                value: 0,
                dataIndex: 'videoId',
            },
            {
                header: '标清试看视频：',
                dataIndex: 'previewL',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '标清试看视频'
            },
            {
                header: '高清试看视频：',
                dataIndex: 'previewM',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '高清试看视频'
            },
            {
                header: '超清试看视频：',
                dataIndex: 'previewH',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '超清试看视频'
            },
            {
                header: '完整视频id：',
                xtype: 'text',
                value: 0,
                dataIndex: 'previewVideoId',
            },
            {
                header: '标清视频：',
                dataIndex: 'videoL',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '标清视频'
            },
            {
                header: '高清视频：',
                dataIndex: 'videoM',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '高清视频'
            },
            {
                header: '超清视频：',
                dataIndex: 'videoH',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '超清视频'
            },
            // {
            //     header: '技巧图片：',
            //     dataIndex: 'skillImage',
            //     xtype: Plugin('jiakao-misc!upload', {
            //         dataIndex: 'skillImage',
            //         uploadIndex: 'skillImage',
            //         bucket: "exam-room",
            //         isSingle: true,
            //         placeholder: '请选择上传文件',
            //         url: 'simple-upload3://upload/file.htm'
            //     }, function () {
            //         console.log(arguments)
            //     })
            // },
            // {
            //     header: '技巧标题：',
            //     dataIndex: 'skillTitle',
            //     xtype: 'text',
            //     maxlength: 256,
            //     placeholder: '技巧标题'
            // },
            // {
            //     header: '技巧视频：',
            //     dataIndex: 'skillVideo',
            //     xtype: Plugin('jiakao-misc!upload1', {
            //         dataIndex: 'skillVideo',
            //         uploadIndex: 'skillVideo',
            //         bucket: "exam-room",
            //         isSingle: true,
            //         placeholder: '请选择上传文件',
            //         url: 'simple-upload3://upload/file.htm'
            //     }, function () {
            //         console.log(arguments)
            //     })
            // },
            {
                header: '视频的价格：',
                dataIndex: 'price',
                xtype: 'number',
                placeholder: '视频的价格(单位分)',
                number: true,
                digits: true,
                value: 1990,
                check: 'required'
            },
            {
                header: '3D科三考场',
                dataIndex: 'enableD3Scene',
                xtype: 'select',
                store: [{
                    key: false,
                    value: '不启用'
                }, {
                    key: true,
                    value: '启用'
                },
                ],
                placeholder: '3D科三考场',
                check: 'required',
            },
            ]

            if (metaId) {
                var metaVal = '';
                for (var i = 0; i < listData.length; i++) {
                    if (listData[i].key == metaId) {
                        metaVal = listData[i].value;
                    }
                }
                columns = [{
                    xtype: 'hidden',
                    dataIndex: 'metaId',
                    value: metaId
                },
                {
                    header: '对应的考场',
                    xtype: 'text',
                    dataIndex: 'metaName',
                    disabled: true,
                    value: metaVal
                }
                ].concat(columns)
            } else {
                columns = [{
                    header: '对应的考场信息Id',
                    dataIndex: 'metaId',
                    xtype: 'select',
                    placeholder: '视频名称',
                    store: listData,
                    index: [{
                        key: 'key',
                        value: 'value'
                    }]
                },].concat(columns)
            }
            var configData = ''
            Table({
                title: '添加',
                width: 600,
                store: 'jiakao-misc!route-video-item/data/insert',
                success: function (obj, dialog) {
                    dialog.close();
                    table.render();
                },
                form: {
                    submitHandler: function (form) {

                        const videoImage = JSON.parse(form.videoImage.value || '[]');

                        const params = {
                            videoImageEncodedData: videoImage[0]?.encode,
                        }

                        return params
                    },
                },
                renderAfter: function (tab, dom, lineData) {
                    dom.item('config-btn').on('click', function () {
                        Plugin('simple!product-filter', {
                            store: configData && JSON.parse(configData) || '',
                            submit: function (data) {
                                configData = data;
                                dom.item('configString').val(data);
                            }
                        }).render().done(function () {

                        });
                    });


                    /*
                     *
                     * 标清、高清、超清视频的后缀修改为middle、high、higher
                     * */
                    dom.item('videoL').on('blur', function () {
                        var val = $(this).val();
                        var highVal = val.replace(/\.mp4/, ".middle.mp4");
                        if (val.indexOf("middle") == -1) {
                            $(this).val(highVal);
                        }

                        var midVal = val.replace(/\.high.mp4|\.mp4/, ".high.mp4");
                        dom.item('videoM').val(midVal);
                        dom.item('videoM').attr("readOnly", "true");

                        var lowVal = val.replace(/\.high.mp4|\.mp4/, ".higher.mp4");
                        dom.item('videoH').val(lowVal);
                        dom.item('videoH').attr("readOnly", "true");
                    })


                    dom.item('previewL').on('blur', function () {
                        var val = $(this).val();
                        var highVal = val.replace(/\.mp4/, ".middle.mp4");
                        if (val.indexOf("middle") == -1) {
                            $(this).val(highVal);
                        }

                        var midVal = val.replace(/\.high.mp4|\.mp4/, ".high.mp4");
                        dom.item('previewM').val(midVal);
                        dom.item('previewM').attr("readOnly", "true");

                        var lowVal = val.replace(/\.high.mp4|\.mp4/, ".higher.mp4");
                        dom.item('previewH').val(lowVal);
                        dom.item('previewH').attr("readOnly", "true");
                    })

                },
                columns: [
                    {
                        dataIndex: 'reason',
                        xtype: function () {
                            return `
                         <div>
                         您当前尝试操作的是一项关键运营数据，您需说明操作原因以便审核。相关操作将在<span style="color:red;font-weight: bold">审核通过后生效</span>
                         </div>
                        
                        `
                        },
                        placeholder: '请输入操作说明供审核'
                    },
                    {
                        header: '操作说明供审核',
                        dataIndex: 'reason',
                        check: 'required',
                        xtype: 'select',
                        store: [{
                            key: '路线更新',
                            value: '路线更新'
                        }, {
                            key: '路线错误',
                            value: '路线错误'
                        }],
                        placeholder: '请输入操作说明供审核'
                    },
                ].concat(columns)
            }).add();
        })


    }
    var editByLanguageType = function (table, lineData = {}, languageType) {
        let config = {
            title: languageType === 'WEIYU' ? '编辑维语' : '编辑',
            width: 600,
            store: 'jiakao-misc!route-video-item/data/update' + (languageType === 'WEIYU' ? '?_lang=ug' : ''),
            success: function (obj, dialog, e) {
                dialog.close();

                table.render();
            },
            form: {
                submitHandler: function (form) {

                    const videoImage = JSON.parse(form.videoImage.value || '[]');

                    const params = {
                        videoImageEncodedData: videoImage[0]?.encode,
                    }

                    return params
                },
            },
            renderAfter: function (table, $form) {
                if (lineData.videoImageEncodedData) {
                    setTimeout(() => {
                        $form.item('videoImage-group').find("input[name=videoImage]").val(JSON.stringify([{ encode: lineData.videoImageEncodedData }]))
                    }, 300)
                } else if (lineData.videoImage) {
                    $form.item('videoImage-group').find("input[name=videoImage]").val(lineData.videoImage)
                }
            },
            columns: [
                {
                    dataIndex: 'languageType',
                    xtype: 'hidden',
                    value: languageType
                },
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    dataIndex: 'reason',
                    xtype: function () {
                        return `
                         <div>
                         您当前尝试操作的是一项关键运营数据，您需说明操作原因以便审核。相关操作将在<span style="color:red;font-weight: bold">审核通过后生效</span>
                         </div>
                        
                        `
                    },
                    placeholder: '请输入操作说明供审核'
                },
                {
                    header: '操作说明供审核',
                    dataIndex: 'reason',
                    check: 'required',
                    xtype: 'select',
                    store: [{
                        key: '路线更新',
                        value: '路线更新'
                    }, {
                        key: '路线错误',
                        value: '路线错误'
                    }],
                    placeholder: '请输入操作说明供审核'
                },
                {
                    header: '对应的考场信息Id：',
                    dataIndex: 'metaId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '对应的考场信息Id'
                },
                {
                    header: '视频名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '视频名称'
                },
                {
                    header: '标签：',
                    dataIndex: 'tagName',
                    xtype: 'text',
                    placeholder: '标签'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'textarea',
                    placeholder: '描述'
                },
                {
                    header: '封面图：',
                    dataIndex: 'videoImage',
                    xtype: Simple.Plugin('simple!jiakao-upload', {
                        useSecureUpload: true,
                        bucket: 'jiakao-image',
                        prefix: 'jiakao-image',
                        // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                        getAppSpaceId: 'jiakao-misc!common/data/getAppSpaceId',
                        imageSrc: {
                            // 必填。
                            dataIndex: 'encode',
                        },
                        parse: function (value) {
                            return [
                                {
                                    encode: value
                                }
                            ]
                        }
                    })
                },
                {
                    header: '考场路线地图id：',
                    dataIndex: 'mapId',
                    xtype: 'text',
                    placeholder: '考场路线地图id'
                },
                {
                    header: '排序：',
                    dataIndex: 'order',
                    xtype: 'text',
                },
                {
                    header: '适合车型',
                    dataIndex: 'carGear',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '适合车型'
                    }, {
                        key: 1,
                        value: '手动挡'
                    },
                    {
                        key: 2,
                        value: '自动挡'
                    }, {
                        key: 3,
                        value: '自动挡+手动挡'
                    }
                    ],
                    placeholder: '适合车型',
                    check: 'required'
                },
                {
                    header: '试看视频id：',
                    xtype: 'text',
                    dataIndex: 'videoId',
                },
                {
                    header: '标清试看视频：',
                    dataIndex: 'previewL',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '标清试看视频'
                },
                {
                    header: '高清试看视频：',
                    dataIndex: 'previewM',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '高清试看视频'
                },
                {
                    header: '超清试看视频：',
                    dataIndex: 'previewH',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '超清试看视频'
                },
                {
                    header: '完整视频id：',
                    xtype: 'text',
                    dataIndex: 'previewVideoId',
                },
                {
                    header: '标清视频：',
                    dataIndex: 'videoL',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '标清视频'
                },
                {
                    header: '高清视频：',
                    dataIndex: 'videoM',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '高清视频'
                },
                {
                    header: '超清视频：',
                    dataIndex: 'videoH',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '超清视频'
                },
                {
                    header: '地图标清视频：',
                    dataIndex: 'mapVideoL',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '标清视频'
                },
                {
                    header: '地图高清视频：',
                    dataIndex: 'mapVideoM',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '高清视频'
                },
                {
                    header: '地图超清视频：',
                    dataIndex: 'mapVideoH',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '超清视频'
                },

                {
                    header: '互动视频标清视频：',
                    dataIndex: 'practiceVideoL',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '标清视频'
                },
                {
                    header: '互动视频高清视频：',
                    dataIndex: 'practiceVideoM',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '高清视频'
                },
                {
                    header: '互动视频超清视频：',
                    dataIndex: 'practiceVideoH',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '超清视频'
                },
                // {
                //     header: '技巧图片：',
                //     dataIndex: 'skillImage',
                //     xtype: Plugin('jiakao-misc!upload', {
                //         dataIndex: 'skillImage',
                //         uploadIndex: 'skillImage',
                //         bucket: "exam-room",
                //         isSingle: true,
                //         placeholder: '请选择上传文件',
                //         url: 'simple-upload3://upload/file.htm'
                //     }, function () {
                //         console.log(arguments)
                //     })
                // },
                // {
                //     header: '技巧标题：',
                //     dataIndex: 'skillTitle',
                //     xtype: 'text',
                //     maxlength: 256,
                //     placeholder: '技巧标题'
                // },
                // {
                //     header: '技巧视频：',
                //     dataIndex: 'skillVideo',
                //     xtype: Plugin('jiakao-misc!upload1', {
                //         dataIndex: 'skillVideo',
                //         uploadIndex: 'skillVideo',
                //         bucket: "exam-room",
                //         isSingle: true,
                //         placeholder: '请选择上传文件',
                //         url: 'simple-upload3://upload/file.htm'
                //     }, function () {
                //         console.log(arguments)
                //     })
                // },
                {
                    header: '视频的价格：',
                    dataIndex: 'price',
                    xtype: 'number',
                    placeholder: '视频的价格(单位分)',
                    number: true,
                    digits: true,
                    check: 'required'
                },
                {
                    header: '3D科三考场',
                    dataIndex: 'enableD3Scene',
                    xtype: 'select',
                    store: [{
                        key: false,
                        value: '不启用'
                    }, {
                        key: true,
                        value: '启用'
                    },
                    ],
                    placeholder: '3D科三考场',
                    check: 'required'
                },

            ]
        }
        Table().edit(lineData, config)
    }
    var requireRealVideo = function (videoUrl) {
        return new Promise((resolve) => {
            Store(['jiakao-misc!route-video-item/data/getRealVideoUrl'], [{
                aliases: 'realVideo',
                params: {
                    videoUrl
                }
            }]).load().then(data => {
                resolve(data.data.realVideo.data.value)
            })
        })
    }
    var list = function (panel, routeData) {
        var metaId = routeData.id || '';

        Table({
            description: '考场路线视频的路线信息列表',
            title: '考场路线视频的路线信息列表',
            search: [{
                header: '城市等级',
                dataIndex: 'cityLevel',
                xtype: Plugin('simple!auto-prompt2', {
                    store: [{
                        key: 0,
                        value: '一般'
                    }, {
                        key: 1,
                        value: '中'
                    }, {
                        key: 2,
                        value: '高'
                    }, {
                        key: 3,
                        value: '极高'
                    }],
                    dataIndex: 'cityLevel',
                    isMulti: true,
                    defaultVal: false,
                    placeholder: '请选择城市等级'
                }, function (plugin, value) {
                }),
            }, {
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
            }, {
                placeholder: '考场ID',
                dataIndex: 'metaId',
                xtype: 'text',
                value: metaId
            },
            {
                placeholder: '考场名称',
                dataIndex: 'schoolName',
                xtype: 'text'
            },
            {
                placeholder: '城市名称',
                dataIndex: 'cityName',
                xtype: 'text'
            },
            {
                placeholder: '区县名称',
                dataIndex: 'areaName',
                xtype: 'text'
            }, {
                placeholder: '地图状态',
                dataIndex: 'mapStatus',
                xtype: 'select',
                store: mapStatusStore
            },
            {
                dataIndex: 'startUpdateTime',
                xtype: 'datetime',
                placeholder: '更新开始时间'
            },
            {
                dataIndex: 'endUpdateTime',
                xtype: 'datetime',
                placeholder: '更新结束时间'
            }, {
                dataIndex: 'startVideoUpdateTime',
                xtype: 'datetime',
                placeholder: '视频更新开始时间'
            },
            {
                dataIndex: 'endVideoUpdateTime',
                xtype: 'datetime',
                placeholder: '视频更新结束时间'
            }, {
                dataIndex: 'fromCoachId',
                xtype: 'text',
                placeholder: '教练id'
            }, {
                dataIndex: 'id',
                xtype: 'text',
                placeholder: '路线id'
            }],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: function (obj) {
                        add(obj, metaId)
                    }
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '编辑',
                    class: 'warning',
                    click: function (table, dom, lineData) {
                        Store(['jiakao-misc!route-video-item/data/view?id=' + lineData.id + '&languageType=HANYU']).load([{
                            aliases: 'view'
                        }]).done(function (store, data) {
                            var listData = data.view.data;
                            editByLanguageType(table, listData, 'HANYU')
                        }).fail((err) => {
                            Widgets.dialog.alert(err.message)
                        })
                    }
                },
                {
                    name: '编辑维语',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        Store(['jiakao-misc!route-video-item/data/view?id=' + lineData.id + '&languageType=WEIYU&_lang=ug']).load([{
                            aliases: 'view'
                        }]).done(function (store, data) {
                            var listData = data.view.data;
                            editByLanguageType(table, listData, 'WEIYU')
                        }).fail((err) => {
                            Widgets.dialog.alert(err.message)
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    click(table, row, lineData) {
                        Table().edit(lineData, {
                            title: '提交审核',
                            width: 500,
                            store: 'jiakao-misc!route-video-item/data/delete',
                            success: function (obj, dialog, e) {
                                dialog.close();
                                table.render();
                            },
                            renderAfter: function (tab, dom) {
                                $(dom.item('show-group')).find('.control-label').css('width', '15%')
                                $(dom.item('reason-group')).find('.control-label').css('width', '15%')
                            },
                            columns: [{
                                dataIndex: 'id',
                                xtype: 'hidden'
                            },
                            {
                                header: '',
                                dataIndex: 'show',
                                xtype: function () {
                                    return `<div>您当前尝试操作的是一项关键运营数据，您需说明操作原因以便审核。相关操作将在<span style="color:#000;fontWeight:blod;">审核通过后生效</span></div>`
                                },
                            },
                            {
                                header: '',
                                dataIndex: 'reason',
                                xtype: 'textarea',
                                rows: 5,
                                check: 'required',
                                cols: 10,
                                maxlength: '400',
                                placeholder: '请输入操作说明供审核'
                            }
                            ]
                        })
                    }
                },
                {
                    name: '上传视频',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        var _this = $(this);
                        if (_this.hasClass('disabled')) {
                            return;
                        }
                        _this.addClass('disabled');
                        setTimeout(function () {
                            _this.removeClass('disabled');
                        }, 1000)
                        Plugin("jiakao-misc!select-file", {
                            uploadUrl: "jiakao-misc://api/admin/route-video-item/upload-video.htm",
                            lineData: lineData,
                            postData: {
                                id: lineData.id
                            },
                            complete: function () {
                                // DOM.parent().parent().parent().find('.close').click();
                                table.render();
                                // this.close();
                            }
                        }).render();
                    }
                },
                {
                    name: '上传维语视频',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        var _this = $(this);
                        if (_this.hasClass('disabled')) {
                            return;
                        }
                        _this.addClass('disabled');
                        setTimeout(function () {
                            _this.removeClass('disabled');
                        }, 1000)
                        Plugin("jiakao-misc!select-file3", {
                            uploadUrl: "jiakao-misc://api/admin/video-upload/upload-video.htm",
                            lineData: lineData,
                            postData: {
                                languageType: "WEIYU",
                                bizId: lineData.id
                            },
                            complete: function () {
                                // DOM.parent().parent().parent().find('.close').click();
                                table.render();
                                // this.close();
                            }
                        }).render();
                        // Plugin("jiakao-misc!select-single-file", {
                        //     uploadUrl: "jiakao-misc://api/admin/video-upload/upload-video.htm",
                        //     postData: {
                        //         languageType: "WEIYU",
                        //         bizId: lineData.id,
                        //         bizTypeFilterArray: ['preview_route_video', 'full_route_video']
                        //     },
                        //     limit: 300,
                        //     complete: function () {
                        //         console.log(777);
                        //         table.render();
                        //     }
                        // }).render();
                    }
                },
                {
                    name: '编辑路线',
                    class: 'primary',
                    render: function (data, tableData, index) {

                        if (tableData[index].routeMapId > 0) {
                            return '编辑路线'
                        } else {
                            return ''
                        }
                    },
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main', 'jiakao-misc!app/route-video-item/editMap'], function (Main, ModeVs) {
                            var panel = Main.panel({
                                id: 'luxian-change' + lineData.routeMapId,
                                name: '编辑路线'
                            });
                            ModeVs.edit(panel, {
                                id: lineData.routeMapId
                            });
                        })
                    }
                },
                {
                    name: '上线地图',
                    class: 'primary',
                    render: function (name, data, index) {
                        if (!data[index].routeMapStatus && data[index].routeMapId !== 0) {
                            return name
                        }
                    },
                    click: function (table, row, lineData) {
                        Simple.Store(['jiakao-misc!route-video-item-clear/data/line']).load([{
                            params: {
                                publish: true,
                                id: lineData.routeMapId
                            }
                        }]).then(function (data) {
                            table.render();
                        })
                    }

                },
                {
                    name: '下线地图',
                    class: 'danger',
                    render: function (name, data, index) {
                        if (data[index].routeMapStatus && data[index].routeMapId !== 0) {
                            return name
                        }
                    },
                    click: function (table, row, lineData) {
                        Simple.Store(['jiakao-misc!route-video-item-clear/data/line']).load([{
                            params: {
                                publish: false,
                                id: lineData.routeMapId
                            }
                        }]).then(function (data) {
                            table.render();
                        })
                    }

                },
                {
                    name: '关联教练视频ID',
                    class: 'danger',
                    xtype: 'edit',
                    title: '关联教练视频ID',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!route-video-item/data/view',
                        save: 'jiakao-misc!route-video-item/data/updateCoachVideo'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    },
                    {
                        header: '教练视频id',
                        dataIndex: 'fromUploadVideoId',
                        xtype: 'text'
                    }
                    ],
                },
                {
                    name: '开启3d科三考场',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].isEnableK3Scene == false ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认开启3d科三考场吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!route-video-item/data/enableK3Scene?id=' + lineData.id + '&isEnableK3Scene=true']).save().done(data => {
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '关闭3d科三考场',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].isEnableK3Scene == true ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认关闭3d科三考场吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!route-video-item/data/disableK3Scene?id=' + lineData.id + '&isEnableK3Scene=false']).save().done(data => {
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '设置真实考场路线id',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        const { id, metaId } = lineData;
                        Table({
                            title: '设置真实考场路线id',
                            width: 600,
                            store: `jiakao-misc!route-video-item/data/saveOuterId?id=${id}`,
                            success: function (obj, dialog, arr, data) {
                                dialog.close();
                                table.render();
                            },
                            columns: [
                                {
                                    header: '真实考场路线id',
                                    xtype: 'text',
                                    dataIndex: 'outerItemId',
                                    value: lineData?.outerItemId
                                }
                            ]
                        }).add();
                    }
                },
                {
                    name: '上传记录',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html(lineData.schoolName + lineData.name + '视频上传记录', {
                            width: 900,
                        }).done(function (dialog) {
                            Table({
                                columns: [
                                    {
                                        header: '#',
                                        dataIndex: 'id',
                                        width: 20
                                    },
                                    {
                                        header: ' 上传时间',
                                        render: function (data) {
                                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                        },
                                        dataIndex: 'uploadTime'
                                    },
                                    {
                                        header: '视频更新时间',
                                        render: function (data) {
                                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                        },
                                        dataIndex: 'transcodedTime'
                                    },
                                    {
                                        header: '操作人',
                                        dataIndex: 'createUserName'
                                    },
                                    {
                                        header: '关联教练id',
                                        dataIndex: 'coachId'
                                    },
                                    {
                                        header: '关联教练视频id',
                                        dataIndex: 'coachVideoId'
                                    },
                                    {
                                        header: ' 更新原因',
                                        dataIndex: 'reason'
                                    },
                                ]
                            }, ['jiakao-misc!route-item-upload-log/data/list?itemId=' + lineData.id], $(dialog.body), null).render();
                        })
                    }
                },
                {
                    name: '删除轨迹路线数据',
                    width: 400,
                    class: 'danger',
                    render: function (name, arr, i) {
                        return arr[i].existTrackInfo ? name : ''
                    },
                    click: function (table, row, lineData) {
                        if (!lineData.existTrackInfo) {
                            return
                        }

                        Widgets.dialog.confirm(`确定删除吗？`, function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!route-video-audit/data/removeTrackInfo?id=' + lineData.id])
                                    .save().done(function () {
                                        table.render();
                                        Widgets.dialog.alert('成功')
                                    }).fail(err => {
                                        console.log(err)
                                    })
                            }
                        })

                    }
                }
            ],
            columns: [
                {
                    header: '驾考路线ID',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '考场ID',
                    dataIndex: 'metaId'
                },
                {
                  header: '终端场地ID',
                  dataIndex: 'unifiedExamSiteId'  
                },
                {
                    header: '终端路线ID',
                    dataIndex: 'unifiedRouteId'
                },
                {
                    header: '考场名称',
                    dataIndex: 'schoolName',
                    xtype: 'text'
                },
                {
                    header: '视频名称',
                    dataIndex: 'name'
                },
                {
                    header: '标签',
                    dataIndex: "tagName"
                },

                {
                    header: '描述',
                    dataIndex: 'desc',
                    render: function (data) {
                        return '<div style="width: 200px; word-break: break-all">' + data + '</div>'
                    }
                    // render: function (data) {
                    //     if (data) {
                    //         return '<a>点击查看</a>'
                    //     }
                    // },
                    // click: function (table, row, lineData) {
                    //     Widgets.dialog.html('描述', {}).done(function (dialog) {
                    //         $(dialog.body).html('<p>' + lineData.desc + '</p>')
                    //     })
                    // }
                },
                {
                    header: '所属城市',
                    dataIndex: 'metaCityName',
                    render: function (data, all, row) {
                        return data + '/' + row.metaAreaName
                    }
                },
                {
                    header: '城市等级',
                    dataIndex: 'cityLevel',
                    render: function (data) {
                        return data == 0 ? '一般' : data == 1 ? '中' : data == 2 ? '高' : data == 3 ? '极高' : ''
                    }
                },
                {
                    header: '封面图',
                    dataIndex: 'videoImage',
                    render: function (data) {
                        if (data) {
                            return '<a><img style="width:120px;" src="' + data + '" /></a>';
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('详情image', {}).done(function (dialog) {
                            $(dialog.body).append('<img src="' + lineData.videoImage + '" />')
                        })
                    }
                },
                {
                    header: '考场路线地图id',
                    dataIndex: 'mapId'
                },
                {
                    header: '真实考场路线id',
                    dataIndex: 'outerItemId'
                },
                {
                    header: '标清试看视频',
                    dataIndex: 'previewL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {

                        requireRealVideo(lineData.previewL).then(previewL => {
                            Widgets.dialog.html('标清试看视频', {}).done(function (dialog) {
                                lineData.previewMd5Key = lineData.previewMd5Key || ''

                                $(dialog.body).html('<a href="' + previewL + '" target="_blank">' + previewL + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.previewMd5Key + '<br><video src="' + previewL + '" controls></video>')

                            })
                        })

                    }
                },
                {
                    header: '高清试看视频',
                    dataIndex: 'previewM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.previewM).then(previewM => {
                            Widgets.dialog.html('高清试看视频', {}).done(function (dialog) {
                                lineData.previewMd5Key = lineData.previewMd5Key || ''

                                $(dialog.body).html('<a href="' + previewM + '" target="_blank">' + previewM + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.previewMd5Key + '<br><video src="' + previewM + '" controls></video>')
                            })
                        })
                    }
                },
                {
                    header: '超清试看视频',
                    dataIndex: 'previewH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.previewH).then(previewH => {
                            Widgets.dialog.html('超清试看视频', {}).done(function (dialog) {
                                lineData.previewMd5Key = lineData.previewMd5Key || ''

                                $(dialog.body).html('<a href="' + previewH + '" target="_blank">' + previewH + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.previewMd5Key + '<br><video src="' + previewH + '" controls></video>')
                            })
                        })
                    }
                },
                {
                    header: '标清视频',
                    dataIndex: 'videoL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.videoL).then(videoL => {
                            Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                                lineData.videoMd5Key = lineData.videoMd5Key || ''

                                $(dialog.body).html('<a href="' + videoL + '" target="_blank">' + videoL + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.videoMd5Key + '<br><video src="' + videoL + '" controls></video>')
                            })
                        })


                    }
                },
                {
                    header: '高清视频',
                    dataIndex: 'videoM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.videoM).then(videoM => {
                            Widgets.dialog.html('高清视频', {}).done(function (dialog) {
                                lineData.videoMd5Key = lineData.videoMd5Key || ''

                                $(dialog.body).html('<a href="' + videoM + '" target="_blank">' + videoM + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.videoMd5Key + '<br><video src="' + videoM + '" controls></video>')
                            })
                        })

                    }
                },
                {
                    header: '超清视频',
                    dataIndex: 'videoH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.videoH).then(videoH => {
                            Widgets.dialog.html('超清视频', {}).done(function (dialog) {
                                lineData.videoMd5Key = lineData.videoMd5Key || ''

                                $(dialog.body).html('<a href="' + videoH + '" target="_blank">' + videoH + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.videoMd5Key + '<br><video src="' + videoH + '" controls></video>')
                            })
                        })

                    }
                },
                {
                    header: '互动视频',
                    dataIndex: 'practiceVideoM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.practiceVideoM).then(practiceVideoM => {
                            Widgets.dialog.html('超清视频', {}).done(function (dialog) {
                                lineData.practiceVideoMd5Key = lineData.practiceVideoMd5Key || ''

                                $(dialog.body).html('<a href="' + practiceVideoM + '" target="_blank">' + practiceVideoM + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.practiceVideoMd5Key + '<br><video src="' + practiceVideoM + '" controls></video>')
                            })
                        })

                    }
                },
                {
                    header: '互动视频地图',
                    dataIndex: 'mapVideoM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.mapVideoM).then(mapVideoM => {
                            Widgets.dialog.html('超清视频', {}).done(function (dialog) {
                                lineData.mapVideoMd5Key = lineData.mapVideoMd5Key || ''

                                $(dialog.body).html('<a href="' + mapVideoM + '" target="_blank">' + mapVideoM + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + lineData.mapVideoMd5Key + '<br><video src="' + mapVideoM + '" controls></video>')
                            })
                        })

                    }
                },
                // {
                //     header: '技巧图片',
                //     dataIndex: 'skillImage',
                //     render: function (data) {
                //         if (data) {
                //             return '<a><img style="width:120px;" src="' + data + '" /></a>';
                //         }
                //     },
                //     click: function (table, row, lineData) {
                //         Widgets.dialog.html('技巧图片', {}).done(function (dialog) {
                //             $(dialog.body).append('<img src="' + lineData.skillImage + '" />')
                //         })
                //     }

                // },
                // {
                //     header: '技巧标题',
                //     dataIndex: 'skillTitle'
                // },
                // {
                //     header: '技巧视频',
                //     dataIndex: 'skillVideo',
                //     render: function (data) {
                //         if (data) {
                //             return '<a><video width="100" ><source' +
                //                 ' src="' + data + '"  type="video/mp4" /></video></a>';
                //         }
                //     },
                //     click: function (table, row, lineData) {
                //         Widgets.dialog.html('技巧视频', {
                //             width: 800
                //         }).done(function (dialog) {
                //             $(dialog.body).append('<video width="800" controls="controls" autoplay="autoplay"><source' +
                //                 ' src="' + lineData.skillVideo + '"  type="video/mp4" /></video>')
                //         })
                //     }
                // },

                {
                    header: '完整视频上传状态',
                    dataIndex: 'videoStatus',
                    render: function (data) {
                        return statusMap[data];
                    }
                },
                {
                    header: '预览视频上传状态',
                    dataIndex: 'previewVideoStatus',
                    render: function (data) {
                        return statusMap[data];
                    }
                },
                {
                    header: '互动视频上传状态',
                    dataIndex: 'practiceVideoStatus',
                    render: function (data) {
                        if (data == 2) {
                            data = 3
                        }
                        return statusMap[data];
                    }
                },
                {
                    header: '互动视频地图上传状态',
                    dataIndex: 'mapVideoStatus',
                    render: function (data) {
                        if (data == 2) {
                            data = 3
                        }
                        return statusMap[data];
                    }
                },
                {
                    header: '维语试看视频上传状态',
                    dataIndex: 'weiyuPreviewVideoStatus',
                    render: function (data) {
                        return statusMap[data];
                    }
                },
                {
                    header: '维语完整视频上传状态',
                    dataIndex: 'weiyuVideoStatus',
                    render: function (data) {
                        return statusMap[data];
                    }
                },
                {
                    header: '维语试看视频',
                    dataIndex: 'weiyuPreviewVideoM',
                    render: function (data) {
                        return data ? `<a>点击查看</a>` : ''
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.weiyuPreviewVideoM).then(weiyuPreviewVideoM => {
                            Widgets.dialog.html('维语试看视频', {}).done(function (dialog) {
                                $(dialog.body).html('<a href="' + weiyuPreviewVideoM + '" target="_blank">' + weiyuPreviewVideoM + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + (lineData.weiyuPreviewVideoKey || '') + '<br><video src="' + weiyuPreviewVideoM + '" controls></video>')

                            })
                        })

                    }
                },
                {
                    header: '维语完整视频',
                    dataIndex: 'weiyuVideoM',
                    render: function (data) {
                        return data ? `<a>点击查看</a>` : ''
                    },
                    click: function (table, row, lineData) {
                        requireRealVideo(lineData.weiyuVideoM).then(weiyuVideoM => {
                            Widgets.dialog.html('维语完整视频', {}).done(function (dialog) {
                                $(dialog.body).html('<a href="' + weiyuVideoM + '" target="_blank">' + weiyuVideoM + '</a><br>考场名称: ' + lineData.schoolName + '<br>视频名称: ' + lineData.name + '<br>视频编号: ' + (lineData.weiyuVideoKey || '') + '<br><video src="' + weiyuVideoM + '" controls></video>')

                            })
                        })

                    }
                },
                {
                    header: '维语完整视频更新时间',
                    dataIndex: 'weiyuVideoFinshTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },


                {
                    header: '视频的价格，单位分',
                    dataIndex: 'price'
                },
                {
                    header: '教练上传视频id',
                    dataIndex: 'fromUploadVideoId'
                },
                {
                    header: '教练id',
                    dataIndex: 'fromCoachId'
                },
                {
                    header: '适合车型',
                    dataIndex: 'carGear',
                    render: function (data) {
                        return data && data.toString()
                    },
                },
                {
                    header: '3D科三考场',
                    dataIndex: 'enableD3Scene',
                    render: function (data) {
                        return data ? '启用' : '不启用'
                    }
                },
                // {
                //     header: '正在上传的教练视频ID',
                //     dataIndex: 'videoKey',
                //     render: function (data) {
                //         return data && JSON.parse(data).coachRouteVideoId;
                //     }
                // },
                {
                    header: '真实考场资源id',
                    dataIndex: 'outerItemId'
                },
                {
                    header: '排序',
                    dataIndex: 'order',

                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '视频更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'videoUpdateTime'
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!route-video-item/data/list2?metaId=' + metaId], panel, null).render();
    }

    return {
        list: list
    }

});