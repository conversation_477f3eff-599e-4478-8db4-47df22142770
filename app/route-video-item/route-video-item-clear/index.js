/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {


    var addMapFile = function (table) {
        Table({
            title: '导入',
            width: 500,
            store: 'jiakao-misc!route-video-item-clear/data/uploadMap',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '选择文件：',
                dataIndex: 'data',
                xtype: 'file'
            }]
        }).add();
    }

    var list = function (panel,params) {
        Table({
            title: '考场路线清洗',
            search: [
                {
                    placeholder: '考场名称',
                    dataIndex: 'placeName',
                    xtype: 'text'
                },
                {
                    placeholder: '城市名称',
                    dataIndex: 'cityName',
                    xtype: 'text'
                },
                {
                    placeholder: '清洗状态',
                    dataIndex: 'clearStatus',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择清洗状态'
                        },
                        {
                            key: 1,
                            value: '未清洗'
                        },
                        {
                            key: 2,
                            value: '已清洗'
                        }
                    ]
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (obj) {
                            addMapFile()
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class:'warning',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main', 'jiakao-misc!app/route-video-item/editMap'], function (Main, ModeVs) {
                            var panel = Main.panel({
                                id: 'luxian-change' + lineData.id,
                                name: '编辑路线'
                            });
                            ModeVs.edit(panel, { id: lineData.id, selectKaochang: true });
                        })
                    }
                   
                },
                
              
            ], 
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '省份',
                    dataIndex: 'provinceName',
                    xtype: 'text'
                },
                {
                    header: '市',
                    dataIndex: 'cityName'
                },
                {
                    header: '考场',
                    dataIndex: 'placeName',
                },
                {
                    header: '线路',
                    dataIndex: 'routeName',
                },
                {
                    header: '上线状态',
                    dataIndex: 'status',
                    render: function(table, row, lineData) {
                        if (lineData.status == 0){
                            return '未发布'
                        }
                        if (lineData.status == 1) {
                            return '测试发布'
                        }
                        if (lineData.status == 2) {
                            return '正式发布'
                        }
                    }
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName',
                },
                {
                    header: '操作时间',
                    dataIndex: 'updateTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                },
            ]
        }, ['jiakao-misc!route-video-item-clear/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
