/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var appPriceArr = [{
        key: 4000,
        value: 4000
    }, {
        key: 2500,
        value: 2500
    }, {
        key: 5000,
        value: 5000
    }, {
        key: 3000,
        value: 3000
    }, {
        key: 2800,
        value: 2800
    }, {
        key: 4500,
        value: 4500
    }, {
        key: 1800,
        value: 1800
    }, {
        key: 7800,
        value: 7800
    }, {
        key: 9800,
        value: 9800
    }, {
        key: 12800,
        value: 12800
    }, {
        key: 25800,
        value: 25800
    }, {
        key: 34800,
        value: 34800
    }]

    var appPriceIdMap = {
        4000: 'session_diamond_kemu3200',
        2500: 'session_diamond_250',
        5000: 'session_diamond_500',
        3000: 'session_diamond_300',
        2800: 'session_diamond_280',
        4500: 'session_diamond_450',
    }
    var mapStatusStore = [{
        key: '',
        value: '地图状态'
    },
    {
        key: '1',
        value: '没地图'
    },
    {
        key: '2',
        value: '有地图没上线'
    },
    {
        key: '3',
        value: '有地图且上线'
    },
    ]
    var add = function (table) {
        Table({
            title: '添加',
            width: 700,
            store: 'jiakao-misc!route-video-meta/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {

                    const image = JSON.parse(form.image.value || '[]');

                    const params = {
                        imageEncodedData: image[0]?.encode,
                    }

                    return params
                },
            },
            renderAfter: function (tab, dom, lineData) {
                dom.item('applePrice').on('change', function () {
                    dom.item('applePriceId').val(appPriceIdMap[$(this).val()])
                }).trigger('change')
            },
            columns: [{
                header: '考场名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 32,
                placeholder: '视频名称'
            },


            // {
            //     header: '考场的城市：',
            //     dataIndex: 'cityCode',
            //     xtype: 'text',
            //     placeholder: '考场的城市'
            // },

            {
                header: '考场的城市：',
                xtype: Plugin('jiakao-misc!select-district', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {
                    // if(['120102','110000','500000','310000'].indexOf(code)>-1){
                    //     console.log(arguments)
                    //     plugin.config.target.parent('.col-sm-8').find('.col-sm-6').eq(1).css('display','none')
                    // }else{
                    //     plugin.config.target.parent('.col-sm-8').find('.col-sm-6').eq(1).css('display','block')
                    // }


                }),
                dataIndex: 'cityCode'
            },
            {
                header: '封面图：',
                dataIndex: 'image',
                xtype: Simple.Plugin('simple!jiakao-upload', {
                    useSecureUpload: true,
                    bucket: 'jiakao-image',
                    prefix: 'jiakao-image',
                    // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                    getAppSpaceId: 'jiakao-misc!common/data/getAppSpaceId',
                    imageSrc: {
                        // 必填。
                        dataIndex: 'encode',
                    },
                    parse: function (value) {
                        return [
                            {
                                encode: value
                            }
                        ]
                    }
                })
            },
            {
                header: '价格：',
                dataIndex: 'price',
                xtype: 'text',
                placeholder: '单位（分）',
                value: 4000
            },
            {
                header: '苹果价格：',
                dataIndex: 'applePrice',
                xtype: 'text',
                // store: appPriceArr,
                placeholder: '单位（分）',
                value: 4000
            },
            {
                header: '苹果价格ID：',
                dataIndex: 'applePriceId',
                xtype: 'text',
                required: true,
                placeholder: '苹果价格ID',
                value: 'session_diamond_kemu3200'
            },
            {
                header: '3d单包对应的苹果价格id:',
                dataIndex: 'd3ApplePriceId',
                xtype: 'text',
                required: true,
                placeholder: '3d单包对应的苹果价格id',
                value: 'session_3D_diamond_400'
            },
            {
                header: '3D科三考场',
                dataIndex: 'enableD3Scene',
                xtype: 'select',
                store: [{
                    key: false,
                    value: '不启用'
                }, {
                    key: true,
                    value: '启用'
                },
                ],
                placeholder: '3D科三考场',
                check: 'required'
            },

            ]
        }).add();
    }
    var addCity = function (table) {
        Table({
            title: '添加',
            width: 600,
            store: 'jiakao-misc!route-video-meta/data/addCity',
            success: function (obj, dialog, arr, data) {
                dialog.close();
                // getCityConfig()
            },
            columns: [{
                header: '考场的城市：',
                xtype: Plugin('jiakao-misc!select-district', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
                dataIndex: 'cityCode'
            }

            ]
        }).add();
    }

    var setBizIdOrKey = function (table, lineData, type) {
        Table({
            title: '设置路考仪考场' + type,
            width: 600,
            store: 'jiakao-misc!route-video-meta/data/' + (type == 'id' ? 'saveOuterId' : 'saveOuterKey'),
            success: function (obj, dialog, arr, data) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: 'id',
                    xtype: 'hidden',
                    dataIndex: 'id',
                    value: lineData && lineData.id
                }
            ].concat(type == "id" ? [
                {
                    header: '路考仪考场id: ',
                    xtype: 'text',
                    dataIndex: 'bizId',
                    value: lineData && lineData.bizId
                }
            ] : [
                {
                    header: '路考仪考场key: ',
                    xtype: 'text',
                    dataIndex: 'outerKey',
                    value: lineData && lineData.outerKey
                }
            ])
        }).add();
    }
    var delCity = function (table) {
        Table({
            title: '添加',
            width: 600,
            store: 'jiakao-misc!route-video-meta/data/delCity',
            success: function (obj, dialog, arr, data) {
                dialog.close();
                getCityConfig()
            },
            columns: [{
                header: '考场的城市：',
                xtype: Plugin('jiakao-misc!select-district', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
                dataIndex: 'cityCode'
            }

            ]
        }).add();
    }

    var getCityConfig = function () {
        Widgets.dialog.html('已选城市').done(function (dialog) {
            Table({
                description: '',
                title: '',
                buttons: {
                    top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加城市',
                        class: 'primary',
                        click: addCity
                    }

                    ],
                    bottom: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }]
                },
                columns: [{
                    header: '城市名称',
                    dataIndex: 'name'
                }],
                operations: [{
                    name: '上线',
                    class: 'success',
                    render: function (name, arr, index) {
                        console.log(arguments)
                        if (arr[index].value === 'true') {
                            return '';
                        }
                        return name;
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确定上线吗？', function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!route-video-meta/data/updateCity']).save([{
                                    params: {
                                        id: lineData.id,
                                        value: 'true'
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
                {
                    name: '下线',
                    class: 'warning',
                    render: function (name, arr, index) {
                        if (arr[index].value === 'false') {
                            return '';
                        }
                        return name;
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确定上线吗？', function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!route-video-meta/data/updateCity']).save([{
                                    params: {
                                        id: lineData.id,
                                        value: 'false'
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确定删除吗？', function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!route-video-meta/data/delCity']).save([{
                                    params: {
                                        cityCode: lineData.key
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                }
                ]
            }, ['jiakao-misc!route-video-meta/data/getCityConfig'], dialog.body, function () {
                $(dialog.body).item('download-data').remove()
            }).render();
        })
    }
    var editByLanguageType = function (table, lineData = {}, languageType) {
        var config = {
            title: languageType === 'WEIYU' ? '编辑维语' : '编辑',
            width: 600,
            store: 'jiakao-misc!route-video-meta/data/update' + (languageType === 'WEIYU' ? '?_lang=ug' : ''),
            success: function (obj, dialog, e) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {

                    const image = JSON.parse(form.image.value || '[]');

                    const params = {
                        imageEncodedData: image[0]?.encode,
                    }

                    return params
                },
            },
            renderAfter: function (tab, $form) {
                $form.item('applePrice').on('change', function () {
                    $form.item('applePriceId').val(appPriceIdMap[$(this).val()])
                }).trigger('change')

                if (lineData.imageEncodedData) {
                    setTimeout(() => {
                        $form.item('image-group').find("input[name=image]").val(JSON.stringify([{ encode: lineData.imageEncodedData }]))
                    }, 300)
                } else if (lineData.image) {
                    setTimeout(() => {
                        $form.item('image-group').find("input[name=image]").val(lineData.image)
                    }, 500)
                }
            },
            columns: [
                {
                    dataIndex: 'languageType',
                    xtype: 'hidden',
                    value: languageType
                },
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '考场名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '视频名称'
                },
                {
                    header: '考场的城市：',
                    dataIndex: 'areaCode',
                    xtype: Plugin('jiakao-misc!select-district', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {
                        // if(['120102','110000','500000','310000'].indexOf(code)>-1){
                        //     console.log(arguments)
                        //     plugin.config.target.parent('.col-sm-8').find('.col-sm-6').eq(1).css('display','none')
                        // }else{
                        //     plugin.config.target.parent('.col-sm-8').find('.col-sm-6').eq(1).css('display','block')
                        // }


                    }),

                },
                {
                    header: '封面图：',
                    dataIndex: 'image',
                    xtype: Simple.Plugin('simple!jiakao-upload', {
                        useSecureUpload: true,
                        bucket: 'jiakao-image',
                        prefix: 'jiakao-image',
                        // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                        getAppSpaceId: 'jiakao-misc!common/data/getAppSpaceId',
                        imageSrc: {
                            // 必填。
                            dataIndex: 'encode',
                        },
                        parse: function (value) {
                            return [
                                {
                                    encode: value
                                }
                            ]
                        }
                    })
                },
                {
                    header: '价格：',
                    dataIndex: 'price',
                    xtype: 'text',
                    placeholder: '单位（分）'
                },
                {
                    header: '苹果价格：',
                    dataIndex: 'applePrice',
                    xtype: 'text',
                    // store: appPriceArr,
                    placeholder: '单位（分）'
                },
                {
                    header: '苹果价格ID：',
                    dataIndex: 'applePriceId',
                    xtype: 'text',
                    required: true,
                    placeholder: '苹果价格ID'
                },
                {
                    header: '3d单包对应的苹果价格id:',
                    dataIndex: 'd3ApplePriceId',
                    xtype: 'text',
                    required: true,
                    placeholder: '3d单包对应的苹果价格id'
                },
                {
                    header: '3D科三考场',
                    dataIndex: 'enableD3Scene',
                    xtype: 'select',
                    store: [{
                        key: false,
                        value: '不启用'
                    }, {
                        key: true,
                        value: '启用'
                    },
                    ],
                    placeholder: '3D科三考场',
                    check: 'required'
                },

            ]
        }
        Table().edit(lineData, config)
    }

    var list = function (panel, configData) {
        var cityCode = configData.cityCode || '';
        var cityName = configData.name || '';
        Table({
            description: '考场路线视频的考场信息列表',
            title: '考场路线视频的考场信息列表',
            search: [{
                header: '城市等级',
                dataIndex: 'cityLevel',
                xtype: Plugin('simple!auto-prompt2', {
                    store: [{
                        key: 0,
                        value: '一般'
                    }, {
                        key: 1,
                        value: '中'
                    }, {
                        key: 2,
                        value: '高'
                    }, {
                        key: 3,
                        value: '极高'
                    }],
                    dataIndex: 'cityLevel',
                    isMulti: true,
                    defaultVal: false,
                    placeholder: '请选择城市等级'
                }, function (plugin, value) {
                }),
            }, {
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
            },
            {
                xtype: 'text',
                dataIndex: 'id',
                placeholder: '考场ID'
            },
            {
                xtype: 'text',
                dataIndex: 'name',
                placeholder: '考场名称'
            },
            {
                xtype: 'text',
                dataIndex: 'cityName',
                placeholder: '城市名称',
                value: cityName
            },
            {
                xtype: 'text',
                dataIndex: 'areaName',
                placeholder: '区域名称'
            }, {
                placeholder: '地图状态',
                dataIndex: 'mapStatus',
                xtype: 'select',
                store: mapStatusStore
            }, {
                placeholder: '考场状态',
                dataIndex: 'status',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择'
                }, {
                    key: 1,
                    value: '正常'
                }, {
                    key: -1,
                    value: '下架'
                }]
            }, {
                dataIndex: 'createTimeStart',
                xtype: 'date',
                placeholder: '开始时间'
            },
            {
                dataIndex: 'createTimeEnd',
                xtype: 'date',
                placeholder: '结束时间'
            }
            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                },
                // {
                //     name: '查看城市配置',
                //     class: 'warning',
                //     click: getCityConfig
                // },
                // {
                //     name: '添加城市',
                //     class: 'info',
                //     click: addCity
                // },
                // {
                //     name: '删除城市',
                //     class: 'danger',
                //     click: delCity
                // },
                {
                    name: '',
                    class: 'warning',
                    click: function (table) {
                        var selectCity = Plugin('simple!select-city', {
                            // countries: true
                        });
                        var showCityList = function (list) {
                            list = list || [];
                            cityList.empty();
                            for (var i = 0; i < list.length; i++) {
                                $('<div class="btn-group btn-group-xs" style="margin: 0 5px 5px 0;"><button type="button" class="btn btn-default">' + list[i].name + '</button><button type="button" data-item="close" class="btn btn-primary">&times;</button></div>').appendTo(cityList).item('close').on('click', {
                                    code: list[i].code
                                }, function (e) {
                                    for (var i = 0; i < data.cityCode.itemList.length; i++) {
                                        if (data.cityCode.itemList[i].code === e.data.code) {
                                            data.cityCode.itemList.splice(i, 1);
                                            i--;
                                        }
                                    }
                                    $(this).parent().remove();
                                });
                            }
                        };

                        // selectCity.render({value: []}).done(function (obj, value) {
                        //     console.log(arguments)
                        //     Store(['jiakao-misc!route-video-meta/data/setEntrance']).load([{
                        //         params: {
                        //             cityList: JSON.stringify(value)
                        //         }
                        //     }]).done(function (store, data) {
                        //         table.render();
                        //     })
                        // });

                        Store(['jiakao-misc!route-video-meta/data/getEntrance']).load([{
                            aliases: 'list'
                        }]).done(function (store, data) {
                            var list = data.list.data;
                            if (list) {
                                list = JSON.parse(list);
                            } else {
                                list = [];
                            }

                            selectCity.render({
                                value: list
                            }).done(function (obj, value) {
                                Store(['jiakao-misc!route-video-meta/data/setEntrance']).save([{
                                    params: {
                                        cityList: JSON.stringify(value)
                                    }
                                }]).done(function (store, data) {
                                    table.render();
                                })
                            });
                        })

                    }
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '编辑',
                    class: 'warning',
                    click: function (table, dom, lineData) {
                        Store(['jiakao-misc!route-video-meta/data/view?id=' + lineData.id + '&languageType=HANYU']).load([{
                            aliases: 'view'
                        }]).done(function (store, data) {

                            var listData = data.view.data;
                            editByLanguageType(table, listData, 'HANYU')
                        }).fail((err) => {
                            Widgets.dialog.alert(err.message)
                        })
                    }
                },
                {
                    name: '编辑维语',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        Store(['jiakao-misc!route-video-meta/data/view?id=' + lineData.id + '&languageType=WEIYU&_lang=ug']).load([{
                            aliases: 'view'
                        }]).done(function (store, data) {
                            var listData = data.view.data;
                            editByLanguageType(table, listData, 'WEIYU')
                        }).fail((err) => {
                            Widgets.dialog.alert(err.message)
                        })
                    }
                },
                {
                    name: '上架',
                    class: 'info',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认上架吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!route-video-meta/data/updateStatus?status=1&id=' + lineData.id]).load().done(function () {
                                    table.render()
                                }).fail(err => {
                                    if (err.message != null) {
                                        Widgets.dialog.alert(err.message);
                                    } else {
                                        Widgets.dialog.alert('接口请求失败...');

                                    }
                                })

                            }
                        })

                    },
                    render: function (name, arr, index) {
                        if (arr[index].status == 1) {
                            return ''
                        } else {
                            return name;
                        }
                    }

                },
                {
                    name: '下架',
                    class: '',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认下架吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!route-video-meta/data/updateStatus?status=-1&id=' + lineData.id]).load().done(function () {
                                    table.render();
                                }).fail(err => {
                                    if (err.message != null) {
                                        Widgets.dialog.alert(err.message);
                                    } else {
                                        Widgets.dialog.alert('接口请求失败...');

                                    }
                                })

                            }
                        })
                    },
                    render: function (name, arr, index) {
                        if (arr[index].status == -1) {
                            return ''
                        } else {
                            return name;
                        }
                    }

                },
                {
                    name: '删除',
                    class: 'danger',
                    click(table, row, lineData) {
                        Table().edit(lineData, {
                            title: '确认删除 “' + table.config.columns[0].header + '” 为 “' + lineData[table.config.columns[0].dataIndex] + '” 的信息吗？',
                            width: 500,
                            store: 'jiakao-misc!route-video-meta/data/delete',
                            success: function (obj, dialog, e) {
                                dialog.close();
                                table.render();
                            },
                            columns: [{
                                dataIndex: 'id',
                                xtype: 'hidden'
                            },
                            {
                                header: '删除原因',
                                dataIndex: 'reason',
                                xtype: 'text',
                                check: 'required',
                                placeholder: '请填写删除原因'
                            }]
                        })
                    }
                },
                {
                    name: '开启3d科三考场',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].isEnableK3Scene == false ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认开启3d科三考场吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!route-video-meta/data/enableK3Scene?id=' + lineData.id + '&isEnableK3Scene=true']).save().done(data => {
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '关闭3d科三考场',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].isEnableK3Scene == true ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认关闭3d科三考场吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!route-video-meta/data/disableK3Scene?id=' + lineData.id + '&isEnableK3Scene=false']).save().done(data => {
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '设置路考仪考场id',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        setBizIdOrKey(table, lineData, 'id')
                    }
                },
                {
                    name: '设置路考仪考场key',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        setBizIdOrKey(table, lineData, 'key')
                    }
                },
            ],
            columns: [{
                header: '考场id',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '考场名称',
                dataIndex: 'name',
                render: function (data) {
                    return '<a>' + data + '</a>'
                },
                click: function (table, row, lineData) {
                    require(['simple!app/layout/main'], function (Main) {
                        var nPanel = Main.panel('route-video-item-' + lineData.id);
                        if (nPanel.length == 0) {
                            nPanel = Main.panel({
                                id: 'route-video-item-' + lineData.id,
                                name: lineData.name + '-路线'
                            })
                        }
                        require(['jiakao-misc!app/route-video-item/index'], function (Item) {
                            Item.list(nPanel, lineData)
                        })
                    });
                }
            },
            {
                header: '考场的城市',
                dataIndex: 'cityName'
            },
            {
                header: '城市等级',
                dataIndex: 'cityLevel',
                render: function (data) {
                    return data == 0 ? '一般' : data == 1 ? '中' : data == 2 ? '高' : data == 3 ? '极高' : ''
                }
            },
            {
                header: '考场的区域',
                dataIndex: 'areaName'
            },
            {
                header: '封面图',
                dataIndex: 'image',
                render: function (data) {
                    return '<a><img src="' + data + '" style="width:30px; height:auto;"  /></a>'
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('封面图', { width: 1200 }).done(function (dialog) {
                        $(dialog.body).html('<img src="' + lineData.image + '"></img>')
                    })

                }
            },
            {
                header: '价格',
                dataIndex: 'price',
                render: function (data) {
                    return data ? data / 100 + '元' : 0
                }
            },
            {
                header: '苹果价格',
                dataIndex: 'applePrice',
                render: function (data) {
                    return data ? data / 100 + '元' : 0
                }
            },
            {
                header: '苹果ID',
                dataIndex: 'applePriceId'
            },
            {
                header: '3d单包对应的苹果价格id',
                dataIndex: 'd3ApplePriceId'
            },
            {
                header: '考场状态',
                dataIndex: 'status',
                render: function (data) {
                    if (data == 1) {
                        return '正常'
                    } else if (data == -1) {
                        return '下架'
                    }
                }

            },
            {
                header: '3D科三考场',
                dataIndex: 'enableD3Scene',
                render: function (data) {
                    return data ? '启用' : '不启用'
                }
            },
            {
                header: '路考仪考场id',
                dataIndex: 'bizId'
            },
            {
                header: '路考仪考场key',
                dataIndex: 'outerKey'
            },
            {
                header: '公共考场库id',
                dataIndex: 'jiaxiaoSceneId'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '更新时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '更新人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!route-video-meta/data/list2?cityCode=' + cityCode], panel, null).render();
    }

    return {
        list: list
    }

});
