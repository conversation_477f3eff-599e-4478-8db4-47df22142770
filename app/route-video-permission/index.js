/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/plugin', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Plugin, Form) {

    var selectCity = function (table, line, data) {
        Table({
            title: '修改城市',
            width: 500,
            store: 'jiakao-misc!route-video-permission/data/city',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden',
                    value: data.id
                },{
                    header: '城市：',
                    xtype: Plugin('jiakao-misc!select-district', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                    dataIndex: 'cityCode'
                },

            ]
        }).add();
    }
    var selectPlace = function (table, line, data) {
        Table({
            title: '修改考场',
            width: 500,
            store: 'jiakao-misc!route-video-permission/data/place',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden',
                    value: data.id
                },{
                    header: '考场Id：',
                    xtype: 'text',
                    dataIndex: 'placeId'
                },

            ]
        }).add();
    }
    

    var add = function (table) {
        Table({
            title: '添加',
            width: 680,
            store: 'jiakao-misc!route-video-permission/data/bind',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '用户id：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    placeholder: '用户id'
                },
                {
                    header: '订单号：',
                    dataIndex: 'orderNumber',
                    xtype: 'text',
                    placeholder: '订单号'
                }, {
                    header: '业务ID：',
                    dataIndex: 'bizId',
                    xtype: 'text',
                    placeholder: '业务ID'
                }, {
                    header: '业务ID类型：',
                    dataIndex: 'bizIdType',
                    xtype: 'select',
                    store: 'jiakao-misc!route-video-permission/data/bizIdTypeList'

                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '考场路线视频的权限列表',
            title: '考场路线视频的权限列表',
            search: [{
                    dataIndex: 'userId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '用户id',
                    value:hashParams.mucangId
                },
                {
                    dataIndex: 'routeId',
                    xtype: 'text',
                    placeholder: '视频id'
                },
                {
                    dataIndex: 'startTime',
                    xtype: 'datetime',
                    placeholder: '开始时间'
                },
                {
                    dataIndex: 'endTime',
                    xtype: 'datetime',
                    placeholder: '结束时间'
                }
            ],
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    // {
                    //     name: '添加',
                    //     class: 'primary',
                    //     click: add
                    // }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!route-video-permission/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '有权限的路线视频id：',
                            dataIndex: 'routeId'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                //    {
                //        name: '编辑',
                //        xtype: 'edit',
                //        width: 500,
                //        class: 'warning',
                //        title: '编辑',
                //        success: function(obj, dialog, e) {
                //            dialog.close();
                //            obj.render();
                //        },
                //        store: {
                //            load: 'jiakao-misc!route-video-permission/data/view',
                //            save: 'jiakao-misc!route-video-permission/data/update'
                //        },
                //        columns: [
                //            {
                //                dataIndex: 'id',
                //                xtype: 'hidden'
                //            },
                // {
                //     header: '用户id：',
                //     dataIndex: 'userId',
                //     xtype: 'text',
                //     maxlength: 64,
                //     placeholder: '用户id'
                // },
                // {
                //     header: '有权限的路线视频id：',
                //     dataIndex: 'routeId',
                //     xtype: 'text',
                //     placeholder: '有权限的路线视频id'
                // },
                // {
                //     header: 'updateTime：',
                //     dataIndex: 'updateTime',
                //     xtype: 'date',
                //     placeholder: 'updateTime'
                // }
                //
                //        ]
                //    },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!route-video-permission/data/delete'
                },
                {
                    name: '更改城市',
                    class: 'warning',
                    render: function (name, arr, i) {
                        return arr[i].bizIdType == 'city' ? name : '';
    
                    },
                    click: selectCity
                },
                {
                    name: '更改考场',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].bizIdType == 'route_place_id' ? name : '';
    
                    },
                    click: selectPlace
                },
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户id',
                    dataIndex: 'userId'
                },
                {
                    header: '业务ID的类型',
                    dataIndex: 'bizIdType'
                },
                {
                    header: '有权限的路线视频id',
                    dataIndex: 'routeId'
                },
                {
                    header: '城市/考场名',
                    dataIndex: 'name'
                },
                {
                    header: '过期时间',
                    dataIndex: 'expireTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, [`jiakao-misc!route-video-permission/data/list?userId=${hashParams.mucangId ? hashParams.mucangId : -1}`], panel, null).render();
    }

    return {
        list: list
    }

});