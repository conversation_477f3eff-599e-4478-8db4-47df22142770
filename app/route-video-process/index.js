/*
 * index v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form','simple!core/plugin','jiakao-misc!app/common/download','simple!app/layout/main',], function(Template, Table, Utils, Widgets, Store, Form,Plugin,Download,Layout) {
    // const processStatusMap = {
    //     0: '初始上传',
    //     1: '预处理中',
    //     2: '预处理完成',
    //     10: '初剪上传中',
    //     11: '初剪上传完成',
    //     20: '开始制作中/重新制作中',
    //     21: '制作完成',
    //     42: '制作失败',
    //     30: '上传小地图',
    //     31: '小地图上传完成',
    //     40: '成片制作中',
    //     41: '成片制作完成'
    // };
    const processStatusMap = {
        0: '待处理',
        3: '已配置',
        4: '已上传讲解',
        31: '小地图上传完成',
        40: '成片制作中',
        41: '成片制作完成',
        42: '成片制作失败',
    };
    const processStatusArray = []
    for (let key in processStatusMap) {
        processStatusArray.push({ key: key, value: processStatusMap[key] })
    }
    const routeCarTypeMap = {
        1: '手动',
        2: '自动',
        3: '手动和自动'
    }
    var assRespose = function (table, selectorArr=[],hasSelectArr){
       Table({
           title:'批处理关联责任人',
           width: 500,
           store: 'jiakao-misc!route-video-process/data/updateLiableUser',
           success: function (obj, dialog) {
               dialog.close();
               table.render();
           },
           form: {
               submitHandler(form) {
                 return {
                     idList: selectorArr.join(',')
                 }

               },
               reverseParam: true
           },
           columns:[
              {
                header:'',
                xtype: function () {
                    return `已勾选<span style="color:red;font-weight:blod;">${selectorArr.length}</span>项数据，其中<span style="color:red;font-weight:blod;">${hasSelectArr.length}</span>项数据已有责任人。保存后将覆盖。`
                }
              },
               {
                   dataIndex: 'liableUserId',
                   xtype: 'hidden',
               },
               {
                   header:'责任人：',
                   dataIndex: 'liableUserName',
                   xtype: Plugin('simple!auto-prompt2', {
                       store: [],
                       url: 'jiakao-misc!route-video-process/data/listUsers?withFired=false',
                       dataIndex: 'liableUserName',
                       index: {
                           key: 'nickname',
                           value: 'nickname',
                           search: 'nickname'
                       },
                       check: 'required',
                       online: true,
                       isMulti: false,
                       value: ''
                   }, function (plugin, value, obj) {
                       var form = plugin.config.target.closest('[data-item=body]');
                       form.item('liableUserId').val(obj.userId || '');
                   })
               },


           ]
       }).add()
    }
    var list = function(panel) {
        Table({
            description: '初剪视频管理',
            title: '初剪视频管理',
            searchReset: {
                class: 'danger'
            },
            search: [
                {
                    dataIndex: 'liableUserId',
                    xtype: 'hidden',
                    minWidth: '0'
                },
                {

                    dataIndex: 'liableUserName',
                    xtype: Plugin('simple!auto-prompt2', {
                        store: [],
                        url: 'jiakao-misc!route-video-process/data/listUsers?withFired=false',
                        dataIndex: 'liableUserName',
                        index: {
                            key: 'nickname',
                            value: 'nickname',
                            search: 'nickname'
                        },

                        online: true,
                        isMulti: false,
                        value: '',
                        placeholder: '请输入责任人'
                    }, function (plugin, value, obj) {
                        var values = $(panel).find('div[data-item="liableUserId-group"]').find('input[data-item="liableUserId"]')
                        values.val(obj.userId || '')
                    })
                },
                {
                    header: '制作进度',
                    xtype: 'select',
                    dataIndex: 'processStatus',
                    store: [{ key: '', value: '请选择制作进度' }].concat(processStatusArray)
                },
                {
                    dataIndex:'name',
                    xtype:'text',
                    placeholder:'责任人名称'
                },
                {
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name:'请选择区县'
                        }]
                    }
                })
            }, {
                header: '教练上传视频ID',
                xtype: 'text',
                dataIndex:'coachVideoId',
                placeholder:'教练上传视频ID'
            }, {
                header: '教练id',
                xtype: 'text',
                dataIndex:'coachId',
                placeholder:'教练id'
            }, {
                header: '考场名称',
                xtype: 'text',
                dataIndex:'sceneName',
                placeholder:'考场名称'
            }, {
                header: '考场key',
                xtype: 'text',
                dataIndex:'sceneKey',
                placeholder:'考场key'
            }, {
                header: '城市名称',
                xtype: 'text',
                dataIndex:'cityName',
                placeholder:'城市名称'
            },  {
                dataIndex: 'startTime',
                xtype: 'date',
                placeholder: '开始时间'
            },
            {
                dataIndex: 'endTime',
                xtype: 'date',
                placeholder: '结束时间'
            }],
           
            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '批量关联责任人',
                        class: 'primary',
                        click: function (table, init, data, selectorArr) {
                            console.log('selectorArr', selectorArr)
                            var hasSelectArr = []
                            var dataArray = data.data||[]
                            dataArray.forEach(function(res){
                                console.log('res.liableUserId+', res.liableUserId + '')
                                if (selectorArr.indexOf(res.id + '') !== -1 && res.liableUserId){
                                    hasSelectArr.push(res)
                                }
                            })
                            console.log(arguments)
                            if(selectorArr.length<=0){
                                Widgets.dialog.alert('请选择需要关联责任人的数据项')
                                return 
                            }
                            assRespose(table, selectorArr,hasSelectArr) 
                        }
                    },
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            selector: {
                dataIndex: 'id',
            },
            operations: [
                {
                    name: '编辑片头',
                    click: function (table, dom, lineData) {
                        Store([`jiakao-misc!route-video-process/data/view?id=${lineData.id}`], [{
                            aliases: 'list'
                        }]).load().done((store) => {
                            const data = store.data.list.data;

                            require(['jiakao-misc!app/edit-video/index'], function (demo) {
                                var panelId = 'edit-video-header' + data.id;
                                var viewpanel = Layout.panel(panelId);
                                if (viewpanel.length === 0) {
                                    viewpanel = Layout.panel({
                                        id: panelId,
                                        name: '编辑片头'
                                    });
                                }
                            
                                demo.list(
                                    viewpanel,
                                    {
                                        title: '编辑片头',
                                        id: data.id,
                                        sceneName: data.sceneName,
                                        subSceneName: data.subSceneName,
                                        routeName: data.routeName,
                                        vehicleModel: data.vehicleModel,
                                        routeExplainFileUrl: data.routeExplainFileUrl,
                                        routeExplainImgUrl: data.routeExplainImgUrl,
                                        openingCover: data.openingCover,
                                        lightExamItems: data.lightExamItems,
                                        lightTemplate: data.lightTemplate,
                                        lightTipText: data.lightTipText,
                                        lightExamJson: data.lightExamJson,
                                        originalVideoUrl: data.originalVideoUrl
                                    }
                                )
                            })
                        })
                            
                    }
                },
                {
                    name: '编辑片尾',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        Store([`jiakao-misc!route-video-process/data/view?id=${lineData.id}`], [{
                            aliases: 'list'
                        }]).load().done((store) => {
                            const data = store.data.list.data;

                            require(['jiakao-misc!app/edit-video/index'], function (demo) {
                                    var panelId = 'edit-video-footer' + data.id;
                                    var viewpanel = Layout.panel(panelId);
                                    if (viewpanel.length === 0) {
                                        viewpanel = Layout.panel({
                                            id: panelId,
                                            name: '编辑片尾'
                                        });
                                    }
                                
                                    demo.list(
                                        viewpanel,
                                        {
                                            title: '编辑片尾',
                                            id: data.id,
                                            sceneName: data.sceneName,
                                            subSceneName: data.subSceneName,
                                            routeName: data.routeName,
                                            vehicleModel: data.vehicleModel,
                                            routeExplainFileUrl: data.routeExplainFileUrl,
                                            routeExplainImgUrl: data.routeExplainImgUrl,
                                            lightExamItems: data.lightExamItems,
                                            lightTemplate: data.lightTemplate,
                                            endTipText: data.endTipText
                                        }
                                    )
                            })
                        })
                    }
                },
                {
                    name: '制作策略',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        Store([`jiakao-misc!route-video-process/data/view?id=${lineData.id}`], [{
                            aliases: 'list'
                        }]).load().done((store) => {
                            const data = store.data.list.data;
                            // if (!data.clipVideoUrl) {
                            //     Widgets.dialog.alert('暂无可编辑的视频')
                            //     return;
                            // }
                                
                            require(['jiakao-misc!app/film-video/index'], function (demo) {
                                var panelId = 'film-video' + data.id;
                                var viewpanel = Layout.panel(panelId);
                                if (viewpanel.length === 0) {
                                    viewpanel = Layout.panel({
                                        id: panelId,
                                        name: '制作策略'
                                    });
                                }
                              
                                demo.list(
                                    viewpanel,
                                    data,
                                    {
                                        type: 'timelineVideo',
                                        title: '制作策略'
                                    }
                                )
                            })
                            
                        })
                            

                    }
                },
                // {
                //     name: '开始制作',
                //     render: function (name, arr, index) {
                //         if (!arr[index].processStatus >= 20) {
                //             return '重新制作';
                //         }
                //         return name;
                //     },
                //     class: 'danger',
                //     click: function (table, dom, lineData) {
                //         Store(['jiakao-misc!route-video-process/data/processVideo']).save([{
                //             params: {
                //                 id:lineData.id
                //             }
                //         }]).done(function (tab, dom, confirm, data) {
                //             Widgets.dialog.alert('成功')
                //             table.render();
                //         }).fail(function (str) {
                //             Widgets.dialog.alert(str.message);
                //         })
                //     }
                // },
            ],
            columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '考场key',
                        dataIndex: 'sceneKey'
                },
                {
                    header: '考场名称第一行',
                    dataIndex: 'sceneName'
                },
                {
                    header: '考场名称第二行',
                    dataIndex:'subSceneName'
                },
                {
                    header: '城市',
                    dataIndex: 'cityName',
                    render: function (data,arr, lineData) {
                        return data + lineData.cityCode
                    }
                },
                {
                    header: '区县',
                    dataIndex: 'countyName',
                    render: function (data,arr, lineData) {
                        return data + lineData.countyCode
                    }
                },
                {
                    header: '线路名称',
                    dataIndex: 'routeName'
                },
                {
                    header: '合适车型',
                    dataIndex: 'routeCarType',
                    render: function (data) {
                        return routeCarTypeMap[data]
                    }
                },
                {
                    header: '车辆型号',
                    dataIndex: 'vehicleModel'
                },
                     {
                         header: '教练上传视频ID',
                         dataIndex: 'coachVideoId'
                     },
                     {
                         header: '教练ID',
                         dataIndex: 'coachId'
                     },
                   
                   
                     {
                         header: '终端考场id',
                         dataIndex: 'sceneId'
                     },
                 
                     {
                         header: '终端线路id',
                         dataIndex: 'routeId'
                     },
                     {
                         header: 'misc线路id',
                         dataIndex: 'routeItemId'
                     },
                 
                    
                     {
                        header: '原始视频',
                        dataIndex: 'originalVideoUrl',
                        render: function () {
                            return `<a>点击查看</a>`
                        },
                         click: function (table, row, lineData) {
                            if (!lineData.originalVideoUrl) {
                                Widgets.dialog.alert('暂无数据')
                                return
                            }
                            Widgets.dialog.html('原始视频', {}).done(function (dialog) {
                                $(dialog.body).html(`<video src="${lineData.originalVideoUrl}" controls width="500">`)
                            })
                        }
                     },
                 
                     {
                         header: '视频的点位时间轴',
                         dataIndex: 'timelineVideo',
                         render: function () {
                            return `<a>点击查看</a>`
                        },
                         click: function (table, row, lineData) {
                            if (!lineData.timelineVideo) {
                                Widgets.dialog.alert('暂无数据')
                                return
                            }
                            Widgets.dialog.html('视频的点位时间轴', {}).done(function (dialog) {
                                var data = lineData.timelineVideo && JSON.stringify(JSON.parse(lineData.timelineVideo), null, 4)
                                $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                     },
                     {
                         header: '互动视频的点位时间轴json',
                         dataIndex: 'timelinePractice',
                         render: function () {
                            return `<a>点击查看</a>`
                        },
                         click: function (table, row, lineData) {
                            if (!lineData.timelinePractice) {
                                Widgets.dialog.alert('暂无数据')
                                return
                            }
                            Widgets.dialog.html('互动视频的点位时间轴', {}).done(function (dialog) {
                                var data = lineData.timelinePractice ? JSON.stringify(JSON.parse(lineData.timelinePractice), null, 4) :''
                                $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                     },
                    //  {
                    //      header: '预处理视频url',
                    //      dataIndex: 'preVideoUrl'
                    //  },
                     {
                         header: '上传时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '处理视频的结果',
                         dataIndex: 'processResultId',
                         render: function () {
                            return `<a>点击查看</a>`
                        },
                         click: function (table, row, lineData) {
                            if (!lineData.preVideoUrl) {
                                Widgets.dialog.alert('暂无数据')
                                return
                            }
                            Widgets.dialog.html('处理视频的结果', {}).done(function (dialog) {
                                $(dialog.body).html(`<video src="${lineData.preVideoUrl}" controls width="500">`)
                            })
                        }
                     },
                     {
                         header: '制作进度',
                         dataIndex: 'processStatus',
                         render: function (data) {
                             return processStatusMap[data]
                         }
                     },
                     {
                        header:'错误信息',
                        dataIndex:'errorReason'
                     },
                        {
                            header: '责任人',
                            dataIndex: 'liableUserName'
                        },
                         {
                         header: '视频的轨迹数据',
                         dataIndex: 'trackInfo',
                         render: function () {
                            return `<a>点击查看</a>`
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('视频的轨迹数据', {}).done(function (dialog) {
                                $(dialog.body).html(lineData.trackInfo)
                            })
                        }
                     },
                     {
                         header: '更新人昵称',
                         dataIndex: 'updateUserName'
                     }

            ]
        }, ['jiakao-misc!route-video-process/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});