/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

'use strict';

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/download',
    'simple!app/layout/main',
    'simple!core/ajax',
    'https://share-m.kakamobi.com/activity.kakamobi.com/qichebaojiazhijia-base-common/source/resource/js/s-upload.js',
], function (
    Template,
    Table,
    Utils,
    Widgets,
    Store,
    Form,
    Plugin,
    Download,
    Layout,
    Ajax,
    MCSUpload
) {
    const processStatusMap = {
        0: '待处理',
        3: '已配置',
        4: '已上传讲解',
        31: '小地图上传完成',
        40: '成片制作中',
        41: '成片制作完成',
        42: '成片制作失败',
    };
    const processStatusArray = [];
    for (let key in processStatusMap) {
        processStatusArray.push({ key: key, value: processStatusMap[key] });
    }
    const routeCarTypeMap = {
        1: '手动',
        2: '自动',
        3: '手动和自动',
    };
    const hasSyncMap = {
        true: '已同步',
        false: '未同步',
    };
    const hasSyncArray = [];
    for (let key in hasSyncMap) {
        hasSyncArray.push({ key: key, value: hasSyncMap[key] });
    }
    var auditStatusMap = {
        pending: '待审核',
        accept: '审核通过',
        reject: '审核不通过',
    };
    const auditStatusArray = [];
    for (let key in auditStatusMap) {
        auditStatusArray.push({ key: key, value: auditStatusMap[key] });
    }
    var hasBindRouteMap = {
        true: '已绑定',
        false: '未绑定',
    };
    const hasBindRouteArray = [];
    for (let key in hasBindRouteMap) {
        hasBindRouteArray.push({ key: key, value: hasBindRouteMap[key] });
    }
    var applyVideoLog = function (table, lineData = {}) {
        Widgets.dialog
            .html('视频合成日志', {
                width: 900,
            })
            .done(function (dialog) {
                Table(
                    {
                        description: '教练上传视频ID：' + lineData.coachVideoId,
                        title: '#' + lineData.id,
                        columns: [
                            {
                                header: '发起合成时间',
                                render: function (data) {
                                    return Utils.format.date(
                                        data,
                                        'yyyy-MM-dd HH:mm:ss'
                                    );
                                },
                                dataIndex: 'createTime',
                            },
                            {
                                header: '系统处理开始时间',
                                render: function (data) {
                                    return Utils.format.date(
                                        data,
                                        'yyyy-MM-dd HH:mm:ss'
                                    );
                                },
                                dataIndex: 'finishTime',
                            },
                            {
                                header: '合成结果',
                                dataIndex: 'processStatus',
                                render: function (data) {
                                    return processStatusMap[data];
                                },
                            },
                            {
                                header: '是否上传小地图',
                                render: function (data) {
                                    if (data) {
                                        return '是';
                                    } else {
                                        return '否';
                                    }
                                },
                                dataIndex: 'miniMapUploaded',
                            },
                            {
                                header: '是否上传互动视频地图',
                                render: function (data) {
                                    if (data) {
                                        return '是';
                                    } else {
                                        return '否';
                                    }
                                },
                                dataIndex: 'practiceMapUploaded',
                            },
                        ],
                    },
                    [
                        'jiakao-misc!route-video-process/data/routeVideoProcessLog?processId=' +
                            lineData.id,
                    ],
                    dialog.body,
                    null
                ).render();
            });
    };
    var renderApplyLog = function (table, lineData = {}) {
        Widgets.dialog
            .html('', {
                width: 900,
            })
            .done(function (dialog) {
                Table(
                    {
                        description: '教练上传视频ID：' + lineData.coachVideoId,
                        title: '#' + lineData.id + '的审核日志',
                        columns: [
                            {
                                header: '审核时间',
                                dataIndex: 'createTime',
                                render: function (data) {
                                    return Utils.format.date(
                                        data,
                                        'yyyy-MM-dd HH:mm:ss'
                                    );
                                },
                            },
                            {
                                header: '审核人',
                                dataIndex: 'createUserName',
                            },
                            {
                                header: '审核结果',
                                dataIndex: 'auditStatus',
                                render: function (data) {
                                    return auditStatusMap[data];
                                },
                            },
                            {
                                header: '审核不通过的原因',
                                dataIndex: 'auditStatus',
                                render: function (data) {
                                    if (data == 'reject') {
                                        return `<a>查看详情</a>`;
                                    }
                                },
                                click: function (table, dom, ownObejct) {
                                    viewReason(table, ownObejct, lineData);
                                },
                            },
                        ],
                    },
                    [
                        'jiakao-misc!route-video-process/data/processList?processId=' +
                            lineData.id,
                    ],
                    dialog.body,
                    null
                ).render();
            });
    };
    var applyOprator = function (table, lineData = {}) {
        Table({
            title: '审核',
            width: 600,
            store: 'jiakao-misc!route-video-process/data/processAudit',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler(form) {
                    var rejectReason = form.rejectReason.value;
                    var rejectImgs = form.newrejectImgs.value;
                    var auditStatus = form.auditStatus.value;
                    var imgs = JSON.parse(rejectImgs || '[]');
                    var newArray = [];
                    imgs.forEach((res) => {
                        newArray.push(res.newrejectImgs);
                    });
                    if (auditStatus == 'reject' && !rejectReason) {
                        Widgets.dialog.alert('审核不通过必须填写原因');
                        return;
                    }
                    if (auditStatus == 'reject' && imgs.length > 5) {
                        Widgets.dialog.alert('最多支持5张图');
                        return;
                    }
                    return {
                        rejectImgs: newArray.join(','),
                    };
                },
                reverseParam: true,
            },
            renderAfter: function (tab, dom, lineData) {
                var rejectImgs = dom.item('newrejectImgs-group');
                var rejectReason = dom.item('rejectReason-group');
                rejectImgs.css('display', 'none');
                rejectReason.css('display', 'none');
                dom.item('auditStatus').on('change', function () {
                    if ($(this).val() == 'reject') {
                        rejectImgs.css('display', 'block');
                        rejectReason.css('display', 'block');
                    } else {
                        rejectImgs.css('display', 'none');
                        rejectReason.css('display', 'none');
                    }
                });
            },
            columns: [
                {
                    dataIndex: 'processId',
                    xtype: 'hidden',
                    value: lineData.id,
                },
                {
                    header: '#',
                    xtype: function () {
                        return lineData.id || '';
                    },
                },
                {
                    header: '教练上传视频ID',
                    xtype: function () {
                        return lineData.coachVideoId || '';
                    },
                },
                {
                    header: '审核状态：',
                    dataIndex: 'auditStatus',
                    xtype: 'select',
                    check: 'required',
                    store: [
                        {
                            key: '',
                            value: '请选择审核状态',
                        },
                        ...auditStatusArray,
                    ],
                },
                {
                    header: '原因描述：',
                    dataIndex: 'rejectReason',
                    xtype: 'textarea',
                    placeholder: '请输入原因描述',
                },
                {
                    header: '上传图片附件:',
                    dataIndex: 'newrejectImgs',
                    xtype: Plugin(
                        'jiakao-misc!select-images',
                        {
                            dataIndex: 'newrejectImgs',
                            multiple: true,
                            isNoWangpan: true,
                            useSecureUpload: true,
                            // 选填，如果需要获取文件 md5，saveUploadUrl 必填，且 saveUploadUrl 接口必须返回 md5 字段。
                            saveUploadUrl:
                                window.j.host.panda +
                                '/api/admin/upload/save.htm',
                            // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                            appSpaceId: '02fb648802fb886a5a5c',
                            limit: 5,
                            imageSrc: {
                                // 必填。
                                dataIndex: 'newrejectImgs',
                            },
                        },
                        function (plugin, value) {}
                    ),
                },
            ],
        }).add();
    };
    var viewReason = function (table, lineData = {}, parentObejct = {}) {
        Widgets.dialog
            .html('查看详情', {
                width: 900,
            })
            .done(function (dialog) {
                console.log('lineData', lineData);
                var img = '';
                var imgArr = lineData.rejectImg?.split(',') || [];
                imgArr &&
                    imgArr.forEach((res) => {
                        img += `
                 <img class="img-click" src="${res}"  style="width:100px;height:100px;margin-right:10px;margin-bottom:10px;cursor:pointer"/>
                
                `;
                    });

                var html = `
              <div>
               <div style="margin-bottom:20px;display: flex">
                 <label style="width:130px;text-align:right;">#：</label>
                 <span style="flex:1">${parentObejct.id}</span>
               </div>
               <div style="margin-bottom:20px;display: flex">
                 <label style="width:130px;text-align:right;">教练上传视频ID：</label>
                 <span style="flex:1">${parentObejct.coachVideoId}</span>
               </div>
               <div style="margin-bottom:20px;display: flex">
                 <label style="width:130px;text-align:right;">不通过原因描述：</label>
                 <span style="flex:1">${lineData.rejectReason}</span>
               </div>
                <div style="margin-bottom:20px;display: flex">
                 <label style="width:130px;text-align:right;">上传图片附件：</label>
                 <span style="flex:1">${img || '无'}</span>
               </div>
              
              </div>
           `;
                dialog.body.html(html);
                setTimeout(() => {
                    $('.img-click').on('click', function () {
                        var srcS = $(this).attr('src');
                        Widgets.dialog
                            .html('上传图片附件', {})
                            .done(function (dialog2) {
                                $(dialog2.body).html(`
                        <img src="${srcS}"/>
                        
                        
                        `);
                            });
                    });
                }, 800);
            });
    };

    var appSpaceId;

    function getVideoFrame(video) {
        console.log(video);
        return new Promise((resolve, reject) => {
            try {
                // 检查视频状态
                if (!video || video.readyState < 2) {
                    reject(new Error('视频还未加载完成'));
                    return;
                }

                if (video.videoWidth === 0 || video.videoHeight === 0) {
                    reject(new Error('无法获取视频尺寸'));
                    return;
                }

                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');

                // 绘制视频帧到canvas
                ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);

                // 尝试导出canvas数据
                try {
                    const dataURL = canvas.toDataURL('image/png');
                    if (!dataURL || dataURL === 'data:,') {
                        reject(new Error('无法捕获视频帧'));
                        return;
                    }
                    resolve(dataURL);
                } catch (securityError) {
                    // 处理CORS错误
                    console.error(
                        'CORS error when capturing frame:',
                        securityError
                    );
                    reject(
                        new Error(
                            '由于跨域限制无法捕获视频帧，请联系管理员配置视频服务器CORS'
                        )
                    );
                }
            } catch (error) {
                console.error('Error in getVideoFrame:', error);
                reject(error);
            }
        });
    }

    function uploadBase64(base64Str, callback) {
        console.log(base64Str);
        new MCSUpload.default({
            admin: true,
            appSpaceId: appSpaceId,
            files: [base64Str],
            encrypt: false,
            onUploadProcess(percent, percentAray) {
                console.log(percent + '%', percentAray);
            },
        })
            .then((sRes) => {
                console.log('成功', sRes);
                callback(sRes[0]);
            })
            .catch((fRes) => {
                console.log('失败', fRes);
            });
    }

    var cutPosterImage = function (table, lineData, callback) {
        console.log(lineData, 'lineData');
        keyExchangeData(lineData.videoItemKey).then((res) => {
            console.log('res------', res);
            res = res.replace(
                'hw-jiakao-video.jiakaobaodian.com',
                'upload-video.mucang.cn'
            );
            if (!res) {
                Widgets.dialog.alert('视频系统转码中...');
                return;
            }
            Widgets.dialog
                .html('选择截图', '', {
                    backdrop: 'static',
                    width: 800,
                    buttons: [
                        {
                            name: '取消',
                            xtype: 'default',
                            click: function () {
                                this.close();
                            },
                        },
                        {
                            name: '截图',
                            xtype: 'success',
                            click: async function () {
                                const _this = this;
                                const video = $(this.body).find('video')[0];
                                const saveBtn = $(this.body)
                                    .parents('.modal-content')
                                    .find('.btn-success');
                                try {
                                    // 检查视频状态
                                    if (!video || video.readyState < 2) {
                                        Widgets.dialog.alert(
                                            '视频还未加载完成，请稍后再试'
                                        );
                                        return;
                                    }

                                    if (
                                        video.videoWidth === 0 ||
                                        video.videoHeight === 0
                                    ) {
                                        Widgets.dialog.alert(
                                            '无法获取视频尺寸，请检查视频文件'
                                        );
                                        return;
                                    }

                                    // 显示加载状态
                                    saveBtn
                                        .prop('disabled', true)
                                        .text('截图中...');

                                    // 获取视频帧
                                    const imgData = await getVideoFrame(video);

                                    // 上传图片
                                    uploadBase64(
                                        imgData,
                                        function (uploadData) {
                                            _this.close();
                                            callback(uploadData);
                                        }
                                    );
                                } catch (error) {
                                    console.error(
                                        'Error capturing frame:',
                                        error
                                    );
                                    Widgets.dialog.alert(
                                        '捕获视频帧失败: ' + error.message
                                    );
                                    saveBtn
                                        .prop('disabled', false)
                                        .text('截图');
                                }
                            },
                        },
                    ],
                })
                .done(function (dialog) {
                    // 创建带有CORS支持的视频元素
                    const videoHtml = `
                    <div style="text-align: center; padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <h4>选择视频封面</h4>
                            <p style="color: #666; font-size: 14px;">播放视频到想要设置为封面的画面，然后点击保存按钮</p>
                        </div>
                        <video crossOrigin controls width="600" style="max-width: 100%;">
                            <source src="${res}" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                `;
                    $(dialog.body).html(videoHtml);

                    // 处理视频加载错误
                    const video = $(dialog.body).find('video')[0];
                    video.addEventListener('error', function (e) {
                        console.error('Video load error:', e);
                        Widgets.dialog.alert('视频加载失败，可能是跨域问题');
                    });

                    video.addEventListener('loadedmetadata', function () {
                        console.log('Video metadata loaded successfully');
                    });
                });
        });
    };

    var editPosterImage = function (table, lineData) {
        var imageData = null;
        Widgets.dialog
            .html('编辑封面', '', {
                backdrop: 'static',
                buttons: [
                    {
                        name: '取消',
                        xtype: 'default',
                        click: function () {
                            this.close();
                        },
                    },
                    {
                        name: '确认',
                        xtype: 'success',
                        click: function () {
                            console.log('imageData-----', imageData);
                            Store([
                                'jiakao-misc!route-video-process/data/updatePosterImage',
                            ])
                                .save([
                                    {
                                        params: {
                                            id: lineData.id,
                                            encodeData: imageData.encodedData,
                                        },
                                    },
                                ])
                                .done(() => {
                                    Widgets.dialog.alert('封面更新成功');
                                    this.close();
                                    table.render();
                                })
                                .fail((err) => {
                                    Widgets.dialog.alert(
                                        '更新失败: ' + err.message
                                    );
                                    saveBtn
                                        .prop('disabled', false)
                                        .text('保存');
                                });
                            this.close();
                        },
                    },
                ],
            })
            .done(function (dialog) {
                $(dialog.body).html(
                    `<div style="padding: 10px 0;"><button data-item="selPoster" class="btn btn-primary">选择封面</button></div><div><img data-item="preview" src="${lineData.encodeData.url}"/></div>`
                );
                dialog.body
                    .find('[data-item="selPoster"]')
                    .on('click', function () {
                        cutPosterImage(table, lineData, function (data) {
                            imageData = data;
                            dialog.body
                                .find('[data-item="preview"]')
                                .attr('src', data.previewUrl);
                        });
                    });
            });
        return;
        let config = {
            title: '编辑视频封面',
            width: 600,
            store: 'jiakao-misc!route-video-process/data/updatePosterImage',
            success: function (obj, dialog, e) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    xtype: 'hidden',
                    dataIndex: 'id',
                },
                {
                    header: '视频封面：',
                    dataIndex: 'encodeData',
                    xtype: Simple.Plugin('simple!jiakao-upload', {
                        useSecureUpload: true,
                        bucket: 'jiakao-image',
                        prefix: 'jiakao-image',
                        // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                        getAppSpaceId:
                            'jiakao-misc!common/data/getMiscAppSpaceId',
                        imageSrc: {
                            // 必填。
                            dataIndex: 'url',
                            encodedDataIndex: 'encodeData',
                        },
                        parse: function (value) {
                            console.log('parse---', value);
                            return [value];
                        },
                        stringify: function (value) {
                            console.log('stringify---', value);
                            return value[0].encodeData;
                        },
                    }),
                },
            ],
        };
        Table().edit(lineData, config);
    };

    var list = function (panel) {
        new MCSUpload.default({
            onSuccess: function () {},
            onFail: function () {},
        });
        Store(['jiakao-misc!common/data/getMiscAppSpaceId?bucket=jiakao-image'])
            .load([
                {
                    aliases: 'list',
                },
            ])
            .done((data) => {
                appSpaceId = data.data.list.data.value;
            });
        Table(
            {
                description: '路线视频成片管理',
                title: '路线视频成片管理',
                searchReset: {
                    class: 'danger',
                },
                search: [
                    {
                        header: '城市编码：',
                        dataIndex: 'cityCode',
                        xtype: Plugin('jiakao-misc!select-district2', {
                            name: 'cityCode',
                            areaName: 'areaCode',
                            insert: {
                                province: [
                                    {
                                        code: '',
                                        name: '请选择省份',
                                    },
                                ],
                                city: [
                                    {
                                        code: '',
                                        name: '请选择市',
                                    },
                                ],
                                area: [
                                    {
                                        code: '',
                                        name: '请选择区县',
                                    },
                                ],
                            },
                        }),
                    },
                    {
                        header: '教练上传视频id',
                        xtype: 'text',
                        dataIndex: 'coachVideoId',
                        placeholder: '教练上传视频id',
                    },
                    {
                        header: '绑定驾考路线ID',
                        xtype: 'text',
                        dataIndex: 'routeItemId',
                        placeholder: '绑定驾考路线ID',
                    },
                    {
                        header: '教练id',
                        xtype: 'text',
                        dataIndex: 'coachId',
                        placeholder: '教练id',
                    },
                    {
                        header: '考场名称',
                        xtype: 'text',
                        dataIndex: 'sceneName',
                        placeholder: '考场名称',
                    },
                    {
                        header: '考场key',
                        xtype: 'text',
                        dataIndex: 'sceneKey',
                        placeholder: '考场key',
                    },
                    {
                        header: '城市名称',
                        xtype: 'text',
                        dataIndex: 'cityName',
                        placeholder: '城市名称',
                    },
                    {
                        header: '制作进度',
                        xtype: 'select',
                        dataIndex: 'processStatus',
                        store: [{ key: '', value: '请选择制作进度' }].concat(
                            processStatusArray
                        ),
                    },
                    {
                        header: '是否同步',
                        xtype: 'select',
                        dataIndex: 'hasSync',
                        store: [{ key: '', value: '请选择是否同步' }].concat(
                            hasSyncArray
                        ),
                    },
                    {
                        dataIndex: 'startTime',
                        xtype: 'date',
                        placeholder: '开始时间',
                    },
                    {
                        dataIndex: 'endTime',
                        xtype: 'date',
                        placeholder: '结束时间',
                    },
                    {
                        header: '是否生成轨迹图',
                        xtype: 'select',
                        dataIndex: 'hasTrackCover',
                        store: [
                            { key: '', value: '请选择轨迹状态' },
                            { key: true, value: '已生成' },
                            { key: false, value: '未生成' },
                        ],
                    },
                    {
                        dataIndex: 'liableUserId',
                        xtype: 'hidden',
                        minWidth: '0',
                    },
                    {
                        dataIndex: 'liableUserName',
                        xtype: Plugin(
                            'simple!auto-prompt2',
                            {
                                store: [],
                                url: 'jiakao-misc!route-video-process/data/listUsers?withFired=false',
                                dataIndex: 'liableUserName',
                                index: {
                                    key: 'nickname',
                                    value: 'nickname',
                                    search: 'nickname',
                                },

                                online: true,
                                isMulti: false,
                                value: '',
                                placeholder: '请输入责任人',
                            },
                            function (plugin, value, obj) {
                                var values = $(panel)
                                    .find('div[data-item="liableUserId-group"]')
                                    .find('input[data-item="liableUserId"]');
                                values.val(obj.userId || '');
                            }
                        ),
                    },
                    {
                        header: '城市等级',
                        dataIndex: 'cityLevel',
                        xtype: Plugin(
                            'simple!auto-prompt2',
                            {
                                store: [
                                    {
                                        key: 0,
                                        value: '一般',
                                    },
                                    {
                                        key: 1,
                                        value: '中',
                                    },
                                    {
                                        key: 2,
                                        value: '高',
                                    },
                                    {
                                        key: 3,
                                        value: '极高',
                                    },
                                ],
                                dataIndex: 'cityLevel',
                                isMulti: true,
                                defaultVal: false,
                                placeholder: '请选择城市等级',
                            },
                            function (plugin, value) {}
                        ),
                    },
                    {
                        dataIndex: 'hasBindRoute',
                        xtype: 'select',
                        store: [
                            {
                                key: '',
                                value: '是否绑定视频ID',
                            },
                            ...hasBindRouteArray,
                        ],
                    },
                    {
                        dataIndex: 'auditStatus',
                        xtype: 'select',
                        store: [
                            {
                                key: '',
                                value: '请选择审核状态',
                            },
                            ...auditStatusArray,
                        ],
                    },
                ],
                buttons: {
                    top: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            },
                        },
                    ],
                    bottom: [
                        {
                            name: '刷新',
                            class: 'info',
                            click: function (obj) {
                                obj.render();
                            },
                        },
                    ],
                },
                operations: [
                    {
                        name: '上传互动视频地图',
                        class: 'info',
                        click: function (table, row, lineData) {
                            if (!lineData.routeItemId) {
                                Widgets.dialog.alert('请先绑定视频id');
                                return;
                            }

                            var _this = $(this);
                            if (_this.hasClass('disabled')) {
                                return;
                            }
                            _this.addClass('disabled');
                            setTimeout(function () {
                                _this.removeClass('disabled');
                            }, 1000);
                            Plugin('jiakao-misc!select-file2', {
                                uploadUrl:
                                    'jk-knowledge://api/admin/route-video-practice/upload-video.htm',
                                lineData: lineData,
                                accept: 'video/*,image/*',
                                type: '10',
                                postData: {
                                    id: lineData.routeItemId,
                                },
                                complete: function () {
                                    // DOM.parent().parent().parent().find('.close').click();
                                    table.render();
                                    // this.close();
                                },
                            }).render();
                        },
                    },
                    // {
                    //     name: '上传小地图视频',
                    //     class: 'warning',
                    //     click: function (table, row, lineData) {
                    //         var _this = $(this);
                    //         if (_this.hasClass('disabled')) {
                    //             return;
                    //         }
                    //         _this.addClass('disabled');
                    //         setTimeout(function () {
                    //             _this.removeClass('disabled');
                    //         }, 1000)
                    //         Plugin("jiakao-misc!select-file2", {
                    //             uploadUrl: "jiakao-misc://api/admin/route-video-process/upload-video.htm",
                    //             type:'miniMap',
                    //             lineData: lineData,
                    //             postData: {
                    //                 id: lineData.id
                    //             },
                    //             complete: function () {
                    //                 // DOM.parent().parent().parent().find('.close').click();
                    //                 table.render();
                    //                 // this.close();
                    //             }
                    //         }).render();
                    //     }
                    // },
                    {
                        name: '上传小地图视频',
                        class: 'warning',
                        click: function (table, row, lineData) {
                            Table({
                                title: '小地图视频',
                                success: function (obj, dialog, e) {
                                    dialog.close();
                                    obj.render();
                                },
                                renderAfter: function (table, dom, data) {
                                    setTimeout(() => {
                                        $('.modal-footer').hide();
                                    }, 200);
                                },
                                columns: [
                                    {
                                        header: '上传小地图视频',
                                        xtype: Plugin(
                                            'jiakao-misc!combine-upload',
                                            {
                                                // 选填，第三版安全文件上传所需参数，保存文件接口。如不填，则保存到表单里的是文件编码，而不是真实文件url。
                                                // 选填，如果需要获取文件 md5，saveUploadUrl 必填，且 saveUploadUrl 接口必须返回 md5 字段。
                                                saveUploadUrl: `jiakao-misc://api/admin/route-video-process/commit.htm?id=${lineData.id}`,
                                                // 必填，第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                                                appSpaceId:
                                                    'd2957fe9d295e951b56b',

                                                imageSrc: {
                                                    dataIndex: '',
                                                    // 选填：需要返回文件名时使用
                                                    fileNameIndex: 'name',
                                                    // 选填：可自定义上传成功后返回数据的 md5 字段 key
                                                    md5Index: '',
                                                },
                                                // minio上传完成更新业务接口
                                                updateUrl: `jiakao-misc://api/admin/route-video-process/update-mini-map-url.htm?id=${lineData.id}`,
                                            },
                                            function () {
                                                console.log('123123', this);
                                                console.log(
                                                    '456456',
                                                    arguments
                                                );
                                                table.render();
                                            }
                                        ),
                                        // xtype:  Plugin('simple!select-file-part', {
                                        //     // 选填，第三版安全文件上传所需参数，保存文件接口。如不填，则保存到表单里的是文件编码，而不是真实文件url。
                                        //     // 选填，如果需要获取文件 md5，saveUploadUrl 必填，且 saveUploadUrl 接口必须返回 md5 字段。
                                        //     saveUploadUrl: `jiakao-misc://api/admin/route-video-process/commit.htm?id=${lineData.id}`,
                                        //     // 必填，第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                                        //     appSpaceId: 'd2957fe9d295e951b56b',

                                        //     imageSrc: {
                                        //         dataIndex: '',
                                        //         // 选填：需要返回文件名时使用
                                        //         fileNameIndex: 'name',
                                        //         // 选填：可自定义上传成功后返回数据的 md5 字段 key
                                        //         md5Index: ''
                                        //     },
                                        // })
                                    },
                                ],
                            }).add();
                        },
                    },
                    {
                        name: '视频生成',
                        class: 'info',
                        click: function (table, row, lineData) {
                            Table({
                                title: '视频生成',
                                store: `jiakao-misc!route-video-process/data/genVideo?id=${lineData.id}`,
                                success: function (obj, dialog, e) {
                                    dialog.close();
                                    obj.render();
                                },
                                columns: [
                                    {
                                        header: '是否合成背景音乐',
                                        dataIndex: 'attachBgm',
                                        xtype: 'radio',
                                        store: [
                                            {
                                                key: true,
                                                value: '合入',
                                            },
                                            {
                                                key: false,
                                                value: '不合入',
                                            },
                                        ],
                                    },
                                ],
                            }).add();

                            // Widgets.dialog.confirm('确认生成视频吗？', function (ev, status) {
                            //     if (status) {
                            //         Store([`jiakao-misc!route-video-process/data/genVideo?id=${lineData.id}`]).load().done(function (store) {
                            //             Widgets.dialog.alert('生成成功')
                            //             table.render()
                            //         }).fail(function (err) {
                            //             Widgets.dialog.alert(err.message)
                            //         });

                            //     } else { }
                            // })
                        },
                    },
                    {
                        name: '编辑视频封面',
                        class: 'primary',
                        click: function (table, row, lineData) {
                            Store(
                                [
                                    'jiakao-misc!route-video-process/data/viewPosterImage?id=' +
                                        lineData.id,
                                ],
                                [
                                    {
                                        aliases: 'view',
                                    },
                                ]
                            )
                                .load()
                                .done(function (store, data) {
                                    console.log(data.view.data);
                                    var viewData = data.view.data;
                                    lineData.encodeData = viewData;
                                    // lineData.url = viewData.url;
                                    editPosterImage(table, lineData);
                                })
                                .fail((err) => {
                                    Widgets.dialog.alert(err.message);
                                });
                        },
                    },
                    {
                        name: '绑定驾考路线ID',
                        class: 'info',
                        xtype: 'edit',
                        store: {
                            load: 'jiakao-misc!route-video-process/data/view',
                            save: 'jiakao-misc!route-video-process/data/update',
                        },
                        success: function (obj, dialog, e) {
                            dialog.close();
                            obj.render();
                        },
                        columns: [
                            {
                                dataIndex: 'id',
                                xtype: 'hidden',
                            },
                            {
                                header: '视频id',
                                dataIndex: 'routeItemId',
                                xtype: 'text',
                                placeholder: '视频id',
                            },
                        ],
                    },
                    {
                        name: '同步路线数据',
                        class: 'warning',
                        render: function (font, array, index) {
                            if (array[index].processStatus == '41') {
                                return '同步路线数据';
                            }
                        },
                        click: function (table, row, lineData) {
                            Store(
                                [
                                    'jiakao-misc!route-video-process/data/checkCity?id=' +
                                        lineData.id,
                                ],
                                [
                                    {
                                        aliases: 'check',
                                    },
                                ]
                            )
                                .load()
                                .done((retData) => {
                                    var checkData =
                                        retData.data.check.data.value;
                                    // if (!checkData) {
                                    //     Widgets.dialog.alert('城市不一致,请确认后再同步!');

                                    // }
                                    Table({
                                        title: '同步路线数据',
                                        success: function (obj, dialog, e) {
                                            // dialog.close();
                                            // obj.render();
                                        },
                                        form: {
                                            submitHandler(form) {
                                                console.log('form', form);
                                                var deleteDifficult =
                                                    form.deleteDifficult.value;
                                                $(form).find('.close').click();
                                                hasSyncRoute(deleteDifficult);
                                                return false;
                                            },
                                        },
                                        columns: [
                                            {
                                                xtype: function () {
                                                    if (!checkData) {
                                                        return '<div style="color:red">城市不一致,请确认后再同步!</div>';
                                                    }
                                                },
                                            },
                                            {
                                                header: '清除重难点',
                                                dataIndex: 'deleteDifficult',
                                                xtype: 'radio',
                                                value: false,
                                                store: [
                                                    {
                                                        key: true,
                                                        value: '是',
                                                    },
                                                    {
                                                        key: false,
                                                        value: '否',
                                                    },
                                                ],
                                            },
                                        ],
                                    }).add();

                                    function hasSyncRoute(deleteDifficult) {
                                        require([
                                            'jiakao-misc!app/route-video-item/editMap',
                                        ], function (ModeVs) {
                                            if (!lineData.trackInfo) {
                                                Widgets.dialog.alert(
                                                    '暂无数据'
                                                );
                                                return;
                                            }

                                            Widgets.dialog
                                                .html(
                                                    '轨迹信息（请等待上传完毕再关闭弹窗）',
                                                    {
                                                        width: '1380px',
                                                    }
                                                )
                                                .done(function (dialog) {
                                                    var data = JSON.parse(
                                                        lineData.trackInfo
                                                    );

                                                    var spotInfoData =
                                                        lineData.spotInfo
                                                            ? JSON.parse(
                                                                  lineData.spotInfo
                                                              )
                                                            : [];

                                                    spotInfoData.forEach(
                                                        (item, i) => {
                                                            data.forEach(
                                                                (el, index) => {
                                                                    if (
                                                                        item.triggerTime ==
                                                                        el.triggerTime
                                                                    ) {
                                                                        data[
                                                                            index
                                                                        ].stepAliasName =
                                                                            item.spot.stepAliasName;
                                                                        data[
                                                                            index
                                                                        ].index =
                                                                            i;
                                                                    }
                                                                }
                                                            );
                                                        }
                                                    );

                                                    const lineArr =
                                                        data &&
                                                        data.map((item) => [
                                                            item.lng,
                                                            item.lat,
                                                        ]);

                                                    const mapDom =
                                                        document.createElement(
                                                            'div'
                                                        );
                                                    mapDom.id = 'mapDom';
                                                    mapDom.style.height =
                                                        '800px';
                                                    mapDom.style.width =
                                                        '1380px';

                                                    var googleLayerf =
                                                        new AMap.TileLayer({
                                                            gettingUrl:
                                                                'http://webst{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scl=1&style=7&x={x}&y={y}&z={z}',
                                                        });
                                                    var roadNetLayerf =
                                                        new AMap.TileLayer.RoadNet();

                                                    var map = new AMap.Map(
                                                        mapDom,
                                                        {
                                                            resizeEnable: true,
                                                            center: lineArr[
                                                                Math.floor(
                                                                    lineArr.length /
                                                                        2
                                                                )
                                                            ],
                                                            layers: [
                                                                googleLayerf,
                                                                roadNetLayerf,
                                                            ],
                                                            zoom: 15,
                                                        }
                                                    );

                                                    AMap.convertFrom(
                                                        lineArr,
                                                        'gps',
                                                        function (
                                                            status,
                                                            result
                                                        ) {
                                                            if (
                                                                result.info ===
                                                                'ok'
                                                            ) {
                                                                var lnglats =
                                                                    result.locations; // 转换后的高德坐标 Array.<LngLat>
                                                                var polyline =
                                                                    new AMap.Polyline(
                                                                        {
                                                                            map: map,
                                                                            path: lnglats,
                                                                            showDir: true,
                                                                            strokeColor:
                                                                                '#44aaff', //线颜色
                                                                            strokeWeight: 6, //线宽
                                                                        }
                                                                    );
                                                            }

                                                            result.locations.forEach(
                                                                (item, i) => {
                                                                    const stepAliasName =
                                                                        data[i]
                                                                            .stepAliasName;
                                                                    if (
                                                                        stepAliasName
                                                                    ) {
                                                                        const marker =
                                                                            new AMap.Marker(
                                                                                {
                                                                                    icon: new window.AMap.Icon(
                                                                                        {
                                                                                            image:
                                                                                                window
                                                                                                    .j
                                                                                                    .root[
                                                                                                    'jiakao-misc'
                                                                                                ] +
                                                                                                '/resources/images/car.png',
                                                                                            anchor: 'center',
                                                                                            imageSize:
                                                                                                new window.AMap.Size(
                                                                                                    32,
                                                                                                    36
                                                                                                ),
                                                                                        }
                                                                                    ),
                                                                                    position:
                                                                                        [
                                                                                            item.lng,
                                                                                            item.lat,
                                                                                        ],
                                                                                    label: {
                                                                                        content: `<div class="map-label-info">${
                                                                                            stepAliasName +
                                                                                            '-' +
                                                                                            (data[
                                                                                                i
                                                                                            ]
                                                                                                .index +
                                                                                                1)
                                                                                        }</div>`,
                                                                                        direction:
                                                                                            'top',
                                                                                    },
                                                                                }
                                                                            );
                                                                        marker.setMap(
                                                                            map
                                                                        );
                                                                    }
                                                                }
                                                            );

                                                            setTimeout(() => {
                                                                map.setFitView();
                                                            }, 200);
                                                        }
                                                    );

                                                    $(dialog.body).html(mapDom);

                                                    Simple.Dialog.toast(
                                                        '路线轨迹图片生成中，请不要关闭弹窗~'
                                                    );
                                                });
                                        });

                                        setTimeout(async () => {
                                            const dom =
                                                document.getElementById(
                                                    'mapDom'
                                                );
                                            console.log(dom, 'domdom');
                                            const canvas = await html2canvas(
                                                dom,
                                                {
                                                    useCORS: true,
                                                }
                                            );
                                            let imgData =
                                                canvas.toDataURL('image/png');
                                            console.log(
                                                imgData,
                                                'imgDataimgData'
                                            );
                                            Simple.Dialog.toast(
                                                '路线轨迹图片上传中，请不要关闭弹窗~'
                                            );
                                            const value = await uploadImageH5(
                                                imgData
                                            );
                                            console.log(value, 'valuevalue');

                                            Store([
                                                `jiakao-misc!route-video-process/data/syncVideoKey`,
                                            ])
                                                .load([
                                                    {
                                                        params: {
                                                            id: lineData.id,
                                                            trackCover: value,
                                                            deleteDifficult:
                                                                deleteDifficult,
                                                        },
                                                    },
                                                ])
                                                .done((store) => {
                                                    Widgets.dialog.alert(
                                                        '同步成功'
                                                    );
                                                    table.render();
                                                })
                                                .fail((err) => {
                                                    Widgets.dialog.alert(
                                                        err.message
                                                    );
                                                });
                                        }, 2000);
                                    }
                                });
                        },
                    },
                    {
                        name: '生成轨迹图',
                        class: 'danger',
                        click: function (table, row, lineData) {
                            require([
                                'jiakao-misc!app/route-video-item/editMap',
                            ], function (ModeVs) {
                                if (!lineData.trackInfo) {
                                    Widgets.dialog.alert('暂无数据');
                                    return;
                                }

                                Widgets.dialog
                                    .html(
                                        '生成轨迹图（请等待上传完毕再关闭弹窗）',
                                        {
                                            width: '1380px',
                                        }
                                    )
                                    .done(function (dialog) {
                                        var data = JSON.parse(
                                            lineData.trackInfo
                                        );

                                        var spotInfoData = lineData.spotInfo
                                            ? JSON.parse(lineData.spotInfo)
                                            : [];

                                        const lineArr =
                                            data &&
                                            data.map((item) => [
                                                item.lng,
                                                item.lat,
                                            ]);

                                        const mapDom =
                                            document.createElement('div');
                                        mapDom.id = 'mapDom';
                                        mapDom.style.height = '800px';

                                        var googleLayerf = new AMap.TileLayer({
                                            gettingUrl:
                                                'http://webst{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scl=1&style=7&x={x}&y={y}&z={z}',
                                        });
                                        var roadNetLayerf =
                                            new AMap.TileLayer.RoadNet();

                                        var map = new AMap.Map(mapDom, {
                                            resizeEnable: true,
                                            center: lineArr[
                                                Math.floor(lineArr.length / 2)
                                            ],
                                            layers: [
                                                googleLayerf,
                                                roadNetLayerf,
                                            ],
                                            zoom: 15,
                                        });

                                        let lnglats = [];
                                        let PromiseList = [];

                                        for (
                                            let index = 0;
                                            index < lineArr.length / 1000;
                                            index++
                                        ) {
                                            PromiseList.push(
                                                new Promise((resolve) => {
                                                    AMap.convertFrom(
                                                        lineArr.slice(
                                                            1000 * index,
                                                            1000 * (index + 1)
                                                        ),
                                                        'gps',
                                                        function (
                                                            status,
                                                            result
                                                        ) {
                                                            if (
                                                                result.info ===
                                                                'ok'
                                                            ) {
                                                                resolve(
                                                                    result.locations
                                                                );
                                                            }
                                                        }
                                                    );
                                                })
                                            );
                                        }
                                        Promise.all(PromiseList).then(
                                            ([...res]) => {
                                                res.forEach((item) => {
                                                    lnglats.push(...item);
                                                });

                                                new AMap.Polyline({
                                                    map: map,
                                                    path: lnglats,
                                                    showDir: true,
                                                    strokeColor: '#28F', //线颜色
                                                    strokeWeight: 6, //线宽
                                                });
                                            }
                                        );

                                        AMap.convertFrom(
                                            spotInfoData.map((item) => [
                                                item.spot.lng,
                                                item.spot.lat,
                                            ]),
                                            'gps',
                                            function (status, result) {
                                                if (result.info === 'ok') {
                                                    var lnglats =
                                                        result.locations; // 转换后的高德坐标 Array.<LngLat>

                                                    lnglats.forEach(
                                                        (item, i) => {
                                                            const marker =
                                                                new AMap.Marker(
                                                                    {
                                                                        icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
                                                                        position:
                                                                            item,
                                                                        label: {
                                                                            content:
                                                                                spotInfoData[
                                                                                    i
                                                                                ]
                                                                                    .spot
                                                                                    .stepAliasName +
                                                                                '-' +
                                                                                (i +
                                                                                    1),
                                                                            direction:
                                                                                'right',
                                                                        },
                                                                    }
                                                                );
                                                            marker.setMap(map);
                                                        }
                                                    );
                                                }
                                            }
                                        );

                                        $(dialog.body).html(mapDom);
                                        Simple.Dialog.toast(
                                            '路线轨迹图片生成中~'
                                        );
                                    });
                            });

                            setTimeout(async () => {
                                const dom = document.getElementById('mapDom');
                                console.log(dom, 'domdom');
                                const canvas = await html2canvas(dom, {
                                    useCORS: true,
                                });
                                let imgData = canvas.toDataURL('image/png');
                                Simple.Dialog.toast('路线轨迹图片上传中~');
                                const value = await uploadImageH5(imgData);
                                console.log(value, 'valuevalue');

                                Store([
                                    `jiakao-misc!route-video-process/data/syncTrackInfo`,
                                ])
                                    .load([
                                        {
                                            params: {
                                                id: lineData.id,
                                                trackCover: value,
                                            },
                                        },
                                    ])
                                    .done((store) => {
                                        Widgets.dialog.alert('同步成功');
                                        table.render();
                                    })
                                    .fail((err) => {
                                        Widgets.dialog.alert(err.message);
                                    });
                            }, 2000);
                        },
                    },
                    {
                        name: '审核',
                        class: 'primary',
                        render: function (font, array, index) {
                            if (array[index].processStatus == 41) {
                                return '审核';
                            }
                        },
                        click: function (table, dom, lineData) {
                            applyOprator(table, lineData);
                        },
                    },
                    {
                        name: '审核日志',
                        class: 'primary',
                        click: function (table, dom, lineData) {
                            renderApplyLog(table, lineData);
                        },
                    },
                    {
                        name: '视频合成日志',
                        class: 'success',
                        click: function (table, dom, lineData) {
                            applyVideoLog(table, lineData);
                        },
                    },
                ],
                columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20,
                    },
                    {
                        header: '教练上传视频id',
                        dataIndex: 'coachVideoId',
                    },
                    {
                        header: '绑定驾考路线ID',
                        dataIndex: 'routeItemId',
                        render: function (data) {
                            if (data) {
                                return data;
                            }
                        },
                    },
                    {
                        header: '终端路线id',
                        dataIndex: 'routeId',
                    },
                    {
                        header: '考场key',
                        dataIndex: 'sceneKey',
                    },
                    {
                        header: '考场名称第一行',
                        dataIndex: 'sceneName',
                    },
                    {
                        header: '考场名称第二行',
                        dataIndex: 'subSceneName',
                    },
                    {
                        header: '线路名称',
                        dataIndex: 'routeName',
                    },
                    {
                        header: '城市',
                        dataIndex: 'cityName',
                        render: function (data, arr, lineData) {
                            return data + lineData.cityCode;
                        },
                    },
                    {
                        header: '区县',
                        dataIndex: 'countyName',
                        render: function (data, arr, lineData) {
                            return data + lineData.countyCode;
                        },
                    },

                    {
                        header: '合适车型',
                        dataIndex: 'routeCarType',
                        render: function (data) {
                            return routeCarTypeMap[data];
                        },
                    },
                    {
                        header: '车辆型号',
                        dataIndex: 'vehicleModel',
                    },
                    {
                        header: '路线视频',
                        dataIndex: 'videoItemKey',
                        render: function (data, rows, lineData) {
                            if (!lineData.videoItemKey) {
                                return '待上传';
                            }
                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            keyExchangeData(lineData.videoItemKey).then(
                                (res) => {
                                    if (!res) {
                                        Widgets.dialog.alert(
                                            '视频系统转码中...'
                                        );
                                        return;
                                    }
                                    Widgets.dialog
                                        .html('路线视频', {})
                                        .done(function (dialog) {
                                            $(dialog.body).html(
                                                `<video src="${res}" controls width="500">`
                                            );
                                        });
                                }
                            );
                        },
                    },
                    {
                        header: '小地图视频',
                        dataIndex: 'miniMapVideoUrl',
                        render: function (data, rows, lineData) {
                            if (!data) {
                                return '待上传';
                            }

                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            if (
                                lineData.miniMapVideoUrl.indexOf('oss://') > -1
                            ) {
                                Store(
                                    [
                                        `jiakao-misc!route-video-process/data/getFileUrl?encodeData=${lineData.miniMapEncodeData}`,
                                    ],
                                    [
                                        {
                                            aliases: 'list',
                                        },
                                    ]
                                )
                                    .load()
                                    .done((retData) => {
                                        const value =
                                            retData.data.list.data.value;
                                        Widgets.dialog
                                            .html('小地图视频', {})
                                            .done(function (dialog) {
                                                $(dialog.body).html(value);
                                            });
                                    });
                            } else {
                                Widgets.dialog
                                    .html('小地图视频', {})
                                    .done(function (dialog) {
                                        $(dialog.body).html(
                                            lineData.miniMapVideoUrl
                                        );
                                    });
                            }
                        },
                    },
                    {
                        header: '互动视频小地图',
                        dataIndex: 'practiceMapVideoKey',
                        render: function (data, rows, lineData) {
                            if (!data) {
                                return '待上传';
                            }

                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            keyExchangeData(lineData.practiceMapVideoKey).then(
                                (res) => {
                                    if (!res) {
                                        Widgets.dialog.alert(
                                            '视频系统转码中...'
                                        );
                                        return;
                                    }
                                    Widgets.dialog
                                        .html('互动视频小地图', {})
                                        .done(function (dialog) {
                                            $(dialog.body).html(
                                                `<video src="${res}" controls width="500">`
                                            );
                                        });
                                }
                            );
                        },
                    },
                    {
                        header: '试看路线视频',
                        dataIndex: 'videoPreKey',
                        render: function (data, rows, lineData) {
                            if (!lineData.videoPreKey) {
                                return '待上传';
                            }
                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            keyExchangeData(lineData.videoPreKey).then(
                                (res) => {
                                    if (!res) {
                                        Widgets.dialog.alert(
                                            '视频系统转码中...'
                                        );
                                        return;
                                    }
                                    Widgets.dialog
                                        .html('试看路线视频', {})
                                        .done(function (dialog) {
                                            $(dialog.body).html(
                                                `<video src="${res}" controls width="500">`
                                            );
                                        });
                                }
                            );
                            // Widgets.dialog.html('试看路线视频', {}).done(function (dialog) {
                            //     $(dialog.body).html(`<video src="${lineData.videoPreData}" controls width="500">`)
                            // })
                        },
                    },
                    {
                        header: '互动视频原片',
                        dataIndex: 'videoPracticeKey',
                        render: function (data, rows, lineData) {
                            if (!lineData.videoPracticeKey) {
                                return '待上传';
                            }
                            return `<a>点击查看</a>`;
                        },
                        click: function (table, row, lineData) {
                            keyExchangeData(lineData.videoPracticeKey).then(
                                (res) => {
                                    if (!res) {
                                        Widgets.dialog.alert(
                                            '视频系统转码中...'
                                        );
                                        return;
                                    }
                                    Widgets.dialog
                                        .html('互动视频原片', {})
                                        .done(function (dialog) {
                                            $(dialog.body).html(
                                                `<video src="${res}" controls width="500">`
                                            );
                                        });
                                }
                            );
                            // Widgets.dialog.html('互动视频原片', {}).done(function (dialog) {
                            //     $(dialog.body).html(`<video src="${lineData.videoPracticeData}" controls width="500">`)
                            // })
                        },
                    },
                    {
                        header: '是否生成轨迹图',
                        dataIndex: 'trackCover',
                        render: function (data) {
                            if (data) {
                                return `<a>查看</a>`;
                            } else {
                                return '未生成';
                            }
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog
                                .html('轨迹图', {})
                                .done(function (dialog) {
                                    $(dialog.body).html(
                                        `<img src="${lineData.trackCover}" width="600">`
                                    );
                                });
                        },
                    },
                    {
                        header: '是否带背景音乐',
                        dataIndex: 'attachBgm',
                        render: function (data) {
                            return data ? '是' : data == false ? '否' : '-';
                        },
                    },
                    {
                        header: '制作进度',
                        dataIndex: 'processStatus',
                        render: function (data) {
                            return processStatusMap[data];
                        },
                    },
                    {
                        header: '错误原因字段',
                        dataIndex: 'errorReason',
                    },
                    {
                        header: '审核状态',
                        dataIndex: 'auditStatus',
                        render: function (data) {
                            return auditStatusMap[data];
                        },
                    },
                    {
                        header: '责任人',
                        dataIndex: 'liableUserName',
                    },
                    {
                        header: '上次同步时间',
                        dataIndex: 'syncTime',
                        render: function (data) {
                            return Utils.format.date(
                                data,
                                'yyyy-MM-dd HH:mm:ss'
                            );
                        },
                    },
                ],
            },
            ['jiakao-misc!route-video-process/data/list3?processStatusGTE=4'],
            panel,
            null
        ).render();
    };

    const getWebSign = (a = 1) => {
        var c = Math.abs(
            parseInt(new Date().getTime() * Math.random() * 10000)
        ).toString();
        var d = 0;
        var b;
        var e;

        for (b = 0; c.length > b; b++) {
            d += parseInt(c[b]);
        }
        e = (function (f) {
            return function (g, h) {
                return h - '' + g.length <= 0
                    ? g
                    : (f[h] || (f[h] = Array(h + 1).join(0))) + g;
            };
        })([]);

        d += c.length;
        d = e(d, 3 - d.toString().length);
        return a.toString() + c + d;
    };

    async function uploadImageH5(imgData) {
        const sign = getWebSign();
        return new Promise(function (resolve, reject) {
            const formData = new FormData();
            formData.append('base64', imgData);
            formData.append('type', 'image');
            formData.append('bucket', 'mcweb-image');
            formData.append('_r', sign);
            formData.append('_s', sign);
            formData.append('prefix', '');
            setTimeout(() => {
                Ajax.request(
                    `https://upload-image.kakamobi.cn/api/web/upload/base64-image.htm`,
                    {
                        data: formData,
                        method: 'post',
                        contentType: false,
                        processData: false,
                        timeout: 20 * 1000,
                        success: function (data) {
                            resolve(data[0].url);
                        },
                        error: function (err) {
                            Widgets.dialog.alert(err.message);
                            console.log(err);
                        },
                    }
                );
            }, 500);
        });
    }

    function keyExchangeData(key) {
        return new Promise(function (resolve, reject) {
            Store(
                [
                    `jiakao-misc!route-video-process/data/getByKey?videoKey=${key}`,
                ],
                [
                    {
                        aliases: 'list',
                    },
                ]
            )
                .load()
                .done((retData) => {
                    resolve(retData.data.list.data.value);
                });
        });
    }

    return {
        list: list,
    };
});
