/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form','simple!core/plugin','jiakao-misc!app/common/download','simple!app/layout/main',], function(Template, Table, Utils, Widgets, Store, Form,Plugin,Download,Layout) {
    const processStatusMap = {
        0: '初始上传',
        1: '预处理中',
        2: '预处理完成',
        10: '初剪上传中',
        11: '初剪上传完成',
        20: '开始制作中/重新制作中',
        21: '制作完成',
        30: '上传小地图',
        31: '小地图上传完成',
        40: '成片制作中',
        41: '成片制作完成'
    };
    
    const routeCarTypeMap = {
        1: '手动',
        2: '自动',
        3: '手动和自动'
    }

    function keyExchangeData(key) {
        return Store([`jiakao-misc!route-video-process/data/getByKey?videoKey=${key}`], [{
            aliases: 'list'
        }]).load().promise().then(retData => retData.data.list.data.value);
    }

    var list = function (panel) {
        Table({
            description: '互动视频时间轴管理',
            title: '互动视频时间轴管理',
            searchReset: {
                class: 'danger'
            },
            search: [{
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name:'请选择区县'
                        }]
                    }
                })
            }, {
                header: '视频id',
                xtype: 'text',
                dataIndex:'coachVideoId',
                placeholder:'视频id'
            }, {
                header: '教练id',
                xtype: 'text',
                dataIndex:'coachId',
                placeholder:'教练id'
            }, {
                header: '考场名称',
                xtype: 'text',
                dataIndex:'sceneName',
                placeholder:'考场名称'
            }, {
                header: '考场key',
                xtype: 'text',
                dataIndex:'sceneKey',
                placeholder:'考场key'
            }, {
                header: '城市名称',
                xtype: 'text',
                dataIndex:'cityName',
                placeholder:'城市名称'
            }, {
                dataIndex: 'startTime',
                xtype: 'date',
                placeholder: '开始时间'
            },
            {
                dataIndex: 'endTime',
                xtype: 'date',
                placeholder: '结束时间'
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑时间轴',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].timelinePractice ? '编辑时间轴' : ''
                    },
                    click: function (table, row, lineData) {
                        Store([`jiakao-misc!route-video-process/data/view?id=${lineData.id}`], [{
                            aliases: 'list'
                        }]).load().done((store) => {
                            const data = store.data.list.data;
                            
                             keyExchangeData(data.videoItemKey).then(res => {
                                 data.videoItemData = res;
                                 console.log(data,'ddddd');
                                require(['jiakao-misc!app/film-video/index'], function (demo) {
                                    var panelId = 'film-video' + data.id;
                                    var viewpanel = Layout.panel(panelId);
                                    if (viewpanel.length === 0) {
                                        viewpanel = Layout.panel({
                                            id: panelId,
                                            name: '互动视频时间轴编辑'
                                        });
                                    }
                                    demo.list(
                                        viewpanel,
                                        data,
                                        {
                                            type: 'timelinePractice',
                                            title: '互动视频时间轴编辑'
                                        }
                                    )
                                })
                            })
                            
                        })
                    }
                },
                {
                    name: '同步时间轴数据',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        Store([`jiakao-misc!route-video-process/data/syncTimeline?id=${lineData.id}`]).load().done(store => {
                            Widgets.dialog.alert('同步成功')
                            table.render()
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        })
                    }
                }
            ],
            columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '考场key',
                        dataIndex: 'sceneKey'
                },
                {
                    header: '考场名称第一行',
                    dataIndex: 'sceneName'
                },
                {
                    header: '考场名称第二行',
                    dataIndex: 'subSceneName'
                },
                {
                    header: '线路名称',
                    dataIndex: 'routeName'
                },
                {
                    header: '视频成片',
                    dataIndex: 'videoPracticeData',
                    render: function (data, rows, lineData) {
                        if (!lineData.videoPracticeKey) {
                            return '待上传';
                        }

                        if (!data) {
                            return '处理中';
                        }

                        return `<a>点击查看</a>`
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('视频成片', {}).done(function (dialog) {
                            $(dialog.body).html(`<video src="${lineData.videoPracticeData}" controls width="500">`)
                        })
                    }
                },
            
                {
                    header: '时间轴数据',
                    dataIndex: 'timelinePractice',
                    render: function () {
                        return `<a>点击查看</a>`
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('时间轴数据', {}).done(function (dialog) {
                            var data = lineData.timelinePractice ? JSON.stringify(JSON.parse(lineData.timelinePractice), null, 4) : ''
                            $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                        })
                    }
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!route-video-process/data/list4'], panel, null).render();
          
    }

    function keyExchangeData(key) {
        return new Promise(function (resolve, reject) {
            Store([`jiakao-misc!route-video-process/data/getByKey?videoKey=${key}`], [{
                aliases: 'list'
            }]).load().done(retData => {
                resolve(retData.data.list.data.value);
            })     
       })
            
    }

    return {
        list: list
    }

});