/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form','simple!core/plugin','jiakao-misc!app/common/download','simple!app/layout/main',], function(Template, Table, Utils, Widgets, Store, Form,Plugin,Download,Layout) {
    const processStatusMap = {
        0: '初始上传',
        1: '预处理中',
        2: '预处理完成',
        10: '初剪上传中',
        11: '初剪上传完成',
        20: '开始制作中/重新制作中',
        21: '制作完成',
        30: '上传小地图',
        31: '小地图上传完成',
        40: '成片制作中',
        41: '成片制作完成',
        42:'制作失败'
    };
    
    const routeCarTypeMap = {
        1: '手动',
        2: '自动',
        3: '手动和自动'
    }

    var list = function (panel) {
        Table({
            description: '视频二次剪辑更新',
            title: '视频二次剪辑更新',
            searchReset: {
                class: 'danger'
            },
            search: [{
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name:'请选择区县'
                        }]
                    }
                })
            }, {
                header: '视频id',
                xtype: 'text',
                dataIndex:'coachVideoId',
                placeholder:'视频id'
            }, {
                header: '教练id',
                xtype: 'text',
                dataIndex:'coachId',
                placeholder:'教练id'
            }, {
                header: '考场名称',
                xtype: 'text',
                dataIndex:'sceneName',
                placeholder:'考场名称'
            }, {
                header: '考场key',
                xtype: 'text',
                dataIndex:'sceneKey',
                placeholder:'考场key'
            }, {
                header: '城市名称',
                xtype: 'text',
                dataIndex:'cityName',
                placeholder:'城市名称'
            }, {
                dataIndex: 'startTime',
                xtype: 'date',
                placeholder: '开始时间'
            },
            {
                dataIndex: 'endTime',
                xtype: 'date',
                placeholder: '结束时间'
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '下载试看视频',
                    class: 'info',
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('试看路线视频', {}).done(function (dialog) {
                            $(dialog.body).html(lineData.videoPreData)
                        })
                    }
                },
                {
                    name: '下载路线视频',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('路线视频', {}).done(function (dialog) {
                            $(dialog.body).html(lineData.videoItemData)
                        })
                    }
                },
                {
                    name: '下载互动视频',
                    class: 'danger',
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('互动视频原片', {}).done(function (dialog) {
                            $(dialog.body).html(lineData.videoPracticeData)
                        })
                    }
                }
            ],
            columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '考场key',
                        dataIndex: 'sceneKey'
                },
                {
                    header: '考场名称第一行',
                    dataIndex: 'sceneName'
                },
                {
                    header: '考场名称第二行',
                    dataIndex: 'subSceneName'
                },
                {
                    header: '城市',
                    dataIndex: 'cityName',
                    render: function (data,arr, lineData) {
                        return data + lineData.cityCode
                    }
                },
                {
                    header: '区县',
                    dataIndex: 'countyName',
                    render: function (data,arr, lineData) {
                        return data + lineData.countyCode
                    }
                },
                {
                    header: '线路名称',
                    dataIndex: 'routeName'
                },
                {
                    header: '合适车型',
                    dataIndex: 'routeCarType',
                    render: function (data) {
                        return routeCarTypeMap[data]
                    }
                },
                {
                    header: '车辆型号',
                    dataIndex: 'vehicleModel'
                },
                // {
                //     header: '路线视频',
                //     dataIndex: 'videoItemData',
                //     render: function (data,rows,lineData) {
                //         if (!lineData.videoItemKey) {
                //             return '待上传';
                //         }
                //         console.log(data,'21512512');
                //         if (!data) {
                //             return '处理中';
                //         }

                //         return `<a>点击查看</a>`
                //     },
                //     click: function (table, row, lineData) {
                //         Widgets.dialog.html('路线视频', {}).done(function (dialog) {
                //             $(dialog.body).html(lineData.videoItemData)
                //         })
                //     }
                // },
                // {
                //     header: '试看路线视频',
                //     dataIndex: 'videoPreData',
                //     render: function (data,rows,lineData) {
                //         if (!lineData.videoPreKey) {
                //             return '待上传';
                //         }

                //         if (!data) {
                //             return '处理中';
                //         }

                //         return `<a>点击查看</a>`
                //     },
                //     click: function (table, row, lineData) {
                //       
                //     }
                // },
                // {
                //     header: '互动视频原片',
                //     dataIndex: 'videoPracticeData',
                //     render: function (data, rows, lineData) {
                //         if (!lineData.videoPracticeKey) {
                //             return '待上传';
                //         }

                //         if (!data) {
                //             return '处理中';
                //         }

                //         return `<a>点击查看</a>`
                //     },
                //     click: function (table, row, lineData) {
                //         Widgets.dialog.html('互动视频原片', {}).done(function (dialog) {
                //             $(dialog.body).html(lineData.videoPracticeData)
                //         })
                //     }
                // },
                {
                    header: '更新人',
                    dataIndex:'updateUserName'
                }
            ]
        }, ['jiakao-misc!route-video-process/data/list3?processStatus=41'], panel, null).render();
    }

 

    return {
        list: list
    }

});