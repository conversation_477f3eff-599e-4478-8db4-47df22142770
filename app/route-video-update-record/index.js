/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin',], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var renderSchoolFn = function (dom, valueMetaId) {
        let city;
        let area;
        if (valueMetaId) {
            city = dom.item('areaCode-group').find('#city option:selected').text();
            area = dom.item('areaCode-group').find('#area option:selected').text();
        } else {
            city = dom.item('cityCode-group').find('#city option:selected').text();
            area = dom.item('cityCode-group').find('#area option:selected').text();
        }
        if (city.indexOf('请选择') > -1) {
            city = ''
        }
        if (area.indexOf('请选择') > -1) {
            area = ''
        }
        console.log('valueMetaId', valueMetaId)
        Plugin('jiakao-misc!auto-prompt', {
            target: dom.item('metaId'),
            store: `jiakao-misc!route-video-meta/data/list?cityName=${city}&areaName=${area}`,
            dataIndex: 'metaId',
            value: valueMetaId,
            index: {
                key: 'id',
                value: 'name',
                search: 'name'
            },
            placeholder: '选择已有的考场',
            isMulti: false,
            defaultVal: false
        }, function (plugin, value) {
            console.log('value', value)
            renderItemFn(dom, value)
        }).render()
    }

    var renderItemFn = function (dom, metaId, itemId) {
        Plugin('jiakao-misc!auto-prompt', {
            target: dom.item('itemId'),
            store: `jiakao-misc!route-video-item/data/list?metaId=${metaId}`,
            dataIndex: 'itemId',
            value: itemId,
            index: {
                key: 'id',
                value: 'name',
                search: 'name'
            },
            placeholder: '选择已有的路线',
            isMulti: false,
            defaultVal: false
        }, function (plugin, value) {
        }).render()
    }



    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!route-video-update-record/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var metaId = form.metaId && form.metaId.value
                    var newmetaName = form.newmetaName.value
                    var itemId = form.itemId && form.itemId.value
                    var newitemIdName = form.newitemIdName.value
                    let object = {}
                    if (!metaId || metaId <= 0) {
                        object.metaName = newmetaName || ''
                    }
                    if (!itemId || itemId <= 0) {
                        object.itemName = newitemIdName
                    }
                    return object
                },
            },
            renderAfter: function (config, dom, data) {
                //在此绑定事件

                setTimeout(() => {
                    var $province = dom.item('cityCode-group').find('#province');
                    var $city = dom.item('cityCode-group').find('#city');
                    var $area = dom.item('cityCode-group').find('#area');
                    $province.on('change', (e) => {
                        console.log(12512);
                        renderSchoolFn(dom)
                    })
                    $city.on('change', (e) => {
                        renderSchoolFn(dom)
                    })
                    $area.on('change', (e) => {
                        renderSchoolFn(dom)
                    })

                    $('#metaId').bind('change', () => {
                        console.log(1);
                    })
                }, 200)




            },
            columns: [
                {
                    header: '考场的城市：',
                    xtype: Plugin('jiakao-misc!select-district', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {
                        // getAllMetaName();
                    }),
                    dataIndex: 'cityCode'
                },
                {
                    header: '考场名称：',
                    dataIndex: 'metaId',
                    xtype: function () {
                        return '<div data-item="metaId" class="dev" id="metaId"></div>'
                    }
                },
                {
                    header: '新考场名称：',
                    dataIndex: 'newmetaName',
                    xtype: 'text',
                    placeholder: '请输入新考场名称'
                },
                {
                    header: '路线：',
                    dataIndex: 'itemId',
                    xtype: function () {
                        return '<div data-item="itemId" class="dev"></div>'
                    }
                },
                {
                    header: '新路线：',
                    xtype: 'text',
                    dataIndex: 'newitemIdName',
                    placeholder: '请输入新路线'
                },
                {
                    header: '原因：',
                    dataIndex: 'reason',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择'
                    },{
                        key: '路线错误',
                        value:'路线错误'
                    }, {
                        key: '路线变更',
                        value:'路线变更'
                        }, {
                        key: '视频超期',
                            value:'视频超期'
                    }],
                    maxlength: 128,
                    check: 'required',
                    placeholder: '原因'
                },
                {
                    header: '处理人名称：',
                    dataIndex: 'handleUserName',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '处理人名称'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '待更新考场管理',
            title: '待更新考场管理',
            selector: {
                dataIndex: 'id',
            },
            search: [{
                header: '城市等级',
                dataIndex: 'cityLevel',
                xtype: Plugin('simple!auto-prompt2', {
                    store: [{
                        key: 0,
                        value: '一般'
                    }, {
                        key: 1,
                        value: '中'
                    }, {
                        key: 2,
                        value: '高'
                    }, {
                        key: 3,
                        value: '极高'
                    }],
                    dataIndex: 'cityLevel',
                    isMulti: true,
                    defaultVal: false,
                    placeholder: '请选择城市等级 '
                }, function (plugin, value) {
                }),
            }, {
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district2', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
            }, {
                dataIndex: 'status',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择更新状态'
                }, {
                    key: 0,
                    value: '待更新'
                }, {
                    key: 1,
                    value: '已更新'
                }, {
                    key: 2,
                    value: '已关闭'
                }]
            }, {
                dataIndex: 'handleUserName',
                xtype: 'text',
                placeholder: '处理人'
            }, {
                dataIndex: 'metaName',
                xtype: 'text',
                placeholder: '考场名'
            },  {
                header: '原因：',
                dataIndex: 'reason',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择'
                },{
                    key: '路线错误',
                    value:'路线错误'
                }, {
                    key: '路线变更',
                    value:'路线变更'
                    }, {
                    key: '视频超期',
                        value:'视频超期'
                }],
                maxlength: 128,
                placeholder: '原因'
            }, {
                placeholder: '终端场地ID',
                dataIndex: 'unifiedExamSiteId',
                xtype: 'text',
            }, {
                placeholder: '终端路线ID',
                dataIndex: 'unifiedRouteId',
                xtype: 'text'
            },{
                dataIndex: 'newlyUpload',
                xtype: 'select',
                store: [
                    {
                        key: '',
                        value: '请选择是否有新视频上传'
                    },
                    {
                        key: false,
                        value: '否'
                    },
                    {
                        key: true,
                        value: '是'
                    }
                ]
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    },
                    {
                        name: '批量删除',
                        class: 'danger',
                        click: function (table, dom, config, selectedArr) {
                            //  console.log(selectedArr.join(','));
                            if (selectedArr.length > 0) {
                                Widgets.dialog.confirm(`确认删除这${selectedArr.length}个考场吗？`, function (ev, status) {
                                    if (status) {
                                        Store(['jiakao-misc!route-video-update-record/data/batchDelete'], [{
                                            params: {
                                                ids: selectedArr.join(',')
                                            }
                                        }]).save().done(function (store) {
                                            table.render();
                                        }).fail(function () {
                                        });
                                    } else { }
                                })
                            } else {
                                Widgets.dialog.alert('请选择数据')
                            }

                        },
                    },
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!route-video-update-record/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '城市：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '区域：',
                            dataIndex: 'areaCode'
                        },
                        {
                            header: '考场id：',
                            dataIndex: 'metaId'
                        },
                        {
                            header: '城市等级',
                            dataIndex: 'cityLevel',
                            render: function (data) {
                                return data == 0 ? '一般' : data == 1 ? '中' : data == 2 ? '高' : data == 3 ? '极高' : ''
                            }
                        },
                        {
                            header: '考场名称：',
                            dataIndex: 'metaName'
                        },
                        {
                            header: '路线id：',
                            dataIndex: 'itemId'
                        },
                        {
                            header: '路线名称：',
                            dataIndex: 'itemName'
                        },
                        {
                            header: '原因：',
                            dataIndex: 'reason'
                        },
                        {
                            header: '处理人id：',
                            dataIndex: 'handleUserId'
                        },
                        {
                            header: '处理人名称：',
                            dataIndex: 'handleUserName'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            render: function (data) {
                                return data == 0 ? '待更新' : data == 1 ? '已更新' : data == 2 ? '已关闭' : ''
                            }
                        },

                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更新人：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: function (form) {
                            var metaId = form.metaId && form.metaId.value
                            var newmetaName = form.newmetaName.value
                            var itemId = form.itemId && form.itemId.value
                            var newitemIdName = form.newitemIdName.value
                            let object = {}
                            if (!metaId || metaId <= 0) {
                                object.metaName = newmetaName || ''
                            }
                            if (!itemId || itemId <= 0) {
                                object.itemName = newitemIdName
                            }
                            return object
                        },
                    },
                    renderAfter: function (config, dom, data) {
                        //在此绑定事件
                        console.log(data, 'datadata');
                        let metaId = data.data.metaId
                        let itemId = data.data.itemId
                        let areaCode = data.data.areaCode
                        let cityCode = data.data.cityCode
                        setTimeout(() => {
                            var $province = dom.item('areaCode-group').find('#province');
                            var $city = dom.item('areaCode-group').find('#city');
                            var $area = dom.item('areaCode-group').find('#area');
                            $province.on('change', (e) => {
                                renderSchoolFn(dom)
                            })
                            $city.on('change', (e) => {
                                renderSchoolFn(dom)
                            })
                            $area.on('change', (e) => {
                                renderSchoolFn(dom, metaId);
                                console.log(itemId, 'itemIditemId');
                                renderItemFn(dom, metaId, itemId)
                            })
                            if (areaCode > 0 && cityCode > 0) {
                                $area.trigger('change');
                            }

                        }, 200)
                    },
                    store: {
                        load: 'jiakao-misc!route-video-update-record/data/view',
                        save: 'jiakao-misc!route-video-update-record/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '考场的城市：',
                            dataIndex: 'areaCode',
                            xtype: Plugin('jiakao-misc!select-district', {
                                name: 'cityCode',
                                areaName: 'areaCode',
                                insert: {
                                    province: [{
                                        code: '',
                                        name: '请选择省份'
                                    }],
                                    city: [{
                                        code: '',
                                        name: '请选择市'
                                    }],
                                    area: [{
                                        code: '',
                                        name: '请选择区域'
                                    }]
                                }
                            }, function (plugin, code) {


                            }),

                        },
                        {
                            header: '考场名称：',
                            dataIndex: 'metaId',
                            xtype: function () {
                                return '<div data-item="metaId" class="dev" id="metaId"></div>'
                            }
                        },
                        {
                            header: '新考场名称：',
                            dataIndex: 'newmetaName',
                            xtype: 'text',
                            render: function (data, index, obj) {
                                if (obj.metaId <= 0) {
                                    return obj.metaName
                                }
                            },
                            placeholder: '请输入新考场名称'
                        },
                        {
                            header: '路线：',
                            dataIndex: 'itemId',
                            xtype: function () {
                                return '<div data-item="itemId" class="dev"></div>'
                            }
                        },
                        {
                            header: '新路线：',
                            xtype: 'text',
                            render: function (data, index, obj) {
                                if (obj.itemId <= 0) {
                                    return obj.itemName
                                }
                            },
                            dataIndex: 'newitemIdName',
                            placeholder: '请输入新路线'
                        },
                        {
                            header: '原因：',
                            dataIndex: 'reason',
                            xtype: 'select',
                            store: [{
                                key: '',
                                value: '请选择'
                            },{
                                key: '路线错误',
                                value:'路线错误'
                            }, {
                                key: '路线变更',
                                value:'路线变更'
                                }, {
                                key: '视频超期',
                                    value:'视频超期'
                            }],
                            maxlength: 128,
                            check: 'required',
                            placeholder: '原因'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!route-video-update-record/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '城市',
                    dataIndex: 'cityName'
                },
                {
                    header: '区域',
                    dataIndex: 'areaName'
                },
                {
                    header: '终端场地id',
                    dataIndex: 'unifiedExamSiteId'
                },
                {
                    header: '考场id',
                    dataIndex: 'metaId'
                },
                {
                    header: '考场名称',
                    dataIndex: 'metaName',
                    render: function (data) {
                        return '<a>' + data + '</a>'
                    },
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('route-video-item-' + lineData.metaId);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    metaId: 'route-video-item-' + lineData.metaId,
                                    name: lineData.metaName + '-路线'
                                })
                                console.log(nPanel, 'nPanelnPanel');
                            }
                            require(['jiakao-misc!app/route-video-item/index'], function (Item) {
                                lineData.id = lineData.metaId;
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                {
                    header: '城市等级',
                    dataIndex: 'cityLevel',
                    render: function (data) {
                        return data == 0 ? '一般' : data == 1 ? '中' : data == 2 ? '高' : data == 3 ? '极高' : ''
                    }
                },
                {
                    header: '终端路线ID',
                    dataIndex: 'unifiedRouteId'
                },
                {
                    header: '路线id',
                    dataIndex: 'itemId'
                },
                {
                    header: '路线名称',
                    dataIndex: 'itemName'
                },
                {
                    header: '新视频上传',
                    dataIndex: 'newlyUpload',
                    render: function (data) {
                        return data ? '是' : '否'
                    }
                },
                {
                    header: '原因',
                    dataIndex: 'reason'
                },
                {
                    header: '处理人名称',
                    dataIndex: 'handleUserName'
                },
                {
                    header: '状态：',
                    dataIndex: 'status',
                    render: function (data) {
                        return data == 0 ? '待更新' : data == 1 ? '已更新' : data == 2 ? '已关闭' : ''
                    }
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }, {
                    header: '视频更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'videoUpdateTime'
                }



            ]
        }, ['jiakao-misc!route-video-update-record/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});