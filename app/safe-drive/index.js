/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!safe-drive/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 45,
                    check: 'required',
                    placeholder: '标题'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    check: 'required',
                    maxlength: 45,
                    placeholder: '描述'
                },
                {
                    header: '一元视频key：',
                    dataIndex: 'videoKey',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '一元视频key'
                },
                {
                    header: '视频类型：',
                    dataIndex: 'videoType',
                    xtype: 'select',
                    store: [{
                            key: '0',
                            value: '普通视频'
                        },
                        {
                            key: '1',
                            value: '一元视频'
                        },

                    ]

                },
                {
                    header: '封面图：',
                    check: 'required',
                    dataIndex: 'image',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'image',
                        uploadIndex: 'image',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '课程顺序：',
                    dataIndex: 'indexName',
                    check: 'required',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '课程顺序'
                },
                {
                    header: '视频地址：',
                    dataIndex: 'videoUrl',
                    xtype: 'textarea',
                    check: 'required',
                    maxlength: 255,
                    placeholder: '视频地址'
                },
                {
                    header: '视频时长：',
                    dataIndex: 'videoDuration',
                    check: 'required',
                    xtype: 'text',
                    placeholder: '视频时长'
                },


            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '安全驾驶列表',
            title: '安全驾驶列表',

            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!safe-drive/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '封面图：',
                            dataIndex: 'image'
                        },
                        {
                            header: '课程顺序：',
                            dataIndex: 'indexName'
                        },
                        {
                            header: '视频地址：',
                            dataIndex: 'videoUrl'
                        },
                        {
                            header: '视频时长：',
                            dataIndex: 'videoDuration'
                        },


                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!safe-drive/data/view',
                        save: 'jiakao-misc!safe-drive/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '标题',
                            check: 'required'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '描述',
                            check: 'required'
                        },
                        {
                            header: '一元视频key：',
                            dataIndex: 'videoKey',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '一元视频key'
                        },
                        {
                            header: '视频类型：',
                            dataIndex: 'videoType',
                            xtype: 'select',
                            store: [{
                                    key: '0',
                                    value: '普通视频'
                                },
                                {
                                    key: '1',
                                    value: '一元视频'
                                },

                            ]

                        },
                        {
                            header: '封面图：',
                            dataIndex: 'image',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'image',
                                uploadIndex: 'image',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            }),
                            check: 'required'

                        },
                        {
                            header: '课程顺序：',
                            dataIndex: 'indexName',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '课程顺序',
                            check: 'required'

                        },
                        {
                            header: '视频地址：',
                            dataIndex: 'videoUrl',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '视频地址',
                            check: 'required'

                        },
                        {
                            header: '视频时长：',
                            dataIndex: 'videoDuration',
                            xtype: 'text',
                            placeholder: '视频时长',
                            check: 'required'

                        },


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!safe-drive/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '一元视频key',
                    dataIndex: 'videoKey',

                },
                {
                    header: '视频类型',
                    dataIndex: 'videoType',
                    render: function (data) {
                        if (data == 1) {
                            return '一元视频'
                        } else if (data == 0) {
                            return '普通视频'
                        }
                    }


                },
                {
                    header: '封面图',
                    dataIndex: 'image',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src='${lineData.image}'>`)
                    }
                },
                {
                    header: '课程顺序',
                    dataIndex: 'indexName'
                },
                {
                    header: '视频地址',
                    dataIndex: 'videoUrl',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(lineData.videoUrl)
                    }
                },
                {
                    header: '视频时长',
                    dataIndex: 'videoDuration'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!safe-drive/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});