/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form','simple!core/plugin'], function(Template, Table, Utils, Widgets, Store, Form,Plugin) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!scene-practice-group/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '名称'
             },
             {
                 header: '封面图：',
                 dataIndex: 'image',
                 xtype: Plugin('jiakao-misc!upload', {
                     dataIndex: 'image',
                     uploadIndex: 'image',
                     bucket: "exam-room",
                     isSingle: true,
                     placeholder: '请选择上传文件',
                     url: 'simple-upload3://upload/file.htm'
                 }, function () {
                     console.log(arguments)
                 })
             },

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '情景练习分组列表',
            title: '情景练习分组列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!scene-practice-group/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '名称：',
                         dataIndex: 'name'
                     },
                     {
                         header: '封面图：',
                         dataIndex: 'image',
                         render: function (data) {
                             return "<img width='100'  src='" + data + "'/>"
                         }
                     },
                     {
                         header: '创建时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '创建人id：',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: '创建人：',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '修改时间：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: '修改人id：',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: '修改人：',
                         dataIndex: 'updateUserName'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!scene-practice-group/data/view',
                        save: 'jiakao-misc!scene-practice-group/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '名称：',
                 dataIndex: 'name',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '名称'
             },
             {
                 header: '封面图：',
                 dataIndex: 'image',
                 xtype: Plugin('jiakao-misc!upload', {
                     dataIndex: 'image',
                     uploadIndex: 'image',
                     bucket: "exam-room",
                     isSingle: true,
                     placeholder: '请选择上传文件',
                     url: 'simple-upload3://upload/file.htm'
                 }, function () {
                     console.log(arguments)
                 })
             },


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!scene-practice-group/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '名称',
                         dataIndex: 'name'
                     },
                     {
                         header: '封面图',
                         dataIndex: 'image',
                         render: function (data) {
                             return "<img width='100' src='" + data + "'/>"
                         }
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '创建人id',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '修改时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: '修改人id',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: '修改人',
                         dataIndex: 'updateUserName'
                     }

            ]
        }, ['jiakao-misc!scene-practice-group/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});