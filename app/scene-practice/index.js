/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!scene-practice/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '标题：',
                dataIndex: 'title',
                xtype: 'text',
                maxlength: 64,
                placeholder: '标题'
            },
                {
                    header: '副标题：',
                    dataIndex: 'subTitle',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '副标题'
                },
                {
                    header: '描述：',
                    dataIndex: 'desc',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '描述'
                }, {
                    header: '分组id:',
                    dataIndex: 'groupId',
                    xtype: 'select',
                    store: 'jiakao-misc!scene-practice/data/groupList'

                },
                {
                    header: '科目，1和4：',
                    dataIndex: 'kemu',
                    xtype: 'text',
                    placeholder: '科目，1和4'
                },
                {
                    header: '视频时长 (秒)：',
                    dataIndex: 'duration',
                    xtype: 'text',
                    placeholder: '视频时长 (秒)'
                },
                {
                    header: '原始视频：',
                    dataIndex: 'videoOriginal',
                    xtype: 'textarea',
                    maxlength: 512,
                    placeholder: '原始视频'
                },
                {
                    header: '加密视频：',
                    dataIndex: 'videoEncode',
                    xtype: 'textarea',
                    maxlength: 512,
                    placeholder: '加密视频'
                },
                {
                    header: '强化练习id，逗号分隔：',
                    dataIndex: 'tagId',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '强化练习id，逗号分隔'
                },
                {
                    header: '点评id：',
                    dataIndex: 'commentId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '点评id'
                },
                {
                    header: '安卓做题节点配置：',
                    dataIndex: 'pointList',
                    xtype: 'textarea',

                    placeholder: '做题节点配置'
                },
                {
                    header: 'ios做题节点配置：',
                    dataIndex: 'pointListIos',
                    xtype: 'textarea',

                    placeholder: 'ios做题节点配置'
                },
                {
                    header: '封面图：',
                    dataIndex: 'videoImage',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'videoImage',
                        uploadIndex: 'videoImage',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '试看时长，单位毫秒：',
                    dataIndex: 'previewTime',
                    xtype: 'text',
                    placeholder: '试看时长，单位毫秒'
                },
                {
                    header: '顺序：',
                    dataIndex: 'order',
                    xtype: 'text',
                    placeholder: '顺序'
                }

            ]
        }).add();
    }

    var list = function (panel) {

        Table({
            description: 'scene-practice列表',
            title: 'scene-practice列表',
            search: [
                {
                    dataIndex: 'groupId',
                    xtype: 'select',
                    store: 'jiakao-misc!scene-practice/data/groupList2',
                    insert: [
                        {
                            key: '', value: '选择groupId'
                        }
                    ],

                }
            ],

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!scene-practice/data/view',
                columns: [
                    {
                        header: '#',
                        dataIndex: 'id'
                    },
                    {
                        header: '标题：',
                        dataIndex: 'title'
                    },
                    {
                        header: '副标题：',
                        dataIndex: 'subTitle'
                    },
                    {
                        header: '描述：',
                        dataIndex: 'desc'
                    },
                    {
                        header: '分组id',
                        dataIndex: 'groupId'
                    },
                    {
                        header: '科目，1和4：',
                        dataIndex: 'kemu'
                    },
                    {
                        header: '视频时长 (秒)：',
                        dataIndex: 'duration'
                    },
                    {
                        header: '原始视频：',
                        dataIndex: 'videoOriginal',

                    },
                    {
                        header: '加密视频：',
                        dataIndex: 'videoEncode',

                    },
                    {
                        header: '强化练习id，逗号分隔：',
                        dataIndex: 'tagId'
                    },
                    {
                        header: '点评id：',
                        dataIndex: 'commentId'
                    },
                    {
                        header: '安卓做题节点配置：',
                        dataIndex: 'pointList'
                    },
                    {
                        header: 'ios做题节点配置',
                        dataIndex: 'pointListIos',


                    },
                    {
                        header: '封面图：',
                        dataIndex: 'cover'
                    },
                    {
                        header: '试看时长，单位毫秒：',
                        dataIndex: 'previewTime'
                    },
                    {
                        header: '顺序：',
                        dataIndex: 'order'
                    },
                    {
                        header: 'createTime：',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: 'createUserId：',
                        dataIndex: 'createUserId'
                    },
                    {
                        header: 'createUserName：',
                        dataIndex: 'createUserName'
                    },
                    {
                        header: 'updateTime：',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    },
                    {
                        header: 'updateUserId：',
                        dataIndex: 'updateUserId'
                    },
                    {
                        header: 'updateUserName：',
                        dataIndex: 'updateUserName'
                    }

                ]
            },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!scene-practice/data/view',
                        save: 'jiakao-misc!scene-practice/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '标题'
                        },
                        {
                            header: '副标题：',
                            dataIndex: 'subTitle',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '副标题'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            maxlength: 128,
                            placeholder: '描述'
                        },
                        {
                            header: '分组id:',
                            dataIndex: 'groupId',
                            xtype: 'select',
                            store: 'jiakao-misc!scene-practice/data/groupList'

                        },
                        {
                            header: '科目，1和4：',
                            dataIndex: 'kemu',
                            xtype: 'text',
                            placeholder: '科目，1和4'
                        },
                        {
                            header: '视频时长 (秒)：',
                            dataIndex: 'duration',
                            xtype: 'text',
                            placeholder: '视频时长 (秒)'
                        },
                        {
                            header: '原始视频：',
                            dataIndex: 'videoOriginal',
                            xtype: 'textarea',
                            maxlength: 512,
                            placeholder: '原始视频'
                        },
                        {
                            header: '加密视频：',
                            dataIndex: 'videoEncode',
                            xtype: 'textarea',
                            maxlength: 512,
                            placeholder: '加密视频'
                        },
                        {
                            header: '强化练习id，逗号分隔：',
                            dataIndex: 'tagId',
                            xtype: 'text',
                            maxlength: 128,
                            placeholder: '强化练习id，逗号分隔'
                        },
                        {
                            header: '点评id：',
                            dataIndex: 'commentId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '点评id'
                        },
                        {
                            header: '安卓做题节点配置：',
                            dataIndex: 'pointList',
                            xtype: 'textarea',

                            placeholder: '安卓做题节点配置'
                        },
                        {
                            header: 'ios做题节点配置：',
                            dataIndex: 'pointListIos',
                            xtype: 'textarea',

                            placeholder: 'ios做题节点配置'
                        },
                        {
                            header: '封面图：',
                            dataIndex: 'cover',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '封面图'
                        },
                        {
                            header: '试看时长，单位毫秒：',
                            dataIndex: 'previewTime',
                            xtype: 'text',
                            placeholder: '试看时长，单位毫秒'
                        },
                        {
                            header: '顺序：',
                            dataIndex: 'order',
                            xtype: 'text',
                            placeholder: '顺序'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!scene-practice/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '副标题',
                    dataIndex: 'subTitle'
                },
                {
                    header: '描述',
                    dataIndex: 'desc'
                },
                {
                    header: '科目，1和4',
                    dataIndex: 'kemu'
                },
                {
                    header: '视频时长 (秒)',
                    dataIndex: 'duration'
                },
                {
                    header: '原始视频',
                    dataIndex: 'videoOriginal',
                    render: function (data) {
                        return '<a href=' + data + " target='_blank'> 原始视频链接 </a>"
                    }
                },
                {
                    header: '加密视频',
                    dataIndex: 'videoEncode',
                    click: function (a, b, lineData) {
                        Widgets.dialog.html('加密视频地址', {}).done(function (dialog) {
                            var url = lineData.videoEncode.split(':')[1] + ":" + lineData.videoEncode.split(':')[2]
                            url = url.slice(1, url.length - 2)


                            var oA = $("<a href='" + url + "' target='_blank'>" + lineData.videoEncode + "</a>");

                            dialog.body.append(oA);
                        })

                    },
                    render: function () {

                        return "<a>加密视频</a>"
                    }
                },
                {
                    header: '强化练习id，逗号分隔',
                    dataIndex: 'tagId'
                },
                {
                    header: '点评id',
                    dataIndex: 'commentId'
                },
                {
                    header: '安卓做题节点配置',
                    dataIndex: 'pointList',
                    click: function (a, b, lineData) {

                        Widgets.dialog.html('安卓做题节点配置', {}).done(function (dialog) {


                            var oA = $("<p>" + lineData.pointList + "</p>");

                            dialog.body.append(oA);
                        })

                    },
                    render: function (data) {
                        return "<a >节点配置</a>"
                    }
                },
                {
                    header: 'ios做题节点配置',
                    dataIndex: 'pointListIos',
                    click: function (a, b, lineData) {

                        Widgets.dialog.html('ios做题节点配置', {}).done(function (dialog) {


                            var oA = $("<p>" + lineData.pointListIos + "</p>");

                            dialog.body.append(oA);
                        })

                    },
                    render: function (data) {
                        if (data) {
                            return "<a>节点配置</a>"
                        }
                    }
                },
                {
                    header: '封面图',
                    dataIndex: 'cover',
                    render: function (data) {
                        return "<img src='" + data + "'/>"
                    }
                },
                {
                    header: '试看时长，单位毫秒',
                    dataIndex: 'previewTime'
                },
                {
                    header: '顺序',
                    dataIndex: 'order'
                },
                {
                    header: '分组名称',
                    dataIndex: 'groupName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!scene-practice/data/list'], panel, function () {

        }).render();
    }

    return {
        list: list
    }

});