/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";


define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/mc-pan',], function (Template, Table, Utils, Widgets, Store, Form, Plugin, MCPan) {

    var examItem = ['倒车入库', '侧方停车', '上坡起步', '直角转弯', '曲线行驶', '窄路掉头', '紧急停车', '模拟ETC', '模拟隧道', '雨雾湿滑路']
    var examItemArr = [];
    for (const item of examItem) {
        examItemArr.push({
            key: item,
            value: item
        })
    }

    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '上线'
    }
    var statusStore = Object.keys(statusMap).map(key=> {
        return {
            key: +key,
            value: statusMap[key]
        }
    })

    var tdSceneStatusMap = {
        0: '禁用',
        1: '启用',
        2: '测试启用'
    }
    var tdSceneStatusStore = Object.keys(tdSceneStatusMap).map(key=> {
        return {
            key: +key,
            value: tdSceneStatusMap[key]
        }
    })


    var checkResult = false;
    var initTopicId;

    function renderCodeFn(dom, data) {
        if (data) {
            setTimeout(() => {
                const value = JSON.parse(data.data.sceneIntroduce);
                dom.item('previewVideoId').val(value.previewVideoId);
                dom.item('videoId').val(value.videoId);
                dom.item('panoramaVideoId').val(value.panoramaVideoId);
                dom.item('trySeeHeight').val(value.trySeeHeight);
            }, 200)
        }
    }

    var importFile = function (table) {
        Table({
            title: '导入',
            width: 500,
            store: 'jiakao-misc!scene-supply/data/uploadFile',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '选择文件：',
                    dataIndex: 'fileUrl',
                    xtype: Plugin('jiakao-misc!upload3', {
                        dataIndex: 'fileUrl',
                        uploadIndex: 'fileUrl',
                        bucket: "jiakao-web",
                        isSingle: true,
                        preview: false,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '数据模板：',
                    dataIndex: 'templates',
                    xtype: function () {
                        let url = window.j.root['jiakao-misc'] + '/exam-base-info.xlsx'
                        return `<a class="btn btn-info btn-sm" href="${url}" target="_blank" download>点击下载模板</a>`
                    }
                },]
        }).add();
    }

    var list = function (panel) {
        Store([`jiakao-misc!scene-supply/data/carList`]).load().done(data => {
            const carList = data.data['scene-supply'].data.carList.data;

            Table({
                description: '科目二真实考场列表',
                title: '科目二真实考场列表',
                search: [{
                    header: '考场ID',
                    dataIndex: 'sceneId',
                    xtype: 'text',
                    placeholder: '考场ID'
                }, {
                    header: '考场编号',
                    dataIndex: 'sceneNo',
                    xtype: 'text',
                    placeholder: '考场编号'
                }, {
                    header: '考场名称',
                    dataIndex: 'sceneName',
                    xtype: 'text',
                    placeholder: '考场名称'
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district2', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                }, 
                {
                    header: '状态',
                    dataIndex: 'sceneStatus',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: '请选择状态'
                    }].concat(statusStore)
                }],
                buttons: {
                    top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '导入',
                        class: 'primary',
                        click: function (obj) {
                            importFile(obj)
                        }
                    },

                    ],
                    bottom: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }]
                },
                operations: [
                    {
                        name: '考场图片',
                        class: 'warning',
                        title: '考场图片',
                        click: function (table, dom, lineData) {
                            Store(['jiakao-misc!scene-supply/data/view?id=' + lineData.id]).load().done(data => {
                                const retData = data.data['scene-supply'].data.view.data;
                                let mediaResource = retData.mediaResource && JSON.parse(retData.mediaResource);
                                console.log('mediaResource', mediaResource);

                                let vrUrl, vrUrlCoverImage, videoId, coverImage, plane, real, ke2Steps, threeD;
                                if (mediaResource?.resourceList) {
                                    for (let i = 0; i < mediaResource.resourceList.length; i++) {
                                        const resource = mediaResource.resourceList[i];

                                        if (resource.code == 'vr') {
                                            vrUrl = resource.resourceList[0]?.resourceUrl
                                            if(resource.resourceList[0]?.coverImage) {
                                                vrUrlCoverImage = [{url: resource.resourceList[0]?.coverImage}]
                                            }
                                        }

                                        if (resource.code == 'aerial') {
                                            videoId = resource.resourceList[0]?.videoId
                                            coverImage = [{url:resource.resourceList[0]?.coverImage}]
                                        }

                                        if (resource.code == 'plan') {
                                            plane = resource.resourceList?.map(element => ({
                                                url: element.resourceUrl
                                            }))
                                        }

                                        if (resource.code == 'real') {
                                            real = resource.resourceList?.map(element => ({
                                                url: element.resourceUrl
                                            }))
                                        }

                                        if (resource.code == 'step') {
                                            ke2Steps = resource.resourceList?.map(element => ({
                                                url: element.resourceUrl
                                            }))
                                        }

                                        if (resource.code == 'td') {
                                            threeD = resource.resourceList?.map(element => ({
                                                resourceUrl: element.resourceUrl,
                                                compareResourceUrl: element.compareResourceUrl
                                            }))
                                        }
                                    }
                                }

                                var editData = {
                                    vrUrl,
                                    vrUrlCoverImage: JSON.stringify(vrUrlCoverImage || ''),
                                    videoId,
                                    coverImage: JSON.stringify(coverImage || ''),
                                    plane: JSON.stringify(plane || ''),
                                    real: JSON.stringify(real || ''),
                                    ke2Steps: JSON.stringify(ke2Steps || ''),
                                    threeD: JSON.stringify(threeD || '')
                                }
                                console.log(JSON.stringify(threeD || ''))
                                editData.id = lineData.id
                                Table().edit(editData, {
                                    width: 1200,
                                    store: 'jiakao-misc!scene-supply/data/updateSR',
                                    form: {
                                        submitHandler: function (form) {
                                            var vrUrl = form.vrUrl.value
                                            var vrUrlCoverImage = form.vrUrlCoverImage.value
                                            var videoId = form.videoId.value;
                                            var coverImage = form.coverImage.value

                                            var plane = $('input[name="plane"]').val();
                                            var real = $('input[name="real"]').val();
                                            var ke2Steps = $('input[name="ke2Steps"]').val();
                                            var threeD = []
                                            $(form).item('threeD-group').find('.line').each(function () {
                                                var resourceUrl = $(this).find('[data-item="plugin-image-input"][name="resourceUrl"]').val()
                                                var compareResourceUrl = $(this).find('[data-item="plugin-image-input"][name="compareResourceUrl"]').val()
                                                resourceUrl = resourceUrl && (JSON.parse(resourceUrl)).map(el => (el.url)).filter(Boolean)
                                                compareResourceUrl = compareResourceUrl && (JSON.parse(compareResourceUrl)).map(el => (el.url)).filter(Boolean)
                                                if(resourceUrl || compareResourceUrl) {
                                                    threeD.push({
                                                        resourceUrl: resourceUrl[0],
                                                        compareResourceUrl: compareResourceUrl[0]
                                                    })
                                                }
                                            })

                                            const retData = [];

                                            if (vrUrl) {
                                                vrUrlCoverImage = vrUrlCoverImage && (JSON.parse(vrUrlCoverImage)).map(el => (el.url)).filter(Boolean)
                                                vrUrlCoverImage = vrUrlCoverImage || []
                                                if (!vrUrlCoverImage.length) {
                                                    vrUrlCoverImage.push('')
                                                }
                                                retData.push({
                                                    code: 'vr',
                                                    name: 'VR全景',
                                                    resourceList: vrUrlCoverImage.map(item => {
                                                        return {
                                                            resourceType: 3,
                                                            coverImage: item,
                                                            resourceUrl: vrUrl
                                                        }
                                                    })
                                                })
                                            }

                                            if (videoId) {
                                                coverImage = coverImage && (JSON.parse(coverImage)).map(el => (el.url)).filter(Boolean)
                                                coverImage = coverImage || []
                                                if (!coverImage.length) {
                                                    coverImage.push('')
                                                }
                                                retData.push({
                                                    code: 'aerial',
                                                    name: '航拍视频',
                                                    resourceList: coverImage.map(item => {
                                                        return {
                                                            resourceType: 2,
                                                            coverImage: item,
                                                            videoId
                                                        }
                                                    })
                                                })
                                            }

                                            plane = plane && (JSON.parse(plane)).map(el => (el.url)).filter(Boolean)
                                            if (plane.length) {
                                                retData.push({
                                                    code: 'plan',
                                                    name: '平面图',
                                                    resourceList: plane.map(item => {
                                                        return {
                                                            resourceType: 1,
                                                            resourceUrl: item
                                                        }
                                                    })
                                                })
                                            }

                                            real = real && (JSON.parse(real)).map(el => (el.url)).filter(Boolean)
                                            if (real.length) {
                                                retData.push({
                                                    code: 'real',
                                                    name: '实景图',
                                                    resourceList: real.map(item => {
                                                        return {
                                                            resourceType: 1,
                                                            resourceUrl: item
                                                        }
                                                    })
                                                })
                                            }

                                            ke2Steps = ke2Steps && (JSON.parse(ke2Steps)).map(el => (el.url)).filter(Boolean)
                                            if (ke2Steps.length) {
                                                retData.push({
                                                    code: 'step',
                                                    name: '科二五项',
                                                    resourceList: ke2Steps.map(item => {
                                                        return {
                                                            resourceType: 1,
                                                            resourceUrl: item
                                                        }
                                                    })
                                                })
                                            }

                                            if (threeD.length) {
                                                retData.push({
                                                    code: 'td',
                                                    name: '3D考场',
                                                    resourceList: threeD.map(item => {
                                                        return {
                                                            resourceType: 1,
                                                            ...item
                                                        }
                                                    })
                                                })
                                            }

                                            return {
                                                resourceList: JSON.stringify(retData)
                                            };
                                        }
                                    },
                                    success: function (obj, dialog, e) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [{
                                        dataIndex: 'id',
                                        xtype: 'hidden'
                                    },
                                    {
                                        header: '图片分类排序',
                                        dataIndex: 'resourceSort',
                                        xtype: 'text',
                                        readonly: true,
                                        placeholder: '请在panda管理后台系统配置中设置'
                                    },
                                    {
                                        header: 'VR全景链接:',
                                        dataIndex: 'vrUrl',
                                        value: vrUrl,
                                        xtype: 'text',
                                        placeholder: '请输入链接'
                                    },
                                    {
                                        header: 'VR全景封面:',
                                        dataIndex: 'vrUrlCoverImage',
                                        xtype: Plugin('jiakao-misc!select-images-outer', {
                                            dataIndex: 'vrUrlCoverImage',
                                            multiple: false,
                                        }, function (plugin, value) {
                                        })
                                    },
                                    {
                                        header: '航拍视频',
                                        dataIndex: 'videoId',
                                        xtype: 'text',
                                        value: videoId,
                                        placeholder: '请输入视频id'
                                    },
                                    {
                                        header: '航拍封面:',
                                        dataIndex: 'coverImage',
                                        xtype: Plugin('jiakao-misc!select-images-outer', {
                                            dataIndex: 'coverImage',
                                            multiple: false,
                                        }, function (plugin, value) {
                                        })
                                    },
                                    {
                                        header: '平面图:',
                                        dataIndex: 'plane',
                                        xtype: Plugin('jiakao-misc!select-images-outer', {
                                            dataIndex: 'plane',
                                            multiple: true,
                                        }, function (plugin, value) {
                                        })
                                    },
                                    {
                                        header: '实景图：',
                                        dataIndex: 'real',
                                        xtype: Plugin('jiakao-misc!select-images-outer', {
                                            dataIndex: 'real',
                                            multiple: true,
                                        }, function (plugin, value) {
                                        })
                                    },
                                    {
                                        header: '科二五项:',
                                        dataIndex: 'ke2Steps',
                                        xtype: Plugin('jiakao-misc!select-images-outer', {
                                            dataIndex: 'ke2Steps',
                                            multiple: true,
                                        }, function (plugin, value) {
                                        })
                                    },
                                    {
                                        header: '3D考场:',
                                        dataIndex: 'threeD',
                                        xtype: Plugin('jiakao-misc!images-group', {
                                            dataIndex: 'threeD',
                                            multiple: true,
                                        }, function (plugin, value) {
                                        })
                                    }]
                                })
                            })
                        }
                    },
                    {
                        name: '基础信息',
                        xtype: 'edit',
                        width: 800,
                        class: 'info',
                        title: '基础信息',
                        success: function (obj, dialog, e) {
                            dialog.close();
                            obj.render();
                        },
                        form: {
                            submitHandler(form) {
                                var actionTitle = $(form).find('#actionTitle').val();
                                let clickUrl = '';
                                if (actionTitle === '开始训练') {
                                    clickUrl = $('#clickUrl').val();
                                }
                                const examCarBrandCode = [];
                                const examCarBrand = $(form).find('input[name="examCarBrand"]').val().split(',')
                                carList.forEach(car => {
                                    if (examCarBrand.includes(car.value)) {
                                        examCarBrandCode.push(car.key)
                                    }
                                })


                                return {
                                    clickUrl,
                                    examCarBrandCode: examCarBrandCode + ''
                                }
                            },
                        },
                        renderAfter: function (table, dom, data) {

                            var actionTitleDom = dom.item('actionTitle');
                            var actionTitle = data.data.actionTitle;
                            initTopicId = data.data.topicId;

                            checkResult = false;

                            const sceneId = data.data.sceneId
                            console.log(actionTitle, data, 'actionTitleactionTitle');
                            if (actionTitle == '去看考场') {
                                dom.item('clickUrl')[0].disabled = false;
                                const isTest = window.j.host['jiakao-misc'] === 'https://jiakao-misc.ttt.mucang.cn'
                                dom.item('clickUrl').val(`https://jiakao.nav.mucang.cn/real-scene/detail?id=${sceneId}`);
                            } else if (actionTitle == '开始训练') {
                                dom.item('clickUrl')[0].disabled = true;
                                dom.item('clickUrl').val(`http://jiakao3d.nav.mucang.cn/main?check=false&url=mucang-jiakao3d%3A%2F%2Fk2realitem%3Ffrom%3D0207%26sceneId%3D${sceneId}`)
                            }



                            actionTitleDom.on('change', function () {
                                const value = $(this).val();
                                const sceneId = data.data.sceneId
                                if (value == '去看考场') {
                                    dom.item('clickUrl')[0].disabled = false;
                                    const isTest = window.j.host['jiakao-misc'] === 'https://jiakao-misc.ttt.mucang.cn'

                                    dom.item('clickUrl').val(`https://jiakao.nav.mucang.cn/real-scene/detail?id=${sceneId}`);
                                } else if (value == '开始训练') {
                                    dom.item('clickUrl')[0].disabled = true;
                                    dom.item('clickUrl').val(`http://jiakao3d.nav.mucang.cn/main?check=false&url=mucang-jiakao3d%3A%2F%2Fk2realitem%3Ffrom%3D0207%26sceneId%3D${sceneId}`)
                                }
                            })



                            require(['jiakao-misc!app/route-video-item/editMap'], function (Main, ModeVs) {
                                var btnDom = `<div class="form-group" data-item="value-group"> 
                                    <div class="label label-info" style="cursor:pointer;margin-left:100px" id="addressBtn">经纬度 --> 地址</div>
                                </div>`;
                                dom.append(btnDom);

                                setTimeout(() => {
                                    $('#addressBtn').on('click', function () {
                                        AMap.plugin('AMap.Geocoder', function () {
                                            var geocoder = new AMap.Geocoder({
                                                // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
                                                city: '010'
                                            })
                                            var placeGps = dom.item('placeGps').val();

                                            var lnglat = placeGps.split(',');

                                            geocoder.getAddress(lnglat, function (status, result) {
                                                if (status === 'complete' && result.info === 'OK') {
                                                    dom.item('placeDetail').val(result.regeocode.formattedAddress);
                                                } else {
                                                    Widgets.dialog.alert('输入的gps有误');
                                                }
                                            })
                                        })
                                    })
                                }, 200)
                            })

                        },
                        store: {
                            load: 'jiakao-misc!scene-supply/data/view',
                            save: 'jiakao-misc!scene-supply/data/updateBI'
                        },
                        columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '考场标签：',
                            dataIndex: 'featureTag',
                            xtype: 'text',
                            maxlength: 50,
                            placeholder: '考场标签'
                        },
                        {
                            header: '考试项目：',
                            dataIndex: 'examItem',
                            xtype: Plugin('simple!auto-prompt2', {
                                store: examItemArr,
                                dataIndex: 'examItem',
                                isMulti: true,
                                defaultVal: false,
                                placeholder: '',
                                index: {
                                    key: 'code',
                                    value: 'name',
                                    search: 'name',
                                },
                            }, function (plugin, value) {
                            }),
                        },
                        {
                            header: '考场车型：',
                            dataIndex: 'examCarBrand',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: 'jiakao-misc!scene-supply/data/carList',
                                index: {
                                    key: 'value',
                                    value: 'value',
                                    search: 'value'
                                },
                                dataIndex: 'examCarBrand',
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            }),
                        },
                        {
                            header: '营业时间：',
                            dataIndex: 'businessHours',
                            xtype: 'text',
                            maxlength: 50,
                            placeholder: '营业时间'
                        },

                        {
                            header: '行动按钮：',
                            dataIndex: 'actionTitle',
                            xtype: 'select',
                            store: [{ key: '', value: '请选择' }, { key: '开始训练', value: '开始训练' }, { key: '去看考场', value: '去看考场' }],
                            placeholder: '行动按钮'
                        },
                        {
                            header: '考场跳转链接：',
                            dataIndex: 'clickUrl',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '考场跳转链接'
                        },
                        {
                            header: '上传考场平面图:',
                            dataIndex: 'coverImage',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'coverImage',
                                uploadIndex: 'coverImage',
                                bucket: "jiakao-web",
                                isSingle: true,
                                value: '',
                                isGetImageInfo: true,
                                placeholder: '请选择图片上传',
                                url: 'simple-upload3://upload/file.htm',
                                getSuccessImageInfo: function (data) {
                                    if (data.lng && data.lat) {
                                        $('input[data-item="placeGps"]').val((data.lng).toFixed(5) + ',' + (data.lat).toFixed(5))
                                    } else {
                                        $('input[data-item="placeGps"]').val('')
                                    }

                                }
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '考场地址gps：',
                            dataIndex: 'placeGps',
                            xtype: 'text',
                            maxlength: 256,
                            placeholder: '考场地址gps'
                        },
                        {
                            header: '考场详细地址：',
                            dataIndex: 'placeDetail',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '考场详细地址'
                        },
                        ]
                    },
                    {
                        name: '场地介绍',
                        width: 800,
                        class: 'info',
                        title: '场地介绍',
                        click: function (table, dom, lineData) {
                            console.log(lineData, 'lineData');
                            Store(['jiakao-misc!scene-supply/data/view?id=' + lineData.id]).load().done(data => {
                                console.log(data);
                                const retDataValue = data.data['scene-supply'].data.view.data;
                                console.log(retDataValue, 'retDataValue');
                                const sceneIntroduce = (retDataValue && JSON.parse(retDataValue.sceneIntroduce)) || {};
                                Table().edit(retDataValue, {
                                    success: function (obj, dialog, e) {
                                        dialog.close();
                                        table.render();
                                    },
                                    renderAfter: function (table, dom, data) {
                                        renderCodeFn(dom, data)
                                    },
                                    form: {
                                        submitHandler: function (form) {
                                            console.log($(form).find('[name="editorValue"]'),'12412');
                                            var previewVideoId = $('input[name="previewVideoId"]').val();
                                            var videoId = $('input[name="videoId"]').val();
                                            var panoramaVideoId = $('input[name="panoramaVideoId"]').val();
                                            var trySeeHeight = $('input[name="trySeeHeight"]').val();
                                            var richText = $(form).find('[name="editorValue"]').val();
                                            var videoCoverImage = form.videoCoverImage.value
                                            const resourceConfig = JSON.stringify({
                                                previewVideoId, videoId, panoramaVideoId, trySeeHeight, richText, videoCoverImage
                                            })
                                            return {
                                                resourceConfig,
                                                richText
                                            }
                                        }
                                    },
                                    store: 'jiakao-misc!scene-supply/data/updateSI',
                                    columns: [{
                                        dataIndex: 'id',
                                        xtype: 'hidden'
                                    },
                                    {
                                        header: '视频封面:',
                                        dataIndex: 'videoCoverImage',
                                        xtype: Plugin('simple!select-images', {
                                            dataIndex: 'videoCoverImage',
                                            uploadIndex: 'videoCoverImage',
                                            bucket: "jiakao-image",
                                            multiple: false,
                                            appSpaceId: '02fb648802fb886a5a5c',
                                            fileType: 'image',

                                            isImg: true,
                                            saveUploadUrl: window.j.host.panda + '/api/admin/upload/save.htm',
                                            imageSrc: {
                                                dataIndex: 'videoCoverImage',
                                                suffix: sceneIntroduce.videoCoverImage && sceneIntroduce.videoCoverImage.indexOf('jiakao-image') !== -1 ? '!default' : ''

                                            },
                                            stringify: function (value) {
                                                if (value && value.length > 0) {
                                                    return value[0].videoCoverImage
                                                } else {
                                                    return ''
                                                }
                                            },
                                            value: sceneIntroduce.videoCoverImage ? JSON.stringify([{ 'videoCoverImage': sceneIntroduce.videoCoverImage }]) : '',
                                            useSecureUpload: true,

                                            placeholder: '请选择上传文件',

                                        }, function () {
                                            console.log(arguments)
                                        })
                                    },
                                    {
                                        header: '项目场地试看视频',
                                        dataIndex: 'previewVideoId',
                                        xtype: 'text',
                                        placeholder: '请输入视频id'
                                    },
                                    {
                                        header: '项目场地完整视频',
                                        dataIndex: 'videoId',
                                        xtype: 'text',
                                        placeholder: '请输入视频id'
                                    },
                                    {
                                        header: '全景视频id',
                                        dataIndex: 'panoramaVideoId',
                                        xtype: 'text',
                                        placeholder: '请输入视频id'
                                    },
                                    {
                                        header: '介绍文案试看高度',
                                        dataIndex: 'trySeeHeight',
                                        xtype: 'text',
                                        placeholder: '请输入试看高度，默认是300'
                                    },
                                    {
                                        header: '项目场地介绍文案：',
                                        dataIndex: 'richText',

                                        xtype: Plugin('jiakao-misc!rich-text', {
                                            dataIndex: 'richText',
                                            uploadIndex: 'richText',
                                            bucket: "jiakao-web",
                                            value: retDataValue.sceneIntroduce && (JSON.parse(retDataValue?.sceneIntroduce)).richText,
                                            editorConfig: {
                                                initialFrameWidth: "99.7%",
                                                initialFrameHeight: 300,
                                                autoClearinitialContent: false,
                                                wordCount: false,
                                                elementPathEnabled: false,
                                                autoFloatEnabled: false,
                                                dataIndex: 'richText',
                                                uploadIndex: 'richText',
                                                name: 'richText'

                                            }

                                        }, function () {
                                        })
                                    }
                                    ]
                                });
                            })
                        },

                    },
                    {
                        name: '修改状态',
                        class: 'warning',
                        xtype: 'edit',
                        title: '修改状态',
                        success: function (obj, dialog, e) {
                            dialog.close();
                            obj.render();
                        },
                        form: {
                            submitHandler(form) {
                                var status = form.sceneStatus.value;

                                return {
                                    status,
                                }
                            },
                        },
                        store: {
                            load: 'jiakao-misc!scene-supply/data/view',
                            save: 'jiakao-misc!scene-supply/data/updateStatus'
                        },
                        columns: [
                            {
                                dataIndex: 'id',
                                xtype: 'hidden'
                            },
                            {
                                dataIndex: 'sceneStatus',
                                xtype: 'radio',
                                store: statusStore,
                                header: '状态'
                            }
                        ]
                    }
                ],
                columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '考场名称',
                    dataIndex: 'sceneName'
                },
                {
                    header: '真实考场id',
                    dataIndex: 'sceneId'
                },
                {
                    header: '考场编号',
                    dataIndex: 'sceneNo'
                },
                {
                    header: '考场key',
                    dataIndex: 'examSiteKey'
                },
                {
                    header: '公共考场库id',
                    dataIndex: 'jiaxiaoSceneId'
                },
                {
                    header: '封面图',
                    dataIndex: 'sceneImage',
                    render: function (data) {
                        return `<img src="${data}" style="width:100px;"/>`
                    }
                },
                {
                    header: '精华贴id',
                    dataIndex: 'topicId'
                },
                {
                    header: '考场图片',
                    dataIndex: 'mediaResource',
                    render: function (data) {
                        return data && data !== '{}' && `<a>点击查看</a>`
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('考场图片', {}).done(function (dialog) {
                            const { resourceList = [] } = JSON.parse(lineData.mediaResource)
                            let html = '';
                            for (let i = 0; i < resourceList.length; i++) {
                                const item = resourceList[i];
                                item.resourceList.forEach((resource, index) => {
                                    if (resource.coverImage) {
                                        var indexOf = resource.coverImage.lastIndexOf('!')
                                        if (indexOf !== -1) {
                                            resource.coverImage = resource.coverImage.substring(0, indexOf) + '!800x0'
                                        } else {
                                            resource.coverImage = resource.coverImage + '!800x0'
                                        }
                                    }
                                    if (resource.resourceType === 1 && resource.resourceUrl) {
                                        var indexOf = resource.resourceUrl.lastIndexOf('!')
                                        if (indexOf !== -1) {
                                            resource.resourceUrl = resource.resourceUrl.substring(0, indexOf) + '!800x0'
                                        } else {
                                            resource.resourceUrl = resource.resourceUrl + '!800x0'
                                        }
                                    }

                                    if (resource.resourceType === 3) {
                                        html += `<div style="display:flex; margin-bottom:20px;"><p style="font-weight:bold;margin-right:30px;width:150px">${item.name + '链接'}</p>${resource.resourceUrl}</div>
                                            <div style="display:flex; margin-bottom:20px;"><p style="font-weight:bold;margin-right:30px">${item.name + '封面'}</p><img width="200" src="${resource.coverImage}" /></div>
                                            `
                                    } else if (resource.resourceType === 2) {
                                        html += `<div style="display:flex; margin-bottom:20px;"><p style="font-weight:bold;margin-right:30px">${item.name + 'ID'}</p>${resource.videoId || ''}</div>
                                            <div style="display:flex; margin-bottom:20px;"><p style="font-weight:bold;margin-right:10px">${item.name + '封面'}</p><img width="200" src="${resource.coverImage}" /></div>
                                            `
                                    } else if (resource.resourceType === 1) {
                                        if (item.name === '3D考场' && resource.compareResourceUrl) {
                                            html += `<div style="display:flex; margin-bottom:20px;"><p style="font-weight:bold;margin-right:30px">${item.name + '-' + (index + 1)}</p><img width="200" src="${resource.resourceUrl}" /><img width="200" src="${resource.compareResourceUrl}" /></div>`
                                        } else {
                                            html += `<div style="display:flex; margin-bottom:20px;"><p style="font-weight:bold;margin-right:30px">${item.name + '-' + (index + 1)}</p><img width="200" src="${resource.resourceUrl}" /></div>`
                                        }
                                    }
                                })
                            }

                            $(dialog.body).html(html)
                        })
                    }
                },
                {
                    header: '基础信息',
                    render: function (data, all, lineData) {
                        var show = lineData.examItem || lineData.examCarBrand || lineData.businessHours || lineData.placeGps || lineData.placeDetail;
                        return show && `<a>点击查看</a>`
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('基础信息', {}).done(function (dialog) {
                            const data = {
                                '考试项目': lineData.examItem || '',
                                '考场车型': lineData.examCarBrand || '',
                                '营业时间': lineData.businessHours || '',
                                '考场地址gps': lineData.placeGps || '',
                                '考场详细地址': lineData.placeDetail || ''
                            }
                            var retData = JSON.stringify(data, null, 4)
                            $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + retData + '</pre>')
                        })
                    }
                },
                {
                    header: '场地介绍',
                    dataIndex: 'sceneIntroduce',
                    render: function (data) {
                        return data && `<a>点击查看</a>`
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('场地介绍', {}).done(function (dialog) {
                            var data = lineData.sceneIntroduce && JSON.stringify(JSON.parse(lineData.sceneIntroduce), null, 4)
                            $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                        })
                    }
                },
                {
                    header: '是否设置关键帧',
                    dataIndex: 'isKeyPoint',
                    render: function (data) {
                        return data ? '是' : '否';
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'sceneStatus',
                    render: function (data) {
                        return statusMap[data];
                    }
                },
                {
                    header: '3D考场状态',
                    dataIndex: 'tdSceneStatus',
                    render: function (data) {
                        return tdSceneStatusMap[data];
                    }
                },
                {
                    header: '修改人名称',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }]
            }, ['jiakao-misc!scene-supply/data/list'], panel, null).render();

        })

    }

    return {
        list: list
    }

});