/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";


define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/tiku'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, TIKU) {
    var showMap = {
        true: '是',
        false: '否'
    }
    var sceneCodeMap = {
        '101': '普通场景',
        '102': '扣满12分'
    }

    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }

    var kemuArr = [
        {
            key: '1',
            value: '科目一'
        },
        {
            key: '2',
            value: '科目二'
        },
        {
            key: '3',
            value: '科目三'
        },
        {
            key: '4',
            value: '科目四'
        },
        {
            key: '8',
            value: '资格证'
        }
    ]
    var kemuMap = {}
    kemuArr.forEach(item => {
        kemuMap[item.key] = item.value
    })



    var statusMap = {
        1: '上线',
        2: '下线'
    }

    var statusArr = [];

    for (const key in statusMap) {
        statusArr.push({
            key,
            value: statusMap[key]
        })
    }

    var addEdit =  function (table, lineData = {}, onlyEditData) {
        var isEdit = !!lineData.id;
        Store(['jiakao-misc!keyword-icon/data/list?status=1']).load().done((data) => {
            const keywordOptions = data.data['keyword-icon'].data.list.data.map(item => {
                return {
                    key: item.url,
                    value: item.name
                }
            });
            const upload = isEdit ? {
                header: '上传图标：',
                dataIndex: 'iconUrl',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'iconUrl',
                    uploadIndex: 'iconUrl',
                    bucket: "exam-room",
                    accept: 'image/*',
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            } : {
                header: '选择图标：',
                dataIndex: 'iconUrl',
                xtype: 'select',
                check: "required",
                store: [
                    {
                        key: '',
                        value: '请选择图标'
                    },
                    ...keywordOptions
                ]
            }
            var config = {
                title: isEdit ? '编辑' : '添加',
                width: 900,
                store: 'jiakao-misc!search-keyword/data/' + (isEdit ? 'update' : 'insert'),
                success: function (obj, dialog) {
                    dialog.close();
                    table.render();
                },
                form: {
                    submitHandler(form) {
                        var effectiveStartTime = form.effectiveStartTime && form.effectiveStartTime.value
                        var effectiveEndTime = form.effectiveEndTime && form.effectiveEndTime.value
                        var alias = $(form).find('#alias').val();
                        var iconUrl = $('input[name=iconUrl]').val() || (form.iconUrl && form.iconUrl.value);
                        if (effectiveStartTime && effectiveEndTime && new Date(effectiveEndTime).getTime() <= new Date(effectiveStartTime).getTime()) {
                            Widgets.dialog.alert('生效结束时间必须大于开始时间')
                            return
                        }
                        if (!iconUrl) {
                            Widgets.dialog.alert('请选择图标')
                            return
                        }
    
                        alias = alias.replaceAll('，', ',')
    
                        return {
                            alias
                        }
                    }
                },
                columns: [
                    {
                        dataIndex: 'id',
                        xtype: 'hidden',
                    },
                    {
                        header: '匹配的关键词',
                        dataIndex: 'alias',
                        xtype: 'text',
                        check: "required",
                        placeholder: '匹配的关键词（格式：摩托车,摩托）'
                    },
                    {
                        header: '热词名称：',
                        dataIndex: 'keyword',
                        xtype: 'text',
                        check: "required",
                        maxlength: 64,
                        placeholder: '热词名称'
                    },
                    upload,
                    {
                        header: '类型：',
                        dataIndex: 'keywordType',
                        xtype: 'select',
                        store: [{
                            key: 'normal',
                            value: '普通热词'
                        }, {
                            key: 'hot',
                            value: '热词'
                        }],
                    },
    
                    {
                        header: '是否显示：',
                        dataIndex: 'show',
                        xtype: 'radio',
                        store: [
                            {
                                key: true, value: '是'
                            },
                            {
                                key: false, value: '否'
                            }
                        ],
                    },
                    {
                        header: '是否直达：',
                        dataIndex: 'direct',
                        xtype: 'radio',
                        store: [
                            {
                                key: true, value: '是'
                            },
                            {
                                key: false, value: '否'
                            }
                        ],
                    },
                    {
                        header: '状态：',
                        dataIndex: 'keywordStatus',
                        xtype: 'select',
                        store: statusArr
                    },
                    {
                        header: '车型:',
                        dataIndex: 'carType',
                        xtype: 'checkbox',
                        store: [].concat(carTypeArr)
                    },
                    {
                        header: '科目:',
                        dataIndex: 'kemu',
                        xtype: Plugin('jiakao-misc!auto-prompt', {
                            store: kemuArr,
                            dataIndex: 'kemu',
                            isMulti: true,
                            defaultVal: false
                        }, function (plugin, value) {
                        }),
                    },
                    {
                        header: '访问场景:',
                        dataIndex: 'sceneCode',
                        xtype: 'checkbox',
                        store: [{
                            key: '101',
                            value: '普通场景'
                        }, {
                            key: '102',
                            value: '扣满12分'
                        }]
                    },
                    {
                        header: '热词列表展示顺序',
                        dataIndex: 'displayOrder',
                        xtype: 'text',
                        placeholder: '热词列表展示顺序'
                    },
                    {
                        header: '跳转链接:',
                        dataIndex: 'actionUrl',
                        xtype: 'text',
                        check: "required",
                        maxlength: 512,
                        placeholder: '跳转链接'
                    },
                    {
                        header: '生效开始时间：',
                        dataIndex: 'effectiveStartTime',
                        xtype: 'datetime',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        placeholder: '生效开始时间'
                    },
                    {
                        header: '生效结束时间：',
                        dataIndex: 'effectiveEndTime',
                        xtype: 'datetime',
                        render: function (data) {
                            if(data){
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            }
                        },
                        placeholder: '生效结束时间'
                    },
                ]
            }
            if (isEdit || onlyEditData) {
                console.log('1234123423')
                Table().edit(lineData, config);
            } else {
                Table(config).add();
            }
        })
        
        

    }
    var typeMap = {
        hot: '热词',
        normal: '普通词'
    }
    var list = function (panel) {
        Table({
            description: '搜索关键词列表',
            title: '搜索关键词列表',
            search: [
                {
                    dataIndex: 'keyword',
                    xtype: 'text', 
                    placeholder: '热词名称'
                },
                {
                    dataIndex: 'alias',
                    xtype: 'text', 
                    placeholder: '匹配关键名'
                },
                {
                dataIndex: 'keywordStatus',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择状态'
                }].concat(statusArr)
            }, {
                dataIndex: 'carType',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择车型'
                }].concat(carTypeArr)
            }, {
                dataIndex: 'kemu',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择科目'
                }].concat(kemuArr)
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click:  function (table) {
                             addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!search-keyword/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '关键词',
                            dataIndex: 'keyword'
                        },
                        {
                            header: '关键词类型',
                            dataIndex: 'keywordType', render: (data) => {
                                return typeMap[data]
                            }
                        },
                        {
                            header: '是否显示',
                            dataIndex: 'show',
                            render: (data) => {
                                return showMap[data]
                            }
                        },
                        {
                            header: '跳转Ul',
                            dataIndex: 'actionUrl'
                        },
                        {
                            header: '图标Icon',
                            dataIndex: 'iconUrl',
                            render: function (data) {
                                return data && `<img src="${data}" style="width:100px">`;
                            }
                        },
                        {
                            header: '成就中心展示顺序',
                            dataIndex: 'displayOrder'
                        },
                        {
                            header: '访问场景:',
                            dataIndex: 'sceneCode',
                            return: function (data) {
                                return sceneCodeMap[data]
                            }
                        },
                        {
                            header: '是否显示',
                            dataIndex: 'show',
                            return: function (data) {
                                return showMap[data]
                            }
                        },
                        {
                            header: '匹配关键词',
                            dataIndex: 'alias'
                        },
                        {
                            header: '状态',
                            dataIndex: 'keywordStatus',
                            return: function (data) {
                                return statusMap[data]
                            }
                        },
                        {
                            header: '车型',
                            dataIndex: 'carType',
                            render: function (data) {
                                if (data) {
                                    data = data?.split(',');
                                    var strArr = [];
                                    for (var i = 0; i < data.length; i++) {
                                        strArr.push(TIKU[data[i]])
                                    }
                                    return strArr.join(',');
                                }

                            }
                        },
                        {
                            header: '科目',
                            dataIndex: 'kemu',
                            render: function (data) {
                                var dataArr = data?.split(',');
                                var text = '';
                                dataArr?.forEach((item) => {
                                    console.log(item, kemuMap[item], 'item');
                                    text += item && kemuMap[item];
                                })
                                return text;
                            }
                        },
                        {
                            header: '生效开始时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'effectiveStartTime'
                        },
                        {
                            header: '生效结束时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'effectiveEndTime'
                        },
                        {
                            header: '创建者姓名',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更改者姓名',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更改时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    class: 'warning',
                    click:  function(table, dom, lineData){
                         addEdit(table, lineData)
                    }
                },
                {
                    name: '复制',
                    class: 'primary',
                    click:  function (table, dom, lineData) {
                      
                        const data = {...lineData};
                        data.id = '',
                        data.effectiveStartTime = new Date().getTime()
                        data.effectiveEndTime = '';
                         addEdit(table, data, true)
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!search-keyword/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '热词名称',
                    dataIndex: 'keyword'
                },
                {
                    header: '关键词类型',
                    dataIndex: 'keywordType', render: (data) => {
                        return typeMap[data]
                    }
                },
                {
                    header: '跳转链接',
                    dataIndex: 'actionUrl',
                    render: (data) => {
                        return `<div  style="width: 100px; overflow: hidden; word-wrap: break-word;">${data}</div>`
                    }
                },
                {
                    header: '图标Icon',
                    dataIndex: 'iconUrl',
                    render: function (data) {
                        return data && `<img src="${data}" style="width:100px">`;
                    }
                },

                {
                    header: '访问场景:',
                    dataIndex: 'sceneCode',
                    render: function (data) {
                        return sceneCodeMap[data]
                    }
                },
                {
                    header: '热词列表展示顺序',
                    dataIndex: 'displayOrder'
                },
                {
                    header: '是否显示',
                    dataIndex: 'show',
                    render: function (data) {
                        return showMap[data]
                    }
                },
                {
                    header: '是否直达',
                    dataIndex: 'direct',
                    render: function (data) {
                        return showMap[data]
                    }
                },
                {
                    header: '匹配关键词',
                    dataIndex: 'alias'
                },
                {
                    header: '状态：',
                    dataIndex: 'keywordStatus',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        if (data) {
                            data = data?.split(',');
                            var strArr = [];
                            for (var i = 0; i < data.length; i++) {
                                strArr.push(TIKU[data[i]])
                            }
                            return strArr.join(',');
                        }

                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data) {
                        var dataArr = data?.split(',');
                        var text = '';
                        dataArr?.forEach((item) => {
                            console.log(item, kemuMap[item], 'item');
                            text += item && kemuMap[item];
                        })
                        return text;
                    }
                },
                {
                    header: '生效开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'effectiveStartTime'
                },
                {
                    header: '生效结束时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'effectiveEndTime'
                },
                {
                    header: '创建者姓名',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更改者姓名',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '更改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!search-keyword/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});