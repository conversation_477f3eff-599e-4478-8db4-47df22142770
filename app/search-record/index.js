/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!search-record/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: 'searchContentType：',
                    dataIndex: 'searchContentType',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: 'searchContentType'
                },
                {
                    header: 'carType：',
                    dataIndex: 'carType',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: 'carType'
                },
                {
                    header: 'course：',
                    dataIndex: 'course',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'course'
                },
                {
                    header: 'keyword：',
                    dataIndex: 'keyword',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: 'keyword'
                },
                {
                    header: 'keywordType：',
                    dataIndex: 'keywordType',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: 'keywordType'
                },
                {
                    header: 'hasResult：',
                    dataIndex: 'hasResult',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: 'deleted：',
                    dataIndex: 'deleted',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '创建者id：',
                    dataIndex: 'createUserId',
                    xtype: 'text',
                    placeholder: '创建者id'
                },
                {
                    header: '创建者姓名：',
                    dataIndex: 'createUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '创建者姓名'
                },
                {
                    header: '更改者id：',
                    dataIndex: 'updateUserId',
                    xtype: 'text',
                    placeholder: '更改者id'
                },
                {
                    header: '更改者姓名：',
                    dataIndex: 'updateUserName',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '更改者姓名'
                },
                {
                    header: '更改时间：',
                    dataIndex: 'updateTime',
                    xtype: 'date',
                    placeholder: '更改时间'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '搜索记录列表',
            title: '搜索记录列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!search-record/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: 'searchContentType：',
                            dataIndex: 'searchContentType'
                        },
                        {
                            header: 'carType：',
                            dataIndex: 'carType'
                        },
                        {
                            header: 'course：',
                            dataIndex: 'course'
                        },
                        {
                            header: 'keyword：',
                            dataIndex: 'keyword'
                        },
                        {
                            header: 'keywordType：',
                            dataIndex: 'keywordType'
                        },
                        {
                            header: '搜索内容的分词结果：',
                            dataIndex: 'keywordParticiple'
                        },
                        {
                            header: 'result',
                            dataIndex: 'result',
                            render: function (data) {
                                if (data) {
                                    return '<a>点击查看</a>'
                                }
                            },
                            click: function (table, row, lineData) {
                                Widgets.dialog.html('', {}).done(function (dialog) {
                                    $(dialog.body).html('<div>' + lineData.result + '</div><br/>')
                                })
                            }
                        },
                        {
                            header: 'hasResult：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'hasResult'
                        },
                        {
                            header: 'deleted：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'deleted'
                        },
                        {
                            header: '创建者id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建者姓名：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '更改者id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '更改者姓名：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: '更改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!search-record/data/view',
                        save: 'jiakao-misc!search-record/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: 'searchContentType：',
                            dataIndex: 'searchContentType',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: 'searchContentType'
                        },
                        {
                            header: 'carType：',
                            dataIndex: 'carType',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: 'carType'
                        },
                        {
                            header: 'course：',
                            dataIndex: 'course',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: 'course'
                        },
                        {
                            header: 'keyword：',
                            dataIndex: 'keyword',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: 'keyword'
                        },
                        {
                            header: 'keywordType：',
                            dataIndex: 'keywordType',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: 'keywordType'
                        },
                        {
                            header: 'hasResult：',
                            dataIndex: 'hasResult',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        },
                        {
                            header: 'deleted：',
                            dataIndex: 'deleted',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        },
                        {
                            header: '创建者id：',
                            dataIndex: 'createUserId',
                            xtype: 'text',
                            placeholder: '创建者id'
                        },
                        {
                            header: '创建者姓名：',
                            dataIndex: 'createUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '创建者姓名'
                        },
                        {
                            header: '更改者id：',
                            dataIndex: 'updateUserId',
                            xtype: 'text',
                            placeholder: '更改者id'
                        },
                        {
                            header: '更改者姓名：',
                            dataIndex: 'updateUserName',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '更改者姓名'
                        },
                        {
                            header: '更改时间：',
                            dataIndex: 'updateTime',
                            xtype: 'date',
                            placeholder: '更改时间'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!search-record/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'searchContentType',
                    dataIndex: 'searchContentType'
                },
                {
                    header: 'carType',
                    dataIndex: 'carType'
                },
                {
                    header: 'course',
                    dataIndex: 'course'
                },
                {
                    header: 'keyword',
                    dataIndex: 'keyword'
                },
                {
                    header: 'keywordType',
                    dataIndex: 'keywordType'
                },
                {
                    header: '搜索内容的分词结果：',
                    dataIndex: 'keywordParticiple'
                },
                {
                    header: '识别结果',
                    dataIndex: 'result',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.result + '</div><br/>')
                        })
                    }
                },
                {
                    header: 'hasResult',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'hasResult'
                },
                {
                    header: 'deleted',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'deleted'
                },
                {
                    header: '创建者id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建者姓名',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更改者id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '更改者姓名',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '更改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!search-record/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});