/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var list = function (panel) {
        Table({
            description: '搜索词管理',
            title: '搜索词管理',
            buttons: {
                top: [
                    {
                        name: '自定义词库',
                        class: 'info',
                        click: function (table) {
                            Store(['jiakao-misc!search-word-admin/data/getDic'], [{
                                aliases:'getDic',
                            }]).load().done((store) =>{
                                const {value} = store.data.getDic.data;
                                console.log(value,'valuevalue');
                                Table({
                                    title: '自定义词库',
                                    width: 500,
                                    store: 'jiakao-misc!search-word-admin/data/setDic',
                                    success: function (obj, dialog) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [
                                        {
                                            header: '自定义词库',
                                            dataIndex: 'content',
                                            xtype: 'textarea',
                                            value:value,
                                            placeholder: '自定义词库'
                                        }
                                    ]
                                }).add();
                            }).fail(function (err) {
                                Widgets.dialog.alert(err.message)
                            });
                                
                        }
                    },
                    {
                        name: '同义词库',
                        class: 'info',
                        click: function (table) {
                            Store(['jiakao-misc!search-word-admin/data/getSynonymDic'], [{
                                aliases:'getDic',
                            }]).load().done((store) =>{
                                const {value} = store.data.getDic.data;

                                Table({
                                    title: '同义词库',
                                    width: 500,
                                    store: 'jiakao-misc!search-word-admin/data/setSynonymDic',
                                    success: function (obj, dialog) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [
                                        {
                                            header: '同义词库',
                                            dataIndex: 'content',
                                            xtype: 'textarea',
                                            value:value,
                                            placeholder: '同义词库'
                                        }
                                    ]
                                }).add();
                            }).fail(function (err) {
                                Widgets.dialog.alert(err.message)
                            });
                                
                        }
                    },
                ],
               
            },
            columns: [
              
            ]
        }, {
            data:[]
        }, panel, null).render();
    }

    return {
        list: list
    }

});
