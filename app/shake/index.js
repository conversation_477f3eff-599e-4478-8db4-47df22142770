/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!shake/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '手机号：',
                    dataIndex: 'phone',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '手机号'
                },
                {
                    header: '抖音账号：',
                    dataIndex: 'douYin',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '抖音账号'
                },
                {
                    header: '角色：',
                    dataIndex: 'role',
                    xtype: 'text',
                    maxlength: 8,
                    placeholder: '角色'
                },
                {
                    header: '姓名：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 16,
                    placeholder: '姓名'
                },
                {
                    header: '地址：',
                    dataIndex: 'address',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '地址'
                },
                {
                    header: 'mucang账号：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: 'mucang账号'
                },
                {
                    header: '参加主题1：',
                    dataIndex: 'themeOne',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '参加主题1'
                },
                {
                    header: '奖品1：',
                    dataIndex: 'awardOne',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '奖品1'
                },
                {
                    header: '奖品1已发放：',
                    dataIndex: 'awardOneUsed',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '参加主题2：',
                    dataIndex: 'themeTwo',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '参加主题2'
                },
                {
                    header: '奖品2：',
                    dataIndex: 'awardTwo',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '奖品2'
                },
                {
                    header: '奖品2已发放：',
                    dataIndex: 'awardTwoUsed',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '参加主题3：',
                    dataIndex: 'themeThree',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '参加主题3'
                },
                {
                    header: '奖品3：',
                    dataIndex: 'awardThree',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '奖品3'
                },
                {
                    header: '奖品3已发放：',
                    dataIndex: 'awardThreeUsed',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '平台：',
                    dataIndex: 'platform',
                    xtype: 'select',
                    maxlength: 16,
                    placeholder: '平台',
                    store: [
                        {
                            key: 'android',
                            value: 'android'
                        },
                        {
                            key: 'iphone',
                            value: 'iphone'
                        }
                    ]
                },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'shake列表',
            title: 'shake列表',
            search: [
                {
                    xtype: 'text',
                    dataIndex: 'phone',
                    placeholder: '手机号'
                },
                {
                    xtype: 'text',
                    dataIndex: 'douYin',
                    placeholder: '抖音号'
                },
                {
                    xtype: 'text',
                    dataIndex: 'theme',
                    placeholder: '参加主题'
                },
                {
                    xtype: 'text',
                    dataIndex: 'awardName',
                    placeholder: '奖品名'
                },
                {
                    xtype: 'select',
                    dataIndex: 'awardUsed',
                    store: [
                        {
                            key: '',
                            value: '是否发放'
                        },
                        {
                            key: 'true',
                            value: '是'
                        },
                        {
                            key: 'false',
                            value: '否'
                        }
                    ]
                },

                {
                    xtype: 'select',
                    dataIndex: 'role',
                    store: [
                        {
                            key: '',
                            value: '选择角色'
                        },
                        {
                            key: "我是教练",
                            value: '我是教练'
                        },
                        {
                            key: '我是学员',
                            value: '我是学员'
                        }
                    ]
                },
                {
                    xtype: 'select',
                    dataIndex: 'searchMC',
                    store: [
                        {
                            key: '',
                            value: '是否木仓账号'
                        },
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    xtype: 'select',
                    dataIndex: 'platform',
                    store: [
                        {
                            key: '',
                            value: '选择平台'
                        },
                        {
                            key: "android",
                            value: 'android'
                        },
                        {
                            key: "iphone",
                            value: 'iphone'
                        }
                    ]
                },
            ],

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    // {
                    //     name: '添加',
                    //     class: 'primary',
                    //     click: add
                    // }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!shake/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '手机号：',
                            dataIndex: 'phone'
                        },
                        {
                            header: '抖音账号：',
                            dataIndex: 'douYin'
                        },
                        {
                            header: '角色：',
                            dataIndex: 'role'
                        },
                        {
                            header: '姓名：',
                            dataIndex: 'name'
                        },
                        {
                            header: '地址：',
                            dataIndex: 'address'
                        },
                        {
                            header: 'mucang账号：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '参加主题1：',
                            dataIndex: 'themeOne'
                        },
                        {
                            header: '奖品1：',
                            dataIndex: 'awardOne'
                        },
                        {
                            header: '奖品1已发放：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'awardOneUsed'
                        },
                        {
                            header: '参加主题2：',
                            dataIndex: 'themeTwo'
                        },
                        {
                            header: '奖品2：',
                            dataIndex: 'awardTwo'
                        },
                        {
                            header: '奖品2已发放：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'awardTwoUsed'
                        },
                        {
                            header: '参加主题3：',
                            dataIndex: 'themeThree'
                        },
                        {
                            header: '奖品3：',
                            dataIndex: 'awardThree'
                        },
                        {
                            header: '奖品3已发放：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'awardThreeUsed'
                        },
                        {
                            header: '平台：',
                            dataIndex: 'platform'
                        },
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!shake/data/view',
                        save: 'jiakao-misc!shake/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '手机号：',
                            dataIndex: 'phone',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '手机号'
                        },
                        {
                            header: '抖音账号：',
                            dataIndex: 'douYin',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '抖音账号'
                        },
                        {
                            header: '角色：',
                            dataIndex: 'role',
                            xtype: 'text',
                            maxlength: 8,
                            placeholder: '角色'
                        },
                        {
                            header: '姓名：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 16,
                            placeholder: '姓名'
                        },
                        {
                            header: '地址：',
                            dataIndex: 'address',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '地址'
                        },
                        {
                            header: 'mucang账号：',
                            dataIndex: 'userId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: 'mucang账号'
                        },
                        {
                            header: '参加主题1：',
                            dataIndex: 'themeOne',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '参加主题1'
                        },
                        {
                            header: '奖品1：',
                            dataIndex: 'awardOne',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '奖品1'
                        },
                        {
                            header: '奖品1已发放：',
                            dataIndex: 'awardOneUsed',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        },
                        {
                            header: '参加主题2：',
                            dataIndex: 'themeTwo',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '参加主题2'
                        },
                        {
                            header: '奖品2：',
                            dataIndex: 'awardTwo',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '奖品2'
                        },
                        {
                            header: '奖品2已发放：',
                            dataIndex: 'awardTwoUsed',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        },
                        {
                            header: '参加主题3：',
                            dataIndex: 'themeThree',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '参加主题3'
                        },
                        {
                            header: '奖品3：',
                            dataIndex: 'awardThree',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '奖品3'
                        },
                        {
                            header: '奖品3已发放：',
                            dataIndex: 'awardThreeUsed',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        },
                        {
                            header: '平台：',
                            dataIndex: 'platform',
                            xtype: 'select',
                            maxlength: 16,
                            placeholder: '平台',
                            store: [
                                {
                                    key: 'android',
                                    value: 'android'
                                },
                                {
                                    key: 'iphone',
                                    value: 'iphone'
                                }
                            ]
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!shake/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '手机号',
                    dataIndex: 'phone',
                },
                {
                    header: '抖音账号',
                    dataIndex: 'douYin'
                },
                {
                    header: '角色',
                    dataIndex: 'role'
                },
                {
                    header: '姓名',
                    dataIndex: 'name'
                },
                {
                    header: '地址',
                    dataIndex: 'address',
                    render: function (data) {
                        return "<div style='word-break:break-word;width: 100px;'>" + data + "</div>"
                    }
                },
                {
                    header: 'mucang账号',
                    dataIndex: 'userId',
                    render: function (data) {
                        if (data === null) {
                            return " ";
                        } else {
                            if (data) {
                                return '是';
                            } else {
                                return '否'
                            }
                        }
                    }
                },
                {
                    header: '参加主题1',
                    dataIndex: 'themeOne'
                },
                {
                    header: '奖品1',
                    dataIndex: 'awardOne'
                },
                {
                    header: '奖品1已发放',
                    dataIndex: 'awardOneUsed',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '<div style="color:red">否</div>';
                        }
                    },

                },
                {
                    header: '参加主题2',
                    dataIndex: 'themeTwo'
                },
                {
                    header: '奖品2',
                    dataIndex: 'awardTwo'
                },
                {
                    header: '奖品2已发放',
                    dataIndex: 'awardTwoUsed',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '<div style="color:red">否</div>';
                        }
                    },

                },
                {
                    header: '参加主题3',
                    dataIndex: 'themeThree'
                },
                {
                    header: '奖品3',
                    dataIndex: 'awardThree'
                },
                {
                    header: '奖品3已发放',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '<div style="color:red">否</div>';
                        }
                    },
                    dataIndex: 'awardThreeUsed'
                },
                {
                    header: '平台',
                    dataIndex: 'platform',

                },

            ]
        }, ['jiakao-misc!shake/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
