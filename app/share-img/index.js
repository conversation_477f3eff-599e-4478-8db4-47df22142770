/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    const applySceneMap = {
        1: '模拟考试成绩',
        20: '成就勋章-模拟考试首次及格',
        21: '成就勋章-模拟考试首次满分',
        22: '成就勋章-首次做完500题',
        3: '成就勋章墙',
        4: '12123成绩图',
        5: '12123过考勋章',
        6: '自定义'
    };

    const applySceneStore = [{ key: '', value: '选择素材类型' }].concat(Object.keys(applySceneMap).map(key => {
        return {
            key: +key,
            value: applySceneMap[key]
        }
    }));

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!share-img/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '描述：',
                    dataIndex: 'description',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '描述'
                },
                {
                    header: '应用场景：',
                    dataIndex: 'applyScene',
                    xtype: 'select',
                    store: applySceneStore,
                    placeholder: '应用场景'
                },
                {
                    header: '示例图片：',
                    dataIndex: 'imgUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'imgUrl',
                        uploadIndex: 'imgUrl',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                    maxlength: 256,
                    placeholder: '示例图片'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '分享图片列表',
            title: '分享图片列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!share-img/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description'
                        },
                        {
                            header: '应用场景：',
                            dataIndex: 'applyScene',
                            render(data) {
                                return applySceneMap[data];
                            }
                        },
                        {
                            header: '示例图片：',
                            dataIndex: 'imgUrl',
                            render: function (data) {
                                return `<img src='${data}' style="width:200px"/>`
                            },
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: 'createUserId：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: 'updateUserId：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!share-img/data/view',
                        save: 'jiakao-misc!share-img/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '描述：',
                            dataIndex: 'description',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '描述'
                        },
                        {
                            header: '应用场景：',
                            dataIndex: 'applyScene',
                            xtype: 'select',
                            store: applySceneStore,
                            placeholder: '应用场景'
                        },
                        {
                            header: '示例图片：',
                            dataIndex: 'imgUrl',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'imgUrl',
                                uploadIndex: 'imgUrl',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            }),
                            maxlength: 256,
                            placeholder: '图片地址'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!share-img/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '描述',
                    dataIndex: 'description'
                },
                {
                    header: '应用场景',
                    dataIndex: 'applyScene',
                    render(data) {
                        return applySceneMap[data];
                    }
                },
                {
                    header: '示例图片',
                    dataIndex: 'imgUrl',
                    render: function (data) {
                        return `<a><img src='${data}' style="width:200px"/></a>`
                    },
                    click: function (table, trDom, lineData) {
                        Widgets.dialog.html('查看').done(function (dialog) {
                            var oA = $(`<img src='${lineData.imgUrl}' style="width:100%;"/>`);
                            dialog.body.append(oA);
                        })
                    }
                },
            ]
        }, ['jiakao-misc!share-img/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});