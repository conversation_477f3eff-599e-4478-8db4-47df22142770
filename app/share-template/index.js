/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    const styleTypeMap = {
        1: '纯文案',
        2: '文案+图片',
        3: '文案+链接'
    };

    const styleTypeStore = Object.keys(styleTypeMap).map(key => {
        return {
            key: +key,
            value: styleTypeMap[key]
        }
    });

    const tagTypeMap = {
        1: '晒成绩',
        2: '晒驾照',
        3: '晒干货'
    };

    const tagTypeStore = Object.keys(tagTypeMap).map(key => {
        return {
            key: +key,
            value: tagTypeMap[key]
        }
    });

    const statusMap = {
        0: '下架',
        1: '上架',
    };

    const statusStore = Object.keys(statusMap).map(key => {
        return {
            key: +key,
            value: statusMap[key]
        }
    });

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!share-template/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '标题'
                },
                {
                    header: '素材类型',
                    dataIndex: 'styleType',
                    xtype: 'radio',
                    check: 'required',
                    store: styleTypeStore,
                },
                {
                    header: '关联文案：',
                    check: 'required',
                    dataIndex: 'texts',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!share-text/data/list?limit=1000',
                        placeholder: '关联文案',
                        dataIndex: 'texts',
                        index: {
                            key: 'id',
                            value: 'topic',
                            search: 'topic'
                        },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                    placeholder: '关联文案',
                },
                {
                    header: '关联图片：',
                    dataIndex: 'imgs',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!share-img/data/list?limit=1000',
                        placeholder: '关联图片',
                        dataIndex: 'imgs',
                        index: {
                            key: 'id',
                            value: 'description',
                            search: 'description'
                        },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    }),
                    maxlength: 128,
                    placeholder: '关联图片'
                },
                {
                    header: '类型：',
                    dataIndex: 'tagType',
                    xtype: 'radio',
                    store: tagTypeStore,
                    check: 'required',
                    placeholder: '类型',
                },
                {
                    header: '跳转链接：',
                    dataIndex: 'url',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '跳转链接(可选)'
                },
                {
                    header: '链接标题：',
                    dataIndex: 'urlTitle',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '链接标题(可选)'
                },
                {
                    header: '链接图标：',
                    dataIndex: 'urlIcon',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'urlIcon',
                        uploadIndex: 'urlIcon',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    }),
                    maxlength: 256,
                    placeholder: '链接图标(可选)'
                },
                {
                    header: '排序：',
                    dataIndex: 'sortValue',
                    xtype: 'text',
                    placeholder: '排序'
                },
                {
                    header: '状态：',
                    dataIndex: 'status',
                    xtype: 'radio',
                    store: statusStore,
                    placeholder: '状态'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: 'share-template列表',
            title: 'share-template列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!share-template/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title'
                        },
                        {
                            header: '素材类型',
                            dataIndex: 'styleType',
                            render(data) {
                                return styleTypeMap[data];
                            }
                        },
                        {
                            header: '关联文案：',
                            dataIndex: 'textContentList',
                            render(data) {
                                return (data || []).join('、');
                            }
                        },
                        {
                            header: '关联图片：',
                            dataIndex: 'imgDescList',
                            render(data) {
                                return (data || []).join('、');
                            }
                        },
                        {
                            header: '标签类型：',
                            dataIndex: 'tagType',
                            render(data) {
                                return tagTypeMap[data];
                            }
                        },
                        {
                            header: '跳转链接：',
                            dataIndex: 'url'
                        },
                        {
                            header: '链接标题：',
                            dataIndex: 'urlTitle'
                        },
                        {
                            header: '链接图标：',
                            dataIndex: 'urlIcon',
                            render: function (data) {
                                return `<a><img src='${data}' style="width:200px"/></a>`
                            },
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sortValue'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            render(data) {
                                return statusMap[data];
                            }
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: 'createUserId：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        },
                        {
                            header: 'updateUserId：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!share-template/data/view',
                        save: 'jiakao-misc!share-template/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '标题'
                        },
                        {
                            header: '素材类型',
                            dataIndex: 'styleType',
                            xtype: 'radio',
                            store: styleTypeStore,
                            check: 'required',
                            placeholder: '素材类型 1:纯文案 2:文案+图片 3:文案+链接'
                        },
                        {
                            header: '关联文案：',
                            dataIndex: 'texts',
                            check: 'required',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: 'jiakao-misc!share-text/data/list?limit=1000',
                                placeholder: '关联文案',
                                dataIndex: 'texts',
                                index: {
                                    key: 'id',
                                    value: 'topic',
                                    search: 'topic'
                                },
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            }),
                            maxlength: 128,
                            placeholder: '关联文案'
                        },
                        {
                            header: '关联图片：',
                            dataIndex: 'imgs',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: 'jiakao-misc!share-img/data/list?limit=1000',
                                placeholder: '关联图片',
                                dataIndex: 'imgs',
                                index: {
                                    key: 'id',
                                    value: 'description',
                                    search: 'description'
                                },
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            }),
                            maxlength: 128,
                            placeholder: '关联图片'
                        },
                        {
                            header: '标签位置：',
                            dataIndex: 'tagType',
                            xtype: 'radio',
                            store: tagTypeStore,
                            check: 'required',
                            placeholder: '类型'
                        },
                        {
                            header: '跳转链接：',
                            dataIndex: 'url',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '跳转链接(可选)'
                        },
                        {
                            header: '链接标题：',
                            dataIndex: 'urlTitle',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '链接标题(可选)'
                        },
                        {
                            header: '链接图标：',
                            dataIndex: 'urlIcon',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'urlIcon',
                                uploadIndex: 'urlIcon',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            }),
                            maxlength: 256,
                            placeholder: '链接图标(可选)'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sortValue',
                            xtype: 'text',
                            placeholder: '排序'
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            xtype: 'radio',
                            store: statusStore,
                            placeholder: '状态'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!share-template/data/delete'
                },
                {
                    name: '上/下架',
                    width: 500,
                    class: 'info',
                    render: function (name, arr, index) {
                        return arr[index].status ? '下架' : '上架';
                    },
                    click: function (table, row, lineData) {
                        const header = lineData.status ? '下架' : '上架';
                        const lineStatus = lineData.status ? 0 : 1;
                        Widgets.dialog.confirm('确定' + header + '吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!share-template/data/updateStatus?id=' + lineData.id + '&status=' + lineStatus]).save().done(function () {
                                    table.render()
                                }).fail(err => {
                                    console.log(err);
                                });
                            }
                        })
                    }


                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '素材类型',
                    dataIndex: 'styleType',
                    render(data) {
                        return styleTypeMap[data];
                    }
                },
                {
                    header: '关联文案',
                    dataIndex: 'textContentList',
                    width: 150,
                    render(data) {
                        return (data || []).join('<br/><br/>');
                    }
                },
                {
                    header: '关联图片',
                    dataIndex: 'imgDescList',
                    width: 150,
                    render(data) {
                        return (data || []).join('<br/><br/>');
                    }
                },
                {
                    header: '标签类型',
                    dataIndex: 'tagType',
                    render(data) {
                        return tagTypeMap[data];
                    }
                },
                {
                    header: '跳转链接',
                    dataIndex: 'url'
                },
                {
                    header: '链接标题',
                    dataIndex: 'urlTitle'
                },
                {
                    header: '链接图标',
                    dataIndex: 'urlIcon',
                    render: function (data) {
                        return `<a><img src='${data}' style="width:200px"/></a>`
                    },
                },
                {
                    header: '排序',
                    dataIndex: 'sortValue',
                    order: 'desc'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render(data) {
                        return statusMap[data];
                    }
                },
            ]
        }, ['jiakao-misc!share-template/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});