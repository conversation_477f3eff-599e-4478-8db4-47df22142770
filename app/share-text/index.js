/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!share-text/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '分享主题：',
                    dataIndex: 'topic',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '分享主题'
                },
                {
                    header: '分享内容：',
                    dataIndex: 'content',
                    xtype: 'textarea',
                    maxlength: 1024,
                    rows: 10,
                    check: 'required',
                    placeholder: '分享内容'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '分享文案列表',
            title: '分享文案列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!share-text/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '分享主题：',
                            dataIndex: 'topic'
                        },
                        {
                            header: '分享内容：',
                            dataIndex: 'content',
                            render(data) {
                                return `<div style="white-space:pre-wrap;">${data}</div>`
                            }
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: 'createUserId：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: 'updateTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: 'updateUserId：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!share-text/data/view',
                        save: 'jiakao-misc!share-text/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '分享主题：',
                            dataIndex: 'topic',
                            xtype: 'text',
                            maxlength: 64,
                            check: 'required',
                            placeholder: '分享主题'
                        },
                        {
                            header: '分享内容：',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            maxlength: 1024,
                            rows: 10,
                            check: 'required',
                            placeholder: '分享内容'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!share-text/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '分享主题',
                    dataIndex: 'topic'
                },
                {
                    header: '分享内容',
                    dataIndex: 'content',
                    render(data) {
                        return `<div style="white-space:pre-wrap;">${data}</div>`
                    }
                },
            ]
        }, ['jiakao-misc!share-text/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});