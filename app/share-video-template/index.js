/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'jiakao-misc!app/common/tools'], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Tools) {


    const typeStatus = {
        1: '驾考成绩分享',
        2: '成绩单分享',
        3: '徽章分享'
    }

    const publishStatusMap = {
        0: '未发布',
        1: '测试发布',
        2: '正式发布'
    }

    const enableMap = {
        false: '否',
        true: '是'
    }

    var add = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: 'jiakao-misc!share-video-template/data/' + (isEdit ? 'update' : 'insert'),
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    const coverImgUrlData = JSON.parse(form.coverImgUrl.value || '[]');
                    const params = {
                        coverImgCode: coverImgUrlData[0]?.encode,
                        videoConfig: JSON.stringify({
                            videoId: form.videoId.value
                        })
                    }

                    return params
                }
            },
            renderAfter: function (table, $form) {
                if (isEdit) {
                    setTimeout(() => {
                        $form.item('coverImgUrl-group').find("input[name=coverImgUrl]").val(JSON.stringify([{ encode: lineData.coverImgCode }]))
                    }, 300)
                }
            },
            columns: [
                {
                    xtype: 'hidden',
                    dataIndex: 'id'
                },
                {
                    header: '视频模版名称：',
                    xtype: 'text',
                    dataIndex: 'name',
                    placeholder: '视频模版名称'
                },
                {
                    header: '视频模版描述：',
                    xtype: 'text',
                    dataIndex: 'description',
                    placeholder: '视频模版描述'
                },
                {
                    header: '视频模版封面：',
                    dataIndex: 'coverImgUrl',
                    xtype: Plugin('simple!select-images', {
                        useSecureUpload: true,
                        // 必填。第三版安全文件上传所需参数。应用空间唯一标识，相当于第二版的 bucket。
                        appSpaceId: '9c392d369c3936160d4e',

                        imageSrc: {
                            // 必填。
                            dataIndex: 'encode',
                        },
                        parse: function (value) {
                            return [
                                {
                                    encode: value
                                }
                            ]
                        }
                    }),
                    placeholder: '视频模版封面'
                },
                {
                    header: '适用场景：',
                    dataIndex: 'type',
                    xtype: Plugin('simple!auto-prompt2', {
                        store: Tools.getArrayFromMap(typeStatus),
                        dataIndex: 'type',
                        isMulti: true,
                        defaultVal: false,
                        placeholder: '适用场景'
                    }, function (plugin, value) {
                    }),
                },
                {
                    header: '模版视频Id：',
                    xtype: 'text',
                    dataIndex: 'videoId',
                    placeholder: '模版视频Id'
                },
                {
                    header: '模版排序：',
                    xtype: 'text',
                    dataIndex: 'sort',
                    placeholder: '模版排序'
                }
            ]
        }
        if (isEdit) {
            Table().edit({
                ...lineData,
                videoId: lineData.videoId
            }, config);
        } else {
            Table(config).add();
        }
    }

    var upDateImageInfo = function (table, lineData = {}) {
        var isEdit = !!lineData.id
        var config = {
            title: '编辑图片信息',
            width: 500,
            store: 'jiakao-misc!share-video-template/data/updateImageConfig',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    const $form = $(form);
                    const params = {
                        imageConfig: (() => {
                            const $imgList = $form.item('videoBgImg-group').find('.videoBgImg .img-item');
                            const list = [];
                            $imgList.each((index, $item) => {
                                const duration = $($item).find('.duration').val()
                                const sort = $($item).find('.sort').val()

                                if (duration && sort) {
                                    list.push({
                                        duration,
                                        sort
                                    })
                                }

                            })

                            return JSON.stringify(list)
                        })()
                    }

                    return params
                }
            },
            renderAfter: function (table, $form) {

                const renderVideoBgImg = () => {
                    const $VideoBgImgBox = $form.item('videoBgImg-group').find('.videoBgImg');
                    const $addImg = $(`<div style="margin: 20px auto;width:100px;height:40px;border:1px solid #999;line-height:40px;border-radius:4px;text-align:center;">新增</div>`);
                    let $box = $(`<div class="img-list-box"><div class="img-item-title" style="display: flex; align-items: center;margin-bottom: 15px;"><div style="width:150px;" type="text">展示时长（ms）:</div><div style="width:150px;margin-left:15px;" type="text">图片顺序:</div></div></div>`);

                    function addItem(value = {}) {
                        const $item = $(`<div class="img-item" style="display: flex; align-items: center;margin-bottom: 15px;"><input class="duration" style="width:150px;" placeholder="请输入展示时长" type="text"/><input class="sort" style="width:150px;margin-left:15px;" placeholder="请输入图片顺序" type="text"/> <span style="flex-shrink:0;width: 30px;color: red;margin-left:15px" class="remove">删除</span></div>`);

                        $item.find('.duration').val(value.duration);
                        $item.find('.sort').val(value.sort);

                        $item.find('.remove').on('click', function () {
                            $(this).parent('.img-item').remove();
                        });

                        $box.append($item);
                    }

                    $addImg.on('click', function () {
                        addItem();
                    })
                    const imageConfig = JSON.parse(lineData.imageConfig || '[]')
                    if (!isEdit || !imageConfig?.length) {
                        addItem();
                    } else if (imageConfig?.length) {
                        imageConfig.forEach(item => {
                            addItem(item);
                        });
                    }

                    $VideoBgImgBox.append($box);
                    $VideoBgImgBox.append($addImg);
                }

                renderVideoBgImg()
            },
            columns: [
                {
                    xtype: 'hidden',
                    dataIndex: 'id'
                },
                {
                    header: '模版图片设置：',
                    dataIndex: 'videoBgImg',
                    xtype: () => {
                        return `<div class="videoBgImg"></div>`
                    },
                    placeholder: '模版图片设置'
                }
            ]
        }

        Table().edit({
            ...lineData,
        }, config);
    }

    var preView = function (imgInfoList = [], url) {
        
        var imgList = ['http://exam-room.mc-cdn.cn/exam-room/2023/12/22/16/4463597fa2764122b28dad68d68d8573.jpg', 'http://exam-room.mc-cdn.cn/exam-room/2023/12/22/16/b54845a14f294486b156ecadfd3d148d.jpg'];
        var lastUpdate;
        var container;
        var camera, scene, renderer;
        var uniforms;

        Simple.Dialog.html('相关功能', {
            width: 800,
            buttons: [{
                name: '关闭',
                xtype: 'success',
                click: function () {
                    this.close();
                }
            }]
        }).done(function (dialog) {
            $(dialog.body).append($(`<div id="container" style="margin:0 auto;"><video style="width: 600px;display:none;" id="video" src=""/><button id="rePlay" class="btn btn-primary btn-sm">重播</button></div>`))
            setTimeout(() => {
                init();
            }, 300)

            var $btn = $(dialog.body).find('#rePlay');

            $btn.on('click', function () {
                $(renderer.domElement).remove();
                init();
            })

        })

        function init() {

            container = document.getElementById('container');
            camera = new THREE.PerspectiveCamera();
            camera.position.z = 4;
            scene = new THREE.Scene();

            var video = document.getElementById('video');
            video.src = url;
            video.setAttribute('crossorigin', 'anonymous');

            video.play();
            // video.onended = () => {
            //     video.play();
            // }
            var videoTexture = new THREE.VideoTexture(video);
            videoTexture.minFilter = THREE.LinearFilter;
            videoTexture.magFilter = THREE.LinearFilter;
            videoTexture.format = THREE.RGBAFormat;
            // shader stuff
            uniforms = {
                time: { type: "f", value: 1.0 },
                textureSampler: { type: "sampler2D", value: videoTexture }
            };
            var material = new THREE.ShaderMaterial({
                uniforms: uniforms,
                vertexShader: `varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }`,
                fragmentShader: `#ifdef GL_ES
                precision highp float;
                        #endif

                uniform float time;
                uniform sampler2D textureSampler;
                varying vec2 vUv;

                void main(void ) {
                    gl_FragColor = vec4(
                        texture2D(textureSampler, vec2(0.5 + vUv.x / 2., vUv.y)).rgb,
                        texture2D(textureSampler, vec2(vUv.x / 2., vUv.y)).r
                    );
                }`,
                transparent: true
            });
            lastUpdate = new Date().getTime();
            var geometry = new THREE.PlaneGeometry(2, 2);
            var mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(0, 0, 2)


            function renderBgImg(imgUrl) {
                var textureLoader = new THREE.TextureLoader();
                var imgTexture = textureLoader.load(imgUrl)
                var bgMaterial = new THREE.SpriteMaterial({
                    map: imgTexture
                });
                var sprite = new THREE.Sprite(bgMaterial);
                sprite.scale.set(2.5, 2.5, 1)
                sprite.position.set(0, 0, 1)

                return sprite;
            }

            if (imgInfoList.length) {
                imgInfoList.forEach((item, index) => {
                    item.url = imgList[index % imgList.length]
                });
                imgInfoList.sort((a, b) => +a.sort - b.sort)


                let bgImgSprite;
                imgInfoList.forEach(async (item, index) => {
                    if (index === 0) {
                        bgImgSprite = renderBgImg(item.url);
                        scene.add(bgImgSprite);
                    } else {
                        await new Promise(resolve => {
                            setTimeout(() => {
                                scene.remove(bgImgSprite);
                                bgImgSprite = renderBgImg(item.url);
                                scene.add(bgImgSprite);
                                resolve();
                            }, +imgInfoList[index - 1].duration);
                        })
                    }

                    if (index === imgInfoList.length - 1) {
                        await new Promise(resolve => {
                            setTimeout(() => {
                                scene.remove(bgImgSprite);
                            }, +item.duration);
                        })
                    }
                });
            }

            scene.add(mesh);
            renderer = new THREE.WebGLRenderer({ alpha: true });
            renderer.setPixelRatio(window.devicePixelRatio / 1);

            $(container).prepend(renderer.domElement);

            // renderer.setSize(video.offsetWidth/2, video.offsetHeight);
            renderer.setSize(750, 1100);
            animate();
        }

        function animate() {
            var currentTime = new Date().getTime()
            var timeSinceLastUpdate = currentTime - lastUpdate;
            lastUpdate = currentTime;

            requestAnimationFrame(animate);
            render(timeSinceLastUpdate);

        }
        function render(timeDelta) {
            uniforms.time.value += (timeDelta ? timeDelta / 1000 : 0.05);
            renderer.render(scene, camera);
        }
    }

    var list = function (panel) {
        Table({
            description: '分享视频模板',
            title: '分享视频模板',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            add(table)
                        }
                    },

                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [
                {
                    dataIndex: 'name',
                    xtype: 'text',
                    placeholder: '视频模板名称'
                },
                {
                    dataIndex: 'type',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择适用场景'
                        },
                        ...Tools.getArrayFromMap(typeStatus)
                    ],
                    placeholder: '适用场景'
                }
            ],
            operations: [
                {
                    name: '编辑',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    click: function (table, $tr, lineData) {
                        add(table, lineData);
                    }
                },
                {
                    name: '编辑图片信息',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    click: function (table, $tr, lineData) {
                        upDateImageInfo(table, lineData);
                    }
                },
                {
                    name: '开启',
                    width: 500,
                    class: 'primary',
                    render: function (text, data, index) {
                        if (data[index].publishStatus === 0) {
                            return '测试上线'
                        }
                        if (data[index].publishStatus === 1) {
                            return '上线'
                        }
                        if (data[index].publishStatus === 2) {
                            return '下线'
                        }
                    },
                    click: function (table, lineDom, lineData, dom, data, index) {
                        Store([`jiakao-misc!share-video-template/data/batchUpdate?ids=${lineData.id}&publishStatus=${(lineData.publishStatus + 1) % 3} `]).load([{}]).done(function (store, data) {
                            table.render();
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        })
                    },
                },
                {
                    name: '预览',
                    width: 500,
                    class: 'primary',
                    render: function (text, data, index) {
                        if (data[index].videoEnable) {
                            return '预览'
                        }
                    },
                    click: function (table, lineDom, lineData, dom, data, index) {
                        preView(JSON.parse(lineData.imageConfig || '[]'), lineData.videoUrl);
                    },
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!share-video-template/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '描述',
                    dataIndex: 'description'
                },
                // {
                //     header: '模板封面code',
                //     dataIndex: 'coverImgCode',
                //     render: function (data) {
                //         return `<div style = "max-width: 150px;word-break: break-all;" > ${ data }</div> `
                //     }
                // },
                {
                    header: '模板封面url',
                    dataIndex: 'coverImgUrl',
                    render: function (data) {
                        return `<img style = "width:100px;" src = "${data}" /> `
                    }
                },

                {
                    header: '分享类型',
                    dataIndex: 'type',
                    render: function (data) {
                        const list = String(data).split(',');

                        return (list.map(item => typeStatus[item])).join(',');
                    }
                },

                {
                    header: '视频',
                    dataIndex: 'videoUrl',
                    render: function (data) {
                        return `<a target = "break" style = "max-width: 150px;word-break: break-all;" href = "${data}" > 点击查看</a> `
                    }
                },

                {
                    header: '图片信息',
                    dataIndex: 'imageConfig',
                    render: function (data) {
                        let $boxStr = `<div style = "max-width: 150px;word-break: break-all;" > `
                        data = JSON.parse(data || '[]')
                        data.forEach(item => {
                            $boxStr += `<div> 展示时长：${item.duration}</div>
                    <div>图片顺序：${item.sort}</div>`
                        })

                        $boxStr += '</div>'

                        return $boxStr
                    }
                },
                {
                    header: '使用人数',
                    dataIndex: 'userNum'
                },
                {
                    header: '发布状态',
                    dataIndex: 'publishStatus',
                    render: function (data) {
                        return publishStatusMap[data];
                    }
                },

                {
                    header: '视频是否可用',
                    dataIndex: 'videoEnable',
                    render: function (data) {
                        return enableMap[data];
                    }
                },

                {
                    header: '排序',
                    dataIndex: 'sort'
                },


            ]
        }, ['jiakao-misc!share-video-template/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});