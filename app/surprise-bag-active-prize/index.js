/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var activityId
    
    var prizeTypeStore = [
        {
            key: 1,
            value: '实物奖品'
        }, {
            key: 2,
            value: '名师精品课'
        }, {
            key: 3,
            value: 'VIP特惠价格'
        }, {
            key: 4,
            value: '虚拟奖品'
        }
    ]
    var prizeTypeMap = Tools.getMapfromArray(prizeTypeStore);

    var condition1Store = [
        {
            key: '',
            value: '请选择'
        },
        {
            key: 1,
            value: '全部用户'
        }, {
            key: 2,
            value: '全科VIP专享'
        }
    ]
    var condition1Map = Tools.getMapfromArray(condition1Store);

    var condition2Store = [
        {
            key: '',
            value: '请选择'
        },
        {
            key: 1,
            value: '可重复中奖'
        }, {
            key: 2,
            value: '不可重复中奖'
        }
    ]
    var condition2Map = Tools.getMapfromArray(condition2Store);

    var renderAfter = function(table, dom, data) {
        dom.item('_prizeImg').replaceWith(`<div data-item="_prizeImg" class="form-group upload-success">
            <div class="image-wraper"style="width: 80px;height:80px;background: #eee">
                <img class="upload-image" style="width: 80px; height: 80px;" src="">
            </div>
        </div>`)
        var prizeIdDom = dom.item('prizeId');
        prizeIdDom.on('change', onChange);

        function onChange() {
            var prizeId = prizeIdDom.val();
            if(prizeId) {
                Store(['jiakao-misc!surprise-bag-prize/data/view?id=' + prizeId], [{
                    aliases: 'list'
                }]).load()
                .done(function (store, data, obj, ret) {
                    var data = store.data.list.data
                    dom.item('_prizeImg').find('.upload-image').attr('src', data.prizeImg)
                    dom.item('_prizeType').val(prizeTypeMap[data.prizeType])
                }).fail(function (ret) {
                    Widgets.dialog.alert(ret.message)
                });
            }else{
                dom.item('_prizeImg').find('.upload-image').attr('src', '')
                dom.item('_prizeType').val('')
            }
        }
        
        onChange()
    }
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '福袋中奖概率：',
                dataIndex: 'probability',
                xtype: 'text',
                placeholder: '正整数，例如写入1表示万分之一',
                check: 'required'
            },
            {
                header: '奖品份数：',
                dataIndex: 'quota',
                xtype: 'text',
                placeholder: '奖品份数',
                check: 'required'
            },
            {
                header: '选择奖品：',
                dataIndex: 'prizeId',
                xtype: 'select',
                placeholder: '选择奖品',
                store: 'jiakao-misc!surprise-bag-prize/data/list?limit=10000',
                index: {
                    key: 'id', value: 'prizeName'
                },
                insert: [
                    {
                        key: '',
                        prizeName: '选择奖品'
                    }
                ]
            },
            {
                header: '奖品类型：',
                dataIndex: '_prizeType',
                xtype: 'text',
                check: 'required',
                readonly: true,
            },
            {
                header: '奖品图片：',
                dataIndex: '_prizeImg',
                xtype: 'text',
                check: 'required',
                readonly: true,
            },
            {
                header: '参与条件1：',
                dataIndex: 'userMatchCondition',
                xtype: 'select',
                store: condition1Store,
                check: 'required'
            },
            {
                header: '参与条件2：',
                dataIndex: 'prizeMatchCondition',
                xtype: 'select',
                store: condition2Store,
                check: 'required'
            },
            {
                header: '展示排序：',
                dataIndex: 'displayOrder',
                xtype: 'text',
                placeholder: '展示排序',
                check: 'required'
            },
        ])
    }
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!surprise-bag-active-prize/data/insert?activityId='+activityId,
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns(),
            renderAfter: renderAfter
        }).add();
    }

    var list = function (panel, routeData) {
        activityId = routeData.id
        Table({
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!surprise-bag-active-prize/data/view',
                        save: 'jiakao-misc!surprise-bag-active-prize/data/update?activityId='+activityId
                    },
                    columns: columns(),
                    renderAfter: renderAfter
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!surprise-bag-active-prize/data/delete'
                }
            ],
            columns: [
                {
                    header: '福袋中奖概率',
                    dataIndex: 'probability',
                },
                {
                    header: '奖品份数',
                    dataIndex: 'quota',
                },
                {
                    header: '奖品名',
                    dataIndex: 'prizeName',
                },
                {
                    header: '奖品类型',
                    dataIndex: 'prizeType',
                    render: function(data) {
                        console.log(prizeTypeMap,data)
                        return prizeTypeMap[data]
                    }
                },
                {
                    header: '奖品图片',
                    dataIndex: 'prizeImg',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.prizeImg}">`)
                    }
                },
                {
                    header: '参考价值',
                    dataIndex: 'valuation',
                },
                {
                    header: '过期时间',
                    render: function (data) {
                        return data ? data + '小时后过期' : ''
                    },
                    dataIndex: 'prizeEffectHours',
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '展示排序：',
                    dataIndex: 'displayOrder',
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!surprise-bag-active-prize/data/list?activityId='+activityId], panel, null).render();
    }

    return {
        list: list
    }

});
