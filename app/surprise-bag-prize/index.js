/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var prizeTypeStore = [
        {
            key: 1,
            value: '实物奖品'
        }, {
            key: 2,
            value: '名师精品课'
        }, {
            key: 3,
            value: 'VIP特惠价格'
        }, {
            key: 4,
            value: '虚拟奖品'
        }
    ]
    var prizeTypeMap = Tools.getMapfromArray(prizeTypeStore);

    var effectHoursStore = [
        {
            key: '',
            value: '请选择'
        },
        {
            key: 24,
            value: '1天'
        }, {
            key: 72,
            value: '3天'
        }, {
            key: 168,
            value: '7天'
        }, {
            key: 720,
            value: '30天'
        }
    ]
    var effectHoursMap = Tools.getMapfromArray(effectHoursStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '奖品名：',
                dataIndex: 'prizeName',
                xtype: 'text',
                maxlength: 15,
                placeholder: '奖品名',
                check: 'required'
            },
            {
                header: '奖品描述：',
                dataIndex: 'prizeDesc',
                xtype: 'text',
                placeholder: '奖品描述',
                check: 'required'
            },
            {
                header: '奖品参考价值：',
                dataIndex: 'prizeValueation',
                xtype: 'text',
                placeholder: '单位：元',
                check: 'required'
            },
            {
                header: '奖品类型：',
                dataIndex: 'prizeType',
                xtype: 'select',
                check: 'required',
                store: prizeTypeStore
            },
            {
                header: '奖品发放标识：',
                dataIndex: 'prizeExt',
                xtype: 'text',
                placeholder: '奖品发放标识',
            },
            {
                header: '奖品有效期：',
                dataIndex: 'prizeEffectHours',
                xtype: 'select',
                store: effectHoursStore
            },
            {
                header: '奖品图片：',
                dataIndex: 'prizeImg',
                check: 'required',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'prizeImg',
                    uploadIndex: 'prizeImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            }
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!surprise-bag-prize/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            // renderAfter: function (table, dom, data) {
            //     var prizeTypeDom = dom.item('prizeType');
            //     prizeTypeDom.on('change', onChange);

            //     function onChange() {
            //         var prizeType = prizeTypeDom.val();
            //         if(prizeType == 2) {
            //             dom.item('prizeEffectHours-group').removeClass('hide')
            //         }else{
            //             dom.item('prizeEffectHours-group').addClass('hide')
            //         }
            //     }

            //     onChange()
            // },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '福袋抽奖活动管理',
            title: '福袋抽奖活动管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    // renderAfter: function (table, dom, data) {
                    //     var prizeTypeDom = dom.item('prizeType');
                    //     prizeTypeDom.on('change', onChange);
        
                    //     function onChange() {
                    //         var prizeType = prizeTypeDom.val();
                    //         if(prizeType == 2) {
                    //             dom.item('prizeEffectHours-group').removeClass('hide')
                    //         }else{
                    //             dom.item('prizeEffectHours-group').addClass('hide')
                    //         }
                    //     }
        
                    //     onChange()
                    // },
                    store: {
                        load: 'jiakao-misc!surprise-bag-prize/data/view',
                        save: 'jiakao-misc!surprise-bag-prize/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!surprise-bag-prize/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '奖品名',
                    dataIndex: 'prizeName',
                },
                {
                    header: '奖品描述',
                    dataIndex: 'prizeDesc',
                },
                {
                    header: '奖品参考价值',
                    dataIndex: 'prizeValueation',
                },
                {
                    header: '奖品类型',
                    dataIndex: 'prizeType',
                    render: function(data) {
                        return prizeTypeMap[data]
                    }
                },
                {
                    header: '奖品发放标识',
                    dataIndex: 'prizeExt',
                },
                {
                    header: '奖品有效期',
                    dataIndex: '奖品有效期',
                    render: function(data) {
                        return effectHoursMap[data]
                    }
                },
                {
                    header: '奖品图片',
                    dataIndex: 'prizeImg',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.prizeImg}">`)
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!surprise-bag-prize/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
