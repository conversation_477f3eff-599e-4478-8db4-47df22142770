/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var resultStatusStore = [
        {
            key: 2,
            value: '中奖'
        }, {
            key: 3,
            value: '未中奖'
        }
    ]
    var resultStatusMap = Tools.getMapfromArray(resultStatusStore);

    var list = function (panel) {
        Table({
            description: '福袋抽奖参与记录',
            title: '福袋抽奖参与记录',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!surprise-bag/data/activityUserDel',
                    render: function() {
                        const isTest = window.j.host['jiakao-misc'] === 'https://jiakao-misc.ttt.mucang.cn'
                        return isTest ? '删除' : ''
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '活动id',
                    dataIndex: 'activityId',
                },
                {
                    header: '用户id',
                    dataIndex: 'userId',
                },
                {
                    header: '用户昵称',
                    dataIndex: 'userNickName',
                },
                {
                    header: '参与时间',
                    dataIndex: 'createTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                {
                    header: '参与直播子课程ID',
                    dataIndex: 'lessonItemId',
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data) {
                        return Constants.kemuMap[data]
                    },
                },
                {
                    header: '开奖时间',
                    dataIndex: 'createTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                {
                    header: '奖品名称',
                    dataIndex: 'prizeName',
                },
                {
                    header: '抽奖状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return resultStatusMap[data]
                    },
                },
            ]
        }, ['jiakao-misc!surprise-bag/data/activityUserList'], panel, null).render();
    }

    return {
        list: list
    }

});
