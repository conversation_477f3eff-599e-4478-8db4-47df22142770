/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var list = function (panel) {
        Table({
            description: '福袋中奖信息-虚拟',
            title: '福袋中奖信息-虚拟',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                // {
                //     name: '备注',
                //     width: 500,
                //     class: 'default',
                //     click: function(table, lineDom, lineData) {
                //         Table().edit(lineData, {
                //             title: '备注',
                //             width: 500,
                //             store: 'jiakao-misc!surprise-bag/data/prizeAddRemark',
                //             success: function (obj, dialog, ret) {
                //                 dialog.close();
                //                 table.render();
                //             },
                //             columns: [
                //                 {
                //                     dataIndex: 'id',
                //                     value: lineData.id,
                //                     xtype: 'hidden'
                //                 },
                //                 {
                //                     header: '备注：',
                //                     dataIndex: 'remark',
                //                     xtype: 'textarea',
                //                     placeholder: '备注将会展示在前端页面，用户可以看到备注信息',
                //                     check: 'required'
                //                 },
                //             ]
                //         });
                //     }
                // },
                // {
                //     name: '查看手机号',
                //     width: 500,
                //     class: 'warning',
                //     click: function(table, lineDom, lineData) {
                //         Store(['jiakao-misc!surprise-bag/data/getPhone?id=' + lineData.id], [{
                //             aliases: 'list'
                //         }]).load().done(store => {
                //             var data = store.data.list.data
                //             var phone = data.value
                //             Widgets.dialog.html('手机号', {width: 300}).done(function (dialog) {
                //                 var dom = $(`<p>${phone}</p>`);
                //                 dialog.body.append(dom);
                //             })
                //         }).fail();
                //     }
                // },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户ID',
                    dataIndex: 'userId',
                },
                {
                    header: '用户昵称',
                    dataIndex: 'userNickName',
                },
                {
                    header: '中奖时间',
                    dataIndex: 'createTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                {
                    header: '奖品信息',
                    dataIndex: 'prizeName',
                },
                {
                    header: '中奖记录id',
                    dataIndex: 'prizeRecordId',
                },
                {
                    header: '发放时间',
                    dataIndex: 'sendTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
            //     {
            //         header: '备注',
            //         dataIndex: 'remark',
            //     },
            ]
        }, ['jiakao-misc!surprise-bag/data/virtualRecordList'], panel, null).render();
    }

    return {
        list: list
    }

});
