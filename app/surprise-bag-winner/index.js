/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools',
    'jiakao-misc!plugin/select-district/district'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools, District) {
    var expressStatusStore = [
        {
            key: 1,
            value: '未发货'
        }, {
            key: 2,
            value: '已发货'
        }, {
            key: 3,
            value: '发货中',
        }
    ]
    var expressStatusMap = Tools.getMapfromArray(expressStatusStore);
    
    var upload = function (table) {
        Table({
            description: '注意:上传文件只能是xls或者xlsx.不能直接修改文件后缀的方式上传.',
            title: '批量发货',
            width: 500,
            store: 'jiakao-misc!surprise-bag/data/batchDeliver',
            success: function (obj, dialog, responseData, response) {
                if (response.success) {
                    Widgets.dialog.html('上传结果', { width: 1000 }).done(function (dialog) {
                        var importResult = response.data;
                        var message = "上传数据共:" + importResult.total + "条,";
                        if (importResult.error > 0) {
                            message += "失败:" + importResult.error + "条.\r\n"
                        } else {
                            message += "导入成功:" + importResult.success + "条.\r\n"
                        }
                        var array = importResult.errorList;
                        for (var key in array) {
                            var data = array[key];
                            message = message + data.order + ',' + data.message + '\r\n';
                        }
                        $(dialog.body).append('<pre style="width=97%">' + message + '</pre>');
                    });
                    dialog.close();
                    table.render();
                } else {
                    Widgets.dialog.alert(arguments[3].message);
                    dialog.close();
                }
            },
            fail: function (obj, dialog) {
                Widgets.dialog.alert(arguments[3].message);
            },
            columns: [
                {
                    header: '文件',
                    dataIndex: 'data',
                    xtype: 'file',
                    check: "required",
                    placeholder: '请选择Excel文件'
                },
                {
                    header: '数据模板：',
                    dataIndex: 'templates',
                    xtype: function () {
                        let url = window.j.root['jiakao-misc'] + '/surprise-bag-winner-template.xlsx'
                        return `<a class="btn btn-info btn-sm" href="${url}" target="_blank" download>点击下载模板</a>`
                    }
                },
            ],
            renderAfter: function (table, dom) {

            }
        }).add();
    };

    var list = function (panel) {
        Table({
            description: '福袋中奖信息-实物',
            title: '福袋中奖信息-实物',
            search: [{
                header: '发货状态',
                dataIndex: 'status',
                xtype: 'select',
                store: [{key:'',value:'请选择发货状态'}].concat(expressStatusStore)
            }, {
                header: '活动ID',
                dataIndex: 'activityId',
                xtype: 'text',
                placeholder: '请输入活动ID'
            }, {
                header: '手机号',
                dataIndex: 'phone',
                xtype: 'text',
                placeholder: '请输入手机号'
            }, {
                header: 'userid',
                dataIndex: 'userId',
                xtype: 'text',
                placeholder: '请输入userid'
                }],
            selector: {
                dataIndex: 'id',
            },
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }, {
                        name: '批量发货',
                        class: 'primary',
                        click: upload
                    },
                    {
                        name: '批量标注发货中',
                        class: 'primary',
                        click: function (table, dom, config, selectedArr) {
                            if (selectedArr.length > 0) {
                                Widgets.dialog.confirm('确认标记为发货中吗？', function (ev, status) {
                                    if (status) {
                                        Store(['jiakao-misc!surprise-bag/data/updateStatus?ids=' + selectedArr.join(',')], [{
                                        }]).load().done(function (store) {
                                            Widgets.dialog.alert('标记成功')
                                            table.render()
                                        }).fail(function (err) {
                                            Widgets.dialog.alert(err.message)
                                        });
                                    } else { }
                                })
                            } else {
                                Widgets.dialog.alert('请选择数据')
                            }

                        },
                    },
                    {
                        name: "导出",
                        class: 'info',
                        click: function (table, rows, lineData,selected) {
                            console.log($('#status').val());
                            const status = $('#status').val();
                            const activityId = $('#activityId').val();
                            const phone = $('#phone').val();
                            const userId = $('#userId').val();
                            window.open(`${window.j.host.monkey}/api/admin/live-activity-prize-adress/export.htm?status=${status}&activityId=${activityId}&phone=${phone}&userId=${userId}&ids=${selected.join(',')}`);
                        }
                    },
                    {
                        name: '导入文件',
                        class: 'info',
                        click: function () {
                            Table({
                                title: '导入',
                                width: 500,
                                store: 'jiakao-misc!surprise-bag/data/importExpressNumber',
                                success: function (obj, dialog,responseData, response) {
                                    if (response.success) {
                                        Widgets.dialog.html('上传结果', { width: 1000 }).done(function (dialog) {
                                            var importResult = response.data;
                                            var message = "上传数据共:" + importResult.total + "条,";
                                            if (importResult.error > 0) {
                                                message += "失败:" + importResult.error + "条.\r\n"
                                            } else {
                                                message += "导入成功:" + importResult.success + "条.\r\n"
                                            }
                                            var array = importResult.errorList;
                                            for (var key in array) {
                                                var data = array[key];
                                                message = message + data.order + ',' + data.message + '\r\n';
                                            }
                                            $(dialog.body).append('<pre style="width=97%">' + message + '</pre>');
                                        });
                                        dialog.close();
                                    } else {
                                        Widgets.dialog.alert(arguments[3].message);
                                        dialog.close();
                                    }
                                },
                                columns: [{
                                    header: '选择文件：',
                                    dataIndex: 'data',
                                    xtype: 'file'
                                }, {
                                    header: '选择类型：',
                                    dataIndex: 'type',
                                    xtype: 'select',
                                    store: [{ key: '1', value: '根据获奖ID' }, { key: '2', value: '根据ID' }],
                                }]
                            }).add();
                        }
                    },
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                // {
                //     name: '编辑',
                //     xtype: 'edit',
                //     width: 500,
                //     class: 'primary',
                //     title: '编辑',
                //     success: function (obj, dialog, e) {
                //         dialog.close();
                //         obj.render();
                //     },
                //     store: {
                //         load: 'jiakao-misc!surprise-bag/data/prizeAdressView',
                //         save: 'jiakao-misc!surprise-bag/data/prizeAdressUpdate'
                //     },
                //     form: {
                //         submitHandler: function (form) {
                //             var areaName = $(form).find('[name="areaName"]').val();
                            
                //             if(areaName) {
                //                 return {}
                //             }else {
                //                 Widgets.dialog.alert('请选择所在地区');
                //                 return ''
                //             }
                //         }
                //     },
                //     columns: [
                //         {
                //             dataIndex: 'id',
                //             xtype: 'hidden'
                //         },
                //         {
                //             header: '用户ID：',
                //             dataIndex: 'name',
                //             xtype: 'text',
                //             disabled: true,
                //         },
                //         {
                //             header: '用户昵称：',
                //             dataIndex: 'userNickName',
                //             xtype: 'text',
                //             disabled: true,
                //         },
                //         {
                //             header: '中奖时间：',
                //             xtype: 'datetime',
                //             dataIndex: 'beginTime',
                //             disabled: true,
                //             render: function (data) {
                //                 if (data) {
                //                     return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                //                 }
                //             },
                //         },
                //         {
                //             header: '奖品信息：',
                //             dataIndex: 'prizeName',
                //             xtype: 'text',
                //             disabled: true,
                //         },
                //         {
                //             header: '信息提交时间：',
                //             xtype: 'datetime',
                //             dataIndex: 'createTime',
                //             disabled: true,
                //             render: function (data) {
                //                 if (data) {
                //                     return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                //                 }
                //             },
                //         },
                //         // {
                //         //     header: '收件人：',
                //         //     dataIndex: 'addressee',
                //         //     xtype: 'text',
                //         //     placeholder: '收件人',
                //         //     check: 'required'
                //         // },
                //         {
                //             header: '手机号：',
                //             dataIndex: 'phone',
                //             xtype: 'text',
                //             placeholder: '手机号',
                //             check: 'required'
                //         },
                //         // {
                //         //     header: '所在地区',
                //         //     dataIndex: 'adressArea',
                //         //     xtype: 'text',
                //         //     disabled: true,
                //         // },
                //         {
                //             header: '所在地区：',
                //             xtype: Plugin('jiakao-misc!select-district', {
                //                 name: 'cityCode',
                //                 areaName: 'areaCode',
                //                 insert: {
                //                     province: [{
                //                         code: '',
                //                         name: '请选择省份'
                //                     }],
                //                     city: [{
                //                         code: '',
                //                         name: '请选择市'
                //                     }],
                //                     area: [{
                //                         code: '',
                //                         name: '请选择区域'
                //                     }]
                //                 }
                //             }, function (plugin, code) {
                //                 // TODO
                //                 // adressArea
                //             }),
                //             dataIndex: 'areaCode'
                //         },
                //         // {
                //         //     header: '详细地址：',
                //         //     dataIndex: 'adressDetail',
                //         //     xtype: 'textarea',
                //         //     check: 'required',
                //         //     placeholder: '详细地址'
                //         // },
                //     ]
                // },
                {
                    name: '发货',
                    width: 500,
                    class: 'info',
                    click: function(table, lineDom, lineData) {
                        Table().edit(lineData, {
                            title: '发货',
                            width: 500,
                            store: 'jiakao-misc!surprise-bag/data/prizeAdressDeliver',
                            success: function (obj, dialog, ret) {
                                dialog.close();
                                table.render();
                            },
                            columns: [
                                {
                                    dataIndex: 'id',
                                    value: lineData.id,
                                    xtype: 'hidden'
                                },
                                {
                                    header: '快递单号：',
                                    dataIndex: 'expressNumber',
                                    xtype: 'text',
                                    placeholder: '快递单号',
                                },
                            ]
                        });
                    }
                },
                {
                    name: '备注',
                    width: 500,
                    class: 'default',
                    click: function(table, lineDom, lineData) {
                        Table().edit(lineData, {
                            title: '备注',
                            width: 500,
                            store: 'jiakao-misc!surprise-bag/data/prizeAddRemark',
                            success: function (obj, dialog, ret) {
                                dialog.close();
                                table.render();
                            },
                            columns: [
                                {
                                    dataIndex: 'id',
                                    value: lineData.id,
                                    xtype: 'hidden'
                                },
                                {
                                    header: '备注：',
                                    dataIndex: 'remark',
                                    xtype: 'textarea',
                                    placeholder: '备注将会展示在前端页面，用户可以看到备注信息',
                                    check: 'required'
                                },
                            ]
                        });
                    }
                },
                // {
                //     name: '查看手机号',
                //     width: 500,
                //     class: 'warning',
                //     click: function(table, lineDom, lineData) {
                //         Store(['jiakao-misc!surprise-bag/data/getPhone?id=' + lineData.id], [{
                //             aliases: 'list'
                //         }]).load().done(store => {
                //             var data = store.data.list.data
                //             var phone = data.value
                //             Widgets.dialog.html('手机号', {width: 300}).done(function (dialog) {
                //                 var dom = $(`<p>${phone}</p>`);
                //                 dialog.body.append(dom);
                //             })
                //         }).fail();
                //     }
                // },
                // {
                //     name: '查看收货地址',
                //     width: 500,
                //     class: 'warning',
                //     click: function(table, lineDom, lineData) {
                //         Store(['jiakao-misc!surprise-bag/data/getUserInfo?id=' + lineData.id], [{
                //             aliases: 'list'
                //         }]).load().done(store => {
                //             var data = store.data.list.data
                //             Widgets.dialog.html('收件人信息', `
                //                 <p>收件人：${data.addressee}</p>
                //                 <p>详细地址：${data.addressDetail}</p>
                //             `, {width: 200})
                //         }).fail();
                //     }
                // },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '活动id',
                    dataIndex: 'activityId',
                },
                {
                    header: '中奖记录id',
                    dataIndex: 'winPrizeId',
                },
                {
                    header: '用户id',
                    dataIndex: 'userId',
                },
                {
                    header: '用户昵称',
                    dataIndex: 'userNickName',
                },
                {
                    header: '中奖时间',
                    dataIndex: 'winPrizeTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                {
                    header: '奖品信息',
                    dataIndex: 'prizeName',
                },
                {
                    header: '信息提交时间',
                    dataIndex: 'createTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                // {
                //     header: '收件人',
                //     dataIndex: 'addressee',
                // },
                {
                    header: '手机号',
                    dataIndex: 'phone',
                    reportRender: function(data, allData, lineData) {
                        return lineData['phoneEncrypt']
                    }
                },
                // {
                //     header: '所在地区',
                //     dataIndex: 'adressArea',
                // },
                {
                    header: '省',
                    dataIndex: 'areaCode',
                    render: function(data, allData, lineData) {
                        var areaCode = lineData['areaCode']
                        if(Number(areaCode) > 0) {
                            var defaultData = {
                                provinceVal: {}
                            }
                            var areaData = District.getProAndCityByAreaCode(lineData['areaCode']) || defaultData
                            return areaData.provinceVal.name
                        }else {
                            return ''
                        }
                    },
                    reportRender: function(data, allData, lineData) {
                        var areaCode = lineData['areaCode']
                        if(Number(areaCode) > 0) {
                            var defaultData = {
                                provinceVal: {}
                            }
                            var areaData = District.getProAndCityByAreaCode(lineData['areaCode']) || defaultData
                            return areaData.provinceVal.name
                        }else {
                            return ''
                        }
                    }
                },
                {
                    header: '市',
                    dataIndex: 'areaCode',
                    render: function(data, allData, lineData) {
                        var areaCode = lineData['areaCode']
                        if(Number(areaCode) > 0) {
                            var defaultData = {
                                cityVal: {}
                            }
                            var areaData = District.getProAndCityByAreaCode(lineData['areaCode']) || defaultData
                            return areaData.cityVal.name
                        }else {
                            return ''
                        }
                    },
                    reportRender: function(data, allData, lineData) {
                        var areaCode = lineData['areaCode']
                        if(Number(areaCode) > 0) {
                            var defaultData = {
                                cityVal: {}
                            }
                            var areaData = District.getProAndCityByAreaCode(lineData['areaCode']) || defaultData
                            return areaData.cityVal.name
                        }else {
                            return ''
                        }
                    }
                },
                {
                    header: '区',
                    dataIndex: 'areaCode',
                    render: function(data, allData, lineData) {
                        var areaCode = lineData['areaCode']
                        if(Number(areaCode) > 0) {
                            var defaultData = {
                                areaVal: {}
                            }
                            var areaData = District.getProAndCityByAreaCode(lineData['areaCode']) || defaultData
                            return areaData.areaVal.name
                        }else {
                            return ''
                        }
                    },
                    reportRender: function(data, allData, lineData) {
                        var areaCode = lineData['areaCode']
                        if(Number(areaCode) > 0) {
                            var defaultData = {
                                areaVal: {}
                            }
                            var areaData = District.getProAndCityByAreaCode(lineData['areaCode']) || defaultData
                            return areaData.areaVal.name
                        }else {
                            return ''
                        }
                    }
                },
                {
                    header: '详细地址',
                    dataIndex: 'adressDetail',
                },
                {
                    header: '发货状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return expressStatusMap[data]
                    },
                },
                {
                    header: '快递单号',
                    dataIndex: 'expressNumber',
                },
                {
                    header: '发货时间',
                    dataIndex: 'deliveryTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                {
                    header: '备注',
                    dataIndex: 'remark',
                },
            ]
        }, ['jiakao-misc!surprise-bag/data/prizeAdressList'], panel, null).render();
    }

    return {
        list: list
    }

});
