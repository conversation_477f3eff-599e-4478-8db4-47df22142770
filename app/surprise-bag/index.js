/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var statusStore = [
        {
            key: 2,
            value: '下架'
        }, {
            key: 1,
            value: '上架'
        }, {
            key: 3,
            value: '测试发布'
        }
    ]
    var statusMap = Tools.getMapfromArray(statusStore);

    var activtyTypeStore = [
        {
            key: 1,
            value: '福袋活动'
        },
        {
            key: 2,
            value: '冲单促销活动'
        }
    ]
    var activtyTypeMap = Tools.getMapfromArray(activtyTypeStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '活动名称：',
                dataIndex: 'name',
                xtype: 'text',
                placeholder: '活动名称',
                check: 'required'
            },
            {
                header: '活动描述：',
                dataIndex: 'description',
                xtype: 'text',
                placeholder: '活动描述',
                check: 'required'
            },
            {
                header: '活动类型：',
                dataIndex: 'activityType',
                xtype: 'radio',
                check: 'required',
                store: activtyTypeStore
            },
            {
                header: '活动规则：',
                dataIndex: 'ruleId',
                xtype: 'select',
                store: 'jiakao-misc!surprise-bag/data/getActivityRule?status=2&limit=10000',
                insert: [
                    {
                        key: '', value: '请选择', ruleName: '请选择'
                    }
                ],
                index: {
                    key: 'id',
                    value: 'ruleName'
                },
            },
            {
                header: '活动扩展字段：',
                dataIndex: 'activityExt',
                xtype: 'text',
                placeholder: '活动扩展字段'
            },
            {
                header: '参与口令：',
                dataIndex: 'participateWord',
                xtype: 'text',
                placeholder: '参与口令',
                check: 'required'
            },
            {
                header: '活动开始时间：',
                dataIndex: 'beginTime',
                check: 'required',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '活动开始时间'
            },
            {
                header: '活动结束时间：',
                dataIndex: 'endTime',
                check: 'required',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '活动结束时间'
            },
            {
                dataIndex: 'kemu',
                xtype: 'hidden',
                value: 1
            },
            // {
            //     header: '科目：',
            //     check: 'required',
            //     dataIndex: 'kemu',
            //     xtype: Plugin('jiakao-misc!auto-prompt', {
            //         store: Constants.kemuStore,
            //         placeholder: '可多选，多选代表对多个科目同时生效',
            //         dataIndex: 'kemu',
            //         index: {
            //             key: 'key',
            //             value: 'value',
            //             search: 'key'
            //         },
            //         isMulti: true,
            //         defaultVal: false
            //     }, function (plugin, value) {
            //     })
            // },
            {
                header: '开奖等待时长：',
                dataIndex: 'waitTime',
                xtype: 'text',
                placeholder: '单位：秒',
                check: 'required'
            },
            {
                header: '发布状态：',
                dataIndex: 'status',
                xtype: 'radio',
                check: 'required',
                store: statusStore
            }
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!surprise-bag/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: function (table, dom, data) {
                function ren(activityType) {
                    if (activityType == 1) {
                        dom.item('participateWord').attr('disabled', false)
                        dom.item('waitTime').attr('disabled', false)
                        dom.item('participateWord-group').show();
                        dom.item('waitTime-group').show();
                    } else {
                        dom.item('participateWord').attr('disabled', true)
                        dom.item('waitTime').attr('disabled', true)
                        dom.item('participateWord-group').hide();
                        dom.item('waitTime-group').hide();
                    }
                }
                dom.item('activityType').on('change', function () {
                    let value = $(this).val();
                    ren(value)
                })
                if(data.data) {
                    ren(data.data.activityType)
                }
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '福袋抽奖活动管理',
            title: '福袋抽奖活动管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            line: {
                class: function (data, type) {
                    if (type == 'body') {
                        if (data.status === 3) {
                            return 'danger text-danger';
                        }
                    }
                }
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!surprise-bag/data/view',
                        save: 'jiakao-misc!surprise-bag/data/update'
                    },
                    renderAfter: function (table, dom, data) {
                        function ren(activityType) {
                            if (activityType == 1) {
                                dom.item('participateWord').attr('disabled', false)
                                dom.item('waitTime').attr('disabled', false)
                                dom.item('participateWord-group').show();
                                dom.item('waitTime-group').show();
                            } else {
                                dom.item('participateWord').attr('disabled', true)
                                dom.item('waitTime').attr('disabled', true)
                                dom.item('participateWord-group').hide();
                                dom.item('waitTime-group').hide();
                            }
                        }
                        dom.item('activityType').on('change', function () {
                            let value = $(this).val();
                            ren(value)
                        })
                        if(data.data) {
                            ren(data.data.activityType)
                        }
                    },
                    columns: columns()
                },
                {
                    name: '抽奖设置',
                    class: 'primary',
                    click: function(table, dom, lineData) {
                        Widgets.dialog.html('福袋活动抽奖设置', {
                            width: 800
                        }).done(function (dialog) {
                            require(['jiakao-misc!app/surprise-bag-active-prize/index'], function (Item) {
                                Item.list(dialog.body, lineData)
                            })
                        })
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!surprise-bag/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '活动名称',
                    dataIndex: 'name',
                },
                {
                    header: '活动描述',
                    dataIndex: 'description',
                },
                {
                    header: '活动类型',
                    dataIndex: 'activityType',
                    render: function(data){
                        return activtyTypeMap[data];
                    }
                },
                {
                    header: '活动扩展字段',
                    dataIndex: 'activityExt'
                },
                {
                    header: '参与口令',
                    dataIndex: 'participateWord',
                },
                {
                    header: '活动开始时间',
                    dataIndex: 'beginTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                {
                    header: '活动结束时间',
                    dataIndex: 'endTime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                },
                // {
                //     header: '科目',
                //     dataIndex: 'kemu',
                //     render: function (data) {
                //         var list = data.split(',')
                //         var kemu = list.map(item => {
                //             return Constants.kemuMap[item]
                //         })
                //         return kemu.join(',')
                //     },
                // },
                {
                    header: '开奖等待时长',
                    dataIndex: 'waitTime',
                },
                {
                    header: '参与人数',
                    dataIndex: 'joinCount',
                },
                {
                    header: '已中奖人数',
                    dataIndex: 'prizedQuota',
                },
                {
                    header: '发布状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data]
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!surprise-bag/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
