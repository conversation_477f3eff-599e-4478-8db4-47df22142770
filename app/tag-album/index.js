/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
'simple!core/plugin',
], function (Template, Table, Utils, Widgets, Store, Form,Plugin) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!tag-album/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form:{
                    submitHandler :function (form) {
                        var detail = $(form).find('[name="editorValue"]').val();
                        return {
                        detail
                    }
                }
            },
            columns: [
             {
                 header: '标签id：',
                 dataIndex: 'tagId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '标签id'
             },
             {
                 header: '专项标题：',
                 dataIndex: 'title',
                 xtype: 'text',
                 maxlength: 128,
                 placeholder: '专项标题'
             },
             {
                 header: '描述：',
                 dataIndex: 'description',
                 xtype: 'textarea',
                 maxlength: 512,
                 placeholder: '描述'
             },
             {
                 header: '详情：',
                 dataIndex: 'detail',
                 xtype: Plugin('jiakao-misc!rich-text', {
                    dataIndex: 'detail',
                    uploadIndex: 'detail',
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 300,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                        dataIndex: 'detail',
                        uploadIndex: 'detail',
                        name: 'detail'

                    }

                }, function () {
                })
             },
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '专项强化列表',
            title: '专项强化列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!tag-album/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '标签id：',
                         dataIndex: 'tagId'
                     },
                     {
                         header: '专项标题：',
                         dataIndex: 'title'
                     },
                     {
                         header: '描述：',
                         dataIndex: 'description'
                     },
                     {
                         header: '详情：',
                         dataIndex: 'detail'
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                 
                     {
                         header: '更新人',
                         dataIndex: 'updateUserName'
                     },
                     
                     {
                         header: '更新时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                   

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form:{
                        submitHandler :function (form) {
                            var detail = $(form).find('[name="editorValue"]').val();
                            return {
                                detail
                            }
                    }
                },
                    store: {
                        load: 'jiakao-misc!tag-album/data/view',
                        save: 'jiakao-misc!tag-album/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '标签id：',
                 dataIndex: 'tagId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '标签id'
             },
             {
                 header: '专项标题：',
                 dataIndex: 'title',
                 xtype: 'text',
                 maxlength: 128,
                 placeholder: '专项标题'
             },
             {
                 header: '描述：',
                 dataIndex: 'description',
                 xtype: 'textarea',
                 maxlength: 512,
                 placeholder: '描述'
             },
                    {
                        header: '详情：',
                        dataIndex: 'detail',
                        xtype: Plugin('jiakao-misc!rich-text', {
                            dataIndex: 'detail',
                            uploadIndex: 'detail',
                            bucket: "jiakao-web",
                            editorConfig: {
                                initialFrameWidth: "99.7%",
                                initialFrameHeight: 300,
                                autoClearinitialContent: false,
                                wordCount: false,
                                elementPathEnabled: false,
                                autoFloatEnabled: false,
                                dataIndex: 'detail',
                                uploadIndex: 'detail',
                                name: 'detail'
        
                            }
        
                        }, function () {
                        })
                    }]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!tag-album/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '标签id',
                         dataIndex: 'tagId'
                     },
                     {
                         header: '专项标题',
                         dataIndex: 'title'
                     },
                     {
                         header: '描述',
                         dataIndex: 'description',
                         render: function (data) {
                             return `<a>点击查看</a>`
                         },
                         click: function (table, row, lineData) {
                            Widgets.dialog.html('描述', {}).done(function (dialog) {
                                $(dialog.body).html('<div>' + lineData.description + '</div><br/>')
                            })
                         }
                     },
                     {
                         header: '详情',
                         dataIndex: 'detail',
                         render: function (data) {
                            return `<a>点击查看</a>`
                        },
                        click: function (table, row, lineData) {
                           Widgets.dialog.html('详情', {}).done(function (dialog) {
                               $(dialog.body).html('<div>' + lineData.detail + '</div><br/>')
                           })
                        }
                     },
                     {
                        header: '创建时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: '创建人',
                        dataIndex: 'createUserName'
                    },
                
                    {
                        header: '更新人',
                        dataIndex: 'updateUserName'
                    },
                    
                    {
                        header: '更新时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    },
            ]
        }, ['jiakao-misc!tag-album/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});