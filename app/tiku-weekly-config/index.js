/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/constants',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!plugin/select-district/district3',
    'jiakao-misc!app/operation-config3/index',
], function (Template, Table, Utils, Widgets, Store, Form, Constants, Plugin, TIKU, District3, OpConfig) {
    let imageDto = {

    }

    
    var statusMap = {
        0: '下线',
        1: '测试发布',
        2: '发布'
    }

    function renderCodeFn(dom,data) {
        setTimeout(() => {
            const value = JSON.parse(data.value);
            console.log(value,'valuevalue');
            dom.item('startTime').val(value.startTime);
            dom.item('weekNum').val(value.weekNum);
            dom.item('endTime').val(value.endTime);
            dom.item('jumpUrl').val(value.imageDto.jumpUrl);
            imageDto = value.imageDto;

            dom.item('examAvgScoreRankSubTitle').val(value.examAvgScoreRankSubTitle);
            dom.item('examAvgScoreRankTitle').val(value.examAvgScoreRankTitle);
            dom.item('practiceRankTitle').val(value.practiceRankTitle);

            dom.item('ke1QuestionNumRankList').val(JSON.stringify(value.ke1QuestionNumRankList));
            dom.item('ke4QuestionNumRankList').val(JSON.stringify(value.ke4QuestionNumRankList));
            dom.item('questionNumList').val(JSON.stringify(value.questionNumList));



            const examFailNumber = value.examFailNum.num;
            const examFailUnit = value.examFailNum.unit;

            dom.item('examFailNum').val(examFailNumber);
            dom.item('examFailUnit').val(examFailUnit);



            const examNumber = value.examNum.num;
            const examUnit = value.examNum.unit;

            dom.item('examNum').val(examNumber);
            dom.item('examUnit').val(examUnit);


            const examSuccessNumber = value.examSuccessNum.num;
            const examSuccessUnit = value.examSuccessNum.unit;

            dom.item('examSuccessNum').val(examSuccessNumber);
            dom.item('examSuccessUnit').val(examSuccessUnit);


            const practiceCorrectNumber = value.practiceCorrectNum.num;
            const practiceCorrectUnit = value.practiceCorrectNum.unit;

            dom.item('practiceCorrectNum').val(practiceCorrectNumber);
            dom.item('practiceCorrectUnit').val(practiceCorrectUnit);

            const practiceNumber = value.practiceNum.num;
            const practiceUnit = value.practiceNum.unit;

            dom.item('practiceNum').val(practiceNumber);
            dom.item('practiceUnit').val(practiceUnit);

            
            const practiceWrongNumber = value.practiceWrongNum.num;
            const practiceWrongUnit = value.practiceWrongNum.unit;

            dom.item('practiceWrongNum').val(practiceWrongNumber);
            dom.item('practiceWrongUnit').val(practiceWrongUnit);



            const practiceTime= value.practiceTime.num;
            const practiceTimeUnit = value.practiceTime.unit;

            dom.item('practiceTime').val(practiceTime);
            dom.item('practiceTimeUnit').val(practiceTimeUnit);

            
            


            Plugin('jiakao-misc!group2', {
                dataIndex: 'practiceRankList',
                target: dom.item('practiceRankList-group').find('div[class=col-sm-8]'),
                value:JSON.stringify(value.practiceRankList)
            }, function (plugin, value) {

            }).render();


            Plugin('jiakao-misc!group2', {
                dataIndex: 'ke1ExamAvgScoreRankList',
                target: dom.item('ke1ExamAvgScoreRankList-group').find('div[class=col-sm-8]'),
                value:JSON.stringify(value.ke1ExamAvgScoreRankList)
            }, function (plugin, value) {

            }).render();


            Plugin('jiakao-misc!group2', {
                dataIndex: 'ke4ExamAvgScoreRankList',
                target: dom.item('ke4ExamAvgScoreRankList-group').find('div[class=col-sm-8]'),
                value:JSON.stringify(value.ke4ExamAvgScoreRankList)
            }, function (plugin, value) {

            }).render();


            $('.close-icon').on('click', function () {
                imageDto = {}
            })

        }, 200)
    }


    var addEdit = function (table, lineData = {}) {
        var isEdit = !!lineData.id;

        var config = {
            title: isEdit ? '编辑' : '添加',
            width: 500,
            store: `jiakao-misc!operation-config/data/${isEdit ? 'update':'insert'}?bizType=tiku_week_report`,
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    const startTime = $(form).find('#startTime').val();
                    const endTime = $(form).find('#endTime').val();
                    const weekNum = $(form).find('#weekNum').val();

                    const practiceNumber = $(form).find('#practiceNum').val();
                    const practiceUnit = $(form).find('#practiceUnit').val();

                    const ke1QuestionNumRankList = $(form).find('#ke1QuestionNumRankList').val();
                    const ke4QuestionNumRankList = $(form).find('#ke4QuestionNumRankList').val();
                    const questionNumList = $(form).find('#questionNumList').val();

                    const jumpUrl = $(form).find('#jumpUrl').val();
                    imageDto.jumpUrl = jumpUrl;

                    /**周练习次数 */
                    const practiceNum = {
                        num: practiceNumber,
                        unit:practiceUnit
                    }


                    const practiceWrongNumber = $(form).find('#practiceWrongNum').val();
                    const practiceWrongUnit = $(form).find('#practiceWrongUnit').val();

                    /**周练习（答错）次数 */
                    const practiceWrongNum = {
                        num: practiceWrongNumber,
                        unit:practiceWrongUnit
                    }


                    const practiceCorrectNumber = $(form).find('#practiceCorrectNum').val();
                    const practiceCorrectUnit = $(form).find('#practiceCorrectUnit').val();
                    
                    /**周练习 （答对） 次数 */
                    const practiceCorrectNum = {
                        num: practiceCorrectNumber,
                        unit:practiceCorrectUnit
                    }

                    const examNumber = $(form).find('#examNum').val();
                    const examUnit = $(form).find('#examUnit').val();

                    /**周考试次数 */
                    const examNum = {
                        num: examNumber,
                        unit:examUnit
                    }

                    const examFailNumber = $(form).find('#examFailNum').val();
                    const examFailUnit = $(form).find('#examFailUnit').val();

                    /**周考试（失败）次数 */
                    const examFailNum = {
                        num: examFailNumber,
                        unit:examFailUnit
                    }

                    const examSuccessNumber = $(form).find('#examSuccessNum').val();
                    const examSuccessUnit = $(form).find('#examSuccessUnit').val();

                    /**周考试（答对）次数 */
                    const examSuccessNum = {
                        num: examSuccessNumber,
                        unit:examSuccessUnit
                    }

                    
                    const practiceTimeNumber = $(form).find('#practiceTime').val();
                    const practiceTimeUnit = $(form).find('#practiceTimeUnit').val();

                    /**周练习时长 */
                    const practiceTime = {
                        num: practiceTimeNumber,
                        unit:practiceTimeUnit
                    }
                 
                    
                    /**人均练习时长标题 */
                    const practiceRankTitle = $(form).find('#practiceRankTitle').val();

                    /**练习时长排行榜 */
                    var practiceRankList= $('input[name=practiceRankList]').val();
                    practiceRankList = practiceRankList && JSON.parse(practiceRankList)
                    if (practiceRankList.length != 5) {
                        Widgets.dialog.alert('练习时长排行榜请上传5条数据')
                        return;
                    }



                    /**考试模拟平均分 主标题 */
                    const examAvgScoreRankTitle = $(form).find('#examAvgScoreRankTitle').val();

                    /**考试模拟平均分 副标题 */
                    const examAvgScoreRankSubTitle = $(form).find('#examAvgScoreRankSubTitle').val();
            

                    /**科一考试平均分排行榜 */
                    let ke1ExamAvgScoreRankList = $('input[name=ke1ExamAvgScoreRankList]').val();
                    ke1ExamAvgScoreRankList = ke1ExamAvgScoreRankList && JSON.parse(ke1ExamAvgScoreRankList);
                    if (ke1ExamAvgScoreRankList.length != 5) {
                        Widgets.dialog.alert('科一考试平均分排行榜请上传5条数据')
                        return;
                    }


                    /**科四考试平均分排行榜 */
                    let ke4ExamAvgScoreRankList = $('input[name=ke4ExamAvgScoreRankList]').val();
                    ke4ExamAvgScoreRankList = ke4ExamAvgScoreRankList && JSON.parse(ke4ExamAvgScoreRankList);

                    if (ke4ExamAvgScoreRankList.length != 5) {
                        Widgets.dialog.alert('科四考试平均分排行榜请上传5条数据')
                        return;
                    }


                    const value = JSON.stringify({
                        startTime,
                        endTime,
                        weekNum,
                        imageDto,
                        practiceNum,
                        practiceWrongNum,
                        practiceCorrectNum,
                        examNum,
                        examSuccessNum,
                        examFailNum,
                        practiceTime,

                        practiceRankTitle,
                        practiceRankList,
                        examAvgScoreRankTitle,
                        examAvgScoreRankSubTitle,

                        ke1ExamAvgScoreRankList,
                        ke4ExamAvgScoreRankList,

                        ke1QuestionNumRankList:ke1QuestionNumRankList && JSON.parse(ke1QuestionNumRankList),
                        ke4QuestionNumRankList:ke4QuestionNumRankList && JSON.parse(ke4QuestionNumRankList),
                        questionNumList:questionNumList && JSON.parse(questionNumList) 
                    })

                    return {
                        value
                    };
                },
            },
            renderAfter: function (table, dom, data) {
                if (data.data) {
                    renderCodeFn(dom,data.data)
                }
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden'
                },

                {
                    dataIndex: 'ke1QuestionNumRankList',
                    header: '科一排名',
                    xtype: 'textarea',
                    disabled:!isEdit,
                },

                {
                    dataIndex: 'ke4QuestionNumRankList',
                    xtype: 'textarea',
                    header: '科四排名',
                    disabled:!isEdit,
                },

                {
                    dataIndex: 'questionNumList',
                    xtype: 'textarea',
                    disabled:!isEdit,
                    header:'试题数量'
                },

                {
                    header: '名称',
                    dataIndex: 'name',
                    xtype:'text'
                },
                {
                    header: '配置Code：',
                    dataIndex: 'code',
                    xtype: 'text',
                    placeholder:'格式：年份-期数（例：2023-1）'
                },
                {
                    header: '配置说明：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 128,
                    placeholder: '配置说明'
                },
                {
                    header: '开始时间：',
                    dataIndex: 'startTime',
                    xtype: 'date',
                    placeholder: '开始时间'
                },
                {
                    header: '结束时间：',
                    dataIndex: 'endTime',
                    xtype: 'date',
                    placeholder: '结束时间'
                },
                {
                    header: '周期：',
                    dataIndex: 'weekNum',
                    xtype: 'text',
                },
                {
                    header: '本周大事件图片：',
                    dataIndex: 'imgUrl',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'imgUrl',
                        uploadIndex: 'imgUrl',
                        bucket: "exam-room",
                        valuePath:'value.imageDto.imgUrl',
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function (me, b, imageUrl, wh) {
                        const w = wh.split('x')[0]
                        const h = wh.split('x')[1]
                        
                        imageDto.imgUrl = imageUrl,
                        imageDto.width = w,
                        imageDto.height =h
                      
                        console.log(arguments)
                    })
                },
                {
                    header: '本周大事件跳转链接：',
                    dataIndex: 'jumpUrl',
                    xtype: 'text',
                    placeholder: '本周大事件跳转链接'
                },
                {
                    header: "周练习次数：",
                    xtype: 'text',
                    dataIndex:'practiceNum',
                    placeholder:'周练习次数'
                },
                {
                    header: "周练习次数(单位)：",
                    xtype: 'text',
                    dataIndex:'practiceUnit',
                    placeholder:'周练习次数(单位)'
                },
                {
                    header: '周练习次数答错',
                    dataIndex: 'practiceWrongNum',
                    xtype: 'text',
                    placeholder:'周练习次数答错'
                },
                {
                    header: '周练习次数答错(单位)',
                    placeholder: '周练习次数答错(单位)',
                    dataIndex: 'practiceWrongUnit',
                    xtype: 'text',
                },
                {
                    header: '周练习次数答对',
                    placeholder: '周练习次数答对',
                    dataIndex: 'practiceCorrectNum',
                    xtype: 'text',
                },
                {
                    header: '周练习次数答对(单位)',
                    placeholder: '周练习次数答对(单位)',
                    dataIndex: 'practiceCorrectUnit',
                    xtype: 'text',
                },
                {
                    header: "周考试次数：",
                    xtype: 'text',
                    dataIndex:'examNum',
                    placeholder:'周考试次数'
                },
                {
                    header: "周考试次数（单位）：",
                    xtype: 'text',
                    dataIndex:'examUnit',
                    placeholder:'周考试次数（单位）'
                },
                
                {
                    header: '周考试失败次数',
                    xtype: 'text',
                    dataIndex: 'examFailNum',
                    placeholder:'周考试失败次数'
                },
                {
                    header: '周考试失败次数(单位)',
                    xtype: 'text',
                    dataIndex: 'examFailUnit',
                    placeholder:'周考试失败次数(单位)'
                },
                {
                    header: '周考试通过次数',
                    xtype: 'text',
                    dataIndex: 'examSuccessNum',
                    placeholder:'周考试通过次数'
                },
                {
                    header: '周考试通过次数(单位)',
                    xtype: 'text',
                    dataIndex: 'examSuccessUnit',
                    placeholder:'周考试通过次数(单位)'
                },
                {
                    header: '周练习时长',
                    xtype: 'text',
                    dataIndex: 'practiceTime',
                    placeholder:'周练习时长'
                },
                {
                    header: '周练习时长(单位)',
                    xtype: 'text',
                    dataIndex: 'practiceTimeUnit',
                    placeholder:'周练习时长(单位)'
                },
                
                {
                    header: '练习时长排行榜标题：',
                    dataIndex: 'practiceRankTitle',
                    xtype: 'text',
                    
                    placeholder: '练习时长排行榜标题'
                },
            
                {
                    header: '练习时长排行榜：',
                    dataIndex: 'practiceRankList',
                    xtype: Plugin('jiakao-misc!group2', {
                        dataIndex: 'practiceRankList'
                    }, function (plugin, value) {
        
                    })
                },

            
                {
                    header: '模拟考试平均分排行榜主标题：',
                    dataIndex: 'examAvgScoreRankTitle',
                    xtype: 'text',
                    placeholder: '模拟考试平均分排行榜主标题'
                },
                
                {
                    header: '模拟考试平均分排行榜副标题：',
                    dataIndex: 'examAvgScoreRankSubTitle',
                    xtype: 'text',
                    placeholder: '模拟考试平均分排行榜副标题'
                },
                
                {
                    header: '科一考试平均分排行榜',
                    dataIndex: 'ke1ExamAvgScoreRankList',
                    xtype: Plugin('jiakao-misc!group2', {
                        dataIndex: 'ke1ExamAvgScoreRankList'
                    }, function (plugin, value) {
        
                    })
                },
            
                {
                    header: '科四考试平均分排行榜',
                    dataIndex: 'ke4ExamAvgScoreRankList',
                    xtype: Plugin('jiakao-misc!group2', {
                        dataIndex: 'ke4ExamAvgScoreRankList'
                    }, function (plugin, value) {
        
                    })
                },
            ]
        }

        if (isEdit) {
            Table().edit(lineData, config);
        } else {
            Table(config).add();
        }
    }

    var list = function (panel) {
        Table({
            description: '题库周报配置',
            title: '题库周报配置',
            search: [],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: function (table) {
                            addEdit(table)
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    class:'warning',
                    click: function (table, dom, lineData) {
                        addEdit(table, lineData)
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!operation-config/data/delete'
                },
                {
                    name: '用户画像',
                    class: 'success',
                    click: function (table, dom, lineData) {
                        OpConfig.editPersonas(table, lineData)
                    },
                },
                {
                    class: 'danger',
                    render: function (name, arr, index) {
                        const status = arr[index].status
                        if (status == 0) {
                            return '测试发布';
                        } else if (status == 1) {
                            return '发布';
                        }else if (status == 2) {
                            return '下线';
                        }
                    },
                    click: function (table, row, lineData) {
                        console.log(lineData, 'lineData');
                        const status = lineData.status + 1 > 2 ? 0 : lineData.status + 1;
                        console.log(status, 'status');
                        let title = lineData.status == 1 ? '确定发布吗?' :   lineData.status == 2 ? '确定下线吗?' : '确定测试发布吗?'
                        Widgets.dialog.confirm(title, function (e, confirm) {
                            if (confirm) {
                                Store(['jiakao-misc!operation-config/data/update']).save([{
                                    params: {
                                        id: lineData.id,
                                        status
                                    }
                                }]).done(function () {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message);
                                })
                            }
                        })
                    }
                },
            ],
            columns: [
                    {
                        header: '#',
                        dataIndex: 'id',
                        width: 20
                    },
                    {
                        header: '名称',
                        dataIndex: 'name'
                    },
                    {
                        header: '配置说明',
                        dataIndex: 'remark'
                    },
                    {
                        header: '配置key',
                        dataIndex: 'code'
                    },
                    {
                        header: '配置内容',
                        dataIndex: 'value',
                        render: function () {
                            return `<a>点击查看</a>`
                        },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('配置内容', {}).done(function (dialog) {
                                var data = lineData.value && JSON.stringify(JSON.parse(lineData.value), null, 4)
                                $(dialog.body).html('<pre style="max-height: 200px; overflow: auto">' + data + '</pre>')
                            })
                        }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data, arr, i) {
                        return statusMap[data]
                    }
                },
                    {
                        header: '创建人',
                        dataIndex: 'createUserName'
                    },
                    {
                        header: '创建时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'createTime'
                    },
                    {
                        header: '修改人',
                        dataIndex: 'updateUserName'
                    },
                    {
                        header: '修改时间',
                        render: function (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        },
                        dataIndex: 'updateTime'
                    }

            ]
        }, ['jiakao-misc!operation-config/data/list?bizType=tiku_week_report'], panel, null).render();
    }

    return {
        list: list
    }

});