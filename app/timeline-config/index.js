/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var parentConfigId;
    var columns = [
        {
            header: '随堂练习试题ID：',
            dataIndex: 'practiceIds',
            xtype: 'text',
            placeholder: '依次用英文逗号隔开，用于随堂练习'
        },
        {
            header: '关联知识点ID：',
            dataIndex: 'knowledge',
            xtype: 'text',
            placeholder: '依次用英文逗号隔开，用于课后作业'
        },
        {
            header: '练习时间：',
            dataIndex: 'practiceTime',
            xtype: 'text',
            placeholder: '事件触发时的视频进度，单位s'
        },
        {
            header: '关键点封面:',
            dataIndex: 'coverImage',
            xtype: 'select',
            store: 'jiakao-misc!timeline-config/data/timelineResType?timelineResType=real_scene_project_video',
            index: {
                key: 'coverImage',
                value: 'coverName',
            },

        },

    ]
    var columnsForAdd = [].concat(columns)
    var columnsForEdit = [].concat(columns)
    columnsForAdd.unshift({
        header: '视频类型：',
        dataIndex: 'type',
        xtype: function () {
            return '<div data-item="type"></div>'
        },
        check: 'required',
        placeholder: '视频类型'
    })
    columnsForEdit.unshift({
        header: '1：',
        dataIndex: 'type',
        xtype: 'hidden',
        disabled: 'disabled'
    })
    var submitHandler = function (form) {
        var type = $(form).find('[name="type"]').val();
        var knowledge = $(form).find('[name="knowledge"]').val();
        var practiceIds = $(form).find('[name="practiceIds"]').val();
        var practiceTime = $(form).find('[name="practiceTime"]').val();
        var coverImage = form.coverImage && form.coverImage.value
        let dataParams = {
            knowledge,
            practiceIds,
            practiceTime,
        }
        if (type === 'real_scene_project_video') {
            dataParams.coverImage = coverImage
        }
        return {
            data: JSON.stringify(dataParams)
        };
    }
    var showCoverImage = function (dom, show) {
        if (show) {
            console.log(dom.item('coverImage-group')[0])
            dom.item('coverImage-group').css('display', 'block')
        } else {
            dom.item('coverImage-group').css('display', 'none')
        }
    }
    var resTypeMap = {
        'top-lesson-practice': ['knowledge', 'practiceIds', 'practiceTime']
    }
    var addItemFromTpl = function (table, line, data) {
        var keypointId = data.keypointId
        console.log(data.hasResource)
        if (data.typeCode === 'p-timeline') {
            let promise;
            promise = new Promise((resolve) => {
                Store(['jiakao-misc!timeline-config/data/getResData?id=' + data.id]).load().done(function (store, data) {
                    var lineData = data['timeline-config'].data.getResData.data
                    var resId = lineData.id;
                    var resData = JSON.parse(lineData.data || '{}')
                    resolve({ ...resData, resId });
                }).fail(function (ret) {
                    resolve();
                });
            });
            promise.then((params = {}) => {
                Table().edit({
                    itemId: params.itemId || ''
                }, {
                    title: params.resId ? '修改' : '新增',
                    width: 500,
                    store: 'jiakao-misc!timeline-config/data/' + (params.resId ? 'updateRes?resId=' + params.resId : 'addRes?keypointId=' + data.keypointId + '&type=route_video_practice'),
                    success: function (obj, dialog) {
                        dialog.close();
                        table.render();
                    },
                    form: {
                        submitHandler: function (form) {
                            var id = $(form).find('[name="itemId"]').val();
                            return {
                                data: JSON.stringify({
                                    itemId: id
                                })
                            };
                        }
                    },
                    columns: [
                        {
                            header: '路考仪项目ID：',
                            dataIndex: 'itemId',
                            xtype: 'select',
                            store: 'jiakao-misc!timeline-config/data/lkyTypeList',
                            placeholder: '路考仪项目ID'
                        },
                    ]
                });
            })
        } else if (data.hasResource) {
            Store(['jiakao-misc!timeline-config/data/getResData?id=' + data.id]).load().done(function (store, data) {
                var lineData = data['timeline-config'].data.getResData.data
                var resId = lineData.id;
                var resData = JSON.parse(lineData.data)
                Table().edit({
                    type: lineData.type,
                    knowledge: resData.knowledge,
                    practiceIds: resData.practiceIds,
                    practiceTime: resData.practiceTime,
                    coverImage: resData.coverImage
                }, {
                    title: '修改',
                    width: 500,
                    store: 'jiakao-misc!timeline-config/data/updateRes?resId=' + resId,
                    success: function (obj, dialog) {
                        dialog.close();
                        table.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    renderAfter(table, dom) {
                        if (lineData.type === 'real_scene_project_video') {
                            showCoverImage(dom, true)
                        } else {
                            showCoverImage(dom, false)
                        }
                    },
                    columns: columnsForEdit
                });
            }).fail(function (ret) {
                Widgets.dialog.alert(ret.message)
            });
        } else {
            Table({
                title: '新增',
                width: 500,
                store: 'jiakao-misc!timeline-config/data/addRes?keypointId=' + keypointId,
                success: function (obj, dialog) {
                    dialog.close();
                    table.render();
                },
                renderAfter(table, dom) {
                    function onChange() {
                        Plugin('jiakao-misc!auto-prompt', {
                            target: dom.item("type"),
                            store: 'jiakao-misc!timeline-res/data/bizTypeList',
                            placeholder: '请选择事件类型',
                            dataIndex: 'type',
                            index: {
                                key: 'value',
                                value: 'key',
                                search: 'key'
                            },
                            isMulti: false,
                            defaultVal: false
                        }, function (plugin, value) {
                            console.log(value)
                            console.log(value === 'real_scene_project_video')
                            if (value === 'real_scene_project_video') {
                                showCoverImage(dom, true)
                            } else {
                                showCoverImage(dom, false)
                            }
                        }).render()
                    }
                    onChange();
                    showCoverImage(dom, false)

                },
                form: {
                    submitHandler: submitHandler
                },
                columns: columnsForAdd
            }).add();
        }
    }
    var add = function (table) {
        if (parentConfigId) {
            Table({
                title: '添加',
                width: 500,
                store: 'jiakao-misc!top-lesson-item/data/updateResource?type=top-lesson&configId=' + parentConfigId,
                success: function (obj, dialog) {
                    dialog.close();
                    table.render();
                },
                renderAfter(table, dom) {
                    Plugin('jiakao-misc!auto-prompt', {
                        target: dom.item("eventType"),
                        store: 'jiakao-misc!timeline-res/data/bizTypeList',
                        placeholder: '请选择事件类型',
                        dataIndex: 'eventType',
                        index: {
                            key: 'value',
                            value: 'key',
                            search: 'key'
                        },
                        isMulti: false,
                        defaultVal: false
                    }, function (plugin, value) {
                    }).render()
                },
                form: {
                    submitHandler: function (form) {
                        var practiceIds = $(form).find('#practiceIds').val();
                        var knowledge = $(form).find('#knowledge').val();
                        var practiceTime = $(form).find('#practiceTime').val();
                        var data = { practiceIds, knowledge, practiceTime }
                        return {
                            data: JSON.stringify(data)
                        };
                    },
                },
                columns: [
                    {
                        header: '片段起始时间：',
                        dataIndex: 'beginTime',
                        xtype: 'text',
                        check: 'required',
                        placeholder: '片段开始时对应的视频进度，单位s'
                    },
                    {
                        header: '片段结束时间：',
                        dataIndex: 'endTime',
                        xtype: 'text',
                        check: 'required',
                        placeholder: '片段结束时对应的视频进度，单位s'
                    },
                    {
                        header: '关键点名称：',
                        dataIndex: 'title',
                        xtype: 'text',
                        placeholder: '关键点名称'
                    },
                    {
                        header: '关键点描述：',
                        dataIndex: 'desc',
                        xtype: 'text',
                        placeholder: '关键点描述'
                    },


                    {
                        header: '事件类型：',
                        dataIndex: 'type',
                        xtype: function () {
                            return '<div data-item="eventType"></div>'
                        },
                        check: 'required',
                        placeholder: '事件类型'
                    },
                    {
                        header: '随堂练习试题ID：',
                        dataIndex: 'practiceIds',
                        xtype: 'text',
                        placeholder: '依次用英文逗号隔开，用于随堂练习'
                    },
                    {
                        header: '关联知识点ID：',
                        dataIndex: 'knowledge',
                        xtype: 'text',
                        placeholder: '依次用英文逗号隔开，用于课后作业'
                    },
                    {
                        header: '练习时间：',
                        dataIndex: 'practiceTime',
                        xtype: 'text',
                        placeholder: '事件触发时的视频进度，单位s'
                    },
                ]
            }).add();
        } else {
            Table({
                title: '添加',
                width: 500,
                store: 'jiakao-misc!timeline-config/data/insert',
                success: function (obj, dialog) {
                    dialog.close();
                    table.render();
                },
                renderAfter(table, dom) {
                    function onChange() {
                        Plugin('jiakao-misc!auto-prompt', {
                            target: dom.item("type"),
                            store: 'jiakao-misc!timeline-config/data/bizTypeList',
                            placeholder: '请选择视频类型',
                            dataIndex: 'type',
                            index: {
                                key: 'value',
                                value: 'key',
                                search: 'key'
                            },
                            isMulti: false,
                            defaultVal: false
                        }, function (plugin, value) {
                        }).render()
                    }

                    onChange();
                },
                columns: [
                    {
                        header: '视频类型：',
                        dataIndex: 'type',
                        xtype: function () {
                            return '<div data-item="type"></div>'
                        },
                        check: 'required',
                        placeholder: '视频类型'
                    },
                    {
                        header: '视频id：',
                        dataIndex: 'configId',
                        xtype: 'text',
                        check: 'required',
                        placeholder: '视频id'
                    },
                    {
                        header: '片段起始时间：',
                        dataIndex: 'beginTime',
                        xtype: 'text',
                        check: 'required',
                        placeholder: '片段开始时对应的视频进度，单位s'
                    },
                    {
                        header: '片段结束时间：',
                        dataIndex: 'endTime',
                        xtype: 'text',
                        check: 'required',
                        placeholder: '片段结束时对应的视频进度，单位s'
                    },
                    {
                        header: '关键点名称：',
                        dataIndex: 'title',
                        xtype: 'text',
                        placeholder: '关键点名称'
                    },
                    {
                        header: '关键点描述：',
                        dataIndex: 'desc',
                        xtype: 'text',
                        placeholder: '关键点描述'
                    },

                ]
            }).add();
        }
    }

    var initList = function (panel, configID) {
        list(panel, configID)
    }

    var list = function (panel) {
        parentConfigId = !isNaN(arguments[1]) ? arguments[1] : '';
        console.log();
        Table({
            description: '时间轴业务配置列表',
            title: '时间轴业务配置列表',
            search: [
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district2', {

                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    xtype: 'select',
                    store: 'jiakao-misc!timeline-config/data/bizTypeList',
                    index: {
                        key: 'value',
                        value: 'key',
                        search: 'key'
                    },
                    placeholder: '视频类型',
                    dataIndex: 'type',
                }, {
                    xtype: 'select',
                    dataIndex: 'deleted',
                    store: [{
                        key: '',
                        value: '全部状态'
                    }, {
                        key: true,
                        value: '是'
                    }, {
                        key: false,
                        value: '否'
                    }]
                },
                {
                    xtype: 'text',
                    placeholder: '视频标题',
                    dataIndex: 'configName'
                },
                {
                    xtype: 'text',
                    placeholder: '视频id',
                    dataIndex: 'configId',
                    value: parentConfigId || '',
                    disabled: !!parentConfigId,
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    },
                    {
                        name: '导入文件',
                        class: 'info',
                        click: function (table) {
                            Table({
                                title: '导入',
                                width: 500,
                                store: 'jiakao-misc!timeline-config/data/importExcel',
                                success: function (obj, dialog) {
                                    dialog.close();
                                    table.render();
                                },
                                columns: [{
                                    header: '选择文件：',
                                    dataIndex: 'data',
                                    xtype: 'file'
                                },
                                {
                                    header: '数据模板：',
                                    dataIndex: 'templates',
                                    xtype: function () {
                                        let url = window.j.root['jiakao-misc'] + '/kemu2-video-template.xlsx'
                                        return `<a class="btn btn-info btn-sm" href="${url}" target="_blank" download="时间轴业务配置模板">点击下载模板</a>`
                                    }
                                }
                                ]
                            }).add();
                        }
                    },
                    {
                        name: '批量管理',
                        class: 'primary',
                        click: function (table, dom, config, selectedArr) {
                            if (selectedArr.length > 0) {
                                Widgets.dialog.confirm('确定要删除这些关键点吗？', function (ev, status) {
                                    if (status) {
                                        Store(['jiakao-misc!timeline-config/data/batchDelete'], [{
                                            aliases: 'list',
                                            params: {
                                                publish: false,
                                                ids: selectedArr.join(',')
                                            }
                                        }]).save().done(function (store) {
                                            table.render();
                                        }).fail(function () {
                                        });
                                    }
                                })
                            } else {
                                Widgets.dialog.alert("请选择数据")
                            }
                        }
                    },
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!timeline-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '视频id：',
                            dataIndex: 'configId'
                        },
                        {
                            header: '视频标题：',
                            dataIndex: 'configName'
                        },
                        {
                            header: '视频类型：',
                            dataIndex: 'type'
                        },
                        {
                            header: '开始时间：',
                            dataIndex: 'beginTime'
                        },
                        {
                            header: '结束时间：',
                            dataIndex: 'endTime'
                        },
                        {
                            header: '关键点名称：',
                            dataIndex: 'title'
                        },
                        {
                            header: '关键点描述：',
                            dataIndex: 'desc'
                        },
                        {
                            header: '是否包含资源：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'hasResource'
                        },
                        {
                            header: 'deleted：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'deleted'
                        }
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!timeline-config/data/view',
                        save: 'jiakao-misc!timeline-config/data/updateKeypoint'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '开始时间：',
                            dataIndex: 'beginTime',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '开始时间'
                        },
                        {
                            header: '结束时间：',
                            dataIndex: 'endTime',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '结束时间'
                        },
                        {
                            header: '关键点名称：',
                            dataIndex: 'title',
                            xtype: 'text',
                            placeholder: '关键点名称'
                        },
                        {
                            header: '关键点描述：',
                            dataIndex: 'desc',
                            xtype: 'text',
                            placeholder: '关键点描述'
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!timeline-config/data/delete'
                },
                {
                    name: '事件管理',
                    class: 'primary',
                    click: addItemFromTpl
                }
            ],
            selector: {
                dataIndex: 'id',
            },
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '视频类型',
                    dataIndex: 'type'
                },
                {
                    header: '视频标题',
                    dataIndex: 'configName'
                },
                {
                    header: '视频id',
                    dataIndex: 'configId'
                },
                {
                    header: '开始时间',
                    dataIndex: 'beginTime'
                },
                {
                    header: '结束时间',
                    dataIndex: 'endTime'
                },
                {
                    header: '关键点名称',
                    dataIndex: 'title'
                },
                {
                    header: '关键点描述',
                    dataIndex: 'desc'
                },
                {
                    header: '事件',
                    dataIndex: 'resourceData',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }

                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html('事件', '<pre>' + JSON.stringify(JSON.parse(lineData.resourceData || "{}"), null, 4) + '</pre>');
                    }
                },
                {
                    header: '是否包含资源',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'hasResource'
                },
                {
                    header: 'deleted',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'deleted'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    order: 'desc',
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!timeline-config/data/list?configId=' + parentConfigId], panel, null).render();
    }

    return {
        list: list,
        initList: initList
    }

});