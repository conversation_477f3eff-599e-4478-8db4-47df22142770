/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!timeline-res-rel/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '关键点id：',
                    dataIndex: 'keyPointId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '关键点id'
                },
                {
                    header: '资源的id：',
                    dataIndex: 'resId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '资源的id'
                }
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: 'timeline-res-rel列表',
            title: 'timeline-res-rel列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!timeline-res-rel/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '关键点id：',
                            dataIndex: 'keyPointId'
                        },
                        {
                            header: '资源的id：',
                            dataIndex: 'resId'
                        }
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!timeline-res-rel/data/view',
                        save: 'jiakao-misc!timeline-res-rel/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '关键点id：',
                            dataIndex: 'keyPointId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '关键点id'
                        },
                        {
                            header: '资源的id：',
                            dataIndex: 'resId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '资源的id'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!timeline-res-rel/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '关键点id',
                    dataIndex: 'keyPointId'
                },
                {
                    header: '资源的id',
                    dataIndex: 'resId'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!timeline-res-rel/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});