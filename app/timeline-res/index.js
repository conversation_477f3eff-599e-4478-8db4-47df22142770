/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!timeline-res/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '资源类型：',
                    dataIndex: 'type',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '资源类型'
                },
                {
                    header: '资源的具体内容：',
                    dataIndex: 'data',
                    xtype: 'text',
                    maxlength: 128,
                    check: 'required',
                    placeholder: '资源的具体内容'
                },
                {
                    header: 'deleted：',
                    dataIndex: 'deleted',
                    xtype: 'radio',
                    store: [
                        {
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                }
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '时间轴关键点的资源信息，比如知识点id，试题id等。列表',
            title: '时间轴关键点的资源信息，比如知识点id，试题id等。列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!timeline-res/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '资源类型：',
                            dataIndex: 'type'
                        },
                        {
                            header: '资源的具体内容：',
                            dataIndex: 'data'
                        },
                        {
                            header: 'deleted：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'deleted'
                        }
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!timeline-res/data/view',
                        save: 'jiakao-misc!timeline-res/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '资源类型：',
                            dataIndex: 'type',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '资源类型'
                        },
                        {
                            header: '资源的具体内容：',
                            dataIndex: 'data',
                            xtype: 'text',
                            maxlength: 128,
                            check: 'required',
                            placeholder: '资源的具体内容'
                        },
                        {
                            header: 'deleted：',
                            dataIndex: 'deleted',
                            xtype: 'radio',
                            store: [
                                {
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!timeline-res/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '资源类型',
                    dataIndex: 'type'
                },
                {
                    header: '资源的具体内容',
                    dataIndex: 'data'
                },
                {
                    header: 'deleted',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'deleted'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!timeline-res/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});