/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var typeStore = [
        {
            key: 'normal',
            value: '普通跳转'
        }, {
            key: 'vip',
            value: 'vip售卖'
        }, {
            key: 'group',
            value: '拼单商品'
        }, {
            key: 'lesson',
            value: '课程售卖'
        }
    ]
    var typeMap = Tools.getMapfromArray(typeStore);

    var enableStore = [
        {
            key: 0,
            value: '停用'
        }, {
            key: 1,
            value: '启用'
        }
    ]
    var enableMap = Tools.getMapfromArray(enableStore);

    var statusStore = [
        {
            key: '0',
            value: '未发布'
        }, {
            key: '1',
            value: '测试发布'
        }, {
            key: '2',
            value: '正式发布'
        }
    ]
    var statusMap = Tools.getMapfromArray(statusStore);

    var operationStore = [
        {
            key: 1,
            value: '常规运营位'
        },
        {
            key: 2,
            value: '拼团运营位'
        }
    ]
    var operationMap = Tools.getMapfromArray(operationStore);

    var lessonItemId = 0;

    var submitHandler = function (form) {
        var introduction = $(form).find('[name="editorValue"]').val();
        var operationPositionType = $(form).find('[name="operationPositionType"]').val();
        var operationPositionId = $(form).find('[name="operationPositionId"]').val();
        if (operationPositionType == 2 && operationPositionId == '') {
            Simple.Dialog.toast('请填写拼团活动模板ID');
            return false
        }
        return {
            introduction: introduction
        };
    }

    var applyToItem1 = function(table, line, data) {
        var lineData = data
        Widgets.dialog.html('选择子课程', {
            width: 800
        }).done(function (dialog) {
            Table({
                title: '',
                search: [
                    {
                        placeholder: '子课程ID',
                        dataIndex: 'id',
                        xtype: 'text'
                    },
                    {
                        placeholder: '标题',
                        dataIndex: 'title',
                        xtype: 'text'
                    },
                    {
                        dataIndex: 'kemu',
                        xtype: 'select',
                        store: Constants.kemuStore
                    },
                ],
                selector: {
                    dataIndex: 'id',
                },
                buttons: {
                    top: [
                        {
                            name: '一键应用',
                            class: 'danger',
                            click: function (table, dom, config, selectedArr) {
                                if (selectedArr.length > 0) {
                                    sendApplyToLesson(table, lineData, selectedArr.join(','), function() {
                                        dialog.close()
                                    })
                                } else {
                                    Widgets.dialog.alert("请选择数据")
                                }
                            }
                        }
                    ]
                },
                columns: [{
                    header: '子课程ID',
                    dataIndex: 'id',
                },
                {
                    header: '子课程标题',
                    dataIndex: 'title',
                },
                {
                    header: '讲师名称',
                    dataIndex: 'teacherName'
                }
                ],
            }, ['jiakao-misc!top-lesson-item/data/list?type=2'], dialog.body, function () { }).render();
        });
    }
    var applyToItem2 = function(table, line, data) {
        var lineData = data
        sendApplyToLesson(table, lineData)
    }
    var sendApplyToLesson = function(table, lineData, dataList, callback) {
        Table({
            title: '应用模板',
            width: 300,
            store: 'jiakao-misc!top-lesson-advert-tpl/data/applyToLesson',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
                callback && callback()
            },
            columns: [{
                header: 'id：',
                dataIndex: 'templateId',
                xtype: 'hidden',
                value: lineData.id,
            },{
                header: '全部：',
                dataIndex: 'applyToAllLesson',
                xtype: 'hidden',
                value: dataList ? 'false' : 'true',
            },{
                header: 'ids：',
                dataIndex: 'lessonIds',
                xtype: 'hidden',
                value: dataList,
            },{
                header: '科目：',
                dataIndex: 'kemu',
                xtype: dataList ? 'hidden' : 'select',
                store: Constants.kemuStore,
            },
            {
                header: '状态：',
                dataIndex: 'status',
                xtype: 'select',
                check: 'required',
                store: statusStore
            }]
        }).add();
    }
    var applyDialog = function(table, line, data) {
        Widgets.dialog.html('选择范围', {width: 240}).done(function (dialog) {
            $('<button type="button" class="btn btn-primary">指定范围</button>').one('click', function() {
                applyToItem1(table, line, data)
                dialog.close()
            }).appendTo($(dialog.body))
            $('<button style="margin-left: 5px;" type="button" class="btn btn-success">全部未开始</button>').one('click', function() {
                applyToItem2(table, line, data)
                dialog.close()
            }).appendTo($(dialog.body))
        })
    }
    
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '模板名称：',
                dataIndex: 'templateName',
                xtype: 'text',
                maxlength: 128,
                placeholder: '模板名称'
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required',
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                check: 'required',
            },
            {
                header: '运营位类型：',
                dataIndex: 'operationPositionType',
                xtype: 'select',
                store: operationStore,
                check: () => {
                    console.log(
                        $('#operationPositionType').val()
                    );
                },
                placeholder: '运营位类型'
            },
            {
                header: '拼团活动模板ID：',
                dataIndex: 'templateId',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '请选择'
                }, {
                    key: 1,
                    value: '全科VIP-2人团'
                }, {
                    key: 2,
                    value: '全科VIP-3人团'
                }]
            },
            {
                header: '运营位图片：',
                dataIndex: 'imgUrl',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'imgUrl',
                    uploadIndex: 'imgUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
    
                })
            },
            {
                header: '横条运营位：',
                dataIndex: 'videoAdvertImg',
                check: 'required',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'videoAdvertImg',
                    uploadIndex: 'videoAdvertImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
    
                })
            },
            {
                header: '跳转类型：',
                dataIndex: 'type',
                xtype: 'select',
                store: typeStore
            },
            {
                header: 'URL地址：',
                dataIndex: 'detailUrl',
                xtype: 'textarea',
                maxlength: 512,
                placeholder: 'URL地址'
            },
            {
                header: '商品ID：',
                dataIndex: 'goodsUniqueKey',
                xtype: 'text',
                maxlength: 36,
                placeholder: '商品ID'
            },
            {
                header: '售卖的group_key：',
                dataIndex: 'groupKey',
                xtype: 'text',
                maxlength: 36,
                placeholder: '售卖的group_key'
            },
            {
                header: '相似的售卖group_key：',
                dataIndex: 'similarGroupKey',
                xtype: 'text',
                maxlength: 36,
                placeholder: '相似的售卖group_key'
            },
            {
                header: '弹窗标题：',
                dataIndex: 'popupTitle',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗标题'
            },
            {
                header: '弹窗副标题：',
                dataIndex: 'popupSubTitle',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗副标题'
            },
            {
                header: '弹窗图片：',
                dataIndex: 'popupImg',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'popupImg',
                    uploadIndex: 'popupImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
    
                })
            },
            {
                header: '弹窗标签：',
                dataIndex: 'popupTag',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗标签'
            },
            {
                header: '弹窗购买标签：',
                dataIndex: 'popupBuyTag',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗购买标签'
            },
            {
                header: '启用详情介绍：',
                dataIndex: 'enable',
                xtype: 'radio',
                store: enableStore
            },
            {
                header: '详情介绍：',
                dataIndex: 'introduction',
                xtype: Plugin('jiakao-misc!rich-text', {
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 300,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                    }
                }, function () {
                    console.log(arguments)
                })
            },
        ])
    }

    var add = function (table, lineData) {
        if (!lineData.id) {
            lineData = {}
        }
        var id = lineData.lessonItemId || lessonItemId
        Table().edit(lineData, {
            title: '添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-advert-tpl/data/insert?lessonItemId=' + id,
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        });
    }

    var list = function (panel, routeData) {
        // lessonItemId = routeData.id;
        lessonItemId = 3172
        Table({
            description: '子课程运营位模板管理',
            title: '子课程运营位模板管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-advert-tpl/data/view',
                        save: 'jiakao-misc!top-lesson-advert-tpl/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-advert-tpl/data/delete'
                },
                {
                    name: '一键应用',
                    class: 'info',
                    click: applyDialog
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '模板名称：',
                    dataIndex: 'templateName'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '运营位类型：',
                    dataIndex: 'operationPositionType',
                    render: function (data, arr, i) {
                        return operationMap[data]
                    }
                },
                {
                    header: '运营位图片：',
                    dataIndex: 'imgUrl',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.imgUrl}">`)
                    }
                },
                {
                    header: '弹窗图片',
                    dataIndex: 'popupImg',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.popupImg}">`)
                    }
                },
                {
                    header: '横条运营位：',
                    dataIndex: 'videoAdvertImg',
                    width: 200,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.videoAdvertImg}">`)
                    }
                },
                {
                    header: '跳转类型：',
                    dataIndex: 'type',
                    render: function (data, arr, i) {
                        return typeMap[data]
                    }
                },
                {
                    header: 'URL地址：',
                    dataIndex: 'detailUrl',
                    render: function(data) {
                        return '<div style="width: 160px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '商品ID：',
                    dataIndex: 'goodsUniqueKey'
                },
                {
                    header: '售卖的group_key',
                    dataIndex: 'groupKey'
                },
                {
                    header: '相似的售卖group_key',
                    dataIndex: 'similarGroupKey'
                },
                {
                    header: '弹窗标题',
                    dataIndex: 'popupTitle'
                },
                {
                    header: '弹窗副标题',
                    dataIndex: 'popupSubTitle'
                },
                {
                    header: '弹窗标签',
                    dataIndex: 'popupTag'
                },
                {
                    header: '弹窗购买标签',
                    dataIndex: 'popupBuyTag'
                },
                {
                    header: '拼单活动ID',
                    dataIndex: 'purchaseItemId'
                },
                {
                    header: '启用详情介绍：',
                    dataIndex: 'enable',
                    render: function (data, arr, i) {
                        return enableMap[data]
                    }
                },
                {
                    header: '详情介绍',
                    dataIndex: 'introduction',
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(lineData.introduction)
                    }
                }
            ]
        }, ['jiakao-misc!top-lesson-advert-tpl/data/list?lessonItemId=' + lessonItemId], panel, null).render();
    }

    return {
        list: list,
        add: add
    }
});
