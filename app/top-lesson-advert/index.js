/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var typeStore = [
        {
            key: 'normal',
            value: '普通跳转'
        }, {
            key: 'vip',
            value: 'vip售卖'
        }, {
            key: 'group',
            value: '拼单商品'
        }, {
            key: 'lesson',
            value: '课程售卖'
        }
    ]
    var typeMap = Tools.getMapfromArray(typeStore);

    var statusStore = [
        {
            key: '0',
            value: '未发布'
        }, {
            key: '1',
            value: '测试发布'
        }, {
            key: '2',
            value: '正式发布'
        }
    ]
    var statusMap = Tools.getMapfromArray(statusStore);

    var enableStore = [
        {
            key: 0,
            value: '停用'
        }, {
            key: 1,
            value: '启用'
        }
    ]
    var enableMap = Tools.getMapfromArray(enableStore);

    var timeModeStore = [
        {
            key: '1',
            value: '跟随直播'
        }, {
            key: '2',
            value: '自定义时间段'
        }
    ]
    var timeModeMap = Tools.getMapfromArray(timeModeStore);

    var lessonItemId = 0;

    var submitHandler = function (form) {
        var introduction = $(form).find('[name="editorValue"]').val();

        return {
            introduction: introduction
        };
    }

    var selectGroup = function (table, line, data) {
        var lineData = data
        Widgets.dialog.html('选择模板', {
            width: 600
        }).done(function (dialog) {
            Table({
                title: '',
                columns: [{
                    header: '模板ID',
                    dataIndex: 'templateId',
                },
                {
                    header: '模板名称',
                    dataIndex: 'templateName',
                },
                {
                    header: '商品groupKey',
                    dataIndex: 'bizIds'
                }
                ],
                operations: [{
                    name: '选择',
                    class: 'info',
                    click: function (oTable, line, ld, dom, data, dataIndex, e) {
                        Store(['jiakao-misc!top-lesson-advert/data/generatePurchase']).save([
                            {
                                params: {
                                    lessonItemId: lineData.lessonItemId,
                                    templateId: ld.templateId
                                }
                            }
                        ]).done(function (store, data, obj, ret) {
                            if (lineData.purchaseItemId == ret.data) {
                                Simple.Dialog.toast('选择模板成功');
                                dialog.close()
                            } else {
                                lineData.purchaseItemId = ret.data
                                Store(['jiakao-misc!top-lesson-advert/data/update']).save([
                                    {
                                        params: lineData
                                    }
                                ]).done(function (store, data, obj, ret) {
                                    Simple.Dialog.toast('选择模板成功');
                                    dialog.close()
                                    table.render()
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message)
                                });
                            }
                        }).fail(function (ret) {
                            Widgets.dialog.alert(ret.message)
                        });
                    }
                }
                ],
            }, ['jiakao-misc!top-lesson-advert/data/listPurchaseTemp'], dialog.body, function () { }).render();
        });
    }

    var addItemFromTpl = function (table, lineData) {
        var id = lineData.lessonItemId || lessonItemId

        Table().edit({}, {
            title: '从模板添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-advert/data/createFromTemplate?lessonId=' + id,
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '选择运营位模板：',
                    dataIndex: 'templateId',
                    xtype: Plugin('jiakao-misc!related-select', {
                        dataIndex: 'templateId',
                        selectIndex: 'id',
                        selectName: 'templateName',
                        columns: [{
                            header: '模板ID',
                            dataIndex: 'id',
                        },
                        {
                            header: '模板名称',
                            dataIndex: 'templateName',
                        }],
                        store: 'jiakao-misc!top-lesson-advert-tpl/data/list',
                        placeholder: '请选择模板',
                    }, function () {

                    })
                },
                {
                    header: '运营位生效模式：',
                    dataIndex: 'takeEffectMode',
                    xtype: 'select',
                    check: 'required',
                    store: timeModeStore
                },
                {
                    header: '状态：',
                    dataIndex: 'status',
                    xtype: 'select',
                    check: 'required',
                    store: statusStore
                },
                {
                    header: '开始时间：',
                    dataIndex: 'beginTime',
                    xtype: 'datetime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                    placeholder: '开始时间'
                },
                {
                    header: '结束时间：',
                    dataIndex: 'endTime',
                    xtype: 'datetime',
                    render: function (data) {
                        if (data) {
                            return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                        }
                    },
                    placeholder: '结束时间'
                }
            ]
        });
    }

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required',
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                check: 'required',
            },
            {
                header: '运营位图片：',
                dataIndex: 'imgUrl',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'imgUrl',
                    uploadIndex: 'imgUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
    
                })
            },
            {
                header: '横条运营位：',
                dataIndex: 'videoAdvertImg',
                check: 'required',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'videoAdvertImg',
                    uploadIndex: 'videoAdvertImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
    
                })
            },
            {
                header: 'url跳转地址：',
                dataIndex: 'detailUrl',
                xtype: 'textarea',
                maxlength: 512,
                placeholder: 'url跳转地址'
            },
            {
                header: '开始时间：',
                dataIndex: 'beginTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '开始时间'
            },
            {
                header: '结束时间：',
                dataIndex: 'endTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '结束时间'
            },
            {
                header: '商品ID：',
                dataIndex: 'goodsUniqueKey',
                xtype: 'text',
                maxlength: 36,
                placeholder: '商品ID'
            },
            {
                header: '售卖的group_key：',
                dataIndex: 'groupKey',
                xtype: 'text',
                maxlength: 36,
                placeholder: '售卖的group_key'
            },
            {
                header: '相似的售卖group_key：',
                dataIndex: 'similarGroupKey',
                xtype: 'text',
                maxlength: 36,
                placeholder: '相似的售卖group_key'
            },
            {
                header: '弹窗标题：',
                dataIndex: 'popupTitle',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗标题'
            },
            {
                header: '弹窗副标题：',
                dataIndex: 'popupSubTitle',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗副标题'
            },
            {
                header: '弹窗图片：',
                dataIndex: 'popupImg',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'popupImg',
                    uploadIndex: 'popupImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
    
                })
            },
            {
                header: '弹窗标签：',
                dataIndex: 'popupTag',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗标签'
            },
            {
                header: '弹窗购买标签：',
                dataIndex: 'popupBuyTag',
                xtype: 'text',
                maxlength: 128,
                placeholder: '弹窗购买标签'
            },
            {
                header: '类型:',
                dataIndex: 'type',
                xtype: 'select',
                store: typeStore
            },
            {
                header: '拼单活动ID：',
                dataIndex: 'purchaseItemId',
                xtype: 'text',
                readonly: true
            },
            {
                header: '状态:',
                dataIndex: 'status',
                xtype: 'select',
                store: statusStore
            },
            {
                header: '启用详情介绍：',
                dataIndex: 'enable',
                xtype: 'radio',
                store: enableStore
            },
            {
                header: '详情介绍：',
                dataIndex: 'introduction',
                xtype: Plugin('jiakao-misc!rich-text', {
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 300,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                    }
                }, function () {
                    console.log(arguments)
                })
            },
        ])
    }

    var add = function (table, lineData) {
        if (!lineData.id) {
            lineData = {}
            lineData.imgUrl = ''
            lineData.purchaseImgUrl = ''
            lineData.videoAdvertImg = ''
            lineData.popupImg = ''
        }
        var id = lineData.lessonItemId || lessonItemId
        Table().edit(lineData, {
            title: '添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-advert/data/insert?lessonItemId=' + id,
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        });
    }

    var list = function (panel, routeData) {

        lessonItemId = routeData.id;
        Table({
            description: '运营位配置列表',
            title: '运营位配置列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    },
                    {
                        name: '从模板添加',
                        class: 'primary',
                        click: addItemFromTpl
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-advert/data/view',
                        save: 'jiakao-misc!top-lesson-advert/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-advert/data/delete'
                },
                {
                    name: '选择拼团活动模板',
                    class: 'info',
                    click: selectGroup
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'lessonItemId',
                    dataIndex: 'lessonItemId'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '运营位图片：',
                    dataIndex: 'imgUrl',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.imgUrl}">`)
                    }
                },
                {
                    header: '弹窗图片',
                    dataIndex: 'popupImg',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.popupImg}">`)
                    }
                },
                {
                    header: '横条运营位：',
                    dataIndex: 'videoAdvertImg',
                    width: 200,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.videoAdvertImg}">`)
                    }
                },
                {
                    header: 'URL地址：',
                    dataIndex: 'detailUrl',
                    render: function(data) {
                        return '<div style="width: 160px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'beginTime'
                },
                {
                    header: '结束时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'endTime'
                },
                {
                    header: '商品ID：',
                    dataIndex: 'goodsUniqueKey'
                },
                {
                    header: '售卖的group_key',
                    dataIndex: 'groupKey'
                },
                {
                    header: '相似的售卖group_key',
                    dataIndex: 'similarGroupKey'
                },
                {
                    header: '弹窗标题',
                    dataIndex: 'popupTitle'
                },
                {
                    header: '弹窗副标题',
                    dataIndex: 'popupSubTitle'
                },
                {
                    header: '弹窗标签',
                    dataIndex: 'popupTag'
                },
                {
                    header: '弹窗购买标签',
                    dataIndex: 'popupBuyTag'
                },
                {
                    header: '类型',
                    dataIndex: 'type',
                    render: function (data, arr, i) {
                        return typeMap[data]
                    }
                },
                {
                    header: '拼单活动ID',
                    dataIndex: 'purchaseItemId'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data, arr, i) {
                        return statusMap[data]
                    }
                },
                {
                    header: '启用详情介绍',
                    dataIndex: 'enable',
                    render: function (data, arr, i) {
                        return enableMap[data]
                    }
                },
                {
                    header: '详情介绍',
                    dataIndex: 'introduction',
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert('<pre>' + lineData.introduction + '</pre>')
                    }
                },
            ]
        }, ['jiakao-misc!top-lesson-advert/data/list?lessonItemId=' + lessonItemId], panel, null).render();
    }

    return {
        list: list,
        add: add
    }
});
