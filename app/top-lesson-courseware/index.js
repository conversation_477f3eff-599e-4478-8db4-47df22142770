/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
  "simple!core/template",
  "simple!core/table",
  "simple!core/utils",
  "simple!core/widgets",
  "simple!core/store",
  "simple!core/form",
  "simple!core/plugin",
], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
  var itemId;
  var imageSize;
  var add = function (table) {
    Table({
      title: "添加",
      width: 500,
      store: "jiakao-misc!top-lesson-courseware/data/insert?itemId=" + itemId,
      success: function (obj, dialog) {
        dialog.close();
        table.render();
      },
      form: {
        submitHandler: function (form, fun) {
          return {
            size: imageSize,
          };
        },
      },
      columns: [
        {
          header: "课件图片url：",
          dataIndex: "imageUrl",
          xtype: Plugin(
            "jiakao-misc!upload",
            {
              dataIndex: "imageUrl",
              uploadIndex: "imageUrl",
              bucket: "exam-room",
              isSingle: true,
              placeholder: "请选择上传文件",
              url: "simple-upload3://upload/file.htm",
            },
            function (m, b, url, size) {
              imageSize = size;
              console.log(arguments, size);
            }
          ),
        },
        {
          header: "排序：",
          dataIndex: "sort",
          xtype: "text",
          check: "required",
          placeholder: "排序",
        },
      ],
    }).add();
  };

  var list = function (panel, id) {
    itemId = id;
    Table(
      {
        description: "top-lesson-courseware列表",
        title: "top-lesson-courseware列表",

        buttons: {
          top: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
            {
              name: "添加",
              class: "primary",
              click: add,
            },
          ],
          bottom: [
            {
              name: "刷新",
              class: "info",
              click: function (obj) {
                obj.render();
              },
            },
          ],
        },
        operations: [
          {
            name: "查看",
            xtype: "view",
            width: 400,
            class: "success",
            title: "查看",
            store: "jiakao-misc!top-lesson-courseware/data/view",
            columns: [
              {
                header: "#",
                dataIndex: "id",
              },
              {
                header: "子课程id：",
                dataIndex: "itemId",
              },
              {
                header: "课件图片url：",
                dataIndex: "imageUrl",
                render: function (data) {
                  if (data) {
                    return `<img  src="${data}" style="width: 200px">`;
                  } else {
                    return "";
                  }
                },
              },
              {
                header: "排序：",
                dataIndex: "sort",
              },
              {
                header: "创建时间",
                render: function (data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                },
                dataIndex: "createTime",
              },

              {
                header: "创建人",
                dataIndex: "createUserName",
              },
              {
                header: "更新时间",
                render: function (data) {
                  return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
                },
                dataIndex: "updateTime",
              },
              {
                header: "更新人",
                dataIndex: "updateUserName",
              },
              {
                header: "deleted：",
                render: function (data) {
                  if (data) {
                    return "是";
                  } else {
                    return "否";
                  }
                },
                dataIndex: "deleted",
              },
            ],
          },
          {
            name: "编辑",
            xtype: "edit",
            width: 500,
            class: "warning",
            title: "编辑",
            success: function (obj, dialog, e) {
              dialog.close();
              obj.render();
            },
            form: {
              submitHandler: function (form, fun) {
                return {
                  size: imageSize,
                };
              },
            },
            store: {
              load: "jiakao-misc!top-lesson-courseware/data/view",
              save: "jiakao-misc!top-lesson-courseware/data/update",
            },
            columns: [
              {
                dataIndex: "id",
                xtype: "hidden",
              },
              {
                header: "子课程id：",
                dataIndex: "itemId",
                xtype: "text",
                check: "required",
                placeholder: "子课程id",
              },
              {
                header: "课件图片url：",
                dataIndex: "imageUrl",
                xtype: Plugin(
                  "jiakao-misc!upload",
                  {
                    dataIndex: "imageUrl",
                    uploadIndex: "imageUrl",
                    bucket: "exam-room",
                    isSingle: true,
                    placeholder: "请选择上传文件",
                    url: "simple-upload3://upload/file.htm",
                  },
                  function (m, b, url, size) {
                    imageSize = size;
                    console.log(arguments, size);
                  }
                ),
              },
              {
                header: "排序：",
                dataIndex: "sort",
                xtype: "text",
                check: "required",
                placeholder: "排序",
              },
            ],
          },
          {
            name: "删除",
            class: "danger",
            xtype: "delete",
            store: "jiakao-misc!top-lesson-courseware/data/delete",
          },
        ],
        columns: [
          {
            header: "#",
            dataIndex: "id",
            width: 20,
          },
          {
            header: "子课程id",
            dataIndex: "itemId",
          },
          {
            header: "课件图片url",
            dataIndex: "imageUrl",
            render: function (data) {
              if (data) {
                return `<img  src="${data}" style="width: 200px">`;
              } else {
                return "";
              }
            },
          },
          {
            header: "排序",
            dataIndex: "sort",
          },
          {
            header: "创建时间",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
            dataIndex: "createTime",
          },
          {
            header: "创建人",
            dataIndex: "createUserName",
          },
          {
            header: "更新时间",
            render: function (data) {
              return Utils.format.date(data, "yyyy-MM-dd HH:mm:ss");
            },
            dataIndex: "updateTime",
          },
          {
            header: "更新人",
            dataIndex: "updateUserName",
          },
          {
            header: "deleted",
            render: function (data) {
              if (data) {
                return "是";
              } else {
                return "否";
              }
            },
            dataIndex: "deleted",
          },
        ],
      },
      ["jiakao-misc!top-lesson-courseware/data/list?itemId=" + itemId],
      panel,
      null
    ).render();
  };

  return {
    list: list,
  };
});
