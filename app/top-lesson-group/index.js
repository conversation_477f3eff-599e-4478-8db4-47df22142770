/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    
    var carTypeStore = [
        {
            
            key: 'car',
            value: '小车'
        },
        {
            key: 'truck',
            value: '货车'
        },
        {
            key: 'bus',
            value: '客车'
        },
        {
            key: 'moto',
            value: '摩托车'
        },
        {
            key: 'light_trailer',
            value: '轻型牵引挂车'
        }
    ]
    var carTypeMap = Tools.getMapfromArray(carTypeStore);

    var liveConfig = function (kemu) {
        if (kemu != 1 && kemu != 4) kemu = 1

        Table().edit({}, {
            title: '配置直播入口',
            width: 900,
            store: {
                load: 'jiakao-misc!top-lesson-group/data/liveData?kemu=' + kemu,
                save: 'jiakao-misc!top-lesson-group/data/saveliveData'
            },
            success: function (obj, dialog) {
                dialog.close();
            },
            renderAfter: function (config, dom) {

                dom.find('#kemu').on('change', function (e) {
                    dom.closest('.modal-content').find('.close').click();
                    liveConfig($(this).val())
                }
                )
            },
            columns: [{
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: [
                    {
                        key: '1',
                        value: '科目一'
                    },
                    {
                        key: '4',
                        value: '科目四'
                    }
                ],
                value: kemu
            },
            {
                header: '老师头像：',
                dataIndex: 'teacherAvatar',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'teacherAvatar',
                    uploadIndex: 'teacherAvatar',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {

                })
            },
            {
                header: '主标题：',
                dataIndex: 'title',
                xtype: 'text',
                placeholder: '主标题'
            },
            {
                header: '跳转url：',
                dataIndex: 'navigationUrl',
                xtype: 'text',
                placeholder: '跳转url'
            }


            ]
        });
    }
    var bannerConfig = function (table) {
        Widgets.dialog.html('banner配置', {
            width: 1100,
            buttons: [{
                name: '保存',
                xtype: 'success',
                click: function () {
                    var me = this;
                    var data = []
                    this.body.find('.line').each(function () {
                        $(this).find('.kemu').val();
                        $(this).find('.url1').val();
                        $(this).find('.upload-image').attr('src')

                        data.push({
                            kemu: $(this).find('.kemu').val(),
                            url: $(this).find('.url1').val(),
                            image: $(this).find('.upload-image').attr('src'),
                            vip: $(this).find('.vip')[0].checked
                        })
                    })

                    Store(['jiakao-misc!top-lesson-group/data/saveBannerData'], [{
                        aliases: 'list',
                        params: {
                            data: JSON.stringify(data)
                        }
                    }]).save().done(function (store) {
                        Widgets.dialog.alert('配置成功')
                        me.close();
                    }).fail(function () {
                    });
                }
            }]
        }).done(function (dialog) {
            Plugin('jiakao-misc!banner-config', {
                target: dialog.body
            }).render()
        })
    }
    var questionPopConfig = function (table) {
        Widgets.dialog.html('500题/考前秘卷引导升级引导升级', {
            width: 800,
            buttons: [{
                name: '保存',
                xtype: 'success',
                click: function () {
                    var me = this;
                    var data = {}
                    var title = this.body.item('title').val()
                    var subtitle = this.body.item('subtitle').val()
                    var vip100LessonConfigs = []
                    var vip500LessonConfigs = []
                    this.body.item('vip100LessonConfigs').find('.line').each(function () {
                        vip100LessonConfigs.push({
                            lessonGroupId: $(this).item('lessonGroupId').val(),
                            url: $(this).item('url').val(),
                        })
                    })
                    this.body.item('vip500LessonConfigs').find('.line').each(function () {
                        vip500LessonConfigs.push({
                            lessonGroupId: $(this).item('lessonGroupId').val(),
                            url: $(this).item('url').val(),
                        })
                    })
                    data.title = title
                    data.subtitle = subtitle
                    data.vip100LessonConfigs = vip100LessonConfigs
                    data.vip500LessonConfigs = vip500LessonConfigs

                    Store(['jiakao-misc!top-lesson-group/data/saveQuestionPopData'], [{
                        aliases: 'list',
                        params: {
                            configValue: JSON.stringify(data)
                        }
                    }]).save().done(function (store) {
                        Widgets.dialog.alert('配置成功')
                        me.close();
                    }).fail(function () {
                    });
                }
            }]
        }).done(function (dialog) {
            Plugin('jiakao-misc!question-pop-config', {
                target: dialog.body
            }).render()
        })
    }
    var submitHandler = function (form) {
        var banner = []
        $(form).find('.line').each(function () {
            banner.push({
                url: $(this).find('[data-item="url1"]').val(),
                image: $(this).find('.upload-image').attr('src')
            })
        })
        banner = JSON.stringify(banner)
        console.log(banner);
        var detail = $(form).find('[name="editorValue"]').val();

        return {
            banner: banner,
            detail: detail
        };
    }

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '语言：',
                dataIndex: 'languageType',
                xtype: 'select',
                store: Constants.languageTypeStore
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                check: 'required',
                store: Constants.kemuStore,
            },
            {
                header: '车型：',
                check: 'required',
                dataIndex: 'carType',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: carTypeStore,
                    placeholder: '车型',
                    dataIndex: 'carType',
                    index: {
                        key: 'key',
                        value: 'value',
                        search: 'key'
                    },
                    isMulti: true,
                    defaultVal: ''
                }, function (plugin, value) {
                })
            },
            {
                header: '标题：',
                dataIndex: 'title',
                xtype: 'text',
                maxlength: 45,
                placeholder: '标题'
            },
            {
                header: '商品主图：',
                dataIndex: 'goodsMasterImgUrl',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'goodsMasterImgUrl',
                    uploadIndex: 'goodsMasterImgUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                })
            },
            {
                header: '课程类型：',
                dataIndex: 'type',
                xtype: 'select',
                store: [{
                    key: '1',
                    value: "视频"
                }, {
                    key: '2',
                    value: "直播"
                }]
            },
            {
                header: '课程标签：',
                dataIndex: 'tagIds',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!top-lesson-tag/data/list?limit=1000000',
                    placeholder: '课程标签',
                    dataIndex: 'tagIds',
                    index: {
                        key: 'id',
                        value: 'tagName',
                        search: 'tagName'
                    },
                    isMulti: true,
                }, function (plugin, value) {
                })
            },
            {
                header: '列表图标：',
                dataIndex: 'icon',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'icon',
                    uploadIndex: 'icon',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                })
            },
            {
                header: '分享图片：',
                dataIndex: 'shareImage',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'shareImage',
                    uploadIndex: 'shareImage',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                })
            },
            {
                header: '顶部banner：',
                dataIndex: 'banner',
                xtype: Plugin('jiakao-misc!banner', {
                }, function () {
                }),
            },
            {
                header: '红包文案：',
                dataIndex: 'redPacketText',
                xtype: 'text',
                maxlength: 10,
                placeholder: '红包文案'
            },
            {
                header: '课程描述：',
                dataIndex: 'lessonDesc',
                xtype: 'text',
                maxlength: 50,
                placeholder: '课程描述',
                check: 'required'
            },
            {
                header: '标签列表：',
                dataIndex: 'tags',
                xtype: 'text',
                maxlength: 64,
                placeholder: '标签列表'
            },
            {
                header: '价格：',
                dataIndex: 'price',
                xtype: 'number',
                check: 'required',
                placeholder: '价格'
            },
            {
                header: '原始价格：',
                xtype: 'number',
                check: 'required',
                placeholder: '原始价格',
                dataIndex: 'originalPrice'
            },
            {
                header: '苹果价格：',
                dataIndex: 'applePrice',
                xtype: 'number',
                check: 'required',
                placeholder: '苹果价格'
            },
            {
                header: '苹果价格id：',
                dataIndex: 'applePriceId',
                xtype: 'text',
                check: 'required',
                placeholder: '苹果价格id'
            },
            {
                header: '试看课程名称',
                dataIndex: 'trialLessonName',
                xtype: 'text',
                placeholder: '试看课程名称'
            },
            {
                header: '试看视频时长',
                dataIndex: 'trailLessonDuration',
                xtype: 'number',
                placeholder: '试看视频时长'
            },
            {
                header: '试看课程地址',
                dataIndex: 'trialLessonUrl',
                xtype: 'text',
                placeholder: '试看课程视频链接地址'
            },
            {
                header: '试看课程封面',
                dataIndex: 'trialLessonImage',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'trialLessonImage',
                    uploadIndex: 'trialLessonImage',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                })
            },
            {
                header: '顺序',
                dataIndex: 'order',
                xtype: 'text'
            },
            {
                header: '好课推荐顺序',
                dataIndex: 'orderValue',
                xtype: 'text'
            },
            {
                header: '版本：',
                dataIndex: 'editionCode',
                xtype: 'select',
                check: 'required',
                store: [{
                    key: 102,
                    value:'长辈模式'
                }, {
                    key: 101,
                    value:'普通模式'
                }]
            },
            {
                header: '详情的描述：',
                dataIndex: 'detail',
                xtype: Plugin('jiakao-misc!rich-text', {
                    dataIndex: 'detail',
                    uploadIndex: 'detail',
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 300,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                        dataIndex: 'detail',
                        uploadIndex: 'detail',
                        name: 'detail'

                    }

                }, function () {
                })
            },
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 900,
            store: 'jiakao-misc!top-lesson-group/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        }).add();
    }

    var list = function (panel, aside, id) {
        var sid = id || ''
        Table({
            description: '名师精品课程列表',
            title: '名师精品课程列表',
            search: [
                {
                    dataIndex: 'id',
                    xtype: 'text',
                    placeholder: 'id'
                },
                {
                    placeholder: '标题',
                    dataIndex: 'title',
                    xtype: 'text'
                },
                {
                    dataIndex: 'languageType',
                    xtype: 'select',
                    store: [{ key: '', value: '请选择语言' }].concat(Constants.languageTypeStore)     
                },
                {
                    dataIndex: 'tagId',
                    xtype: 'select',
                    store: 'jiakao-misc!top-lesson-tag/data/list?limit=1000000',
                    index: 
                        {
                            key: 'id', value: 'tagName'
                        }
                    ,
                    insert: [{
                        id: '',
                        tagName: '请选择标签'
                    }],
                },
                {
                    dataIndex: 'type',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: "选择类型"
                    }, {
                        key: '1',
                        value: "视频"
                    }, {
                        key: '2',
                        value: "直播"
                    }],
                    placeholder: '课程类型'
                },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [{
                        key: '',
                        value: "选择状态"
                    }, {
                        key: '0',
                        value: "下线"
                    }, {
                        key: '2',
                        value: "上线"
                    }],
                    placeholder: '状态'
                },
                {
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore,
                    placeholder: '科目'
                },
                {
                    xtype: 'select',
                    dataIndex: 'carType',
                    store: Constants.carTypeStore,
                    placeholder: '车型'
                }
            ],
            selector: {
                dataIndex: 'id',
            },
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                },
                {
                    name: 'banner配置',
                    class: 'success',
                    click: bannerConfig
                },
                {
                    name: '引导升级',
                    class: 'success',
                    click: questionPopConfig
                },
                {
                    name: '直播入口配置',
                    class: 'warning',
                    click: liveConfig
                },
                {
                    name: '排序',
                    class: 'warning',
                    click: function (obj, dom, data) {
                        var params = Object.assign({}, obj.search, { sort: obj.config.params.sort });
                        var search = Object.keys(params).map(function (k) {
                            return k + '=' + encodeURIComponent(params[k]);
                        }).join('&');
                        Plugin('jiakao-misc!drag-sort', {
                            save: 'jiakao-misc!top-lesson-group/data/sortliveData',
                            load: ['jiakao-misc!top-lesson-group/data/list?id=' + sid + '&' + search + '&limit=1000'],
                            showKey: 'title'
                        }, function () {
                            obj.render();
                        }).render()
                    }
                },
                {
                    name: '会员推荐',
                    class: 'info',
                    click: function (table, dom, config, selectedArr) {
                        if (selectedArr.length <= 0) {
                            Widgets.dialog.alert('请勾选数据')
                            return
                        }
                        Widgets.dialog.confirm('确认会员推荐吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-group/data/batchRecommend?ids=' + selectedArr.join(",") + '&recommend=true']).save().done(data => {
                                    table.render();
                                    Widgets.dialog.alert('会员推荐成功');
                                }).fail(err => {
                                    Widgets.dialog.alert(err.message);
                                });
                            }
                        })
                    }
                },
                {
                    name: '取消推荐',
                    class: 'info',

                    click: function (table, dom, config, selectedArr) {
                        if (selectedArr.length <= 0) {
                            Widgets.dialog.alert('请勾选数据')
                            return
                        }
                        Widgets.dialog.confirm('确认取消推荐吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-group/data/batchRecommend?ids=' + selectedArr.join(",") + '&recommend=false']).save().done(data => {
                                    table.render();
                                    Widgets.dialog.alert('取消推荐成功');
                                }).fail(err => {
                                    Widgets.dialog.alert(err.message);
                                });
                            }
                        })
                    }
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 900,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-group/data/view',
                        save: 'jiakao-misc!top-lesson-group/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '上线',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].status == 0 ? name : '';

                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认上线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-group/data/updateStatus?id=' + lineData.id + '&status=2']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail(err => {
                                    if (err.message != null) {
                                        Widgets.dialog.alert(err.message);
                                    } else {
                                        Widgets.dialog.alert('接口请求失败...');
                                    }
                                })
                            }
                        })
                    }
                },
                {
                    name: '下线',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].status == 2 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认下线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-group/data/updateStatus?id=' + lineData.id + '&status=0']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail(err => {
                                    if (err.message != null) {
                                        Widgets.dialog.alert(err.message);
                                    } else {
                                        Widgets.dialog.alert('接口请求失败...');
                                    }
                                })
                            }
                        })
                    }
                },
                {
                    name: '会员推荐',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].vipRecommend ? '已推荐' : '会员推荐'
                    },
                    click: function (table, dom, lineData) {
                        Store(['jiakao-misc!top-lesson-group/data/batchRecommend?ids=' + lineData.id + '&recommend=' + !lineData.vipRecommend]).save().done(data => {
                            table.render();
                        }).fail(err => {
                            Widgets.dialog.alert(err.message);
                        });
                    }
                },
                // {
                //     name: '删除',
                //     class: 'danger',
                //     xtype: 'delete',
                //     store: 'jiakao-misc!top-lesson-group/data/delete'
                // },
                {
                    name: '创建商品',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].channelCode ? '' : name;

                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('创建商品吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-group/data/createChannelGoods?id=' + lineData.id]).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail(function (err) {
                                    Widgets.dialog.alert(err.message)
                                });
                            }
                        })
                    }
                },
                {
                    name: '编辑价格',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].channelCode ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        window.open(window.j.root.squirrel + '?channelCode=' + lineData.channelCode + '#header=squirrel!main%2Faside&aside=squirrel!app%2Fchannel%2Findex%2Flist')
                    }
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        let list = data.split(',')
                        list = list.map(item => carTypeMap[item])
                        return list.join(',')
                    }
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '语言',
                    dataIndex: 'languageType',
                    render:function(data){
                        return Constants.languageTypeMap[data]
                    }
                },
                {
                    header: '课程类型',
                    dataIndex: 'type',
                    render: function (data) {
                        return data == 2 ? '直播' : '视频'
                    }
                },
                {
                    header: '课程标签',
                    dataIndex: 'tagName'
                },
                {
                    header: '列表图标',
                    dataIndex: 'icon',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.icon}">`)
                    }
                },
                {
                    header: '分享图片',
                    dataIndex: 'shareImage',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.shareImage}">`)
                    }
                },
                {
                    header: '顶部banner',
                    dataIndex: 'banner',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(lineData.banner)
                    }
                },
                {
                    header: '标签列表',
                    dataIndex: 'tags'
                },
                {
                    header: '价格',
                    dataIndex: 'price',
                },
                {
                    header: '原始价格',
                    dataIndex: 'originalPrice'
                },
                {
                    header: '苹果价格',
                    dataIndex: 'applePrice',
                },
                {
                    header: '苹果价格id',
                    dataIndex: 'applePriceId',
                },
                {
                    header: '试看课程名称：',
                    dataIndex: 'trialLessonName',
                },
                {
                    header: '试看视频时长',
                    dataIndex: 'trailLessonDuration'
                },
                {
                    header: '试看课程地址：',
                    dataIndex: 'trialLessonUrl',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('试看课程地址', {}).done(function (dialog) {
                            var html = lineData.trialLessonUrl.split(',').map(function (url) {
                                return '<a href="' + url + '" target="_blank">' + url + '</a>' + '<br><video src="' + url + '" width="400" controls></video>';
                            });
                            $(dialog.body).html('<div style="max-height: 600px; overflow: auto;">' + html.join('<br/>') + '</div>');
                        })
                    }
                },
                {
                    header: '试看课程封面：',
                    dataIndex: 'trialLessonImage',
                    width: 50,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.trialLessonImage}">`)
                    }
                },
                {
                    header: '顺序',
                    dataIndex: 'order',
                    order: 'asc'
                },
                {
                    header: '好课推荐顺序',
                    dataIndex: 'orderValue',
                    order: 'asc'
                },
                {
                    header: '版本',
                    dataIndex: 'editionName'
                },
                {
                    header: '详情的描述',
                    dataIndex: 'detail',
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(lineData.detail)
                    }
                },
                {
                    header: '观看人数',
                    dataIndex: 'viewCount'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return data == 2 ? '上线' : '下线'
                    }
                },
                {
                    header: '是否会员推荐',
                    dataIndex: 'vipRecommend',
                    render: function (data) {
                        return data ? '是' : '否'
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-group/data/list?id=' + sid], panel, null).render();
    }

    return {
        list: list
    }

});
