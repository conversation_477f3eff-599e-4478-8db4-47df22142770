/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var bizStore = [
        {
            key: '',
            value: '请选择'
        }, {
            key: 'itemLessonCover',
            value: '子课程封面图'
        }, {
            key: 'itemLessonShareImg',
            value: '子课程分享图'
        }, {
            key: 'itemLessonIcon',
            value: '子课程列表图标'
        }, {
            key: 'vipRecommend',
            value: 'vip课程推荐'
        }, {
            key: 'goodRecommend',
            value: '好课推荐'
        }
    ]
    var bizMap = Tools.getMapfromArray(bizStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '图片：',
                dataIndex: 'url',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'url',
                    uploadIndex: 'url',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '主课程：',
                dataIndex: 'groupId',
                xtype: Plugin('jiakao-misc!related-select', {
                    dataIndex: 'groupId',
                    selectIndex: 'id',
                    selectName: 'title',
                    columns: [{
                        header: 'ID',
                        dataIndex: 'id',
                    },
                    {
                        header: '名称',
                        dataIndex: 'title',
                    }],
                    store: 'jiakao-misc!top-lesson-group/data/list',
                    placeholder: '请选择课程',
                }, function () {

                }),
            },
            {
                header: '讲师：',
                dataIndex: 'teacherId',
                xtype: 'select',
                store: 'jiakao-misc!top-lesson-teacher/data/list?limit=10000',
                insert: [
                    {
                        key: '', value: '请选择', name: '请选择'
                    }
                ],
                index: {
                    key: 'id',
                    value: 'name'
                },
            },
            {
                header: '图片业务key：',
                dataIndex: 'bizKey',
                xtype: 'select',
                store: bizStore
            },
            {
                header: '图片描述：',
                dataIndex: 'desc',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '图片描述'
            }
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!top-lesson-img/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '图片素材库',
            title: '图片素材库',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-img/data/view',
                        save: 'jiakao-misc!top-lesson-img/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-img/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '图片',
                    dataIndex: 'url',
                    render: function (data) {
                        if (data) {
                            return '<a><image style="width: 200px; height: auto;" src="' + data + '"></a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('图片', { width: 220 }).done(function (dialog) {
                            $(dialog.body).html('<img src="' + lineData.url + '"></img>')
                        })
                    }
                },
                {
                    header: '主课程Id',
                    dataIndex: 'groupId',
                },
                {
                    header: '讲师Id',
                    dataIndex: 'teacherId',
                },
                {
                    header: '图片业务key',
                    dataIndex: 'bizKey',
                    render: function (data, arr, i) {
                        return data ? bizMap[data] : ''
                    }
                },
                {
                    header: '图片描述',
                    dataIndex: 'desc',
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-img/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
