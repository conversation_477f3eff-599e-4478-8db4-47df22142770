/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var enableStore = [
        {
            key: false,
            value: '关闭'
        }, {
            key: true,
            value: '开启'
        }
    ]
    var enableMap = Tools.getMapfromArray(enableStore);

    var radiooStore = [
        {
            key: 0,
            value: '关闭'
        }, {
            key: 1,
            value: '开启'
        }
    ]
    var radiooMap = Tools.getMapfromArray(radiooStore);

    var submitHandler = function (form) {
        var desc = $(form).find('[name="editorValue"]').val();

        return {
            desc: desc
        };
    }
    
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '模板名称：',
                dataIndex: 'name',
                xtype: 'text',
                placeholder: '请输入',
                check: 'required',
            },
            {
                header: '主课程ID：',
                dataIndex: 'groupId',
                xtype: 'text',
                placeholder: '主课程ID',
                check: 'required',
            },
            {
                header: '标题：',
                dataIndex: 'title',
                xtype: 'text',
                placeholder: '标题',
                check: 'required',
            },
            {
                header: '副标题：',
                dataIndex: 'subTitle',
                xtype: 'text',
                maxlength: 45,
                placeholder: '副标题'
            },
            {
                header: '讲师排名：',
                dataIndex: 'teacherRank',
                xtype: 'number',
                placeholder: '讲师排名',
                check: 'required',
            },
            {
                header: '专享标签：',
                dataIndex: 'label',
                xtype: 'select',
                store: [
                    {
                        key: '限时免费',
                        value: '限时免费'
                    },
                    {
                        key: 'VIP专享',
                        value: 'VIP专享'
                    }
                ]
            },
            {
                header: '展示顺序：',
                dataIndex: 'orderIndex',
                xtype: 'text',
                placeholder: '展示顺序',
                check: 'required',
            },
            {
                header: '直播类型：',
                dataIndex: 'liveType',
                xtype: 'select',
                store: [{
                    key: '1',
                    value: "常规直播"
                }, {
                    key: '2',
                    value: "66学车节"
                }, {
                    key: '3',
                    value: "公共直播"
                }, {
                    key: '4',
                    value: "会员直播"
                }]
            },
            {
                header: '学车节liveId：',
                dataIndex: 'xueche66LiveId',
                xtype: 'text',
                placeholder: '学车节liveId'
            },
            {
                header: '直播方式：',
                dataIndex: 'liveMode',
                xtype: 'select',
                store: [{
                    key: '1',
                    value: "直播"
                }, {
                    key: '2',
                    value: "录播"
                }]
            },
            {
                header: '定时评论模板：',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!top-lesson-item/data/templateList',
                    placeholder: '定时评论模板',
                    dataIndex: 'msgTemplateId',
                    index: {
                        key: 'templateId',
                        value: 'templateName',
                        search: 'templateId'
                    },
                    isMulti: false,
                }, function (plugin, value) {
                }),
                dataIndex: 'msgTemplateId'
            },
            {
                header: '运营位模板：',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!top-lesson-advert-tpl/data/templateList',
                    placeholder: '运营位模板',
                    dataIndex: 'advertTemplateId',
                    index: {
                        key: 'id',
                        value: 'templateName',
                        search: 'id'
                    },
                    isMulti: false,
                }, function (plugin, value) {
                }),
                dataIndex: 'advertTemplateId'
            },
            {
                header: '推荐商品弹窗模板：',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!top-lesson-recommend-goods-tpl/data/templateList',
                    placeholder: '推荐商品弹窗模板',
                    dataIndex: 'recommendTemplateId',
                    index: {
                        key: 'templateId',
                        value: 'templateName',
                        search: 'templateId'
                    },
                    isMulti: false,
                }, function (plugin, value) {
                }),
                dataIndex: 'recommendTemplateId'
            },
            {
                header: '是否显示挽留弹窗：',
                dataIndex: 'detention',
                xtype: 'radio',
                store: radiooStore
            },
            {
                header: '启用详情介绍：',
                dataIndex: 'descEnable',
                xtype: 'radio',
                store: enableStore
            },
            {
                header: '详情介绍：',
                dataIndex: 'desc',
                xtype: Plugin('jiakao-misc!rich-text', {
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 300,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                    }
                }, function () {
                    console.log(arguments)
                })
            }
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-item-tpl/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '子课程模板',
            title: '子课程模板',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-item-tpl/data/view',
                        save: 'jiakao-misc!top-lesson-item-tpl/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-item-tpl/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '模板名称：',
                    dataIndex: 'name',
                },
                {
                    header: '主课程ID：',
                    dataIndex: 'groupId',
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                },
                {
                    header: '副标题：',
                    dataIndex: 'subTitle'
                },
                {
                    header: '讲师排名：',
                    dataIndex: 'teacherRank',
                },
                {
                    header: '专享标签：',
                    dataIndex: 'label'
                },
                {
                    header: '展示顺序：',
                    dataIndex: 'orderIndex',
                },
                {
                    header: '直播类型：',
                    dataIndex: 'liveType',
                },
                {
                    header: '学车节liveId：',
                    dataIndex: 'xueche66LiveId'
                },
                {
                    header: '直播方式：',
                    dataIndex: 'liveMode',
                },
                {
                    header: '是否显示挽留弹窗：',
                    dataIndex: 'detention',
                    render: function (data) {
                        return radiooMap[data]
                    },
                },
                {
                    header: '启用详情介绍：',
                    dataIndex: 'descEnable',
                    render: function (data) {
                        return enableMap[data]
                    }
                },
                {
                    header: '详情介绍：',
                    dataIndex: 'desc',
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(lineData.desc)
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-item-tpl/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
