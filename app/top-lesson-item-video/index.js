/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var enableStore = [
        {
            key: false,
            value: '停用'
        }, {
            key: true,
            value: '启用'
        }
    ]
    var enableMap = Tools.getMapfromArray(enableStore);

    var multiDel = function (table, dom, config, selectedArr) {
        if (selectedArr.length > 0) {
            Widgets.dialog.confirm('确认删除吗？', function (ev, status) {
                if (status) {
                    Store(['jiakao-misc!top-lesson-item/data/delBatch'], [{
                        params: {
                            ids: selectedArr.join(',')
                        }
                    }]).save().done(function (store) {
                        table.render();
                    }).fail(function () {
                    });
                }
            })
        } else {
            Widgets.dialog.alert("请选择数据")
        }
    }
    var downBatch = function (table, dom, config, selectedArr) {
        if (selectedArr.length > 0) {
            Widgets.dialog.confirm('确认下线吗？', function (ev, status) {
                if (status) {
                    Store(['jiakao-misc!top-lesson-item/data/adds'], [{
                        aliases: 'list',
                        params: {
                            publish: false,
                            ids: selectedArr.join(',')
                        }
                    }]).save().done(function (store) {
                        table.render();
                    }).fail(function () {
                    });
                }
            })
        } else {
            Widgets.dialog.alert("请选择数据")
        }
    }
    var upBatch = function (table, dom, config, selectedArr) {
        if (selectedArr.length > 0) {
            Widgets.dialog.confirm('确认上线吗？', function (ev, status) {
                if (status) {
                    Store(['jiakao-misc!top-lesson-item/data/adds'], [{
                        aliases: 'list',
                        params: {
                            publish: true,
                            ids: selectedArr.join(',')
                        }
                    }]).save().done(function (store) {
                        table.render();
                    }).fail(function () {
                    });
                }
            })
        } else {
            Widgets.dialog.alert("请选择数据")
        }
    }
    var submitHandler = function (form) {
        var detail = $(form).find('[name="editorValue"]').val();

        return {
            desc: detail
        };
    }

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '语言',
                dataIndex: 'languageType',
                xtype: 'select',
                store: Constants.languageTypeStore
            },
            {
                header: 'groupId：',
                dataIndex: 'groupId',
                xtype: 'text',
                check: 'required',
                placeholder: 'groupId'
            },
            {
                header: '封面图：',
                dataIndex: 'cover',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'cover',
                    uploadIndex: 'cover',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '分享图片：',
                dataIndex: 'shareImage',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'shareImage',
                    uploadIndex: 'shareImage',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '列表图标：',
                dataIndex: 'icon',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'icon',
                    uploadIndex: 'icon',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '标题：',
                dataIndex: 'title',
                xtype: 'text',
                maxlength: 45,
                placeholder: '标题'
            },
            {
                header: '副标题：',
                dataIndex: 'subTitle',
                xtype: 'text',
                maxlength: 45,
                placeholder: '副标题'
            },
            {
                header: '讲师：',
                dataIndex: 'teacherId',
                xtype: 'select',
                store: 'jiakao-misc!top-lesson-teacher/data/list?limit=10000',
                index: {
                    key: 'id',
                    value: 'name'
                }
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                check: 'required',
                store: Constants.kemuStore,
            },
            {
                header: '横竖屏：',
                dataIndex: 'orientation',
                xtype: 'select',
                check: 'required',
                store: Constants.orientationStore,
            },
            {
                header: '试题列表：',
                xtype: 'text',
                placeholder: '试题列表',
                dataIndex: 'questionList'
            },
            {
                header: '专享标签：',
                dataIndex: 'label',
                xtype: 'select',
                store: [
                    {
                        key: '限时免费',
                        value: '限时免费'
                    },
                    {
                        key: 'VIP专享',
                        value: 'VIP专享'
                    }
                ]
            },
            {
                header: '展示顺序：',
                dataIndex: 'order',
                xtype: 'text',
                check: 'required',
                placeholder: '展示顺序'
            },
            {
                header: '标清视频：',
                dataIndex: 'videoL',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '标清视频'
            },
            {
                header: '高清视频：',
                dataIndex: 'videoM',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '高清视频'
            },
            {
                header: '超清视频：',
                dataIndex: 'videoH',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '超清视频'
            },
            {
                header: '标清加密视频：',
                dataIndex: 'videoEncodeL',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '标清加密视频'
            },
            {
                header: '高清加密视频：',
                dataIndex: 'videoEncodeM',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '高清加密视频'
            },
            {
                header: '超清加密视频：',
                dataIndex: 'videoEncodeH',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '超清加密视频'
            },
            {
                header: '启用详情介绍：',
                dataIndex: 'descEnable',
                xtype: 'radio',
                store: enableStore
            },
            {
                header: '详情介绍：',
                dataIndex: 'desc',
                xtype: Plugin('jiakao-misc!rich-text', {
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 300,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                    }
                }, function () {
                    console.log(arguments)
                })
            }
        ])
    }

    var add = function (table, lineData) {
        if (!lineData.id) {
            lineData = {}
            lineData.cover = ''
            lineData.shareImage = ''
            lineData.icon = ''
        }
        Table().edit(lineData, {
            title: '添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-item/data/insert?type=1',
            success: function (obj, dialog, ret) {
                dialog.close();
                var itemId = lineData.id
                if (itemId) {
                    var advertId
                    var lessonItemId = ret.id
                    Store(['jiakao-misc!top-lesson-item/data/view?id=' + itemId,]).load()
                        .done(function (store, data, obj, ret) {
                            var lineData = data['top-lesson-item'].data.view.data
                            advertId = lineData.advertId
                            if (advertId) {
                                Store(['jiakao-misc!top-lesson-advert/data/view?id=' + advertId,]).load()
                                    .done(function (store, data, obj, ret) {
                                        var lineData = data['top-lesson-advert'].data.view.data
                                        lineData.lessonItemId = lessonItemId
                                        lineData.beginTime = ''
                                        lineData.endTime = ''

                                        require(['jiakao-misc!app/top-lesson-advert/index'], function (Item) {
                                            Item.add(table, lineData)
                                        })
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message)
                                    });
                            } else {
                                table.render();
                            }
                        }).fail(function (ret) {
                            Widgets.dialog.alert(ret.message)
                        });
                } else {
                    table.render();
                }
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        });
    }

    var list = function (panel, aside) {
        Table({
            description: '名师精品子课程视频列表',
            title: '名师精品子课程视频列表',
            search: [
                {
                    placeholder: '子课程ID',
                    dataIndex: 'id',
                    xtype: 'text'
                },
                {
                    placeholder: '标题',
                    dataIndex: 'title',
                    xtype: 'text'
                },
                {
                    placeholder: 'groupId',
                    dataIndex: 'groupId',
                    xtype: 'text'
                },
                {
                    dataIndex: 'languageType',
                    xtype: 'select',
                    store: [{ key: '', value: '请选择语言' }].concat(Constants.languageTypeStore)
                },
                {
                    dataIndex: 'label',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '选择专属标签'
                        },
                        {
                            key: '限时免费',
                            value: '限时免费'
                        },
                        {
                            key: 'VIP专享',
                            value: 'VIP专享'
                        }
                    ]
                },
                // {
                //     dataIndex: 'type',
                //     xtype: 'select',
                //     store: [
                //         {
                //             key: '',
                //             value: "选择类型"
                //         },
                //         {
                //             key: '1',
                //             value: "视频"
                //         },
                //         {
                //             key: '2',
                //             value: "直播"
                //         }
                //     ],
                //     placeholder: '课程类型'
                // },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: "选择状态"
                        },
                        {
                            key: 2,
                            value: '上线'
                        },
                        {
                            key: 0,
                            value: '下线'
                        }
                    ]
                }
            ],
            selector: {
                dataIndex: 'id',
            },
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }, {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }, {
                        name: '批量上线',
                        class: 'warning',
                        click: upBatch
                    }, {
                        name: '批量下线',
                        class: 'danger',
                        click: downBatch
                    }, {
                        name: '批量定时上下线',
                        class: 'warning',
                        click: function (table, dom, config, selectedArr) {
                            if (selectedArr.length > 0) {
                                Table().edit({}, {
                                    title: '定时上下线',
                                    width: 700,
                                    store: 'jiakao-misc!top-lesson-item/data/lessonOnOffBatch?lessonIds=' + selectedArr.join(','),
                                    success: function (obj, dialog) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [
                                        {
                                            header: '上架时间：',
                                            dataIndex: 'onTime',
                                            xtype: 'datetime',
                                            placeholder: '上架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '下架时间：',
                                            dataIndex: 'offTime',
                                            xtype: 'datetime',
                                            placeholder: '下架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '是否启用：',
                                            dataIndex: 'enable',
                                            xtype: 'radio',
                                            store: [
                                                {
                                                    key: 0,
                                                    value: '否'
                                                }, {
                                                    key: 1,
                                                    value: '是'
                                                }
                                            ]
                                        },
                                    ]
                                });
                            } else {
                                Widgets.dialog.alert("请选择数据")
                            }
                        }
                    }, {
                        name: '批量删除',
                        class: 'danger',
                        click: multiDel
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-item/data/view',
                        save: 'jiakao-misc!top-lesson-item/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '复制',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        add(table, lineData)
                    }
                },
                {
                    name: '定时上下线',
                    class: 'warning',
                    click: function (table, dom, lineData) {
                        var lessonItemId = lineData.id

                        Store(['jiakao-misc!top-lesson-item/data/lessonOnOff']).load([{
                            params: {
                                lessonId: lessonItemId
                            }
                        }])
                            .done(function (store, data) {
                                var lineData = data['top-lesson-item'].data.lessonOnOff.data
                                Table().edit(lineData || {}, {
                                    title: '定时上下线',
                                    width: 700,
                                    store: 'jiakao-misc!top-lesson-item/data/lessonOnOff?lessonId=' + lessonItemId,
                                    success: function (obj, dialog) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [
                                        {
                                            header: '上架时间：',
                                            dataIndex: 'onTime',
                                            xtype: 'datetime',
                                            placeholder: '上架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '下架时间：',
                                            dataIndex: 'offTime',
                                            xtype: 'datetime',
                                            placeholder: '下架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '是否启用：',
                                            dataIndex: 'enable',
                                            xtype: 'radio',
                                            store: [
                                                {
                                                    key: 0,
                                                    value: '否'
                                                }, {
                                                    key: 1,
                                                    value: '是'
                                                }
                                            ]
                                        },
                                    ]
                                });
                            }).fail(function (ret) {
                                Widgets.dialog.alert(ret.message)
                            });
                    }
                },
                {
                    name: '上线',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].status == 0 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认上线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/updateStatus?id=' + lineData.id + '&status=2']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '下线',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].status == 2 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认下线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/updateStatus?id=' + lineData.id + '&status=0']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '推荐',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].recommend == 0 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认推荐吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/update?id=' + lineData.id + '&recommend=true']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '取消推荐',
                    class: 'default',
                    render: function (name, arr, i) {
                        return arr[i].recommend == 1 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认取消推荐吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/update?id=' + lineData.id + '&recommend=false']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '广告配置',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('top-lesson-advert-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'top-lesson-advert-' + lineData.id,
                                    name: '广告配置'
                                })
                            }
                            require(['jiakao-misc!app/top-lesson-advert/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                // {
                //     name: '删除',
                //     class: 'danger',
                //     xtype: 'delete',
                //     store: 'jiakao-misc!top-lesson-item/data/delete'
                // },
                {
                    name: '消息列表',
                    class: 'info',
                    click: function (table, row, lineData) {
                        var nameStr = '视频' + lineData.title;
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('message-room-' + lineData.barrageRoomId);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'message-room-' + lineData.barrageRoomId,
                                    name: nameStr + ' 消息列表'
                                })
                            }
                            require(['danmu!app/message/index'], function (Item) {
                                Item.list(nPanel, null, lineData.barrageRoomId, nameStr + ' ')
                            })
                        });
                    }
                },
                {
                    name: '实时弹幕',
                    class: 'info',
                    click: function (table, row, lineData) {
                        var status;
                        var nowTime = new Date().getTime();

                        if (nowTime < lineData.liveBeginTime) {
                            status = 0
                        } else if (nowTime > lineData.liveBeginTime && nowTime < lineData.liveEndTime) {
                            status = 1
                        } else {
                            status = 2
                        }
                        $(`[data-item='message-room-2${lineData.barrageRoomId}']`).find('span').click();

                        var nameStr = '视频' + lineData.title;

                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('message-room-2' + lineData.barrageRoomId);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'message-room-2' + lineData.barrageRoomId,
                                    name: nameStr + ' 消息列表'
                                })
                            }

                            require(['danmu!app/message2/index'], function (Item) {
                                console.log("lineData.id", lineData.id)
                                Item.list(nPanel, null, lineData.barrageRoomId, nameStr + ' ', status, lineData.id)
                            })
                        });
                    }
                },
                {
                    name: '课件',
                    class: 'info',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('lesson-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'lesson-' + lineData.id,
                                    name: '课件'
                                })
                            };
                            require(['jiakao-misc!app/top-lesson-courseware/index'], function (Item) {
                                Item.list(nPanel, lineData.id)
                            });
                        });
                    }
                },
                {
                    name: '关键点管理',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('lesson-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'lesson-' + lineData.id,
                                    name: '配置'
                                })
                            };
                            require(['jiakao-misc!app/timeline-config/index'], function (Item) {
                                Item.initList(nPanel, lineData.id)
                            });
                        });
                    }

                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'groupId',
                    dataIndex: 'groupId',
                    render: function (data) {
                        return `<a>${data}</a>`
                    },
                    click: function (table, rows, lineData) {
                        require(['simple!app/layout/main', 'jiakao-misc!app/top-lesson-group/index'], function (main, topLessonGroup) {
                            //获取面板
                            var nPanel = main.panel('top-lesson-group-id-' + lineData.groupId);

                            if (nPanel.length == 0) {
                                //创建面板
                                nPanel = main.panel({
                                    id: 'top-lesson-group-id-' + lineData.groupId,
                                    name: '名师精品课堂-id-' + lineData.groupId
                                });
                                topLessonGroup.list(nPanel, aside, lineData.groupId)
                            }
                        })
                    }
                },
                {
                    header: '封面图',
                    dataIndex: 'cover',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.cover}">`)
                    }
                },
                {
                    header: '分享图片',
                    dataIndex: 'shareImage',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.shareImage}">`)
                    }
                },
                {
                    header: '列表图标',
                    dataIndex: 'icon',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.icon}">`)
                    }
                },
                {
                    header: '标题',
                    dataIndex: 'title',
                },
                {
                    header: '副标题：',
                    dataIndex: 'subTitle',
                },
                {
                    header: '语言',
                    dataIndex: 'languageType',
                    render: function (data) {
                        return Constants.languageTypeMap[data]
                    }
                },
                {
                    header: '标清视频',
                    dataIndex: 'videoL',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.videoL + '" target="_blank">' + lineData.videoL + '</a><br><video src="' + lineData.videoL + '" controls></video>')
                        })
                    }
                },
                {
                    header: '高清视频',
                    dataIndex: 'videoM',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.videoM + '" target="_blank">' + lineData.videoM + '</a><br><video src="' + lineData.videoM + '" controls></video>')
                        })
                    }
                },
                {
                    header: '超清视频',
                    dataIndex: 'videoH',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.videoH + '" target="_blank">' + lineData.videoH + '</a><br><video src="' + lineData.videoH + '" controls></video>')
                        })
                    }
                },
                {
                    header: '标清加密视频',
                    dataIndex: 'videoEncodeL',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                            $(dialog.body).html(lineData.videoEncodeL)
                        })
                    }
                },
                {
                    header: '高清加密视频',
                    dataIndex: 'videoEncodeM',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                            $(dialog.body).html(lineData.videoEncodeM)
                        })
                    }
                },
                {
                    header: '超清加密视频',
                    dataIndex: 'videoEncodeH',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                            $(dialog.body).html(lineData.videoEncodeH)
                        })
                    }
                },
                {
                    header: '老师名称',
                    dataIndex: 'teacherName'
                },
                {
                    header: '老师头像',
                    dataIndex: 'teacherIcon',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.teacherIcon}">`)
                    }
                },
                {
                    header: '试题列表',
                    dataIndex: 'questionList',
                    render: function (data) {
                        try {
                            if (data) {
                                var showData = data.split(',').slice(0, 10);
                                return '<div style="width: 150px;word-break: break-all;">' + showData.join(',') + '...</div><a>查看全部</a>'
                            }
                        } catch (e) {

                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert('<div>' + lineData.questionList + '</div>')
                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '横竖屏',
                    dataIndex: 'orientation',
                    render: function (data, arr, i) {
                        return Constants.orientationMap[data]
                    }
                },
                {
                    header: '专享标签',
                    dataIndex: 'label'
                },
                {
                    header: '展示顺序',
                    dataIndex: 'order'
                },
                {
                    header: '启用详情介绍',
                    dataIndex: 'descEnable',
                    render: function (data, arr, i) {
                        return enableMap[data]
                    }
                },
                {
                    header: '详情介绍',
                    dataIndex: 'desc',
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert('<pre>' + lineData.desc + '</pre>')
                    }
                },
                {
                    header: '观看人数',
                    dataIndex: 'viewCount'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return data == 2 ? '上线' : '下线'
                    }
                },
                {
                    header: '直播间ID',
                    dataIndex: 'barrageRoomId'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-item/data/list?type=1'], panel, null).render();
    }

    return {
        list: list
    }
});
