/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var enableStore = [
        {
            key: false,
            value: '停用'
        }, {
            key: true,
            value: '启用'
        }
    ]
    var enableMap = Tools.getMapfromArray(enableStore);

    var liveModeStore = [
        {
            key: '1',
            value: "直播"
        }, {
            key: '2',
            value: "录播"
        }
    ]
    var liveModeMap = Tools.getMapfromArray(liveModeStore);

    var playStatusStore = [
        {
            key: '1',
            value: "正常"
        }, {
            key: '2',
            value: "网络异常"
        }
    ]
    var playStatusMap = Tools.getMapfromArray(playStatusStore);

    //0-未处理;1-处理中;2-上传完成;3-上传失败;4-转码完成;5-转码失败
    var dealBackStatusMap = {
        "-1": "排队等待处理",
        0: "未处理",
        1: "处理中",
        2: "上传成功",
        3: "上传失败",
        4: "转码成功",
        5: "转码失败",
        6: "转储中",
        7: "处理成功",
        8: "处理失败",
    };
    var multiDel = function (table, dom, config, selectedArr) {
        if (selectedArr.length > 0) {
            Widgets.dialog.confirm('确认删除吗？', function (ev, status) {
                if (status) {
                    Store(['jiakao-misc!top-lesson-item/data/delBatch'], [{
                        params: {
                            ids: selectedArr.join(',')
                        }
                    }]).save().done(function (store) {
                        table.render();
                    }).fail(function () {
                    });
                }
            })
        } else {
            Widgets.dialog.alert("请选择数据")
        }
    }
    var downBatch = function (table, dom, config, selectedArr) {
        if (selectedArr.length > 0) {
            Widgets.dialog.confirm('确认下线吗？', function (ev, status) {
                if (status) {
                    Store(['jiakao-misc!top-lesson-item/data/adds'], [{
                        aliases: 'list',
                        params: {
                            publish: false,
                            ids: selectedArr.join(',')
                        }
                    }]).save().done(function (store) {
                        table.render();
                    }).fail(function () {
                    });
                }
            })
        } else {
            Widgets.dialog.alert("请选择数据")
        }
    }
    var upBatch = function (table, dom, config, selectedArr) {
        if (selectedArr.length > 0) {
            Widgets.dialog.confirm('确认上线吗？', function (ev, status) {
                if (status) {
                    Store(['jiakao-misc!top-lesson-item/data/adds'], [{
                        aliases: 'list',
                        params: {
                            publish: true,
                            ids: selectedArr.join(',')
                        }
                    }]).save().done(function (store) {
                        table.render();
                    }).fail(function () {
                    });
                }
            })
        } else {
            Widgets.dialog.alert("请选择数据")
        }
    }
    var upload = function (table) {
        Table({
            description: '注意:上传文件只能是xls或者xlsx.不能直接修改文件后缀的方式上传.',
            title: '上传子课程',
            width: 500,
            store: 'jiakao-misc!top-lesson-item/data/batchUpload',
            success: function (obj, dialog, responseData, response) {
                if (response.success) {
                    Widgets.dialog.html('上传结果', { width: 1000 }).done(function (dialog) {
                        var importResult = response.data;
                        var message = "上传数据共:" + importResult.total + "条,";
                        if (importResult.error > 0) {
                            message += "失败:" + importResult.error + "条.\r\n"
                        } else {
                            message += "导入成功:" + importResult.success + "条.\r\n"
                        }
                        var array = importResult.errorList;
                        for (var key in array) {
                            var data = array[key];
                            message = message + data.order + ',' + data.message + '\r\n';
                        }
                        $(dialog.body).append('<pre style="width=97%">' + message + '</pre>');
                    });
                    dialog.close();
                    table.render();
                } else {
                    Widgets.dialog.alert(arguments[3].message);
                    dialog.close();
                }
            },
            fail: function (obj, dialog) {
                Widgets.dialog.alert(arguments[3].message);
            },
            columns: [
                {
                    header: '文件',
                    dataIndex: 'data',
                    xtype: 'file',
                    check: "required",
                    placeholder: '请选择Excel文件'
                },
                {
                    header: '数据模板：',
                    dataIndex: 'templates',
                    xtype: function () {
                        let url = window.j.root['jiakao-misc'] + '/top-lesson-item-template.xlsx'
                        return `<a class="btn btn-info btn-sm" href="${url}" target="_blank" download>点击下载模板</a>`
                    }
                },
            ],
            renderAfter: function (table, dom) {

            }
        }).add();
    };
    var submitHandler = function (form, formSubmit) {
        var liveBeginTime = $(form).find('#liveBeginTime').val().slice(0, -2) + '00';
        var liveEndTime = $(form).find('#liveEndTime').val().slice(0, -2) + '00';
        var detail = $(form).find('[name="editorValue"]').val();
        $(form).find('#liveBeginTime').attr("name", "")
        $(form).find('#liveEndTime').attr("name", "")

        Widgets.dialog.html('请在提交时确认', {
            width: 400,
            buttons: [{
                name: '确认',
                xtype: 'success',
                click: function () {
                    formSubmit({
                        liveBeginTime: new Date(liveBeginTime).getTime(),
                        liveEndTime: new Date(liveEndTime).getTime(),
                        desc: detail
                    })
                    this.close();
                }
            }, {
                name: '取消',
                xtype: 'primary',
                click: function () {
                    this.close();
                }
            }]
        }).done(function (dialog) {
            var playStatus = $(form).find('[name="playStatus"]').val();
            var liveMode = $(form).find('[name="liveMode"]').val();
            playStatus = playStatusMap[playStatus];
            liveMode = liveModeMap[liveMode];
            $(dialog.body).html(`<p>播放状态：${playStatus}</p><p>直播方式：${liveMode}</p>`)
        })
    }

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: 'groupId：',
                dataIndex: 'groupId',
                xtype: 'text',
                check: 'required',
                placeholder: 'groupId'
            },
            {
                header: '封面图：',
                dataIndex: 'cover',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'cover',
                    uploadIndex: 'cover',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '分享图片：',
                dataIndex: 'shareImage',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'shareImage',
                    uploadIndex: 'shareImage',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '列表图标：',
                dataIndex: 'icon',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'icon',
                    uploadIndex: 'icon',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '标题：',
                dataIndex: 'title',
                xtype: 'text',
                maxlength: 45,
                placeholder: '标题'
            },
            {
                header: '副标题：',
                dataIndex: 'subTitle',
                xtype: 'text',
                maxlength: 45,
                placeholder: '副标题'
            },
            {
                header: '讲师：',
                dataIndex: 'teacherId',
                xtype: 'select',
                store: 'jiakao-misc!top-lesson-teacher/data/list?limit=10000',
                index: {
                    key: 'id',
                    value: 'name'
                }
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                store: Constants.kemuStore,
                xtype: 'select',
                check: 'required',
            },
            {
                header: '专享标签：',
                dataIndex: 'label',
                xtype: 'select',
                store: [
                    {
                        key: '限时免费',
                        value: '限时免费'
                    },
                    {
                        key: 'VIP专享',
                        value: 'VIP专享'
                    }
                ]
            },
            {
                header: '展示顺序：',
                dataIndex: 'order',
                xtype: 'text',
                check: 'required',
                placeholder: '展示顺序'
            },
            {
                header: '直播类型：',
                dataIndex: 'roomType',
                xtype: 'select',
                store: [{
                    key: '1',
                    value: "常规直播"
                }, {
                    key: '2',
                    value: "66学车节"
                }, {
                    key: '3',
                    value: "公共直播"
                }, {
                    key: '4',
                    value: "会员直播"
                }]
            },
            {
                header: '学车节liveId：',
                dataIndex: 'xueche66LiveId',
                xtype: 'text',
                placeholder: '学车节liveId'
            },
            {
                header: '直播方式：',
                dataIndex: 'liveMode',
                xtype: 'select',
                store: liveModeStore,
            },
            {
                header: '直播开始时间：',
                dataIndex: 'liveBeginTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '直播开始时间'
            },
            {
                header: '直播结束时间：',
                dataIndex: 'liveEndTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '直播结束时间'
            },
            {
                header: '是否显示挽留弹窗：',
                dataIndex: 'detention',
                xtype: 'radio',
                store: [
                    {
                        key: 0,
                        value: '否'
                    }, {
                        key: 1,
                        value: '是'
                    }
                ]
            },
            {
                header: '直播地址：',
                xtype: 'text',
                placeholder: '直播地址',
                dataIndex: 'liveRoomUrl'
            },
            {
                header: '直播Token：',
                xtype: 'text',
                placeholder: '直播Token',
                dataIndex: 'liveToken'
            },
            {
                header: '定时评论模板：',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!top-lesson-item/data/templateList',
                    placeholder: '定时评论模板',
                    dataIndex: 'msgTemplateId',
                    index: {
                        key: 'templateId',
                        value: 'templateName',
                        search: 'templateId'
                    },
                    isMulti: false,
                }, function (plugin, value) {
                }),
                dataIndex: 'msgTemplateId'
            },
            {
                header: '推荐商品弹窗模板：',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: 'jiakao-misc!top-lesson-recommend-goods-tpl/data/templateList',
                    placeholder: '推荐商品弹窗模板',
                    dataIndex: 'recommendTemplateId',
                    index: {
                        key: 'templateId',
                        value: 'templateName',
                        search: 'templateId'
                    },
                    isMulti: false,
                }, function (plugin, value) {
                }),
                dataIndex: 'recommendTemplateId'
            },
            {
                header: '播放状态：',
                dataIndex: 'playStatus',
                xtype: 'select',
                store: playStatusStore,
            },
            {
                header: '启用详情介绍：',
                dataIndex: 'descEnable',
                xtype: 'radio',
                store: enableStore
            },
            {
                header: '详情介绍：',
                dataIndex: 'desc',
                xtype: Plugin('jiakao-misc!rich-text', {
                    bucket: "jiakao-web",
                    editorConfig: {
                        initialFrameWidth: "99.7%",
                        initialFrameHeight: 300,
                        autoClearinitialContent: false,
                        wordCount: false,
                        elementPathEnabled: false,
                        autoFloatEnabled: false,
                    }
                }, function () {
                    console.log(arguments)
                })
            }
        ])
    }

    var add = function (table, lineData) {
        if (!lineData.id) {
            lineData = {}
            lineData.cover = ''
            lineData.shareImage = ''
            lineData.icon = ''
        }
        Table().edit(lineData, {
            title: '添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-item/data/insert?type=2',
            success: function (obj, dialog, ret) {
                dialog.close();
                var itemId = lineData.id
                if (itemId) {
                    var advertId
                    var lessonItemId = ret.id
                    Store(['jiakao-misc!top-lesson-item/data/view?id=' + itemId,]).load()
                        .done(function (store, data, obj, ret) {
                            var lineData = data['top-lesson-item'].data.view.data
                            advertId = lineData.advertId
                            if (advertId) {
                                Store(['jiakao-misc!top-lesson-advert/data/view?id=' + advertId,]).load()
                                    .done(function (store, data, obj, ret) {
                                        var lineData = data['top-lesson-advert'].data.view.data
                                        lineData.lessonItemId = lessonItemId
                                        lineData.beginTime = ''
                                        lineData.endTime = ''
                                        lineData.purchaseItemId = ''

                                        require(['jiakao-misc!app/top-lesson-advert/index'], function (Item) {
                                            Item.add(table, lineData)
                                        })
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message)
                                    });
                            } else {
                                table.render();
                            }
                        }).fail(function (ret) {
                            Widgets.dialog.alert(ret.message)
                        });
                } else {
                    table.render();
                }
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        });
    }

    var list = function (panel, aside) {
        Table({
            description: '名师精品子课程直播列表',
            title: '名师精品子课程直播列表',
            search: [
                {
                    placeholder: '子课程ID',
                    dataIndex: 'id',
                    xtype: 'text'
                },
                {
                    placeholder: '标题',
                    dataIndex: 'title',
                    xtype: 'text'
                },
                {
                    placeholder: 'groupId',
                    dataIndex: 'groupId',
                    xtype: 'text'
                },
                {
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore
                },
                {
                    dataIndex: 'label',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '选择专属标签'
                        },
                        {
                            key: '限时免费',
                            value: '限时免费'
                        },
                        {
                            key: 'VIP专享',
                            value: 'VIP专享'
                        }
                    ]
                },
                {
                    dataIndex: 'roomType',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '选择直播类型'
                        }, {
                            key: '1',
                            value: "常规直播"
                        }, {
                            key: '2',
                            value: "66学车节"
                        }, {
                            key: '3',
                            value: "公共直播"
                        }, {
                            key: '4',
                            value: "会员直播"
                        }
                    ]
                },
                // {
                //     dataIndex: 'type',
                //     xtype: 'select',
                //     store: [
                //         {
                //             key: '',
                //             value: "选择类型"
                //         },
                //         {
                //             key: '1',
                //             value: "视频"
                //         },
                //         {
                //             key: '2',
                //             value: "直播"
                //         }
                //     ],
                //     placeholder: '课程类型'
                // },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: "选择状态"
                        },
                        {
                            key: 2,
                            value: '上线'
                        },
                        {
                            key: 0,
                            value: '下线'
                        }
                    ]
                }
            ],
            selector: {
                dataIndex: 'id',
            },
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }, {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }, {
                        name: '批量导入',
                        class: 'primary',
                        click: upload
                    }, {
                        name: '批量上线',
                        class: 'warning',
                        click: upBatch
                    }, {
                        name: '批量下线',
                        class: 'danger',
                        click: downBatch
                    }, {
                        name: '批量定时上下线',
                        class: 'warning',
                        click: function (table, dom, config, selectedArr) {
                            if (selectedArr.length > 0) {
                                Table().edit({}, {
                                    title: '定时上下线',
                                    width: 700,
                                    store: 'jiakao-misc!top-lesson-item/data/lessonOnOffBatch?lessonIds=' + selectedArr.join(','),
                                    success: function (obj, dialog) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [
                                        {
                                            header: '上架时间：',
                                            dataIndex: 'onTime',
                                            xtype: 'datetime',
                                            placeholder: '上架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '下架时间：',
                                            dataIndex: 'offTime',
                                            xtype: 'datetime',
                                            placeholder: '下架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '是否启用：',
                                            dataIndex: 'enable',
                                            xtype: 'radio',
                                            store: [
                                                {
                                                    key: 0,
                                                    value: '否'
                                                }, {
                                                    key: 1,
                                                    value: '是'
                                                }
                                            ]
                                        },
                                    ]
                                });
                            } else {
                                Widgets.dialog.alert("请选择数据")
                            }
                        }
                    }, {
                        name: '批量删除',
                        class: 'danger',
                        click: multiDel
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-item/data/view',
                        save: 'jiakao-misc!top-lesson-item/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '复制',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        lineData.liveRoomUrl = ''
                        lineData.liveBeginTime = ''
                        lineData.liveEndTime = ''
                        add(table, lineData)
                    }
                },
                {
                    name: '定时上下线',
                    class: 'warning',
                    click: function (table, dom, lineData) {
                        var lessonItemId = lineData.id

                        Store(['jiakao-misc!top-lesson-item/data/lessonOnOff']).load([{
                            params: {
                                lessonId: lessonItemId
                            }
                        }])
                            .done(function (store, data) {
                                var lineData = data['top-lesson-item'].data.lessonOnOff.data
                                Table().edit(lineData || {}, {
                                    title: '定时上下线',
                                    width: 700,
                                    store: 'jiakao-misc!top-lesson-item/data/lessonOnOff?lessonId=' + lessonItemId,
                                    success: function (obj, dialog) {
                                        dialog.close();
                                        table.render();
                                    },
                                    columns: [
                                        {
                                            header: '上架时间：',
                                            dataIndex: 'onTime',
                                            xtype: 'datetime',
                                            placeholder: '上架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '下架时间：',
                                            dataIndex: 'offTime',
                                            xtype: 'datetime',
                                            placeholder: '下架时间',
                                            render: function (data) {
                                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                            }
                                        },
                                        {
                                            header: '是否启用：',
                                            dataIndex: 'enable',
                                            xtype: 'radio',
                                            store: [
                                                {
                                                    key: 0,
                                                    value: '否'
                                                }, {
                                                    key: 1,
                                                    value: '是'
                                                }
                                            ]
                                        },
                                    ]
                                });
                            }).fail(function (ret) {
                                Widgets.dialog.alert(ret.message)
                            });
                    }
                },
                {
                    name: '上线',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].status == 0 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认上线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/updateStatus?id=' + lineData.id + '&status=2']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '下线',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].status == 2 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认下线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/updateStatus?id=' + lineData.id + '&status=0']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '推荐',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].recommend == 0 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认推荐吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/update?id=' + lineData.id + '&recommend=true']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '取消推荐',
                    class: 'default',
                    render: function (name, arr, i) {
                        return arr[i].recommend == 1 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认取消推荐吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/update?id=' + lineData.id + '&recommend=false']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                // {
                //     name: '更新转码状态',
                //     class: 'success',
                //     render: function (name, arr, i) {
                //         return arr[i].dealBackStatus === 2 ? name : '';
                //     },
                //     click: function (table, dom, lineData) {
                //         Widgets.dialog.confirm('确认更新转码状态吗', function (e, stat) {
                //             if (stat) {
                //                 Store(['jiakao-misc!top-lesson-item/data/updateTranscodeStatus?id=' + lineData.id]).save().done(data => {
                //                     console.log(data)
                //                     table.render();
                //                 }).fail();
                //             }
                //         })
                //     }
                // },
                {
                    name: '处理回放',
                    class: 'success',
                    click: function (table, rows, lineData) {
                        Widgets.dialog.confirm('确定处理回放吗？', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-item/data/deal?id=' + lineData.id]).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    },
                    render: function (name, arr, i) {
                        var temp = [0, 2, 3, 5, 8]
                        return temp.includes(arr[i].dealBackStatus) ? name : '';
                    },
                },
                {
                    name: '运营位配置',
                    class: 'warning',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('top-lesson-advert-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'top-lesson-advert-' + lineData.id,
                                    name: '运营位配置'
                                })
                            }
                            require(['jiakao-misc!app/top-lesson-advert/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-item/data/delete'
                },
                {
                    name: '消息列表',
                    class: 'info',
                    click: function (table, row, lineData) {
                        var startTimeStr = Utils.format.date(lineData.liveBeginTime, 'yyyy-MM-dd HH:mm:ss');
                        var endTimeStr = Utils.format.date(lineData.liveEndTime, 'yyyy-MM-dd HH:mm:ss');
                        var nameStr = lineData.title + ' [ ' + startTimeStr + ' 至 ' + endTimeStr + ' ]';
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('message-room-' + lineData.barrageRoomId);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'message-room-' + lineData.barrageRoomId,
                                    name: nameStr + ' 消息列表'
                                })
                            }
                            require(['danmu!app/message/index'], function (Item) {
                                Item.list(nPanel, null, lineData.barrageRoomId, nameStr + ' ')
                            })
                        });
                    }
                },
                {
                    name: '实时弹幕',
                    class: 'info',
                    click: function (table, row, lineData) {
                        var status;
                        var nowTime = new Date().getTime();

                        if (nowTime < lineData.liveBeginTime) {
                            status = 0
                        } else if (nowTime > lineData.liveBeginTime && nowTime < lineData.liveEndTime) {
                            status = 1
                        } else {
                            status = 2
                        }
                        $(`[data-item='message-room-2${lineData.barrageRoomId}']`).find('span').click();

                        var startTimeStr = Utils.format.date(lineData.liveBeginTime, 'yyyy-MM-dd HH:mm:ss');
                        var endTimeStr = Utils.format.date(lineData.liveEndTime, 'yyyy-MM-dd HH:mm:ss');
                        var nameStr = lineData.title + ' [ ' + startTimeStr + ' 至 ' + endTimeStr + ' ]';

                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('message-room-2' + lineData.barrageRoomId);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'message-room-2' + lineData.barrageRoomId,
                                    name: nameStr + ' 消息列表'
                                })
                            }

                            require(['danmu!app/message2/index'], function (Item) {
                                console.log("lineData.id", lineData.id)
                                Item.list(nPanel, null, lineData.barrageRoomId, nameStr + ' ', status, lineData.id)
                            })
                        });
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'groupId',
                    dataIndex: 'groupId',
                    render: function (data) {
                        return `<a>${data}</a>`
                    },
                    click: function (table, rows, lineData) {
                        require(['simple!app/layout/main', 'jiakao-misc!app/top-lesson-group/index'], function (main, topLessonGroup) {
                            //获取面板
                            var nPanel = main.panel('top-lesson-group-id-' + lineData.groupId);

                            if (nPanel.length == 0) {
                                //创建面板
                                nPanel = main.panel({
                                    id: 'top-lesson-group-id-' + lineData.groupId,
                                    name: '名师精品课堂-id-' + lineData.groupId
                                });
                                topLessonGroup.list(nPanel, aside, lineData.groupId)
                            }
                        })
                    }
                },
                {
                    header: '封面图',
                    dataIndex: 'cover',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.cover}">`)
                    }
                },
                {
                    header: '分享图片',
                    dataIndex: 'shareImage',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.shareImage}">`)
                    }
                },
                {
                    header: '列表图标',
                    dataIndex: 'icon',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.icon}">`)
                    }
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '副标题：',
                    dataIndex: 'subTitle',
                },
                {
                    header: '老师名称',
                    dataIndex: 'teacherName'
                },
                {
                    header: '老师头像',
                    dataIndex: 'teacherIcon',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>' + '<img src="' + data + '">' + '</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.teacherIcon}">`)
                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '专享标签',
                    dataIndex: 'label'
                },
                {
                    header: '展示顺序',
                    dataIndex: 'order'
                },
                {
                    header: '直播方式',
                    dataIndex: 'liveMode',
                    render: function (data) {
                        return liveModeMap[data];
                    },
                },
                {
                    header: '播放状态',
                    dataIndex: 'playStatus',
                    render: function (data) {
                        return playStatusMap[data];
                    },
                },
                {
                    header: '启用详情介绍',
                    dataIndex: 'descEnable',
                    render: function (data, arr, i) {
                        return enableMap[data]
                    }
                },
                {
                    header: '详情介绍',
                    dataIndex: 'desc',
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert('<pre>' + lineData.desc + '</pre>')
                    }
                },
                {
                    header: '观看人数',
                    dataIndex: 'viewCount'
                },
                {
                    header: '直播开始时间：',
                    dataIndex: 'liveBeginTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                },
                {
                    header: '直播结束时间：',
                    dataIndex: 'liveEndTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                },
                {
                    header: '是否显示挽留弹窗：',
                    dataIndex: 'detention',
                    render: function (data) {
                        return data == 1 ? '是' : '否'
                    }
                },
                {
                    header: '直播地址',
                    dataIndex: "liveRoomUrl",
                    render: function (data) {
                        return data ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(lineData.liveRoomUrl)
                    }
                },
                {
                    header: '直播Token',
                    dataIndex: 'liveToken',
                    render: function (data) {
                        return '<div style="max-width: 160px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return data == 2 ? '上线' : '下线'
                    }
                },
                {
                    header: '回放处理状态',
                    dataIndex: 'dealBackStatus',
                    render: function (data) {
                        return dealBackStatusMap[data]
                    }
                },
                {
                    header: '回放视频ID',
                    dataIndex: 'videoId',
                    render: function (data) {
                        return '<div style="max-width: 160px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '直播间ID',
                    dataIndex: 'barrageRoomId'
                },
                {
                    header: '回放视频地址',
                    dataIndex: 'playbackUrl',
                    render: function (name) {
                        return name ? '<a>查看</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<a href="${lineData.playbackUrl}" target="_blank">${lineData.playbackUrl}</a>`)
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-item/data/list?type=2'], panel, null).render();
    }

    return {
        list: list
    }
});
