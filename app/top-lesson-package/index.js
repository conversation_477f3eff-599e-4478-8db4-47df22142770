/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var add = function (table) {
        Table({
            title: '添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-package/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '选择课程：',
                    dataIndex: 'lessonIds',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!top-lesson-group/data/list?limit=1000000',
                        placeholder: '选择课程',
                        dataIndex: 'lessonIds',
                        index: {
                            key: 'id',
                            value: 'title',
                            search: 'title'
                        },
                        isMulti: true,
                        defaultVal: false
                    }, function (plugin, value) {
                    })
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '标题'
                },
                {
                    header: '折扣数字：',
                    dataIndex: 'discount',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '折扣数字'
                },
                {
                    header: '商品的价格：',
                    dataIndex: 'price',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '商品的价格'
                },
                {
                    header: '苹果的价格：',
                    dataIndex: 'applePrice',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '苹果的价格'
                },
                {
                    header: '苹果价格的id：',
                    dataIndex: 'applePriceId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '苹果价格的id'
                },
                {
                    header: '商品的原始价格：',
                    dataIndex: 'originalPrice',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '商品的原始价格'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: Constants.carTypeStore,
                    check: 'required',
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore,
                    check: 'required',
                },
                {
                    header: '排序：',
                    dataIndex: 'order',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '排序'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '名师精品课程打包售卖列表',
            title: '名师精品课程打包售卖列表',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-package/data/view',
                        save: 'jiakao-misc!top-lesson-package/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '选择课程：',
                            dataIndex: 'lessonIds',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: 'jiakao-misc!top-lesson-group/data/list?limit=1000000',
                                placeholder: '选择课程',
                                dataIndex: 'lessonIds',
                                index: {
                                    key: 'id',
                                    value: 'title',
                                    search: 'title'
                                },
                                isMulti: true,
                                defaultVal: false
                            }, function (plugin, value) {
                            })
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '标题'
                        },
                        {
                            header: '折扣数字：',
                            dataIndex: 'discount',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '折扣数字'
                        },
                        {
                            header: '商品的价格：',
                            dataIndex: 'price',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '商品的价格'
                        },
                        {
                            header: '苹果的价格：',
                            dataIndex: 'applePrice',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '苹果的价格'
                        },
                        {
                            header: '苹果价格的id：',
                            dataIndex: 'applePriceId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '苹果价格的id'
                        },
                        {
                            header: '商品的原始价格：',
                            dataIndex: 'originalPrice',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '商品的原始价格'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType',
                            xtype: 'select',
                            store: Constants.carTypeStore,
                            check: 'required',
                        },
                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: Constants.kemuStore,
                            check: 'required',
                        },
                        {
                            header: '排序：',
                            dataIndex: 'order',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '排序'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-package/data/delete'
                },
                {
                    name: '上线',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].status == 0 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认上线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-package/data/update?id=' + lineData.id + '&status=2']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '下线',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].status == 2 ? name : '';

                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认下线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-package/data/update?id=' + lineData.id + '&status=0']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '课程id',
                    dataIndex: 'lessonGroupList',
                    render: function (data) {
                        var str ='';
                        for(var i=0;i<data.length; i++){
                            str += data[i].title + ' |<br/>';
                        }
                        return str;
                    }
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '折扣数字',
                    dataIndex: 'discount'
                },
                {
                    header: '商品的价格',
                    dataIndex: 'price'
                },
                {
                    header: '苹果的价格',
                    dataIndex: 'applePrice'
                },
                {
                    header: '苹果价格的id',
                    dataIndex: 'applePriceId'
                },
                {
                    header: '商品的原始价格',
                    dataIndex: 'originalPrice'
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '排序',
                    dataIndex: 'order'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        if(data ==2){
                            return '上线'
                        }else if(status== 0){
                            return ' 下线'
                        }
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }
            ]
        }, ['jiakao-misc!top-lesson-package/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
