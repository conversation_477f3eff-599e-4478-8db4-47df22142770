/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var statusStore = [
        {
            key: 0,
            value: '下线'
        },
        {
            key: 2,
            value: '上线'
        }
    ]
    var statusMap = Tools.getMapfromArray(statusStore);

    var popupTypeStore = [
        {
            key: 'normal',
            value: '普通跳转'
        }, {
            key: 'vip',
            value: 'vip售卖'
        }
    ]
    var popupTypeMap = Tools.getMapfromArray(popupTypeStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '语言',
                dataIndex: 'languageType',
                xtype: 'select',
                store: Constants.languageTypeStore
            },
            {
                header: '访问模式：',
                dataIndex: 'editionCode',
                xtype: 'select',
                check: 'required',
                store: [{
                    key: '101',
                    value:'普通模式'
                }, {
                    key: '102',
                    value:'长辈模式'
                }]
            },
            {
                header: '访问场景:',
                dataIndex: 'sceneCode',
                xtype: 'select',
                check: 'required',
                store: [{
                    key: '101',
                    value: '普通场景'
                }, {
                    key: '102',
                    value: '扣满12分'
                }]
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required',
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                check: 'required',
            },
            {
                header: '必学精选：',
                dataIndex: 'lessonIds',
                xtype: function() {
                    return '<div data-item="lessonIds"></div>'
                }
            },
            {
                header: '专项强化：',
                dataIndex: 'lessonStrengthenIds',
                xtype: function() {
                    return '<div data-item="lessonStrengthenIds"></div>'
                }
            },
            {
                header: '标题：',
                dataIndex: 'title',
                xtype: 'text',
                maxlength: 128,
                check: 'required',
                placeholder: '标题'
            },
            {
                header: '封面图：',
                dataIndex: 'cover',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'cover',
                    uploadIndex: 'cover',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
            
                })
            },
            {
                header: '标签：',
                dataIndex: 'tags',
                xtype: 'text',
                placeholder: '标签'
            },
            {
                header: '视频url：',
                dataIndex: 'videoUrl',
                xtype: 'text',
                placeholder: '视频url'
            },
            {
                header: '跳转详情url：',
                dataIndex: 'detailUrl',
                xtype: 'text',
                placeholder: '跳转详情url'
            },
            {
                header: '商品key：',
                dataIndex: 'groupKey',
                xtype: 'text',
                placeholder: '商品key'
            },
            {
                header: '相似商品key：',
                dataIndex: 'similarGroupKey',
                xtype: 'text',
                placeholder: '相似商品key'
            },
            {
                header: '立省购买标题：',
                dataIndex: 'buyTitle',
                xtype: 'text',
                maxlength: 128,
                check: 'required',
                placeholder: '立省购买标题'
            },
            {
                header: '立省购买副标题：',
                dataIndex: 'buySubTitle',
                xtype: 'text',
                maxlength: 128,
                check: 'required',
                placeholder: '立省购买副标题'
            },
            {
                header: 'VIP售卖入口图片：',
                dataIndex: 'sellImg',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'sellImg',
                    uploadIndex: 'sellImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                })
            },
            {
                header: 'VIP售卖入口角标文案：',
                dataIndex: 'sellCornerMark',
                xtype: 'text',
                placeholder: 'VIP售卖入口角标文案'
            },
            {
                header: '弹窗类型：',
                dataIndex: 'popupType',
                xtype: 'select',
                store: popupTypeStore,
                placeholder: '弹窗类型'
            },
            {
                header: '弹窗标题：',
                dataIndex: 'popupTitle',
                xtype: 'text',
                placeholder: '弹窗标题'
            },
            {
                header: '弹窗副标题：',
                dataIndex: 'popupSubTitle',
                xtype: 'text',
                placeholder: '弹窗副标题'
            },
            {
                header: '弹窗图片：',
                dataIndex: 'popupImg',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'popupImg',
                    uploadIndex: 'popupImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
            
                })
            },
            {
                header: '弹窗购买标签：',
                dataIndex: 'popupBuyTag',
                xtype: 'text',
                placeholder: '弹窗购买标签'
            }
        ])
    }

    function renderAfter(table, dom, lineData) {
        lineData = lineData.data || {}
        var kemuDom = dom.item('kemu');
        
        kemuDom.on('change', onChange);

        function onChange() {
            var kemu = kemuDom.val();
            Plugin('jiakao-misc!auto-prompt', {
                target: dom.item("lessonIds"),
                store: 'jiakao-misc!top-lesson-group/data/list?limit=1000000&kemu=' + kemu,
                placeholder: '请选择必学精选',
                dataIndex: 'lessonIds',
                index: {
                    key: 'id',
                    value: 'title',
                    search: 'title'
                },
                isMulti: true,
                defaultVal: false
            }, function (plugin, value) {
            }).render({
                value: lineData.lessonIds
            })
            Plugin('jiakao-misc!auto-prompt', {
                target: dom.item("lessonStrengthenIds"),
                store: 'jiakao-misc!top-lesson-group/data/list?limit=1000000&kemu=' + kemu,
                placeholder: '请选择专项强化',
                dataIndex: 'lessonStrengthenIds',
                index: {
                    key: 'id',
                    value: 'title',
                    search: 'title'
                },
                isMulti: true,
                defaultVal: false
            }, function (plugin, value) {
            }).render({
                value: lineData.lessonStrengthenIds
            })
        }

        onChange();
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!top-lesson-ranklist/data/insert?parentId=0',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: renderAfter,
            columns: columns()
        }).add();
    }
    var edit = function (table, row, lineData) {
        
        Store(['jiakao-misc!top-lesson-ranklist/data/getSubDetail?parentId=' + lineData.id]).load().done(function (store) {
            try {
                var data = store.data['top-lesson-ranklist'].data.getSubDetail.data;
            } catch (error) {
                
            }
            Table().edit(data || {}, {
                title: '默认弹窗',
                width: 500,
                store: 'jiakao-misc!top-lesson-ranklist/data/updateSubDetail?parentId=' + lineData.id,
                success: function (obj, dialog) {
                    dialog.close();
                    table.render();
                },
                columns: [
                    {
                        dataIndex: 'id',
                        xtype: 'hidden',
                    },
                    {
                        header: '跳转详情url：',
                        dataIndex: 'detailUrl',
                        xtype: 'text',
                        placeholder: '跳转详情url'
                    },
                    {
                        header: '商品key：',
                        dataIndex: 'groupKey',
                        xtype: 'text',
                        placeholder: '商品key'
                    },
                    {
                        header: '相似商品key：',
                        dataIndex: 'similarGroupKey',
                        xtype: 'text',
                        placeholder: '相似商品key'
                    },
                    {
                        header: '立省购买标题：',
                        dataIndex: 'buyTitle',
                        xtype: 'text',
                        maxlength: 128,
                        check: 'required',
                        placeholder: '立省购买标题'
                    },
                    {
                        header: '立省购买副标题：',
                        dataIndex: 'buySubTitle',
                        xtype: 'text',
                        maxlength: 128,
                        check: 'required',
                        placeholder: '立省购买副标题'
                    },
                    {
                        header: '弹窗类型：',
                        dataIndex: 'popupType',
                        xtype: 'select',
                        store: popupTypeStore,
                        placeholder: '弹窗类型'
                    },
                    {
                        header: '弹窗标题：',
                        dataIndex: 'popupTitle',
                        xtype: 'text',
                        placeholder: '弹窗标题'
                    },
                    {
                        header: '弹窗副标题：',
                        dataIndex: 'popupSubTitle',
                        xtype: 'text',
                        placeholder: '弹窗副标题'
                    },
                    {
                        header: '弹窗图片：',
                        dataIndex: 'popupImg',
                        xtype: Plugin('jiakao-misc!upload', {
                            dataIndex: 'popupImg',
                            uploadIndex: 'popupImg',
                            bucket: "jiakao-web",
                            isSingle: true,
                            placeholder: '请选择上传文件',
                            url: 'simple-upload3://upload/file.htm'
                        }, function () {
                    
                        })
                    },
                    {
                        header: '弹窗购买标签：',
                        dataIndex: 'popupBuyTag',
                        xtype: 'text',
                        placeholder: '弹窗购买标签'
                    },

                    {
                        header: '状态',
                        dataIndex: 'status',
                        xtype: 'select',
                        check: 'required',
                        store: statusStore,
                        placeholder: '状态',
                    },
                ]
            });
        
        }).fail(function () {});

    }

    var list = function (panel) {
        Table({
            description: '名师精品课程排行榜列表',
            title: '名师精品课程排行榜列表',
            search:[
                
                {
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: Constants.carTypeStore,
                },
                {
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore
                },
                {
                    dataIndex: 'languageType',
                    xtype: 'select',
                    store: [{ key: '', value: '请选择语言' }].concat(Constants.languageTypeStore)
                },  
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-ranklist/data/view',
                        save: 'jiakao-misc!top-lesson-ranklist/data/update'
                    },
                    renderAfter: renderAfter,
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-ranklist/data/delete'
                },
                {
                    name: '排序',
                    class: 'warning',
                    click: function (obj, dom, data) {
                        Plugin('jiakao-misc!drag-sort', {
                            save: 'jiakao-misc!top-lesson-ranklist/data/sortliveData',
                            load: { list: { data: data.lessonGroupList } },
                            showKey: 'title',
                            saveParams: function (params) {
                                return {
                                    id: data.id,
                                    lessonIds: params.ids
                                }
                            }
                        }, function () {
                            obj.render();
                        }).render()
                    }
                },
                {
                    name: '上线',
                    class: 'primary',
                    render: function (name, arr, i) {
                        return arr[i].status == 0 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认上线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-ranklist/data/update?id=' + lineData.id + '&status=2']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '下线',
                    class: 'info',
                    render: function (name, arr, i) {
                        return arr[i].status == 2 ? name : '';
                    },
                    click: function (table, dom, lineData) {
                        Widgets.dialog.confirm('确认下线吗', function (e, stat) {
                            if (stat) {
                                Store(['jiakao-misc!top-lesson-ranklist/data/update?id=' + lineData.id + '&status=0']).save().done(data => {
                                    console.log(data)
                                    table.render();
                                }).fail();
                            }
                        })
                    }
                },
                {
                    name: '默认弹窗',
                    width: 500,
                    class: 'warning',
                    click: edit
                },
                {
                    name: '删除默认弹窗',
                    class: 'danger',
                    click: function(table, row, lineData) {
                        function detele(id) {
                            Store(['jiakao-misc!top-lesson-ranklist/data/delete?id=' + id]).save().done(function (store) {
                                table.render();
                            }).fail(function () {});
                        }
                        Store(['jiakao-misc!top-lesson-ranklist/data/getSubDetail?parentId=' + lineData.id]).load().done(function (store) {
                            try{
                                var data = store.data['top-lesson-ranklist'].data.getSubDetail.data;
                                if(data.id) {
                                    detele(data.id)
                                }
                            } catch(err){

                            }
                            
                        
                        }).fail(function () {});
                    }
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '课程id',
                    dataIndex: 'lessonGroupList',
                    render: function (data) {
                        if(!data) return '';
                        var str = '';
                        for (var i = 0; i < data.length; i++) {
                            if(data[i] && data[i].title){
                                str += data[i].title + ' |<br/>';
                            }
                        }
                        return str;
                    }
                },
                {
                    header: '访问模式：',
                    dataIndex: 'editionCode',
                    render: function (data, arr, i) {
                        return data == '102' ? '长辈模式' : '普通模式'
                    }
                },
                {
                    header: '访问场景：',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        return data == '102' ? '扣满12分' : '普通场景'
                    }
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    // render: function (data, arr, i) {
                    //     return Constants.carTypeMap[data]
                    // }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '语言',
                    dataIndex: 'languageType',
                    render: function (data) {
                        return Constants.languageTypeMap[data]
                    }
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '封面图',
                    dataIndex: 'cover',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.cover}">`)
                    }
                },
                {
                    header: '弹窗图片',
                    dataIndex: 'popupImg',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.popupImg}">`)
                    }
                },
                {
                    header: '标签',
                    dataIndex: 'tags'
                },
                {
                    header: '视频url',
                    dataIndex: 'videoUrl',
                    render: function(data) {
                        return '<div style="max-width: 160px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '跳转详情url',
                    dataIndex: 'detailUrl',
                    render: function(data) {
                        return '<div style="width: 160px;word-break: break-all;">' + data + '</div>'
                    }
                },
                {
                    header: '商品key',
                    dataIndex: 'groupKey'
                },
                // {
                //     header: '相似商品key',
                //     dataIndex: 'similarGroupKey'
                // },
                {
                    header: '立省购买标题：',
                    dataIndex: 'buyTitle'
                },
                {
                    header: '立省购买副标题：',
                    dataIndex: 'buySubTitle'
                },
                {
                    header: '弹窗类型',
                    dataIndex: 'popupType',
                    render: function (data, arr, i) {
                        return popupTypeMap[data]
                    }
                },
                {
                    header: '弹窗标题',
                    dataIndex: 'popupTitle'
                },
                {
                    header: '弹窗副标题',
                    dataIndex: 'popupSubTitle'
                },
                {
                    header: '弹窗购买标签',
                    dataIndex: 'popupBuyTag'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data];
                    },
                }
            ]
        }, ['jiakao-misc!top-lesson-ranklist/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});