/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var recommendTypeStore = [
        {
            key: 1,
            value: 'vip'
        },
        {
            key: 2,
            value: '课程'
        }
    ]
    var recommendTypeMap = Tools.getMapfromArray(recommendTypeStore);

    var submitHandler = function (form) {
        var imgUrl = $(form).find('[name="imgUrl"]').val();
        var iconUrl = $(form).find('[name="iconUrl"]').val();

        if (!imgUrl) {
            Widgets.dialog.alert("请上传广告图片")
            return false
        }
        if (!iconUrl) {
            Widgets.dialog.alert("请上传小图标")
            return false
        }

        return {};
    }
    
    var applyToItem1 = function(table, line, data) {
        var lineData = data
        Widgets.dialog.html('选择子课程', {
            width: 800
        }).done(function (dialog) {
            Table({
                title: '',
                search: [
                    {
                        placeholder: '子课程ID',
                        dataIndex: 'id',
                        xtype: 'text'
                    },
                    {
                        placeholder: '标题',
                        dataIndex: 'title',
                        xtype: 'text'
                    },
                    {
                        dataIndex: 'kemu',
                        xtype: 'select',
                        store: Constants.kemuStore
                    },
                ],
                selector: {
                    render: function (rowData, data, config, index) {
                        return JSON.stringify({
                            lessonId: rowData.id,
                            roomId: rowData.barrageRoomId
                        });
                    }
                },
                buttons: {
                    top: [
                        {
                            name: '一键应用',
                            class: 'danger',
                            click: function (table, dom, config, selectedArr) {
                                if (selectedArr.length > 0) {
                                    var lessonData = selectedArr.map(item => {
                                        return JSON.parse(item)
                                    })
                                    Store(['jiakao-misc!top-lesson-recommend-goods-tpl/data/applyToLesson'], [{
                                        params: {
                                            recommendGoodsTemplateId: lineData.id,
                                            applyToAllLesson: false,
                                            lessonData: JSON.stringify(lessonData)
                                        }
                                    }]).save().done(function (store) {
                                        dialog.close()
                                        table.render()
                                    }).fail(function (err) {
                                        Widgets.dialog.alert(err.message)
                                    });
                                } else {
                                    Widgets.dialog.alert("请选择数据")
                                }
                            }
                        }
                    ]
                },
                columns: [{
                    header: '子课程ID',
                    dataIndex: 'id',
                },
                {
                    header: '子课程标题',
                    dataIndex: 'title',
                },
                {
                    header: '讲师名称',
                    dataIndex: 'teacherName'
                }
                ],
            }, ['jiakao-misc!top-lesson-item/data/list?type=2'], dialog.body, function () { }).render();
        });
    }
    var applyToItem2 = function(table, line, data) {
        var lineData = data
        sendApplyToLesson(table, lineData)
    }
    var sendApplyToLesson = function(table, lineData) {
        Table({
            title: '应用模板',
            width: 300,
            store: 'jiakao-misc!top-lesson-recommend-goods-tpl/data/applyToLesson',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: 'id：',
                dataIndex: 'recommendGoodsTemplateId',
                xtype: 'hidden',
                value: lineData.id,
            },{
                header: '全部：',
                dataIndex: 'applyToAllLesson',
                xtype: 'hidden',
                value: 'true',
            },{
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                placeholder: '科目'
            }]
        }).add();
    }
    var applyDialog = function(table, line, data) {
        Widgets.dialog.html('选择范围', {width: 240}).done(function (dialog) {
            $('<button type="button" class="btn btn-primary">指定范围</button>').one('click', function() {
                applyToItem1(table, line, data)
                dialog.close()
            }).appendTo($(dialog.body))
            $('<button style="margin-left: 5px;" type="button" class="btn btn-success">全部未开始</button>').one('click', function() {
                applyToItem2(table, line, data)
                dialog.close()
            }).appendTo($(dialog.body))
        })
    }
    
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '模板名称：',
                dataIndex: 'templateName',
                xtype: 'text',
                placeholder: '请输入',
                check: 'required',
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required',
                placeholder: '车型'
            },
            {
                header: '商品类型：',
                dataIndex: 'recommendType',
                xtype: 'select',
                store: recommendTypeStore,
                check: 'required',
                placeholder: '商品类型'
            },
            {
                header: '商品ID：',
                dataIndex: 'goodsUniqueKey',
                xtype: 'text',
                maxlength: 36,
                placeholder: '商品ID'
            },
            {
                header: '商品group_key：',
                dataIndex: 'groupKey',
                xtype: 'text',
                placeholder: '请输入',
                check: 'required',
            },
            {
                header: '对比商品group_key：',
                dataIndex: 'similarGroupKey',
                xtype: 'text',
                placeholder: '请输入，未配置不显示日常价',
    
            },
            {
                header: '广告图片：',
                dataIndex: 'imgUrl',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'imgUrl',
                    uploadIndex: 'imgUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                })
            },
            {
                header: '小图标：',
                dataIndex: 'iconUrl',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'iconUrl',
                    uploadIndex: 'iconUrl',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                })
            },
            {
                header: '弹窗购买标签：',
                dataIndex: 'buyTag',
                xtype: 'text',
                placeholder: '请输入，未配置不显示',
    
            },
            {
                header: '限时特惠时间(弹窗维持时间)：',
                dataIndex: 'popupTime',
                xtype: 'number',
                placeholder: '请输入，单位为秒',
                check: 'required',
            }
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!top-lesson-recommend-goods-tpl/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '推荐商品弹窗模板',
            title: '推荐商品弹窗模板',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-recommend-goods-tpl/data/view',
                        save: 'jiakao-misc!top-lesson-recommend-goods-tpl/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-recommend-goods-tpl/data/delete'
                },
                {
                    name: '一键应用',
                    class: 'info',
                    click: applyDialog
                }
            ],
            form: {
                submitHandler: submitHandler
            },
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '模板名称：',
                    dataIndex: 'templateName',
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '商品类型',
                    dataIndex: 'recommendType',
                    render: function (data, arr, i) {
                        return recommendTypeMap[data]
                    }
                },
                {
                    header: '小图标',
                    dataIndex: 'iconUrl',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.iconUrl}">`)
                    }
                },
                {
                    header: '广告图片',
                    dataIndex: 'imgUrl',
                    width: 80,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.imgUrl}">`)
                    }
                },
                {
                    header: '弹窗购买标签',
                    dataIndex: 'buyTag',
                },
                {
                    header: '限时特惠时间(弹窗维持时间)',
                    dataIndex: 'popupTime',
                    render: function (data) {
                        return data + "s"
                    },
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-recommend-goods-tpl/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
