/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!app/common/constants',
    "simple!core/plugin",

], function (Template, Table, Utils, Widgets, Store, Form,TIKU,Constants,Plugin) {
    var carTypeArr = [];

    for (var k in TIKU) {
        carTypeArr.push({
            key: k,
            value: TIKU[k]
        })
    }
    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!top-lesson-share-config/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {
                    var clickUrl = $(form).find('#clickUrl').val();
                    var image = $(form).find('input[data-item="bug-manage-value"]').val();
                    
                    const configValue = {
                        clickUrl,image
                    }

                    console.log(configValue,'configValue');

                    return {
                        configValue : JSON.stringify(configValue)
                    };
                },  
            },
            columns: [
                {
                    header: "车型:",
                    dataIndex: "carType",
                    xtype: "select",
                    store: [{key:'',value:'请选择'}].concat(carTypeArr)
                  },
                  {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore,
                },
             {
                 header: '直播课Id',
                 dataIndex: 'filterValue',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: '直播课Id'
             },
             {
                 header: '配置类型：',
                 dataIndex: 'configType',
                 xtype: 'select',
                 store: [{key:'',value:'请选择'},{
                     key: 'lesson_detail',
                     value:'课程详情页'
                 }],
                 placeholder: '配置类型'
             },
            
                {
                    header: "图片：",
                    dataIndex: "image",
                    xtype: Plugin(
                      "jiakao-misc!upload",
                      {
                        dataIndex: "image",
                        uploadIndex: "image",
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: "请选择上传文件",
                        url: "simple-upload3://upload/file.htm",
                      },
                      function () {
                        console.log(arguments);
                      }
                    ),
                },
                {
                    header: '跳转链接',
                    dataIndex: 'clickUrl',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '跳转链接'
                },
            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '分享入口配置',
            title: '分享入口配置',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!top-lesson-share-config/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '车型：',
                         dataIndex: 'carType'
                     },
                     {
                         header: '科目：',
                         dataIndex: 'kemu'
                     },
                     {
                         header: '直播课Id：',
                         dataIndex: 'filterValue'
                     },
                     {
                         header: '配置类型：',
                         dataIndex: 'configType'
                     },
                     {
                         header: '配置的信息：',
                         dataIndex: 'configValue'
                     },
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    renderAfter: function (table,dom,data) {
                        setTimeout(() => {
                            console.log(data.data.configValue);
                            const configValue = JSON.parse(data.data.configValue);
                            console.log(configValue,dom.item('bug-manage-value'),'configValue');
                            dom.item('clickUrl').val(configValue.clickUrl);
                            $('input[name="image"]').val(configValue.image);

                            dom.item('configValueTarget').val(data.data.configValue);
                       },500)
                    },
                    form: {
                        submitHandler: function (form) {
                            var clickUrl = $(form).find('#clickUrl').val();
                            var image = $(form).find('input[data-item="bug-manage-value"]').val();
                            
                            const configValue = {
                                clickUrl,image
                            }
        
                            console.log(configValue,'configValue');
        
                            return {
                                configValue : JSON.stringify(configValue)
                            };
                        },  
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-share-config/data/view',
                        save: 'jiakao-misc!top-lesson-share-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: "车型:",
                            dataIndex: "carType",
                            xtype: "select",
                            store: [].concat(carTypeArr)
                          },
                          {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            check: 'required',
                            store: Constants.kemuStore,
                        },
             {
                 header: '直播课Id：',
                 dataIndex: 'filterValue',
                 xtype: 'text',
                 maxlength: 64,
                 placeholder: '直播课Id'
             },
             {
                header: '配置类型：',
                dataIndex: 'configType',
                xtype: 'select',
                store: [{key:'',value:'请选择'},{
                    key: 'lesson_detail',
                    value:'课程详情页'
                }],
                placeholder: '配置类型'
            },
                        {
                            header: '配置内容：',
                            dataIndex: 'configValueTarget',
                            xtype: 'textarea',
                            disabled: true,
                            rows:5,
                            placeholder: '配置内容'
                        },
                        {
                            header: "图片：",
                            dataIndex: "image",
                            xtype: Plugin(
                              "jiakao-misc!upload",
                              {
                                dataIndex: "image",
                                uploadIndex: "image",
                                bucket: "exam-room",
                                isSingle: true,
                                placeholder: "请选择上传文件",
                                url: "simple-upload3://upload/file.htm",
                              },
                              function () {
                                console.log(arguments);
                              }
                            ),
                        },
                        {
                            header: '跳转链接',
                            dataIndex: 'clickUrl',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '跳转链接'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-share-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '车型',
                         dataIndex: 'carType',
                         render: function (data) {
                             
                            return  TIKU[data]
                        }
                     },
                     {
                         header: '科目',
                         dataIndex: 'kemu',
                         render: function (data, arr, i) {
                            return Constants.kemuMap[data]
                        }
                     },
                     {
                         header: '直播课Id',
                         dataIndex: 'filterValue'
                     },
                     {
                         header: '配置类型',
                         dataIndex: 'configType'
                     },
                     {
                         header: '配置的信息',
                         dataIndex: 'configValue',
                         render: function (data) {
                             return `<a>点击查看</a>`;
                         },
                        click: function (table, row, lineData) {
                            Widgets.dialog.html('配置的信息', {}).done(function (dialog) {
                                $(dialog.body).html(lineData.configValue)
                            })
                         }
                     },
                     {
                         header: '创建时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: '创建人',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: '更新时间',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: '更新人',
                         dataIndex: 'updateUserName'
                     },

            ]
        }, ['jiakao-misc!top-lesson-share-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});