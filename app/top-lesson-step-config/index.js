/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {

    var patternCodeList = [
        {
            key: '101',
            value: '普通模式'
        },
        {
            key: '102',
            value: '长辈模式'
        }
    ]
    var sceneCodeList = [
        {
            key: '101',
            value: '普通场景'
        },
        {
            key: '102',
            value: '扣满12分'
        }
    ]

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!top-lesson-step-config/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '选择课程：',
                    dataIndex: 'lessonId',
                    xtype: Plugin('jiakao-misc!auto-prompt', {
                        store: 'jiakao-misc!top-lesson-group/data/list?limit=1000000',
                        placeholder: '选择课程',
                        dataIndex: 'lessonId',
                        index: {
                            key: 'id',
                            value: 'title',
                            search: 'title'
                        },
                        isMulti: false,
                        defaultVal: false
                    }, function (plugin, value) {
                    })
                },
                {
                    header: '标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 128,
                    check: 'required',
                    placeholder: '标题'
                },
                {
                    header: '车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: Constants.carTypeStore,
                    check: 'required',
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: Constants.kemuStore,
                    check: 'required',
                },
                {
                    header: '访问模式：',
                    dataIndex: 'patternCode',
                    xtype: 'radio',
                    store: patternCodeList,
                    check: 'required',
                },
                {
                    header: '访问场景：',
                    dataIndex: 'sceneCode',
                    xtype: 'radio',
                    store: sceneCodeList,
                    check: 'required',
                },
                {
                    header: '排序：',
                    dataIndex: 'order',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '排序'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '名师精品课四步抢分配置列表',
            title: '名师精品课四步抢分配置列表',
            search: [{
                header: '车型类型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: [
                    {
                        key: '',
                        value: '请选择车型'
                    },
                    {
                        key: 'car',
                        value: '小车'
                    },
                    {
                        key: 'bus',
                        value: '客车'
                    },
                    {
                        key: 'truck',
                        value: '货车'
                    },
                    {
                        key: 'moto',
                        value: '摩托车'
                    },
                    {
                        key: 'light_trailer',
                        value: '轻型牵引挂车'
                    }]
            }, {
                header: '科目',
                dataIndex: 'kemu',
                xtype: 'select',
                store: [
                    {
                        key: '',
                        value: '请选择科目'
                    },
                    {
                        key: 1,
                        value: '科目一'
                    },
                    {
                        key: 2,
                        value: '科目二'
                    },
                    {
                        key: 3,
                        value: '科目三'
                    },
                    {
                        key: 4,
                        value: '科目四'
                    }
                ]
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-step-config/data/view',
                        save: 'jiakao-misc!top-lesson-step-config/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '选择课程：',
                            dataIndex: 'lessonId',
                            xtype: Plugin('jiakao-misc!auto-prompt', {
                                store: 'jiakao-misc!top-lesson-group/data/list?limit=1000000',
                                placeholder: '选择课程',
                                dataIndex: 'lessonId',
                                index: {
                                    key: 'id',
                                    value: 'title',
                                    search: 'title'
                                },
                                isMulti: false,
                                defaultVal: false
                            }, function (plugin, value) {
                            })
                        },
                        {
                            header: '标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 128,
                            check: 'required',
                            placeholder: '标题'
                        },
                        {
                            header: '车型：',
                            dataIndex: 'carType',
                            xtype: 'select',
                            store: Constants.carTypeStore,
                            check: 'required',
                        },
                        {
                            header: '科目：',
                            dataIndex: 'kemu',
                            xtype: 'select',
                            store: Constants.kemuStore,
                            check: 'required',
                        },
                        {
                            header: '访问模式：',
                            dataIndex: 'patternCode',
                            xtype: 'radio',
                            store: patternCodeList,
                            check: 'required',
                        },
                        {
                            header: '访问场景：',
                            dataIndex: 'sceneCode',
                            xtype: 'radio',
                            store: sceneCodeList,
                            check: 'required',
                        },
                        {
                            header: '排序：',
                            dataIndex: 'order',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '排序'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-step-config/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '课程id',
                    dataIndex: 'lessonId'
                },
                {
                    header: '标题',
                    dataIndex: 'title'
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '访问模式',
                    dataIndex: 'patternCode',
                    render: function (data, arr, i) {
                        return Tools.getMapfromArray(patternCodeList)[data]
                    }
                },
                {
                    header: '访问场景',
                    dataIndex: 'sceneCode',
                    render: function (data, arr, i) {
                        return Tools.getMapfromArray(sceneCodeList)[data]
                    }
                },
                {
                    header: '排序',
                    dataIndex: 'order'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!top-lesson-step-config/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
