/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var enableStore = [
        {
            key: false,
            value: '关闭'
        }, {
            key: true,
            value: '开启'
        }
    ]
    var enableMap = Tools.getMapfromArray(enableStore);

    var liveTypeStore = [{
        key: 1,
        value: '常规直播'
    },{
        key: 2,
        value: '66学车节'
    },{
        key: 3,
        value: '公共直播'
    },{
        key: 4,
        value: '会员专享直播'
    },{
        key: 5,
        value: '长辈专属直播'
    },{
        key: 6,
        value: '小班教学'
    },{
        key: 7,
        value: '考前辅导'
    },{
        key: 8,
        value: '万人模考'
    },{
        key: 9,
        value: '专项攻克'
    },{
        key: 11,
        value: '付费刷题班'
    },{
        key: 12,
        value: '免费刷题班'
    }]

    var liveTypeMap = Tools.getMapfromArray(liveTypeStore);

    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
                check: 'required',
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
                check: 'required',
            },
            {
                header: '直播类型：',
                dataIndex: 'liveType',
                xtype: Plugin('jiakao-misc!auto-prompt', {
                    store: liveTypeStore,
                    placeholder: '直播类型：',
                    dataIndex: 'liveType',
                    index: {
                        key: 'key',
                        value: 'value',
                        search: 'value'
                    },
                    isMulti: true,
                    defaultVal: false
                }, function (plugin, value) {
                })
            },
            {
                header: '消息内容：',
                dataIndex: 'content',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '消息内容'
            },
            {
                header: '发送频率：',
                dataIndex: 'interval',
                xtype: 'select',
                check: 'required',
                placeholder: '发送频率',
                store: 'jiakao-misc!top-lesson-sys-message/data/getIntervalList',
                index: {
                    key: 'interval',
                    value: 'desc'
                },
            },
            {
                header: '入场发送：',
                dataIndex: 'intoSend',
                xtype: 'radio',
                store: enableStore
            },
            {
                header: '生效时间：',
                dataIndex: 'effectTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '生效时间'
            },
            {
                header: '失效时间：',
                dataIndex: 'expireTime',
                xtype: 'datetime',
                render: function (data) {
                    if (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                placeholder: '失效时间'
            },
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!top-lesson-sys-message/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '直播间系统消息管理',
            title: '直播间系统消息管理',
            search:[{
                header: '直播类型：',
                dataIndex: 'liveType',
                xtype: 'select',
                store: [{ key: '', value: '所有直播类型' }].concat(liveTypeStore)
            },
            {
                header: '车型：',
                dataIndex: 'carType',
                xtype: 'select',
                store: Constants.carTypeStore,
            },
            {
                header: '科目：',
                dataIndex: 'kemu',
                xtype: 'select',
                store: Constants.kemuStore,
            }],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-sys-message/data/view',
                        save: 'jiakao-misc!top-lesson-sys-message/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-sys-message/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data, arr, i) {
                        return Constants.carTypeMap[data]
                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data, arr, i) {
                        return Constants.kemuMap[data]
                    }
                },
                {
                    header: '直播类型',
                    dataIndex: 'liveType',
                    render: function(data){
                        var str = '';
                        if(data){
                            var types = data.split(',');
                            var typeStr = [];
                            for(var i=0; i<types.length; i++){
                                typeStr.push(liveTypeMap[types[i]])
                            }

                            str = typeStr.join('，')
                        }

                        return str;
                    }
                },
                {
                    header: '消息内容',
                    dataIndex: 'content',
                },
                {
                    header: '发送频率',
                    dataIndex: 'interval',
                    render: function (data) {
                        return data ? data + '分/次' : '';
                    }
                },
                {
                    header: '入场发送',
                    dataIndex: 'intoSend',
                    render: function (data, arr, i) {
                        return enableMap[data]
                    }
                },
                {
                    header: '生效时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'effectTime'
                },
                {
                    header: '失效时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'expireTime'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-sys-message/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
