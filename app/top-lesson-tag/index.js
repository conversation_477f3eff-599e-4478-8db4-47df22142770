/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '标签Key：',
                dataIndex: 'tagKey',
                xtype: 'text',
                maxlength: 128,
                placeholder: '标签Key'
            },
            {
                header: '标签名：',
                dataIndex: 'tagName',
                xtype: 'text',
                maxlength: 128,
                placeholder: '标签名'
            },
            {
                header: '标签描述：',
                dataIndex: 'tagDesc',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '标签描述'
            }
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!top-lesson-tag/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '课程标签管理',
            title: '课程标签管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-tag/data/view',
                        save: 'jiakao-misc!top-lesson-tag/data/update'
                    },
                    columns: columns()
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-tag/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标签Key',
                    dataIndex: 'tagKey',
                },
                {
                    header: '标签名',
                    dataIndex: 'tagName',
                },
                {
                    header: '标签描述',
                    dataIndex: 'tagDesc',
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-tag/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
