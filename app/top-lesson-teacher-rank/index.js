/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var list = function (panel) {
        Table({
            description: '讲师排名管理',
            title: '讲师排名管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑排名',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑排名',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-teacher-rank/data/view',
                        save: 'jiakao-misc!top-lesson-teacher-rank/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'rankData',
                            xtype: Plugin('jiakao-misc!drag-sort2', {
                                dataIndex: 'rankData',
                                showKey: 'teacherName',
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '修改原因：',
                            dataIndex: 'remark',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '修改原因',
                            check: 'required',
                        }
                    ],
                    // form: {
                    //     submitHandler: function(form) {
                    //         var $form = $(form);
        
                    //         var rankData = $form.find('[name=rankData]').val();
                    //         console.log(rankData)
                    //         return {}
                    //     }
                    // },
                },
                {
                    name: '修改记录',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        Widgets.dialog.html('修改记录', {
                            width: 600
                        }).done(function (dialog) {
                            var id = lineData.id
                            Table({
                                title: '',
                                columns: [{
                                    header: '#',
                                    dataIndex: 'id',
                                },
                                {
                                    header: '修改前',
                                    dataIndex: 'oldData',
                                    render: function (data) {
                                        try {
                                            data = JSON.parse(data)
                                            
                                            return data.map((item, index) => {
                                                return 'No.'+(index+1) +': ' + item.teacherName
                                            }).join('<br>');
                                        } catch (error) {
                                            console.error(error)
                                        }
                                    },
                                },
                                {
                                    header: '修改后',
                                    dataIndex: 'changeData',
                                    render: function (data) {
                                        try {
                                            data = JSON.parse(data)
                                            
                                            return data.map((item, index) => {
                                                return 'No.'+(index+1) +': ' + item.teacherName
                                            }).join('<br>');
                                        } catch (error) {
                                            console.error(error)
                                        }
                                    },
                                },
                                {
                                    header: '修改原因：',
                                    dataIndex: 'remark',
                                },
                                {
                                    header: '修改时间',
                                    render: function (data) {
                                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                                    },
                                    dataIndex: 'createTime'
                                },
                                {
                                    header: '修改人',
                                    dataIndex: 'createUserName'
                                }
                                ],
                            },
                            ['top-lesson-teacher-rank/data/getChangeRecordList?id=' + id],
                            dialog.body, null).render();
                        })
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '生成排名时间',
                    dataIndex: 'rankDate',
                },
                {
                    header: '讲师排名',
                    dataIndex: 'rankData',
                    render: function (data) {
                        try {
                            data = JSON.parse(data)
                            
                            return data.map((item, index) => {
                                return 'No.'+(index+1) +': ' + item.teacherName
                            }).join('<br>');
                        } catch (error) {
                            console.error(error)
                        }
                    },
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!top-lesson-teacher-rank/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
