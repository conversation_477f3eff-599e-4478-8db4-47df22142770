/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    function columns() {
        function cls(from) {
            return from
        }
        return cls([
            {
                dataIndex: 'id',
                xtype: 'hidden'
            },
            {
                header: '讲师名称',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 32,
            },
            {
                header: '讲师简介',
                dataIndex: 'desc',
                xtype: 'text',
                maxlength: 64,
            },
            {
                header: '讲师标签',
                dataIndex: 'tags',
                xtype: 'text',
                maxlength: 64,
            },
            {
                header: '直播账号：',
                dataIndex: 'liveAnchorId',
                xtype: 'select',
                placeholder: '直播账号',
                store: 'jiakao-misc!top-lesson-teacher/data/anchorList?limit=1000000',
                index: {
                    key: 'id', value: 'nickName'
                },
                insert: [{
                    id: '',
                    nickName: '请选择账号',
                }],
                check: 'required'
            },
            {
                header: '讲师照片',
                dataIndex: 'avatar',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'avatar',
                    uploadIndex: 'avatar',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '科目一/四讲师gif图',
                dataIndex: 'kemuOneImg',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'kemuOneImg',
                    uploadIndex: 'kemuOneImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '科目二/三讲师gif图',
                dataIndex: 'kemuTwoImg',
                xtype: Plugin('jiakao-misc!upload', {
                    dataIndex: 'kemuTwoImg',
                    uploadIndex: 'kemuTwoImg',
                    bucket: "jiakao-web",
                    isSingle: true,
                    placeholder: '请选择上传文件',
                    url: 'simple-upload3://upload/file.htm'
                }, function () {
                    console.log(arguments)
                })
            },
            {
                header: '科一教学视频',
                dataIndex: 'k1Video',
                xtype: 'textarea',
                // xtype: Plugin('jiakao-misc!upload2', {
                //     dataIndex: 'k1Video',
                //     uploadIndex: 'k1Video',
                //     bucket: "jiakao-web",
                //     isSingle: true,
                //     placeholder: '请选择上传文件',
                //     url: 'simple-upload3://upload/file.htm'
                // }, function () {
                //     console.log(arguments)
                // })
            },
            {
                header: '科二教学视频',
                dataIndex: 'k2Video',
                xtype: 'textarea',
                // xtype: Plugin('jiakao-misc!upload2', {
                //     dataIndex: 'k2Video',
                //     uploadIndex: 'k2Video',
                //     bucket: "jiakao-web",
                //     isSingle: true,
                //     placeholder: '请选择上传文件',
                //     url: 'simple-upload3://upload/file.htm'
                // }, function () {
                //     console.log(arguments)
                // })
            },
            {
                header: '科三教学视频',
                dataIndex: 'k3Video',
                xtype: 'textarea',
                // xtype: Plugin('jiakao-misc!upload2', {
                //     dataIndex: 'k3Video',
                //     uploadIndex: 'k3Video',
                //     bucket: "jiakao-web",
                //     isSingle: true,
                //     placeholder: '请选择上传文件',
                //     url: 'simple-upload3://upload/file.htm'
                // }, function () {
                //     console.log(arguments)
                // })
            },
            {
                header: '科四教学视频',
                dataIndex: 'k4Video',
                xtype: 'textarea',
                // xtype: Plugin('jiakao-misc!upload2', {
                //     dataIndex: 'k4Video',
                //     uploadIndex: 'k4Video',
                //     bucket: "jiakao-web",
                //     isSingle: true,
                //     placeholder: '请选择上传文件',
                //     url: 'simple-upload3://upload/file.htm'
                // }, function () {
                //     console.log(arguments)
                // })
            }
        ])
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 600,
            store: 'jiakao-misc!top-lesson-teacher/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: columns()
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '名师精品课讲师管理',
            title: '名师精品课讲师管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 600,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-teacher/data/view',
                        save: 'jiakao-misc!top-lesson-teacher/data/update'
                    },
                    columns: columns()
                },
                // {
                //     name: '删除',
                //     class: 'danger',
                //     xtype: 'delete',
                //     store: 'jiakao-misc!top-lesson-teacher/data/delete'
                // }
            ],
            columns: [
                {
                    header: '讲师id',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '讲师名称',
                    dataIndex: 'name'
                },
                {
                    header: '讲师简介',
                    dataIndex: 'desc'
                },
                {
                    header: '讲师标签',
                    dataIndex: 'tags'
                },
                {
                    header: '讲师照片',
                    dataIndex: 'avatar',
                    width: 160,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.avatar}">`)
                    }
                },
                {
                    header: '科目一/四讲师gif图',
                    dataIndex: 'kemuOneImg',
                    width: 160,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.kemuOneImg}">`)
                    }
                },
                {
                    header: '科目二/三讲师gif图',
                    dataIndex: 'kemuTwoImg',
                    width: 160,
                    render: function (data) {
                        if (data) {
                            return '<a>'+'<img src="' + data + '">'+'</a>'
                        }
                    },
                    click: function (table, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.kemuTwoImg}">`)
                    }
                },
                {
                    header: '科一教学视频',
                    dataIndex: 'k1Video',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('视频', {width: 600}).done(function (dialog) {
                            $(dialog.body).html('<video width="600" src="' + lineData.k1Video + '" controls></video>')
                        })
                    }
                },
                {
                    header: '科二教学视频',
                    dataIndex: 'k2Video',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('视频', {width: 600}).done(function (dialog) {
                            $(dialog.body).html('<video width="600" src="' + lineData.k2Video + '" controls></video>')
                        })
                    }
                },
                {
                    header: '科三教学视频',
                    dataIndex: 'k3Video',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('视频', {width: 600}).done(function (dialog) {
                            $(dialog.body).html('<video width="600" src="' + lineData.k3Video + '" controls></video>')
                        })
                    }
                },
                {
                    header: '科四教学视频',
                    dataIndex: 'k4Video',
                    render: function (data) {
                        if (data) {
                            return '<a>查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('视频', {width: 600}).done(function (dialog) {
                            $(dialog.body).html('<video width="600" src="' + lineData.k4Video + '" controls></video>')
                        })
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }
            ]
        }, ['jiakao-misc!top-lesson-teacher/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});