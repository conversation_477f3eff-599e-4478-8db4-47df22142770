/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {



    var list = function (panel) {
        Table({
            description: '交通强国标签列表',
            title: '交通强国标签列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },

                    {
                        name: '同步',
                        class: 'warning',
                        click: function (obj) {
                            Store(['jiakao-misc!traffic-power-tag/data/sync']).load().done(function () {
                                obj.render()
                            }).fail(err => {

                            })
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!traffic-power-tag/data/view',
                        save: 'jiakao-misc!traffic-power-tag/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '短视频标签的ID：',
                            dataIndex: 'shortVideoTagId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '短视频标签的ID'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'tagOrder',
                            xtype: 'text',
                            placeholder: '排序'
                        },
                        {
                            header: '标签的名称：',
                            dataIndex: 'tagName',
                            xtype: 'text',
                            maxlength: 128,
                            check: 'required',
                            placeholder: '标签的名称'
                        },
                        {
                            header: '图标',
                            dataIndex: 'tagCover',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'tagCover',
                                uploadIndex: 'tagCover',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '每个标签的显示视频数量：',
                            dataIndex: 'tagViewLimit',
                            xtype: 'text',
                            placeholder: '推荐视频数量'
                        },
                        {
                            header: '推荐的标签ID：',
                            dataIndex: 'recommendIds',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '推荐的视频ID'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!traffic-power-tag/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: 'ID',
                    dataIndex: 'shortVideoTagId'
                },
                {
                    header: '栏目名',
                    dataIndex: 'tagName'
                },
                {
                    header: '排序',
                    dataIndex: 'tagOrder'
                },
                {
                    header: '图标',
                    dataIndex: 'tagCover'
                },
                {
                    header: '每个标签的显示视频数量',
                    dataIndex: 'tagViewLimit'
                },
                {
                    header: '推荐的标签ID',
                    dataIndex: 'recommendIds',
                    // render: function (data) {
                    //     if (data) {
                    //         return '<a>点击查看</a>'
                    //     }
                    // },
                    // click: function (table, row, lineData) {
                    //     Widgets.dialog.html('练习的配置', {}).done(function (dialog) {
                    //         $(dialog.body).html('<div>' + lineData.recommendIds + '</div><br/>')
                    //     })
                    // }
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新人',
                    dataIndex: 'updateUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                }

            ]
        }, ['jiakao-misc!traffic-power-tag/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});