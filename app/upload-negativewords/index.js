/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var storeBaseUrl='jiakao-misc!crawler-negativewords/data/'
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: storeBaseUrl + 'add',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '负面词：',
                    dataIndex: 'word',
                    xtype: 'text',
                    placeholder: '负面词'
                }


            ]
        }).add();
    }
    var upload = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: storeBaseUrl + 'upload',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '搜索词文件：',
                    dataIndex: 'question',
                    xtype:'file'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '上传负面词',
            title: '上传负面词',

            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '导入负面词',
                    class: 'primary',
                    click: upload
                },
                {
                    name: '添加负面词',
                    class: 'warning',
                    click: add
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [
           
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: storeBaseUrl+'view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '负面词：',
                            dataIndex: 'word'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        }
                      

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: storeBaseUrl+'view',
                        save: storeBaseUrl+'update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    },
                        {
                            header: '负面词：',
                            dataIndex: 'word',
                            xtype: 'text',
                            placeholder: '负面词：'
                        },

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: storeBaseUrl+'delete'
                },
               
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '负面词',
                    dataIndex: 'word'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
               

            ]
        }, [storeBaseUrl+'list?type=1'], panel, null).render();
    }

    return {
        list: list
    }

});
