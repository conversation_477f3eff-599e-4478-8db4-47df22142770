/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'jiakao-misc!app/common/tiku'], function (Template, Table, Utils, Widgets, Store, Form, TIKU) {
    var carTypeStore = []
    for (var key in TIKU) {
        carTypeStore.push({ key: key, value: TIKU[key] })
    }
    var kemuStore = []
    var kemuMap = {
        1: '科目一',
        4: '科目四'
    }
    for (var key in kemuMap) {
        kemuStore.push({ key: key, value: kemuMap[key] })
    }
    var viewResult = function (lineData, table, type) {
        var data = JSON.parse(lineData.result || '{}')
        let defaultData = {
            vipUseInfoData: {
                vip500Count: '-',
                realSceneUseTimes: '-',
                vip100Count: '-',
                practiceVideoCount: '-',
            },
            practiceInfoData: {
                totalQuestionCount: '-',
                accuracyPercent: '-',
                remainCount: '-',
                wrongCount: '-',
            },
            examInfoData: {
                examTimes: '-',
                passedTimes: '-',
                avgScore: '-',
                passRate: '-',
            }
        }
        data.vipUseInfoData = Object.assign({}, defaultData.vipUseInfoData, data.vipUseInfoData)
        data.practiceInfoData = Object.assign({}, defaultData.practiceInfoData, data.practiceInfoData)
        data.examInfoData = Object.assign({}, defaultData.examInfoData, data.examInfoData)
        let vip = [{
            name: '精简500题做题数',
            value: data.vipUseInfoData.vip500Count
        }, {
            name: '真实考场模拟次数',
            value: data.vipUseInfoData.realSceneUseTimes
        }, {
            name: '考前秘卷做题数',
            value: data.vipUseInfoData.vip100Count
        }, {
            name: '练习视频观看次数',
            value: data.vipUseInfoData.practiceVideoCount
        }]
        let practice = [
            {
                name: '做题总数',
                value: data.practiceInfoData.totalQuestionCount
            }, {
                name: '正确率',
                value:  !isNaN(data.practiceInfoData.accuracyPercent) ? (data.practiceInfoData.accuracyPercent * 100) + '%' : data.practiceInfoData.accuracyPercent
            }, {
                name: '未做题数',
                value: data.practiceInfoData.remainCount
            }, {
                name: '错题数',
                value: data.practiceInfoData.wrongCount
            }
        ]
        let exam = [
            {
                name: '考试总次数',
                value: data.examInfoData.examTimes
            }, {
                name: '及格次数',
                value: data.examInfoData.passedTimes
            }, {
                name: '近5次平均分',
                value: data.examInfoData.avgScore
            }, {
                name: '预测通过率',
                value: !isNaN(data.examInfoData.passRate) ? (data.examInfoData.passRate * 100) + '%' : data.examInfoData.passRate
            }
        ]

        Widgets.dialog.html(type == 'comment' ? '评论' : '查询结果', { width: 300 }).done(function (dialog) {
            vip.forEach(item => {
                $(dialog.body).append(`<div>${item.name}: ${item.value}</div><br/>`)
            })
            $(dialog.body).append('<div>-----------------------------</div><br/>')
            practice.forEach(item => {
                $(dialog.body).append(`<div>${item.name}: ${item.value}</div><br/>`)
            })
            $(dialog.body).append('<div>-----------------------------</div><br/>')
            exam.forEach(item => {
                $(dialog.body).append(`<div>${item.name}: ${item.value}</div><br/>`)
            })
            if (type == 'comment') {
                $(dialog.body).append(`
               <div style="width:100%;padding:10px 0px 20px 0px">
                  <textarea  style="width:100%" data-item="get-comment" rows="5" cols="5" placeholder="请输入评论内容">${lineData.comment || ''}</textarea>
               </div>
               <div style="width:100%;border-top:1px solid #f4f4f4;border-bottom:1px solid #f4f4f4;height:80px;line-height:80px;text-align:right">
                  <button style="width:80px;margin-right:10px;" data-item="save-comment"  class="btn btn-info ">保存</button>
                  <button data-item="clear-comment" style="width:80px;" data-item=""  class="btn btn-danger ">重填</button>
               </div>
            `)

                var $commentEle = $(dialog.body).find('textarea[data-item="get-comment"]');
                $(dialog.body).find('button[data-item="save-comment"]').on('click', function () {
                    if (!$commentEle.val().trim()) {
                        Widgets.dialog.alert('请输入评论')
                        return
                    }
                    Store(['jiakao-misc!user-cloud-data/data/comment'], [{
                        aliases: 'list',
                        params: {
                            id: lineData.id,
                            comment: $commentEle.val()
                        }
                    }]).save().done(function (store) {
                        table.render();
                        dialog.close()
                    }).fail(function () { });

                })
                $(dialog.body).find('button[data-item="clear-comment"]').on('click', function () {
                    $commentEle.val('')
                })

            }

        })
    }
    var add = function (table) {
        Table({
            title: '发起查询',
            width: 500,
            store: 'jiakao-misc!user-cloud-data/data/getUseInfoData',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: function (table, dom) {
                dom.item('type').on('change', function () {
                    dom.item('keyword').val('');
                    var text = $(this).val() == 1 ? '请输入手机号' : '请输入木仓ID'
                    dom.item('keyword').attr('placeholder', text);
                })
            },
            columns: [
                {
                    header: '查询依据：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: [
                        { key: 1, value: '根据手机号' },
                        { key: 2, value: '根据木仓ID' },
                    ]
                },
                {
                    header: '',
                    dataIndex: 'keyword',
                    xtype: 'text',
                    placeholder: "请输入手机号"
                },
                {
                    header: ' 车型：',
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [
                        { key: '', value: '请选择车型' },
                        ...carTypeStore
                    ],
                    placeholder: ' 车型'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu',
                    xtype: 'select',
                    store: [
                        { key: '', value: '请选择科目' },
                        ...kemuStore
                    ],
                    placeholder: ' 科目'
                },
                {
                    header: '查询原因：',
                    dataIndex: 'reason',
                    xtype: 'textarea',
                    rows: 5,
                    cols: 5,
                    placeholder: '请输入查询原因'
                }
            ]
        }).add();
    }
    var statusMap = {
        0: '未评论',
        1: '评论'
    }
    var statusArray = []
    for (let key in statusMap) {
        statusArray.push({ key: key, value: statusMap[key] })
    }

    var list = function (panel) {
        Table({
            description: '用户做题数据',
            title: '用户做题数据',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '发起查询',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    class: 'info',
                    click: function (table, dom, lineData) {
                        viewResult(lineData, table)
                    }
                },
                {
                    name: '评论',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        viewResult(lineData, table, 'comment')
                    }
                }
            ],
            search: [
                {
                    dataIndex: 'userId',
                    xtype: 'text',
                    placeholder: '木仓ID'
                },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    placeholder: '',
                    store: [
                        { key: '', value: '状态' },
                        ...statusArray
                    ]
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '木仓ID',
                    dataIndex: 'userId'
                },
                {
                    header: '车型',
                    dataIndex: 'carType',
                    render: function (data) {
                        return TIKU[data]
                    }
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    render: function (data) {
                        return kemuMap[data]
                    }
                },
                {
                    header: '查询原因',
                    dataIndex: 'reason'
                },

                {
                    header: '查询发起人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '查询时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '状态',
                    render: function (data) {
                        return statusMap[data]
                    },
                    dataIndex: 'status'
                },
                {
                    header: '评论人',
                    dataIndex: 'commentUserName'
                },
            ]
        }, ['jiakao-misc!user-cloud-data/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});