/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!app/layout/main'], function (Template, Table, Utils, Widgets, Store, Form, Layout) {



    var list = function (panel) {
        Table({
            description: '里程碑列表',
            title: '里程碑列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }

                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看节点',
                    class: 'primary',
                    click: function (table, dom, lineData) {
                        var panelId = 'view-node' + lineData.id;
                        var viewPanel = Layout.panel(panelId);
                        if (viewPanel.length == 0) {
                            viewPanel = Layout.panel({
                                id: panelId,
                                name: lineData.milestoneName
                            });
                        }
                        require(['jiakao-misc!app/user-milestone-node/view-node'], function (demo) {
                            demo.reportlist(viewPanel, lineData)
                        })

                    }
                }

            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '里程碑事件',
                    dataIndex: 'milestoneName'
                },
                {
                    header: '是否可升级',
                    dataIndex: 'canUpgrade',
                    render: function (data) {
                        return data ? '是' : '否'
                    }
                },
                {
                    header: '升级节点数',
                    dataIndex: 'nodeConut'
                },
                {
                    header: 'key',
                    dataIndex: 'milestoneKey'
                },


                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },

            ]
        }, ['jiakao-misc!user-milestone-node/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});