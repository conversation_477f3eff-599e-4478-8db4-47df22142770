/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {



    var reportlist = function (panel, lineData) {
        Table({
            description: lineData.milestoneName,
            title: lineData.milestoneName,

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }

                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            columns: [
                {
                    header: '升级节点编号',
                    dataIndex: 'nodeSerialNumber'
                },
                {
                    header: '节点',
                    dataIndex: 'nodeName',

                },
                {
                    header: '完成人数占比',
                    dataIndex: 'rate'
                },
            ]
        }, ['jiakao-misc!user-milestone-node/data/getViewNodeList?milestoneId=' + lineData.id], panel, function () {

        }).render();
    }

    return {
        reportlist: reportlist
    }

});