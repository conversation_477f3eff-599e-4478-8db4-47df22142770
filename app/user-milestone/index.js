/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {



    var list = function (panel) {
        Table({
            description: '用户里程碑查询',
            title: '用户里程碑查询',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            search: [
                {
                    xtype: 'text',
                    dataIndex: 'userId',
                    placeholder: '用户id',
                },
                {
                    xtype: 'text',
                    dataIndex: 'phone',
                    placeholder: '用户手机号',
                }
            ],
            columns: [
                {
                    header: '用户id',
                    dataIndex: 'userId'
                },
                {
                    header: '手机号',
                    dataIndex: 'phone'
                },
                {
                    header: '里程碑事件',
                    dataIndex: 'milestoneName'
                },
                {
                    header: '节点',
                    dataIndex: 'node'
                },

                {
                    header: '获得时间',
                    dataIndex: 'getTime'
                },

            ]
        }, ['jiakao-misc!user-milestone/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});