/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/tiku',
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, TIKU) {

    var directionMap = {
        1: '竖屏',
        2: '横屏'
    }

    var directionArr = [];
    for (const key in directionMap) {
        directionArr.push({key,value:directionMap[key]})
    }
    
    var sceneCodeMap = {
        101: '基础场景',
        102: '扣满12分',
        103: '维语场景'
    }
    
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!video-explain/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: 'questionId：',
                dataIndex: 'questionId',
                xtype: 'text',
                maxlength: 32,
                placeholder: 'questionId'
            },
            {
                header: 'kemu：',
                dataIndex: 'kemu',
                xtype: 'text',
                placeholder: 'kemu'
            },
            {
                header: '横屏封面图：',
                dataIndex: 'coverH',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '横屏封面图'
            },
            {
                header: '竖屏封面图：',
                dataIndex: 'coverV',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '竖屏封面图'
            },
            {
                header: '视频地址：',
                dataIndex: 'videoUrl',
                xtype: 'textarea',
                maxlength: 255,
                placeholder: '视频地址'
            },
            {
                header: '加密视频地址：',
                dataIndex: 'videoEncodeUrl',
                xtype: 'textarea',
                maxlength: 1024,
                placeholder: '加密视频地址'
            }

            ]
        }).add();
    }
    let fileUrl = '';
    var list = function (panel) {
        Table({
            description: '视频详解列表',
            title: '视频详解列表',
            search: [{
                placeholder: '试题id',
                dataIndex: 'questionId',
                xtype: 'text',
            }, {
                placeholder: '科目',
                dataIndex: 'kemu',
                xtype: 'text',


            }, {
                dataIndex: 'status',
                xtype: 'select',
                store: [{
                    key: '',
                    value: '选择状态'
                },
                {
                    key: 'true',
                    value: '上架'
                },
                {
                    key: 'false',
                    value: '下架'
                },

                ]



            }

            ],
            selector: {
                dataIndex: 'id',
                name: '',//checkbox 的 name 属性值,默认取 dataIndex 值
                //初始化是否选中checkbox
                checked: function (rowData, data, config, index) {
                    return false;
                },
                render: function (rowData, data, config, index) {
                    //返回值仅仅作为 checkbox 的 value 属性值
                    return rowData.id;
                }
            },
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: "批量添加",
                    class: 'danger',
                    click: function () {
                        Table({
                            title: '批量添加',
                            width: 500,
                            store: 'jiakao-misc!video-explain/data/uploadBatchCoverH',
                            success: function (obj, dialog) {
                                dialog.close();
                                table.render();
                            },
                            error: function (table, dialog, data) {
                                if (data.data == false) {
                                    Widgets.dialog.alert('处理失败')
                                } else {
                                    Widgets.dialog.alert(data.message)
                                }
                            },
                            columns: [{
                                header: '配置文件：',
                                dataIndex: 'config',
                                xtype: 'file',
                                placeholder: '配置文件(xls格式)',

                            },
                            // {
                            //     header: '图片：',
                            //     dataIndex: 'imgs',
                            //     xtype: 'file',
                            //     placeholder: '图片(zip格式)',
                            // },
                            {
                                header: '内置的图片：',
                                dataIndex: 'fileUrl',
                                xtype: Plugin('jiakao-misc!upload1', {
                                    dataIndex: 'fileUrl',
                                    uploadIndex: 'fileUrl',
                                    bucket: "exam-room",
                                    isSingle: true,
                                    placeholder: '请选择上传文件',
                                    url: 'simple-upload3://upload/file.htm'
                                }, function () {
                                    fileUrl = arguments[2];
                                })
                            }

                            ]
                        }).add();
                    }
                },
                {
                    name: '批量上线',
                    class: 'danger',
                    click: function (table, init, data, checklist) {
                        if (checklist && checklist.length <= 0) {
                            Widgets.dialog.alert("请勾选数据")
                            return
                        }
                        let ids = checklist.join(",")
                        Store(['jiakao-misc!video-explain/data/onlineMore']).load([{
                            aliases: 'onlineMore',
                            params: {
                                ids: ids
                            }
                        }]).done(function (store, data, fn) {
                            table.render();
                            Widgets.dialog.alert("批量上线成功")
                        }).fail(function () {
                            Widgets.dialog.alert("批量上线失败")
                        })
                    }
                },

                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!video-explain/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '试题Id：',
                    dataIndex: 'questionId'
                },
                {
                    header: '科目：',
                    dataIndex: 'kemu'
                },
                {
                    header: '横屏封面图：',
                    dataIndex: 'coverH'
                },
                {
                    header: '竖屏封面图：',
                    dataIndex: 'coverV'
                },
                {
                    header: '视频地址：',
                    dataIndex: 'videoUrl'
                },
                {
                    header: '加密视频地址：',
                    dataIndex: 'videoEncodeUrl'
                },
                {
                    header: 'createTime：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: 'createUserId：',
                    dataIndex: 'createUserId'
                },
                {
                    header: 'createUserName：',
                    dataIndex: 'createUserName'
                },
                {
                    header: 'updateTime：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: 'updateUserId：',
                    dataIndex: 'updateUserId'
                },
                {
                    header: 'updateUserName：',
                    dataIndex: 'updateUserName'
                }

                ]
            },
            {
                name: '编辑',
                xtype: 'edit',
                width: 500,
                class: 'warning',
                title: '编辑',
                success: function (obj, dialog, e) {
                    dialog.close();
                    obj.render();
                },
                store: {
                    load: 'jiakao-misc!video-explain/data/view',
                    save: 'jiakao-misc!video-explain/data/update'
                },
                columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: 'questionId：',
                    dataIndex: 'questionId',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'questionId'
                },
                {
                    header: 'kemu：',
                    dataIndex: 'kemu',
                    xtype: 'text',
                    placeholder: 'kemu'
                },
                {
                    header: '横批/竖屏',
                    dataIndex: 'direction',
                    xtype: 'select',
                    store: directionArr,
                    placeholder: '横批/竖屏'
                },
                {
                    header: '横屏封面图：',
                    dataIndex: 'coverH',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'coverH',
                        uploadIndex: 'coverH',
                        bucket: "exam-room",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '竖屏封面图：',
                    dataIndex: 'coverV',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '竖屏封面图'
                },
                {
                    header: '视频地址：',
                    dataIndex: 'videoUrl',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '视频地址'
                },
                {
                    header: '加密视频地址：',
                    dataIndex: 'videoEncodeUrl',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '加密视频地址'
                }

                ]
            },
            {
                name: '上架',
                width: 500,
                class: 'primary',
                render: function (name, arr, index) {
                    return arr[index].status ? '' : name;
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.confirm('确定上架吗？', function (ev, status) {
                        if (status) {
                            Store(['jiakao-misc!video-explain/data/updateStatus?status=true&id=' + lineData.id]).save().done(function () {
                                table.render()
                            }).fail(err => {
                                console.log(err);
                            });
                        }
                    })
                }


            },
            {
                name: '下架',
                width: 500,
                class: '',
                render: function (name, arr, index) {
                    return arr[index].status ? name : '';
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.confirm('确定下架吗？', function (ev, status) {
                        if (status) {
                            Store(['jiakao-misc!video-explain/data/updateStatus?status=false&id=' + lineData.id]).save().done(function () {
                                table.render()
                            }).fail(err => {
                                console.log(err);
                            });
                        }
                    })
                }


            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!video-explain/data/delete'
            }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '试题Id',
                dataIndex: 'questionId'
            },
            // {
            //     header: '科目',
            //     dataIndex: 'kemu'
            // }, {
            //     header: '车型',
            //     dataIndex: 'carType'
            //     },
            //     {
            //         header: '场景:',
            //         dataIndex: 'sceneCode',
            //         render: function (data) {
            //             return sceneCodeMap[data]
            //         }
            //     },
                {
                    header: '车型科目场景题库版本',
                    dataIndex: 'carAndCourse',
                },
            {
                header: '横屏封面图',
                dataIndex: 'coverH',
                render: function (data) {
                    if (data) {
                        return '<a>点击查看</a>'
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('横屏封面图', {}).done(function (dialog) {
                        $(dialog.body).html('<img src="' + lineData.coverH + '"></img>')
                    })
                }
            },
            {
                header: '竖屏封面图',
                dataIndex: 'coverV',
                render: function (data) {
                    if (data) {
                        return '<a>点击查看</a>'
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('竖屏封面图', {}).done(function (dialog) {
                        $(dialog.body).html('<img src="' + lineData.coverV + '"></img>')
                    })
                }
            },
            {
                header: '横盘/竖屏',
                dataIndex: 'direction',
                render: function (data) {
                    return directionMap[data]
                }
            },
            {
                header: '视频地址',
                dataIndex: 'videoUrl',
                render: function (data) {
                    if (data) {
                        return '<a>点击查看</a>'
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('视频地址', {}).done(function (dialog) {
                        $(dialog.body).html('<a href="' + lineData.videoUrl + '" target="_blank">' + lineData.videoUrl + '</a><br/><video style="width: 100%;" src="' + lineData.videoUrl + '" controls></video>')
                    })
                }
            },
            {
                header: '加密视频地址',
                dataIndex: 'videoEncodeUrl',
                render: function (data) {
                    if (data) {
                        return '<a>点击查看</a>'
                    }
                },
                click: function (table, row, lineData) {
                    Widgets.dialog.html('加密视频地址', {}).done(function (dialog) {
                        $(dialog.body).html('<div>' + lineData.videoEncodeUrl + '</div><br/>')
                    })
                }
            }, {
                header: '状态',
                dataIndex: 'status',
                render: function (data) {
                    return data ? '上架' : '下架';
                }
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },

            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '修改时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },

            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!video-explain/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});