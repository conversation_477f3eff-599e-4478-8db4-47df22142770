/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function(Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var getMapfromArray = function(store) {
        var obj = {};
        store.forEach(item => {obj[item.key] = item.value})
        return obj;
    }
    var typeStore = [{
        key: 'normal',
        value: '普通跳转'
    }, {
        key: 'vip',
        value: 'vip售卖'
    }, {
        key: 'group',
        value: '拼单商品'
    }]
    var typeMap = getMapfromArray(typeStore);
    
    var submitHandler = function (form) {
        return {
        };
    }

    var columnsForAdd = [
        {
            header: '视频类型：',
            dataIndex: 'videoType',
            xtype: 'select',
            store: typeStore,
            placeholder: '视频类型'
        },
        {
            header: '视频ID：',
            dataIndex: 'videoId',
            xtype: 'text',
            maxlength: 128,
            placeholder: '视频ID'
        },
        {
            header: '关键点名称：',
            dataIndex: 'name',
            xtype: 'text',
            maxlength: 128,
            placeholder: '关键点名称'
        },
        {
            header: '关键点描述：',
            dataIndex: 'describe',
            xtype: 'text',
            maxlength: 128,
            placeholder: '关键点描述'
        },
        {
            header: '片段起始时间：',
            dataIndex: 'beginTime',
            xtype: 'datetime',
            render: function (data) {
                if(data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                }
            },
            placeholder: '片段起始时间'
        },
        {
            header: '片段结束时间：',
            dataIndex: 'endTime',
            xtype: 'datetime',
            render: function (data) {
                if(data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                }
            },
            placeholder: '片段结束时间'
        },
    ]
    var columnsForEdit = [].concat(columnsForAdd)
    columnsForEdit.unshift({
        dataIndex: 'id',
        xtype: 'hidden'
    })

    var add = function (table, lineData) {
        if(!lineData.id) {
            lineData = {}
        }
        var id = lineData.lessonItemId
        Table().edit(lineData, {
            title: '添加',
            width: 800,
            store: 'jiakao-misc!top-lesson-advert/data/insert?lessonItemId='+id,
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: submitHandler
            },
            columns: columnsForAdd
        });
    }

    var list = function(panel, routeData) {

        Table({
            description: '子课程运营位模板管理',
            title: '子课程运营位模板管理',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 800,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    form: {
                        submitHandler: submitHandler
                    },
                    store: {
                        load: 'jiakao-misc!top-lesson-advert/data/view',
                        save: 'jiakao-misc!top-lesson-advert/data/update'
                    },
                    columns: columnsForEdit
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!top-lesson-advert/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '视频类型：',
                    dataIndex: 'videoType',
                    render: function (data, arr, i) {
                        return typeMap[data]
                    }
                },
                {
                    header: '视频ID：',
                    dataIndex: 'videoId'
                },
                {
                    header: '关键点名称：',
                    dataIndex: 'name'
                },
                {
                    header: '关键点描述：',
                    dataIndex: 'describe'
                },
                {
                    header: '开始时间：',
                    dataIndex: 'beginTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                {
                    header: '结束时间：',
                    dataIndex: 'endTime',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
            ]
        }, ['jiakao-misc!top-lesson-advert/data/list'], panel, null).render();
    }

    return {
        list: list,
        add: add
    }
});
