/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'jiakao-misc!app/common/tiku',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, TIKU, Tools) {


    var sceneCodeMap = {
        101: '基础场景',
        102: '扣满12分',
        103: '维语场景'
    }
    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!video-practice/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '试题id：',
                dataIndex: 'questionId',
                xtype: 'text',
                maxlength: 32,
                placeholder: '试题id'
            },
         
            {
                header: '原始视频地址：',
                dataIndex: 'originalUrl',
                xtype: 'textarea',
                maxlength: 512,
                placeholder: '原始视频地址'
            },
            {
                header: '封面地址：',
                dataIndex: 'cover',
                xtype: 'textarea',
                maxlength: 512,
                placeholder: '封面地址'

            }

            ]
        }).add();
    }


    var multiAdd = function (table) {
        Table({
            title: '批量添加',
            width: 500,
            store: 'jiakao-misc!video-practice/data/createBatch',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '选择文件：',
                dataIndex: 'data',
                xtype: 'file'
            }]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '视频刷题列表',
            title: '视频刷题列表',
            search: [
                {
                    placeholder: '短视频id',
                    dataIndex: 'shortVideoId',
                    xtype: 'text'
                },
                {
                    placeholder: '试题id',
                    dataIndex: 'questionId',
                    xtype: 'text',
                },
                {
                    dataIndex: 'carType',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: '请选择车型'
                        },
                        ...Tools.getArrayFromMap(TIKU)
                    ]
                },
                {
                    placeholder: '创建人',
                    dataIndex: 'createUserName',
                    xtype: 'text',
                },
                {
                    placeholder: '是否启用',
                    dataIndex: 'enable',
                    xtype: 'select',
                    store: [{ key: '', value: '请选择' }, { key: '1', value: '已启用' }, {
                        key: '0', value: '未启用'
                    }]
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '批量启用',
                        class: 'primary',
                        click: function (table, $panel, dataList, selectList) {
                            if (selectList.length <= 0) {
                                Widgets.dialog.alert('请勾选数据');
                                return
                            }
                            Widgets.dialog.confirm('是否批量启用？', function (e, confirm) {
                                if (confirm) {
                                    Store(['jiakao-misc!video-practice/data/updateStatus']).load([{
                                        params: {
                                            ids: selectList.join(','),
                                            enable: 1
                                        }
                                    }]).done(function (store, data, fn) {
                                        table.render()
                                        Widgets.dialog.alert('批量启用成功')
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message);
                                    });
                                }

                            })
                        }
                    },
                    {
                        name: '批量禁用',
                        class: 'primary',
                        click: function (table, $panel, dataList, selectList) {
                            if (selectList.length <= 0) {
                                Widgets.dialog.alert('请勾选数据');
                                return
                            }
                            Widgets.dialog.confirm('是否批量禁用？', function (e, confirm) {
                                if (confirm) {
                                    Store(['jiakao-misc!video-practice/data/updateStatus']).load([{
                                        params: {
                                            ids: selectList.join(','),
                                            enable: 0
                                        }
                                    }]).done(function (store, data, fn) {
                                        table.render()
                                        Widgets.dialog.alert('批量禁用成功')
                                    }).fail(function (ret) {
                                        Widgets.dialog.alert(ret.message);
                                    });
                                }

                            })
                        }
                    }
                    // {
                    //     name: '添加',
                    //     class: 'primary',
                    //     click: add
                    // },
                    // {
                    //     name: '批量添加',
                    //     class: 'warning',
                    //     click: multiAdd
                    // }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!video-practice/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '试题id：',
                            dataIndex: 'questionId'
                        },
                        {
                            header: '原始视频地址：',
                            dataIndex: 'originalUrl',
                            render: function (data) {
                                return '<a href="' + data + '" target="_blank">' + data + '</a>'
                            }
                        },
                        {
                            header: '加密视频地址：',
                            dataIndex: 'encryptedUrl',
                            render: function (data) {
                                return '<a href="' + data + '" target="_blank">' + data + '</a>'
                            }
                        },
                        {
                            header: '封面地址：',
                            dataIndex: 'cover',
                            render: function (data) {
                                return '<a href="' + data + '" target="_blank">' + data + '</a>'
                            }

                        },
                        {
                            header: '短视频id',
                            dataIndex: 'shortVideoId',
                        },
                        {
                            header: 'createTime：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人ID：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改人ID：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!video-practice/data/delete'
                }
            ],
            selector: {
                dataIndex: 'id',
            },
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '试题id',
                    dataIndex: 'questionId'
                },
                {
                    header: '车型',
                    dataIndex: 'carType'
                },
                {
                    header: '场景:',
                    dataIndex: 'sceneCode',
                    render: function (data) {
                        return sceneCodeMap[data]
                    }
                },
                {
                    header: '原始视频地址',
                    dataIndex: 'originalUrl',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('原始视频地址', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.originalUrl + '" target="_blank">' + lineData.originalUrl + '</a>')
                        })
                    }
                },
                {
                    header: '加密视频地址',
                    dataIndex: 'encryptedUrl',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('加密视频地址', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.encryptedUrl + '" target="_blank">' + lineData.encryptedUrl + '</a>')
                        })
                    }
                },
                {
                    header: '封面地址',
                    dataIndex: 'cover',
                    render: function (data) {
                        return "<a href='" + data + "' target='_blank' >点击查看</a>"
                    }
                },
                {
                    header: '短视频id',
                    dataIndex: 'shortVideoId',
                },
                {
                    header: '是否启用',
                    dataIndex: 'enable',
                    render: function (data) {
                        return data ? '是' : '否'
                    }
                },
                // {
                //     header: '原始视频地址',
                //     dataIndex: 'originalUrl',
                //     render: function (data) {
                //         return '<a href="'+data+'" target="_blank">'+data+'</a>'
                //     }
                // },
                // {
                //     header: '加密视频地址',
                //     dataIndex: 'encryptedUrl',
                //     render: function (data) {
                //         return '<a href="'+data+'" target="_blank">'+data+'</a>'
                //     }
                // },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!video-practice/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});