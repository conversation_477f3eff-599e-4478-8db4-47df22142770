/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var statusMap = {
        0: '上传中',
        1: '转码中',
        2: '加密中',
        3: '已完成',
    }
    var directionMap = {
        1: '竖屏',
        2: '横屏'
    }
    var sceneCodeMap = {
        101: '基础场景',
        102: '扣满12分',
        103: '维语场景'
    }

    var sceneCodeArr = [];
    for (const key in sceneCodeMap) {
        sceneCodeArr.push({ key, value: sceneCodeMap[key] })
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!video-upload/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '业务id：',
                dataIndex: 'bizId',
                xtype: 'text',
                maxlength: 32,
                placeholder: '业务id'
            },
            {
                header: '业务类型：',
                dataIndex: 'bizType',
                xtype: 'text',
                maxlength: 32,
                placeholder: '业务类型'
            },
            {
                header: '场景:',
                dataIndex: 'sceneCode',
                xtype: 'select',
                store: sceneCodeArr
            },
            {
                header: '视频上传的key：',
                dataIndex: 'videoKey',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '视频上传的key'
            },
            {
                header: 'status：',
                dataIndex: 'status',
                xtype: 'text',
                placeholder: 'status'
            },
            {
                header: '标清视频：',
                dataIndex: 'videoL',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '标清视频'
            },
            {
                header: '高清视频：',
                dataIndex: 'videoM',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '高清视频'
            },
            {
                header: '超清视频：',
                dataIndex: 'videoH',
                xtype: 'textarea',
                maxlength: 256,
                placeholder: '超清视频'
            },
            {
                header: '标清视频：',
                dataIndex: 'videoEncodeL',
                xtype: 'textarea',
                maxlength: 1024,
                placeholder: '标清视频'
            },
            {
                header: '高清视频：',
                dataIndex: 'videoEncodeM',
                xtype: 'textarea',
                maxlength: 1024,
                placeholder: '高清视频'
            },
            {
                header: '超清视频：',
                dataIndex: 'videoEncodeH',
                xtype: 'textarea',
                maxlength: 1024,
                placeholder: '超清视频'
            }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '视频上传记录列表',
            title: '视频上传记录列表',
            search: [
                {
                    placeholder: '业务类型',
                    dataIndex: 'bizType',
                    xtype: 'select',
                    store: 'jiakao-misc!video-upload/data/bizTypeList',
                    insert: [
                        {
                            key: '',
                            value: '选择业务类型'
                        }
                    ]
                },
                {
                    placeholder: '业务id',
                    dataIndex: 'bizId',
                    xtype: 'text',
                },
                {
                    placeholder: '创建人',
                    dataIndex: 'createUserName',
                    xtype: 'text',
                }
            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                },
                {
                    name: '上传视频',
                    class: 'warning',
                    click: function (table) {
                        var _this = $(this);
                        if (_this.hasClass('disabled')) {
                            return;
                        }
                        _this.addClass('disabled');
                        setTimeout(function () {
                            _this.removeClass('disabled');
                        }, 1000)
                        Plugin("jiakao-misc!select-single-file", {
                            uploadUrl: "jiakao-misc://api/admin/video-upload/upload-video.htm",
                            postData: {},
                            limit: 300,
                            complete: function () {
                                console.log(777);
                                table.render();
                            }
                        }).render();
                    }
                }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!video-upload/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '业务id：',
                            dataIndex: 'bizId'
                        },
                        {
                            header: '业务类型：',
                            dataIndex: 'bizType'
                        },
                        {
                            header: '视频上传的key：',
                            dataIndex: 'videoKey'
                        },
                        {
                            header: 'status：',
                            dataIndex: 'status'
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoL'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoM'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoH'
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoEncodeL'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoEncodeM'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoEncodeH'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人ID：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人ID：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }
                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!video-upload/data/view',
                        save: 'jiakao-misc!video-upload/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '业务id：',
                            dataIndex: 'bizId',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '业务id'
                        },
                        {
                            header: '业务类型：',
                            dataIndex: 'bizType',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '业务类型'
                        },
                        {
                            header: '场景:',
                            dataIndex: 'sceneCode',
                            xtype: 'select',
                            store: sceneCodeArr
                        },
                        {
                            header: '视频上传的key：',
                            dataIndex: 'videoKey',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '视频上传的key'
                        },
                        {
                            header: 'status：',
                            dataIndex: 'status',
                            xtype: 'text',
                            placeholder: 'status'
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoL',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '标清视频'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoM',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '高清视频'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoH',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '超清视频'
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoEncodeL',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '标清视频'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoEncodeM',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '高清视频'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoEncodeH',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '超清视频'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!video-upload/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '业务id',
                    dataIndex: 'bizId'
                },
                {
                    header: '业务类型',
                    dataIndex: 'bizType'
                },
                {
                    header: '时长',
                    dataIndex: 'duration',
                    render: function (data) {
                        data = data ? data : 0;
                        return data + '秒'
                    }
                },
                {
                    header: '场景:',
                    dataIndex: 'sceneCode',
                    render: function (data) {
                        return sceneCodeMap[data]
                    }
                },
                {
                    header: '缩略图',
                    dataIndex: 'thumbnails',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('查看缩略图', {}).done(function (dialog) {
                            var thumbnails = lineData.thumbnails;
                            if (thumbnails) {
                                thumbnails = thumbnails.split(',');
                            }
                            var htmlStr = '';
                            for (var i = 0; i < thumbnails.length; i++) {
                                htmlStr += '<p><img style="width: 100px; height: 100px;" src="' + thumbnails[i] + '"><a href="' + thumbnails[i] + '" target="_blank">' + thumbnails[i] + '</a></p>'
                            }
                            $(dialog.body).html(htmlStr)
                        })
                    }
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data];
                    }
                },
                {
                    header: '横盘/竖屏',
                    dataIndex: 'direction',
                    render: function (data) {
                        return directionMap[data]
                    }
                },
                {
                    header: '标清视频',
                    dataIndex: 'videoL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('标清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.videoL + '" target="_blank">' + lineData.videoL + '</a><br/><video style="width: 100%;" src="' + lineData.videoL + '" controls></video>')
                        })
                    }
                },
                {
                    header: '高清视频',
                    dataIndex: 'videoM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('高清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.videoM + '" target="_blank">' + lineData.videoM + '</a><br/><video style="width: 100%;" src="' + lineData.videoM + '" controls></video>')
                        })
                    }
                },
                {
                    header: '超清视频',
                    dataIndex: 'videoH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('超清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<a href="' + lineData.videoH + '" target="_blank">' + lineData.videoH + '</a><br/><video style="width: 100%;" src="' + lineData.videoH + '" controls></video>')
                        })
                    }
                },
                {
                    header: '加密标清视频',
                    dataIndex: 'videoEncodeL',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('加密标清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.videoEncodeL + '</div><br/>')
                        })
                    }
                },
                {
                    header: '加密高清视频',
                    dataIndex: 'videoEncodeM',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('加密高清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.videoEncodeM + '</div><br/>')
                        })
                    }
                },
                {
                    header: '加密超清视频',
                    dataIndex: 'videoEncodeH',
                    render: function (data) {
                        if (data) {
                            return '<a>点击查看</a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('加密超清视频', {}).done(function (dialog) {
                            $(dialog.body).html('<div>' + lineData.videoEncodeH + '</div><br/>')
                        })
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }
            ]
        }, ['jiakao-misc!video-upload/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});