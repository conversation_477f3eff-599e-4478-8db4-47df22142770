/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {
    var statusMap = {
        0: '下线',
        1: '上线'
    };
    var statusRender = function (data) {
        return statusMap[data];
    };
    var answerRender = function (data) {
        return '<span style="white-space: pre-wrap;">' + data + '</span>';
    }

    var add = function (table, lineData) {
        Table({
            title: '添加',
            width: 1000,
            store: 'jiakao-misc!want-ask-question/data/insert?wantAskId=' + lineData.id,
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '排序：',
                    dataIndex: 'sort',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '排序'
                },
                {
                    header: '问题：',
                    dataIndex: 'question',
                    xtype: 'text',
                    maxlength: 200,
                    check: 'required',
                    placeholder: '问题'
                },
                {
                    header: '回答：',
                    dataIndex: 'answer',
                    xtype: 'textarea',
                    check: 'required',
                }

            ]
        }).add();
    }

    var list = function (panel, lineData) {
        Table({
            description: '猜你想问问答列表',
            title: '猜你想问问答列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click(table) {
                            add(table, lineData);
                        }
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!want-ask-question/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sort',
                            order: 'asc'
                        },
                        {
                            header: '问题：',
                            dataIndex: 'question'
                        },
                        {
                            header: '回答：',
                            dataIndex: 'answer',
                            render: answerRender
                        },
                        {
                            header: '状态：',
                            dataIndex: 'status',
                            render: statusRender
                        },

                    ]
                },
                {
                    name: '上线/下线',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '上线/下线',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render()
                    },
                    store: {
                        load: 'jiakao-misc!want-ask-question/data/view',
                        save: 'jiakao-misc!want-ask-question/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    }, {
                        header: '状态：',
                        dataIndex: 'status',
                        xtype: 'select',
                        check: 'required',
                        store: [{
                            key: '0',
                            value: '下线'
                        },
                        {
                            key: 1,
                            value: '上线'
                        }]
                    },]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!want-ask-question/data/view',
                        save: 'jiakao-misc!want-ask-question/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '排序：',
                            dataIndex: 'sort',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '排序'
                        },
                        {
                            header: '问题：',
                            dataIndex: 'question',
                            xtype: 'text',
                            maxlength: 200,
                            check: 'required',
                            placeholder: '问题'
                        },
                        {
                            header: '回答：',
                            dataIndex: 'answer',
                            xtype: 'textarea',
                            check: 'required',
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!want-ask-question/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '标签id',
                    dataIndex: 'wantAskId'
                },
                {
                    header: '排序',
                    dataIndex: 'sort'
                },
                {
                    header: '问题',
                    dataIndex: 'question'
                },
                {
                    header: '回答',
                    dataIndex: 'answer',
                    render: answerRender
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: statusRender
                },

            ]
        }, ['jiakao-misc!want-ask-question/data/list?wantAskId=' + lineData.id], panel, null).render();
    }

    return {
        list: list
    }

});