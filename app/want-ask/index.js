/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {
    var statusMap = {
        0: '下线',
        1: '上线'
    };
    var askTypeMap = {
        1: '不带更多问题按钮',
        2: '带更多问题按钮'
    };
    var askTypeStore = Object.keys(askTypeMap).map(key => {
        return {
            key: key,
            value: askTypeMap[key]
        }
    });
    var statusRender = function (data) {
        return statusMap[data];
    };
    var askTypeRender = function (data) {
        return askTypeMap[data];
    };

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!want-ask/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '类型：',
                    dataIndex: 'askType',
                    xtype: 'select',
                    check: 'required',
                    store: askTypeStore
                },
                {
                    header: '问答key：',
                    dataIndex: 'qaCode',
                    xtype: 'text',
                    maxlength: 100,
                    placeholder: '问答key'
                },
                {
                    header: '备注：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 100,
                    placeholder: '备注'
                },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '猜你想问入口列表',
            title: '猜你想问入口列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!want-ask/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '类型：',
                            dataIndex: 'askType'
                        },
                        {
                            header: '问答key：',
                            dataIndex: 'qaCode'
                        },
                        {
                            header: '备注',
                            dataIndex: 'remark'
                        },
                        {
                            header: '状态',
                            dataIndex: 'status',
                            render: statusRender
                        },

                    ]
                },
                {
                    name: '上线/下线',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '上线/下线',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render()
                    },
                    store: {
                        load: 'jiakao-misc!want-ask/data/view',
                        save: 'jiakao-misc!want-ask/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    }, {
                        header: '状态：',
                        dataIndex: 'status',
                        xtype: 'select',
                        check: 'required',
                        store: [{
                            key: '0',
                            value: '下线'
                        },
                        {
                            key: 1,
                            value: '上线'
                        }]
                    },]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!want-ask/data/view',
                        save: 'jiakao-misc!want-ask/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '类型',
                            dataIndex: 'askType',
                            xtype: 'select',
                            check: 'required',
                            store: askTypeStore
                        },
                        {
                            header: '问答key：',
                            dataIndex: 'qaCode',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '问答key'
                        },
                        {
                            header: '备注：',
                            dataIndex: 'remark',
                            xtype: 'text',
                            maxlength: 100,
                            placeholder: '备注'
                        }

                    ]
                },
                {
                    name: '问答配置',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        require(['simple!app/layout/main'], function (Main) {
                            var nPanel = Main.panel('want-ask-question-' + lineData.id);
                            if (nPanel.length == 0) {
                                nPanel = Main.panel({
                                    id: 'want-ask-question-' + lineData.id,
                                    name: '问答配置: ' + lineData.qaCode
                                })
                            }
                            require(['jiakao-misc!app/want-ask-question/index'], function (Item) {
                                Item.list(nPanel, lineData)
                            })
                        });
                    }
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!want-ask/data/delete'
                },
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '类型',
                    dataIndex: 'askType',
                    render: askTypeRender
                },
                {
                    header: '问答key',
                    dataIndex: 'qaCode'
                },
                {
                    header: '备注',
                    dataIndex: 'remark'
                },
                {
                    header: '状态',
                    dataIndex: 'status',
                    render: statusRender
                }

            ]
        }, ['jiakao-misc!want-ask/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});