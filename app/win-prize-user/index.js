/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";
define([
    'simple!core/template',
    'simple!core/table',
    'simple!core/utils',
    'simple!core/widgets',
    'simple!core/store',
    'simple!core/form',
    'simple!core/plugin',
    'jiakao-misc!app/common/constants',
    'jiakao-misc!app/common/tools'
], function (Template, Table, Utils, Widgets, Store, Form, Plugin, Constants, Tools) {
    var add = function (table) {
        Table({
            title: '添加中奖人员',
            width: 500,
            store: 'jiakao-misc!win-prize-user/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '中奖用户id：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '中奖用户id'
                },
                {

                    header: '中奖场次id：',
                    dataIndex: 'drawPrizeId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '中奖场次id'
                },
                {
                    header: '备注：',
                    dataIndex: 'remark',
                    xtype: 'text',
                    maxlength: 200,
                    placeholder: '备注'
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '直播中奖人员名单',
            title: '直播中奖人员名单',
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加中奖人员',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!win-prize-user/data/view',
                        save: 'jiakao-misc!win-prize-user/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '中奖用户id：',
                            dataIndex: 'userId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '用户id'
                        },
                        {
                            header: '中奖场次id：',
                            dataIndex: 'drawPrizeId',
                            xtype: 'text',
                            placeholder: '中奖场次id'
                        },
                        {
                            header: '备注：',
                            dataIndex: 'remark',
                            xtype: 'text',
                            maxlength: 200,
                            placeholder: '备注'
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!win-prize-user/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户id',
                    dataIndex: 'userId'
                },
                {
                    header: '中奖用户昵称',
                    dataIndex: 'nickName'
                },
                {
                    header: '中奖用户手机号',
                    dataIndex: 'phone',
                    render: function (data) {
                        if (data) {
                            return `<a>${data}</a>`
                        }
                    },
                    click: function (table, row, lineData) {
                        Store(['jiakao-misc!win-prize-user/data/viewPhone?id=' + lineData.id]).load().done(function (store) {
                            Widgets.dialog.html('手机号码', {}).done(function (dialog) {
                                $(dialog.body).html(store.data['win-prize-user'].data.viewPhone.data.value)
                            })
                        }).fail(function (res) {
                        });

                    }
                },
                {
                    header: '中奖日期',
                    dataIndex: 'date'
                },
                {
                    header: '中奖场次id',
                    dataIndex: 'drawPrizeId'
                },
                {
                    header: '中奖时间',
                    dataIndex: 'time'
                },
                {
                    header: '备注',
                    dataIndex: 'remark'
                },
            ]
        }, ['jiakao-misc!win-prize-user/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});