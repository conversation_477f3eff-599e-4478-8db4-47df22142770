/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-award-round/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            form: {
                submitHandler: function (form) {

                    var presents = $(form).find('.tag-map');

                    var expression = [];

                    presents.each(function (index, item) {
                        var presentId = $(item).find('select').val();
                        var count = $(item).find('input').val();
                        if(presentId && count){
                            expression.push({"presentId":presentId,"count":count})
                        }
                    });

                    form.expression.value = JSON.stringify(expression);
                    return true;
                }
            },
            columns: [
                {
                    header: '直播ID：',
                    dataIndex: 'liveId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '直播ID'
                },
                {
                    header: '第几轮：',
                    dataIndex: 'roundIndex',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '第几轮'
                },
                {
                    header: '开奖时间：',
                    dataIndex: 'openTime',
                    xtype: 'datetime',
                    check: 'required',
                    placeholder: '开奖时间'
                },
                {
                    header: '表达式：',
                    dataIndex: 'expression',
                    xtype: Plugin('jiakao-misc!award-round', {
                        dataIndex: 'expression'
                    }, function (plugin, value) {
                    })
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '开奖轮次表列表',
            title: '开奖轮次表列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-award-round/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '直播ID：',
                            dataIndex: 'liveId'
                        },
                        {
                            header: '第几轮：',
                            dataIndex: 'roundIndex'
                        },
                        {
                            header: '开奖时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'openTime'
                        },
                        {
                            header: '表达式：：',
                            dataIndex: 'expression'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: '创建人：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: '修改人：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    form: {
                        submitHandler: function (form) {

                            var presents = $(form).find('.tag-map');

                            var expression = [];

                            presents.each(function (index, item) {
                                var presentId = $(item).find('select').val();
                                var count = $(item).find('input').val();
                                if(presentId && count){
                                    expression.push({"presentId":presentId,"count":count})
                                }
                            });

                            form.expression.value = JSON.stringify(expression);
                            return true;
                        }
                    },
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-award-round/data/view',
                        save: 'jiakao-misc!xueche-award-round/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '直播ID：',
                            dataIndex: 'liveId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '直播ID'
                        },
                        {
                            header: '第几轮：',
                            dataIndex: 'roundIndex',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '第几轮'
                        },
                        {
                            header: '开奖时间：',
                            dataIndex: 'openTime',
                            xtype: 'datetime',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            check: 'required',
                            placeholder: '开奖时间'
                        },
                        // {
                        //     header: '表达式：：',
                        //     dataIndex: 'expression',
                        //     xtype: 'textarea',
                        //     maxlength: 512,
                        //     check: 'required',
                        //     placeholder: '表达式：'
                        // },
                        {
                            header: '表达式：',
                            dataIndex: 'expression',
                            xtype: Plugin('jiakao-misc!award-round', {
                                dataIndex: 'expression'
                            }, function (plugin, value) {
                            })
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-award-round/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '直播ID',
                    dataIndex: 'liveId'
                },
                {
                    header: '第几轮',
                    dataIndex: 'roundIndex'
                },
                {
                    header: '开奖时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'openTime'
                },
                {
                    header: '表达式',
                    dataIndex: 'expression'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!xueche-award-round/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
