/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-award-rule/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '直播ID：',
                 dataIndex: 'liveId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '直播ID'
             },
             {
                 header: '奖品ID：',
                 dataIndex: 'presentId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '奖品ID'
             },
             {
                 header: 'a：',
                 dataIndex: 'a',
                 xtype: 'text',
                 placeholder: 'a'
             },
             {
                 header: 'b：',
                 dataIndex: 'b',
                 xtype: 'text',
                 placeholder: 'b'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             },
             {
                 header: '随机的抽奖券ID，逗号分隔：',
                 dataIndex: 'randomLotteryIds',
                 xtype: 'textarea',
                 maxlength: 1024,
                 placeholder: '随机的抽奖券ID，逗号分隔'
             },
             {
                 header: '中奖号码：',
                 dataIndex: 'awardCode',
                 xtype: 'text',
                 placeholder: '中奖号码'
             },
             {
                 header: '状态：0-未生效；1-已生效；2-无效：',
                 dataIndex: 'status',
                 xtype: 'text',
                 placeholder: '状态：0-未生效；1-已生效；2-无效'
             },
             {
                 header: '时时彩期数：',
                 dataIndex: 'qishu',
                 xtype: 'text',
                 maxlength: 20,
                 placeholder: '时时彩期数'
             },
             {
                 header: '开奖轮次ID：',
                 dataIndex: 'roundId',
                 xtype: 'text',
                 placeholder: '开奖轮次ID'
             }

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '中奖规则表列表',
            title: '中奖规则表列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-award-rule/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '直播ID：',
                         dataIndex: 'liveId'
                     },
                     {
                         header: '奖品ID：',
                         dataIndex: 'presentId'
                     },
                     {
                         header: 'a：',
                         dataIndex: 'a'
                     },
                     {
                         header: 'b：',
                         dataIndex: 'b'
                     },
                     {
                         header: 'createTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserId：',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: 'createUserName：',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: 'updateTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: 'updateUserId：',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: 'updateUserName：',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '随机的抽奖券ID，逗号分隔：',
                         dataIndex: 'randomLotteryIds'
                     },
                     {
                         header: '中奖号码：',
                         dataIndex: 'awardCode'
                     },
                     {
                         header: '状态：0-未生效；1-已生效；2-无效：',
                         dataIndex: 'status'
                     },
                     {
                         header: '时时彩期数：',
                         dataIndex: 'qishu'
                     },
                     {
                         header: '开奖轮次ID：',
                         dataIndex: 'roundId'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-award-rule/data/view',
                        save: 'jiakao-misc!xueche-award-rule/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '直播ID：',
                 dataIndex: 'liveId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '直播ID'
             },
             {
                 header: '奖品ID：',
                 dataIndex: 'presentId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '奖品ID'
             },
             {
                 header: 'a：',
                 dataIndex: 'a',
                 xtype: 'text',
                 placeholder: 'a'
             },
             {
                 header: 'b：',
                 dataIndex: 'b',
                 xtype: 'text',
                 placeholder: 'b'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             },
             {
                 header: '随机的抽奖券ID，逗号分隔：',
                 dataIndex: 'randomLotteryIds',
                 xtype: 'textarea',
                 maxlength: 1024,
                 placeholder: '随机的抽奖券ID，逗号分隔'
             },
             {
                 header: '中奖号码：',
                 dataIndex: 'awardCode',
                 xtype: 'text',
                 placeholder: '中奖号码'
             },
             {
                 header: '状态：0-未生效；1-已生效；2-无效：',
                 dataIndex: 'status',
                 xtype: 'text',
                 placeholder: '状态：0-未生效；1-已生效；2-无效'
             },
             {
                 header: '时时彩期数：',
                 dataIndex: 'qishu',
                 xtype: 'text',
                 maxlength: 20,
                 placeholder: '时时彩期数'
             },
             {
                 header: '开奖轮次ID：',
                 dataIndex: 'roundId',
                 xtype: 'text',
                 placeholder: '开奖轮次ID'
             }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-award-rule/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '直播ID',
                         dataIndex: 'liveId'
                     },
                     {
                         header: '奖品ID',
                         dataIndex: 'presentId'
                     },
                     {
                         header: 'a',
                         dataIndex: 'a'
                     },
                     {
                         header: 'b',
                         dataIndex: 'b'
                     },
                     {
                         header: 'createTime',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserId',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: 'createUserName',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: 'updateTime',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: 'updateUserId',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: 'updateUserName',
                         dataIndex: 'updateUserName'
                     },
                     {
                         header: '随机的抽奖券ID，逗号分隔',
                         dataIndex: 'randomLotteryIds'
                     },
                     {
                         header: '中奖号码',
                         dataIndex: 'awardCode'
                     },
                     {
                         header: '状态：0-未生效；1-已生效；2-无效',
                         dataIndex: 'status'
                     },
                     {
                         header: '时时彩期数',
                         dataIndex: 'qishu'
                     },
                     {
                         header: '开奖轮次ID',
                         dataIndex: 'roundId'
                     }

            ]
        }, ['jiakao-misc!xueche-award-rule/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});