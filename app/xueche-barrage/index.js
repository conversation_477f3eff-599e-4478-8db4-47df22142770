/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var typeMap = {
        1: '文字',
        2: '礼物图片',
        3: '网络语图片'
    }

    var typeSelect = [].concat(Object.keys(typeMap).map(function (key) {
        return {
            key: key,
            value: typeMap[key]
        }
    }));

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-barrage/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            renderAfter: function (config, dom, data) {
                /** @type {HTMLElement} */
                var parent = dom.find('.form-group')[0].parentElement;
                var textContent = dom.find('.form-group')[1];
                var imageContent = dom.find('.form-group')[2];

                function toggleContentType() {
                    var type = dom.find('select#type')[0].value;

                    if (type <= 1) {
                        imageContent.remove();
                        parent.appendChild(textContent);
                    } else {
                        textContent.remove();
                        parent.appendChild(imageContent);
                    }
                }
                dom.find('select#type').on('change', toggleContentType);
                setTimeout(toggleContentType);
            },
            columns: [
                {
                    header: '类型',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: typeSelect,
                    check: 'required',
                },
                {
                    header: '弹幕内容',
                    dataIndex: 'content',
                    xtype: 'textarea',
                    check: 'required',
                    placeholder: '填写文字内容'
                },
                {
                    header: '弹幕内容',
                    dataIndex: 'content',
                    check: 'required',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'content',
                        uploadIndex: 'content',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '学车节弹幕内容表列表',
            title: '学车节弹幕内容表列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-barrage/data/view',
                        save: 'jiakao-misc!xueche-barrage/data/update'
                    },
                    renderAfter: function (config, dom, data) {
                        /** @type {HTMLElement} */
                        var parent = dom.find('.form-group')[0].parentElement;
                        var textContent = dom.find('.form-group')[2];
                        var imageContent = dom.find('.form-group')[3];

                        function toggleContentType() {
                            var type = dom.find('select#type')[0].value;

                            if (type <= 1) {
                                imageContent.remove();
                                parent.appendChild(textContent);
                            } else {
                                textContent.remove();
                                parent.appendChild(imageContent);
                            }
                        }
                        dom.find('select#type').on('change', toggleContentType);
                        setTimeout(toggleContentType);
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '类型',
                            dataIndex: 'type',
                            xtype: 'select',
                            store: typeSelect,
                            check: 'required',
                        },
                        {
                            header: '弹幕内容',
                            dataIndex: 'content',
                            xtype: 'textarea',
                            check: 'required',
                            placeholder: '填写文字内容'
                        },
                        {
                            header: '弹幕内容',
                            dataIndex: 'content',
                            check: 'required',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'content',
                                uploadIndex: 'content',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-barrage/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '类型',
                    dataIndex: 'type',
                    render: function (data) {
                        return typeMap[data];
                    }
                },
                {
                    header: '弹幕内容',
                    dataIndex: 'content',
                    render: function (data, rows, lineData) {
                        if (lineData.type > 1) {
                            return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                        } else {
                            return data;
                        }
                    },
                    click: function (table, row, lineData) {
                        if (lineData.type > 1) {
                            Widgets.dialog.html('弹幕图片', {}).done(function (dialog) {
                                $(dialog.body).html('<img src="' + lineData.content + '"></img>')
                            })
                        }
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人名称',
                    dataIndex: 'createUserName'
                }
            ]
        }, ['jiakao-misc!xueche-barrage/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});