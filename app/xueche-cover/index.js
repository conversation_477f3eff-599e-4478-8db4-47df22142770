/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-cover/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '剩余天数：',
                    dataIndex: 'remainDay',
                    xtype: 'number',
                    check: 'required',
                    placeholder: '剩余天数'
                },
                {
                    header: '封面地址：',
                    dataIndex: 'cover',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'cover',
                        uploadIndex: 'cover',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '学车节视频封面表列表',
            title: '学车节视频封面表列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-cover/data/view',
                        save: 'jiakao-misc!xueche-cover/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '剩余天数：',
                            dataIndex: 'remainDay',
                            xtype: 'number',
                            check: 'required',
                            placeholder: '剩余天数'
                        },
                        {
                            header: '封面地址：',
                            dataIndex: 'cover',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'cover',
                                uploadIndex: 'cover',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-cover/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '剩余天数',
                    dataIndex: 'remainDay'
                },
                {
                    header: '封面地址',
                    dataIndex: 'cover',
                    render: function (data) {
                        if (data) {
                            return '<a><image style="width: 100px; height: auto;" src="' + data + '"></a>'
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.html('封面地址', {}).done(function (dialog) {
                            $(dialog.body).html('<img src="' + lineData.cover + '"></img>')
                        })
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人名称',
                    dataIndex: 'createUserName'
                }

            ]
        }, ['jiakao-misc!xueche-cover/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});