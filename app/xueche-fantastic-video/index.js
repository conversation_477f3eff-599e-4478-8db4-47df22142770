/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-fantastic-video/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '视频标题：',
                    dataIndex: 'title',
                    xtype: 'text',
                    maxlength: 45,
                    check: 'required',
                    placeholder: '视频标题'
                },
                {
                    header: '视频封面：',
                    dataIndex: 'cover',
                    xtype: 'textarea',
                    maxlength: 255,
                    check: 'required',
                    placeholder: '视频封面'
                },
                {
                    header: '高清视频：',
                    dataIndex: 'videoL',
                    xtype: 'textarea',
                    maxlength: 255,
                    check: 'required',
                    placeholder: '高清视频'
                },
                {
                    header: '标清视频：',
                    dataIndex: 'videoM',
                    xtype: 'textarea',
                    maxlength: 255,
                    check: 'required',
                    placeholder: '标清视频'
                },
                {
                    header: '超清视频：',
                    dataIndex: 'videoH',
                    xtype: 'textarea',
                    maxlength: 255,
                    check: 'required',
                    placeholder: '超清视频'
                },
                {
                    header: '排序号：',
                    dataIndex: 'sorter',
                    xtype: 'text',
                    placeholder: '排序号'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '学车节精彩视频表列表',
            title: '学车节精彩视频表列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-fantastic-video/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '视频标题：',
                            dataIndex: 'title'
                        },
                        {
                            header: '视频封面：',
                            dataIndex: 'cover'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoL'
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoM'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoH'
                        },
                        {
                            header: '排序号：',
                            dataIndex: 'sorter'
                        },
                        {
                            header: '创建时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '修改时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '修改人',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                // {
                //     name: '修改排序号',
                //     xtype: 'edit',
                //     width: 500,
                //     class: 'warning',
                //     title: '编辑',
                //     success: function (obj, dialog, e) {
                //         dialog.close();
                //         obj.render();
                //     },
                //     store: {
                //         load: 'jiakao-misc!xueche-fantastic-video/data/view',
                //         save: 'jiakao-misc!xueche-fantastic-video/data/update'
                //     },
                //     columns: [
                //         {
                //             dataIndex: 'id',
                //             xtype: 'hidden'
                //         },
                //         {
                //             header: '排序号：',
                //             dataIndex: 'sorter',
                //             xtype: 'text',
                //             placeholder: '排序号'
                //         }
                //     ]
                // },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-fantastic-video/data/view',
                        save: 'jiakao-misc!xueche-fantastic-video/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '视频标题：',
                            dataIndex: 'title',
                            xtype: 'text',
                            maxlength: 45,
                            check: 'required',
                            placeholder: '视频标题'
                        },
                        {
                            header: '视频封面：',
                            dataIndex: 'cover',
                            xtype: 'textarea',
                            maxlength: 255,
                            check: 'required',
                            placeholder: '视频封面'
                        },
                        {
                            header: '高清视频：',
                            dataIndex: 'videoL',
                            xtype: 'textarea',
                            maxlength: 255,
                            check: 'required',
                            placeholder: '高清视频'
                        },
                        {
                            header: '标清视频：',
                            dataIndex: 'videoM',
                            xtype: 'textarea',
                            maxlength: 255,
                            check: 'required',
                            placeholder: '标清视频'
                        },
                        {
                            header: '超清视频：',
                            dataIndex: 'videoH',
                            xtype: 'textarea',
                            maxlength: 255,
                            check: 'required',
                            placeholder: '超清视频'
                        },
                        {
                            header: '排序号：',
                            dataIndex: 'sorter',
                            xtype: 'text',
                            placeholder: '排序号'
                        }
                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-fantastic-video/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '视频标题',
                    dataIndex: 'title'
                },
                {
                    header: '视频封面',
                    dataIndex: 'cover',
                    render: function (data) {
                        return '<img style="width: 300px" src="'+data+'"/>'
                    }
                },
                {
                    header: '高清视频',
                    dataIndex: 'videoL',
                    render: function (data) {
                        return '<div style="width: 200px; word-break: break-all;white-space: pre-wrap">'+data+'</div>'
                    }
                },
                {
                    header: '标清视频',
                    dataIndex: 'videoM',
                    render: function (data) {
                        return '<div style="width: 200px; word-break: break-all;white-space: pre-wrap">'+data+'</div>'
                    }
                },
                {
                    header: '超清视频',
                    dataIndex: 'videoH',
                    render: function (data) {
                        return '<div style="width: 200px; word-break: break-all;white-space: pre-wrap">'+data+'</div>'
                    }
                },
                {
                    header: '排序号',
                    dataIndex: 'sorter'
                },
                // {
                //     header: '创建时间',
                //     render: function (data) {
                //         return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                //     },
                //     dataIndex: 'createTime'
                // },
                // {
                //     header: '创建人',
                //     dataIndex: 'createUserName'
                // },
                {
                    header: '修改时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!xueche-fantastic-video/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
