/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-live/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 45,
                placeholder: '名称'
            },
                {
                    header: '封面图：',
                    dataIndex: 'cover',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'cover',
                        uploadIndex: 'cover',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '视频：',
                    dataIndex: 'videoUrl',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '视频'
                },
                {
                    header: '抽奖的开始时间：',
                    dataIndex: 'lotteryStartTime',
                    xtype: 'datetime',
                    placeholder: '抽奖的开始时间'
                },
                {
                    header: '抽奖的结束时间：',
                    dataIndex: 'lotteryEndTime',
                    xtype: 'datetime',
                    placeholder: '抽奖的结束时间'
                },
                {
                    header: '直播地址：',
                    dataIndex: 'liveRoomUrl',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '直播地址'
                },
                {
                    header: '直播的开始时间：',
                    dataIndex: 'liveStartTime',
                    xtype: 'datetime',
                    placeholder: '直播的开始时间'
                },
                {
                    header: '直播的结束时间：',
                    dataIndex: 'liveEndTime',
                    xtype: 'datetime',
                    placeholder: '直播的结束时间'
                },

                {
                    header: 'cityCode：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district1', {
                        dataIndex: 'cityCode',
                        name: 'cityCode',
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],

                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    header: '初始的领取人数：',
                    dataIndex: 'baseUserCount',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '初始的领取人数'
                },


            ]
        }).add();
    }
    var addTest = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-live/data/insert1',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '名称：',
                dataIndex: 'name',
                xtype: 'text',
                maxlength: 45,
                placeholder: '名称'
            },
                {
                    header: '封面图：',
                    dataIndex: 'cover',
                    xtype: Plugin('jiakao-misc!upload', {
                        dataIndex: 'cover',
                        uploadIndex: 'cover',
                        bucket: "jiakao-web",
                        isSingle: true,
                        placeholder: '请选择上传文件',
                        url: 'simple-upload3://upload/file.htm'
                    }, function () {
                        console.log(arguments)
                    })
                },
                {
                    header: '视频：',
                    dataIndex: 'videoUrl',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '视频'
                },
                {
                    header: '抽奖的开始时间：',
                    dataIndex: 'lotteryStartTime',
                    xtype: 'datetime',
                    placeholder: '抽奖的开始时间'
                },
                {
                    header: '抽奖的结束时间：',
                    dataIndex: 'lotteryEndTime',
                    xtype: 'datetime',
                    placeholder: '抽奖的结束时间'
                },
                {
                    header: '直播地址：',
                    dataIndex: 'liveRoomUrl',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '直播地址'
                },
                {
                    header: '直播的开始时间：',
                    dataIndex: 'liveStartTime',
                    xtype: 'datetime',
                    placeholder: '直播的开始时间'
                },
                {
                    header: '直播的结束时间：',
                    dataIndex: 'liveEndTime',
                    xtype: 'datetime',
                    placeholder: '直播的结束时间'
                },

                {
                    header: 'cityCode：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district1', {
                        dataIndex: 'cityCode',
                        name: 'cityCode',
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],

                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    header: '初始的领取人数：',
                    dataIndex: 'baseUserCount',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '初始的领取人数'
                },


            ]
        }).add();
    }


    var addRecord = function (table, row, lineData) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-live/data/addAward',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    dataIndex: 'id',
                    xtype: 'hidden',
                    value: lineData.id
                },
                {
                    header: '抽奖券码：',
                    dataIndex: 'lotteryCodes',
                    xtype: 'textarea',
                    check: 'required',
                    placeholder: '抽奖券码'
                },
                {
                    header: '奖励ID：',
                    dataIndex: 'presentId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '奖励ID'
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '学车节直播列表',
            title: '学车节直播列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    },
                    {
                        name: '添加测试活动',
                        class: 'warning',
                        click: addTest
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }]
            },
            line: {
                class: function (data, type) {
                    if ( type == 'body' ) {
                        if ( data.test) {
                            return 'danger text-danger';
                        }
                    }
                }
            },
            operations: [
                {
                    name: "导出大奖名单",
                    class: 'info',
                    click: function (table, rows, lineData) {
                        window.open(window.j.host.local + '/api/admin/xueche-win-prize-record/export-big-award-excel.htm?liveId=' + lineData.id)

                    }

                },
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-live/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '封面图：',
                            dataIndex: 'cover'
                        },
                        {
                            header: '视频：',
                            dataIndex: 'videoUrl'
                        },
                        {
                            header: '抽奖的开始时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'lotteryStartTime'
                        },
                        {
                            header: '抽奖的结束时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'lotteryEndTime'
                        },
                        {
                            header: '直播地址：',
                            dataIndex: 'liveRoomUrl'
                        },
                        {
                            header: '直播的开始时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'liveStartTime'
                        },
                        {
                            header: '直播的结束时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'liveEndTime'
                        },
                        {
                            header: '城市名称：',
                            dataIndex: 'cityName'
                        },
                        {
                            header: 'cityCode：',
                            dataIndex: 'cityCode'
                        },
                        {
                            header: '初始的领取人数：',
                            dataIndex: 'baseUserCount'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-live/data/view',
                        save: 'jiakao-misc!xueche-live/data/update'
                    },
                    columns: [{
                        dataIndex: 'id',
                        xtype: 'hidden'
                    },
                        {
                            header: '名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '名称'
                        },
                        {
                            header: '封面图：',
                            dataIndex: 'cover',
                            xtype: Plugin('jiakao-misc!upload', {
                                dataIndex: 'cover',
                                uploadIndex: 'cover',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm'
                            }, function () {
                                console.log(arguments)
                            })
                        },
                        {
                            header: '视频：',
                            dataIndex: 'videoUrl',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '视频'
                        },
                        {
                            header: '抽奖的开始时间：',
                            dataIndex: 'lotteryStartTime',
                            xtype: 'datetime',
                            placeholder: '抽奖的开始时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');

                            }
                        },
                        {
                            header: '抽奖的结束时间：',
                            dataIndex: 'lotteryEndTime',
                            xtype: 'datetime',
                            placeholder: '抽奖的结束时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');

                            }
                        },
                        {
                            header: '直播地址：',
                            dataIndex: 'liveRoomUrl',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '直播地址'
                        },
                        {
                            header: '直播的开始时间：',
                            dataIndex: 'liveStartTime',
                            xtype: 'datetime',
                            placeholder: '直播的开始时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');

                            }
                        },
                        {
                            header: '直播的结束时间：',
                            dataIndex: 'liveEndTime',
                            xtype: 'datetime',
                            placeholder: '直播的结束时间',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');

                            }
                        },

                        {
                            header: 'cityCode：',
                            dataIndex: 'cityCode',
                            xtype: Plugin('jiakao-misc!select-district1', {
                                dataIndex: 'cityCode',
                                name: 'cityCode',
                                insert: {
                                    province: [{
                                        code: '',
                                        name: '请选择省份'
                                    }],
                                    city: [{
                                        code: '',
                                        name: '请选择市'
                                    }],

                                }
                            }, function (plugin, code) {

                            }),
                        },
                        {
                            header: '初始的领取人数：',
                            dataIndex: 'baseUserCount',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '初始的领取人数'
                        },


                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-live/data/delete'
                },
                // {
                //     name: '添加中奖纪录',
                //     class: 'primary',
                //     click: addRecord
                // },
                {
                    name: '导出有效报名信息',
                    class: 'info',
                    click: function (table, row, lineData) {
                        window.open(window.j.host.local + '/api/admin/xueche-live/export-valid-user-excel.htm?id=' + lineData.id)
                    }
                },
                // {
                //     name: '确认发奖',
                //     class: 'warning',
                //     click: function (table, row, lineData) {
                //         Widgets.dialog.confirm('确认发奖吗？', function (ev, status) {
                //             if ( status ) {
                //                 Store(['jiakao-misc!xueche-live/data/confirmAward?id=' + lineData.id]).save().done(function (store) {
                //                     table.render();
                //                 }).fail(function (ret) {
                //                     Widgets.dialog.alert(ret.message)
                //                 });
                //             }
                //         })
                //     }
                // },
                {
                    name: '未中奖退款',
                    class: 'primary',
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认发奖吗？', function (ev, status) {
                            if ( status ) {
                                Store(['jiakao-misc!xueche-live/data/unAwardRefund?id=' + lineData.id]).save().done(function (store) {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message)
                                });
                            }
                        })
                    }
                },
                {
                    name: '开奖（测试直播）',
                    class: 'danger',
                    render: function (name, arr, i) {
                        return arr[ i ].test ? name : '';
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认发奖吗？', function (ev, status) {
                            if ( status ) {
                                Store(['jiakao-misc!xueche-live/data/openAward?id=' + lineData.id]).save().done(function (store) {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message)
                                });
                            }
                        })
                    }
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '名称',
                    dataIndex: 'name'
                },
                {
                    header: '封面图',
                    dataIndex: 'cover',
                    render: function (data) {
                        return `<a>查看</a>`
                    },
                    click: function (tab, rows, lineData) {
                        Widgets.dialog.alert(`<img src="${lineData.cover}">`)
                    }
                },
                {
                    header: '视频',
                    dataIndex: 'videoUrl',
                    render: function (data) {
                        return `<a>查看</a>`
                    },
                    click: function (tab, rows, lineData) {
                        Widgets.dialog.alert(`<video src="${lineData.videoUrl}" controls>`)
                    }
                },
                {
                    header: '抽奖的开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'lotteryStartTime'
                },
                {
                    header: '抽奖的结束时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'lotteryEndTime'
                },
                {
                    header: '直播地址',
                    dataIndex: 'liveRoomUrl'
                },
                {
                    header: '直播的开始时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'liveStartTime'
                },
                {
                    header: '直播的结束时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'liveEndTime'
                },
                {
                    header: '城市名称',
                    dataIndex: 'cityName'
                },
                {
                    header: 'cityCode',
                    dataIndex: 'cityCode'
                },
                {
                    header: '初始的领取人数',
                    dataIndex: 'baseUserCount'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人id',
                    dataIndex: 'createUserId'
                },
                {
                    header: 'createUserName',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '更新人id',
                    dataIndex: 'updateUserId'
                },
                {
                    header: 'updateUserName',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!xueche-live/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
