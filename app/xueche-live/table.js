/*
 * table v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/16
 */

"use strict";

define(['simple!core/template', 'simple!core/utils', 'simple!core/store', 'simple!core/widgets', 'simple!core/form', 'simple!core/ajax'], function (Template, Utils, Store, Widgets, Form, Ajax) {

    // 绑定操作按钮
    var bindOperations = function (obj, dom, data) {
        if (obj.config && obj.config.operations) {

            dom.find('table [data-item=operations-item]').on('click', function (e) {
                var line = $(this).parents('tr[data-index]');
                var dataIndex = line.index() - 1;
                var config = obj.config.operations[$(this).attr('data-index')];
                var xtype = config.xtype;
                if (obj[xtype]) {
                    obj[xtype].call(obj, dataIndex, config);
                } else {
                    var lineData = data.data[dataIndex];
                    var click = obj.config.operations[$(this).attr('data-index')].click || $.noop;
                    click.call(this, obj, line, lineData, dom, data, dataIndex, e);
                }

            });

            // 行点击
            dom.find('table tr').on('click', function (e) {
                if (!$(e.target).is('a:not(href), :checkbox')) {
                    $(this).item('operations-item').filter('[data-line-selector=true]').eq(0).trigger('click');
                }
            });

        }
    };
    // 绑定下拉操作按钮
    var bindGroupOperations = function (obj, dom, data) {
        if (obj.config && obj.config.operations) {
            dom.find('table [data-item=group-operations-item]').on('click', function (e) {
                var line = $(this).parents('tr[data-index]');
                var operator = $(this).parents('[data-item=group-operations]');
                var dataIndex = line.index() - 1;
                var operatorIndex = operator.attr('data-index');
                var config = obj.config.operations[operatorIndex].nodes[$(this).attr('data-index')];
                var xtype = config.xtype;

                if (obj[xtype]) {
                    obj[xtype].call(obj, dataIndex, config);
                } else {
                    var lineData = data.data[dataIndex];
                    var click = obj.config.operations[operatorIndex].nodes[$(this).attr('data-index')].click || $.noop;
                    click.call(this, obj, line, lineData, dom, data, dataIndex, e);
                }
                return false;
            });
        }
    };

    // 列事件
    var bindColumns = function (obj, dom, data) {
        if (obj.config && obj.config.columns) {
            var columns = obj.config.columns;
            var offset = obj.config.selector ? 1 : 0;
            dom.find('table tr').each(function () {
                for (var i = 0; i < columns.length; i++) {
                    if (columns[i].click) {
                        $(this).find('td').eq(i + offset).children('a').on('click', {column: i}, function (e) {
                            var line = $(this).parents('tr[data-index]');
                            var dataIndex = parseInt(line.attr('data-index'));
                            columns[e.data.column].click.call(this, obj, line, data.data[dataIndex], dom, data, dataIndex, e);
                        });
                    }
                }
            });
        }
    };

    // 排序
    var bindOrder = function (obj, dom, data) {
        dom.item('thead order').on('click', function () {
            var order = $(this).attr('data-order').toLowerCase();
            if (!(obj.config.order && obj.config.order.multiple === true)) {
                for (var i = 0; i < obj.config.columns.length; i++) {
                    if (obj.config.columns[i].order) {
                        if (typeof obj.config.columns[i].order === 'object') {
                            obj.config.columns[i].order.by = '';
                        } else {
                            obj.config.columns[i].order = '';
                        }
                    }
                }
            }
            var orderInfo = obj.config.columns[$(this).parents('th').attr('data-index')];
            if (order === 'asc') {
                $(this).attr('data-order', 'desc');
                $(this).find('span').removeClass('glyphicon-arrow-asc').addClass('glyphicon-arrow-down');
                if (typeof orderInfo.order === 'object') {
                    orderInfo.order.by = 'desc';
                } else {
                    orderInfo.order = 'desc';
                }
            } else if (order === 'desc') {
                $(this).attr('data-order', '');
                $(this).find('span').removeClass('glyphicon-arrow-down');
                if (typeof orderInfo.order === 'object') {
                    orderInfo.order.by = '';
                } else {
                    orderInfo.order = '';
                }
            } else if (order === '') {
                $(this).attr('data-order', 'asc');
                $(this).find('span').addClass('glyphicon-arrow-up');
                if (typeof orderInfo.order === 'object') {
                    orderInfo.order.by = 'asc';
                } else {
                    orderInfo.order = 'asc';
                }
            }
            obj.render();
        });
    };

    // 绑定按钮
    var bindButtons = function (obj, dom, data) {

        // 行多选按钮
        if (obj.config && obj.config.selector) {
            var headerSele = dom.item('table-header-selector');
            var listSele = dom.item('table-selector');
            headerSele.on('change', function () {
                listSele.prop('checked', $(this).prop('checked'));
            });
            listSele.on('change', function () {
                var change = obj.config.selector.change || $.noop;
                change.call(this, obj, dom, data);
            });
        }

        // 顶部按钮
        if (obj.config && obj.config.buttons && obj.config.buttons.top) {
            dom.item('btn-top').find('button[data-item!=download-data]').each(function () {
                $(this).on('click', function () {
                    var selector = [];
                    var index = $(this).index();
                    var click = obj.config.buttons.top[index].click || $.noop;
                    if (obj.config.selector) {
                        dom.item('table-selector').filter(':checked').each(function () {
                            selector.push($(this).val());
                        });
                    }
                    click.call(this, obj, dom, data, selector);
                });
            });
        }

        // 底部按钮
        if (obj.config && obj.config.buttons && obj.config.buttons.bottom) {
            dom.item('btn-bottom').find('button[data-item!=download-data]').each(function () {
                $(this).on('click', function () {
                    var selector = [];
                    var index = $(this).index();
                    var click = obj.config.buttons.bottom[index].click || $.noop;
                    if (obj.config.selector) {
                        dom.item('table-selector').filter(':checked').each(function () {
                            selector.push($(this).val());
                        });
                    }
                    click.call(this, obj, dom, data, selector);
                });
            });
        }

    };

    // 绑定分页
    var bindPaging = function (obj, dom, data) {
        dom.item('paging').children('li:not(.disabled)').on('click', function () {
            var page = parseInt($(this).attr('data-page'));
            obj.render({
                page: page
            });
        });
    };

    // 上传
    var uploadImages = function (file, success, error, progress, bucket, type, url, name) {
        var formdata = new FormData();
        bucket && formdata.append('bucket', bucket);
        type && formdata.append('type', type);
        formdata.append(name || 'files', file);
        Ajax.request(url || 'simple-upload://upload.htm', {
            data: formdata,
            type: 'post',
            contentType: false,
            processData: false,
            success: function (data, ret) {
                if (!ret.success) {
                    Widgets.dialog.alert(ret.message || "上传失败啦");
                } else {
                    success.call(this, data);
                }
            },
            error: function (data) {
                error.call(this, data);
            },
            progress: function (percentage) {
                progress.call(this, percentage);
            }
        });
    };

    // 绑定FILE控件
    var bindFile = function (config, dom) {

        dom.find('[data-file-async]').each(function () {
            var file = $(this).find(':file');
            var index = $(this).parent().parent().index();
            var success = config.columns[index].success;
            var error = config.columns[index].error;
            if (file) {
                file.on('change', function () {
                    var input = $(this).parent().parent().find(':text');
                    var button = $(this).parent().find('span');
                    uploadImages(this.files[0], function (data) {
                        button.html('选择文件');
                        if (success) {
                            success(data, input);
                        } else {
                            input.val(data[0].url);
                        }
                    }, function (data) {
                        button.html('选择文件');
                        if (error) {
                            error(data, input);
                        } else {
                            Widgets.dialog.alert(data.message);
                        }
                    }, function (percentage) {
                        button.html(percentage + '%');
                    }, $(this).attr('data-bucket'), $(this).attr('data-uploadType'), $(this).attr('data-upload-url'), input.attr('name'));
                });
            }
        });
    };

    // 插件渲染
    var bindPlugin = function (config, dom, data) {

        if (config.columns) {
            for (var i = 0; i < config.columns.length; i++) {
                var option = {};
                option.column = config.columns[i];
                option.data = data;
                if (data && config.columns[i].dataIndex && typeof data[config.columns[i].dataIndex] != 'undefined') {
                    option.value = data[config.columns[i].dataIndex];
                }
                if (config.columns[i].xtype && config.columns[i].xtype.constructor === 'Plugin') {
                    option.target = dom.find('[data-plugin=' + config.columns[i].xtype.id + ']');
                    config.columns[i].xtype.render(option);
                }
            }
        }
        if (config.search) {
            for (var i = 0; i < config.search.length; i++) {
                var option = {};
                option.column = config.search[i];
                option.data = data;
                if (data && config.search[i].dataIndex && typeof data[config.search[i].dataIndex] != 'undefined') {
                    option.value = data[config.search[i].dataIndex];
                }
                if (config.search[i].xtype && config.search[i].xtype.constructor === 'Plugin') {
                    option.target = dom.find('[data-plugin=' + config.search[i].xtype.id + ']');
                    config.search[i].xtype.render(option);
                }
            }
        }
    };

    // 绑定搜索
    var bindSearch = function (form, obj) {

        if (form.size() > 0) {

            // 绑定插件
            bindPlugin(obj.config, form, obj.search);

            // 绑定事件
            for (var i = 0; i < obj.config.search.length; i++) {
                if (obj.config.search[i].autoSubmit) {
                    $(form.get(0)[obj.config.search[i].dataIndex]).on('change', function () {
                        form.trigger('submit');
                    });
                }
            }

            Form.bind(form, {
                submitHandler: function (form) {
                    var data = {};
                    for (var i = 0; i < obj.config.search.length; i++) {
                        var key = obj.config.search[i].dataIndex;
                        if (key) {
                            var value = form[key].value;
                            obj.config.search[i].value = value;
                            data[key] = value;
                        }
                    }
                    obj.search = data;
                    obj.render({
                        page: 1
                    });
                    return false;
                }
            });

        }

    };

    // 获取默认排序
    var getOrder = function (columns) {
        columns = columns || [];
        var sort = [];
        for (var i = 0; i < columns.length; i++) {
            if (columns[i].order) {
                var order = '';
                var orderField = '';
                if (typeof columns[i].order === 'object') {
                    order = (columns[i].order.by || '').toUpperCase();
                    orderField = columns[i].order.dataIndex || columns[i].dataIndex;
                } else {
                    order = columns[i].order.toUpperCase();
                    orderField = columns[i].dataIndex;
                }
                sort.push({
                    property: orderField,
                    direction: order
                });
            }
        }
        return sort;
    };

    // 获取配置里的数据源
    var columnsStore = function (columns) {
        columns = columns || [];
        var stores = [];
        for (var i = 0; i < columns.length; i++) {
            if (columns[i].store && typeof columns[i].store === 'string') {
                stores.push(columns[i].store);
            }
        }
        return $.Deferred(function (dtd) {
            Store(stores).load().done(function (obj, data, getStore) {
                for (var i = 0; i < columns.length; i++) {
                    if (columns[i].store && typeof columns[i].store === 'string') {
                        columns[i].store = getStore(columns[i].store).data;
                        columns[i].insert = columns[i].insert || [];
                        columns[i].insert.reverse();
                        for (var j = 0; j < columns[i].insert.length; j++) {
                            columns[i].store.unshift(columns[i].insert[j]);
                        }
                    }
                }
                dtd.resolve(columns);
            }).fail(dtd.reject);
            return dtd.promise();
        });
    };

    // 绑定条数
    var bindLimit = function (table, dom, data) {

        dom.item('table-limit').on('change', function () {
            table.render({}, {
                params: {
                    limit: parseInt(this.value)
                }
            });
        });

    };

    // 绑定跳转页数
    var bindGotoPage = function (obj, dom, data) {
        if (data.paging) {
            dom.item('table-page').val(data.paging.page + 1);
            dom.item('table-page').on('keydown', function (e) {
                if (e.keyCode == 13) {
                    obj.render({
                        page: parseInt($(this).val())
                    });
                }
            });
        }
    };

    // 绑定下载
    var bindDownload = function (table, dom, data) {

        dom.item('download-data').on('click', function () {

            var jsonData = data.json && data.json.data || data.data;

            // 生成数据
            var retData = [];
            var config = table.config;
            var columnsHead = [];
            var columnsTd = [];

            for (var i = 0; i < config.columns.length; i++) {
                columnsHead.push('"' + config.columns[i].header.toString().replace(/"/ig, '""') + '"');
            }
            if (config.reportColumns && config.reportColumns.length > 0) {
                for (var i = 0; i < config.reportColumns.length; i++) {
                    columnsHead.push('"' + config.reportColumns[i].header.toString().replace(/"/ig, '""') + '"');
                }
            }

            retData.push(columnsHead.join(','));

            for (var i = 0; i < jsonData.length; i++) {
                columnsTd = [];
                for (var j = 0; j < config.columns.length; j++) {
                    var value = jsonData[i][config.columns[j].dataIndex];
                    var format = config.columns[j].render;
                    if (config.columns[j].reportRender) {
                        format = config.columns[j].reportRender;
                    }
                    if (value && typeof value === 'object') {
                        value = JSON.stringify(value);
                    }
                    if (format) {
                        value = format(value, config.columns[j], data, i);
                    }
                    if (!config.columns[j].reportRender) {
                        if(value === 0){
                            value = value.toString();
                        }
                        value = value ? value.toString().replace(/<[^>]*>/gi, '') : '';
                    }
                    if (value !== undefined) {
                        if (value === null) {
                            value = '<<null>>'
                        }
                        columnsTd.push('"' + value.toString().replace(/"/ig, '""') + '"');
                    } else {
                        columnsTd.push('');
                    }
                }


                if (config.reportColumns && config.reportColumns.length > 0) {
                    for (var n = 0; n < config.reportColumns.length; n++) {
                        var addValue = jsonData[i][config.reportColumns[n].dataIndex];

                        var format1 = config.reportColumns[n].render;
                        if (config.reportColumns[n].reportRender) {
                            format1 = config.reportColumns[n].reportRender;
                        }
                        if (addValue && typeof addValue === 'object') {
                            addValue = JSON.stringify(addValue);
                        }

                        if (format1) {
                            addValue = format1(addValue, config.reportColumns[n], data, i);
                        }
                        if (!config.reportColumns[n].reportRender) {
                            if(addValue === 0){
                                addValue = addValue.toString();
                            }
                            addValue = addValue ? addValue.toString().replace(/<[^>]*>/gi, '') : '';
                        }
                        if (addValue !== undefined) {
                            if (addValue === null) {
                                addValue = '<<null>>'
                            }
                            columnsTd.push('"' + addValue.toString().replace(/"/ig, '""') + '"');
                        } else {
                            columnsTd.push('');
                        }
                    }
                }

                retData.push(columnsTd.join(','));
            }

            var blob = new Blob(['\ufeff' + retData.join('\n')], {type: 'text/csv; charset=UTF-8'});
            var a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.setAttribute('download', (table.config.title || table.config.description || Utils.format.date(new Date().getTime(), 'yyyyMMdd-HHmmss')) + '.csv');
            a.click();
        });

    };

    // 搜索配置的数据源
    var searchStore = function (search) {
        var stores = [];
        if (search) {
            for (var i = 0; i < search.length; i++) {
                if (search[i].store && typeof search[i].store === 'string') {
                    stores.push(search[i].store);
                }
            }
        }
        return $.Deferred(function (dtd) {
            Store(stores).load().done(function (obj, data, getStore) {
                if (search) {
                    for (var i = 0; i < search.length; i++) {
                        if (search[i].store && typeof search[i].store === 'string') {
                            search[i].store = getStore(search[i].store).data;
                            search[i].insert = search[i].insert || [];
                            search[i].insert.reverse();
                            for (var j = 0; j < search[i].insert.length; j++) {
                                search[i].store.unshift(search[i].insert[j]);
                            }
                        }
                    }
                }
                dtd.resolve(search);
            }).fail(dtd.reject);
            return dtd.promise();
        });
    };

    // 渲染编辑器
    var renderEditor = function (dom, config) {

        dom.find('[data-richtext=true]').each(function () {

            var id = 'richtext-' + Math.random().toString(16).substring(2);
            var name = $(this).attr('name');

            $(this).attr('id', id);

            if (typeof UE != 'undefined' && UE.getEditor) {
                var option = {};
                for (var i = 0; i < config.columns.length; i++) {
                    if (config.columns[i].dataIndex === name) {
                        option = config.columns[i].editor;
                    }
                }
                var editor = UE.getEditor(id, option);

                (function (dom, textarea, editor) {

                    editor.ready(function () {

                        var me = editor;

                        var drop = $(me.document);
                        var dropMasks = dom.item('drop-masks');
                        var progressEle = dom.item('upload-progress');

                        $(window).on('dragover', false);
                        $(window).on('drop', false);

                        // 拖拽上传
                        drop.on('dragenter drag dragover', function (e) {
                            dropMasks.show();
                            e.stopPropagation();
                            e.preventDefault();
                        });

                        dropMasks.on('dragenter', function () {
                            dropMasks.show();
                        });

                        dropMasks.on('dragleave', function () {
                            dropMasks.hide();
                        });

                        dropMasks.on('dragover', false);

                        dropMasks.on('drop', function (e) {

                            dropMasks.hide();

                            var placeholder, fileType;

                            var files = e.originalEvent.dataTransfer.files;

                            if (files.length < 11) {

                                for (var i = 0; i < files.length; i++) {

                                    !function (file) {

                                        // 判断类型
                                        switch (file.type) {
                                            case 'image/jpeg':
                                            case 'image/jpg':
                                            case 'image/png':
                                            case 'image/gif':
                                                fileType = 'image';
                                                placeholder = file.name + '_' + Math.random().toString(16).substring(2);
                                                me.execCommand('insertimage', {
                                                    src: window.j.framework.root + '/v' + window.j.framework.version + '/' + '/resources/css/images/img-loader.gif',
                                                    title: placeholder
                                                });
                                                break;
                                            default:
                                                fileType = 'file';
                                                placeholder = file.name + '_' + Math.random().toString(16).substring(2);
                                                me.execCommand('link', {
                                                    url: 'javascript:;',
                                                    textValue: '[' + file.name + '，正在上传]',
                                                    title: placeholder,
                                                    target: '_blank'
                                                });
                                                break;
                                        }

                                        (function (me, placeholder, fileType, fileName) {

                                            uploadImages(file, function (data) {

                                                if (fileType === 'image') {
                                                    $(me.document.body).find('img[title="' + placeholder + '"]').css('max-width', '100%').attr({
                                                        src: data[0].url,
                                                        _src: data[0].url
                                                    });
                                                } else {
                                                    $(me.document.body).find('a[title="' + placeholder + '"]').html(fileName).attr('href', url);
                                                }

                                            }, function () {

                                                if (fileType === 'image') {
                                                    $(me.document.body).find('img[title="' + placeholder + '"]').attr({
                                                        src: window.j.framework.root + '/v' + window.j.framework.version + '/' + '/resources/css/images/img-error.png',
                                                        _src: window.j.framework.root + '/v' + window.j.framework.version + '/' + '/resources/css/images/img-error.png'
                                                    });
                                                } else {
                                                    $(me.document.body).find('a[title="' + placeholder + '"]').html('<span style="color: #f00;">' + fileName + '上传失败</span>');
                                                }

                                            }, function (percentage) {

                                                var item = progressEle.find('[data-item="' + placeholder + '"]');

                                                if (percentage >= 100) {
                                                    item.remove();
                                                    return;
                                                }

                                                if (item.size() > 0) {
                                                    item.html(file.name + '：' + percentage + '%');
                                                } else {
                                                    progressEle.append('<div data-item="' + placeholder + '">' + file.name + '：' + percentage + '%' + '</div>');
                                                }

                                            }, textarea.attr('data-bucket'), 'image', textarea.attr('data-upload-url'));

                                        })(me, placeholder, fileType, file.name);

                                    }(files[i]);

                                }

                            } else {
                                Widgets.dialog.alert('最多支持拖拽10个文件');
                            }

                            e.stopPropagation();

                            e.preventDefault();

                        });

                    });

                })($(this).parent(), $(this), editor);

            }

        });

    };

    // 格式化CHECK
    var formatCheck = function (columns) {
        var rules = {};
        var messages = {};
        for (var i = 0; i < columns.length; i++) {
            if (typeof columns[i].check === 'object') {
                rules[columns[i].dataIndex] = columns[i].check;
            }
            messages[columns[i].dataIndex] = columns[i].messages;
        }
        return {
            rules: rules,
            messages: messages
        }
    };

    // 检查是否有富文本
    var checkRichtext = function (config) {

        for (var i = 0; i < config.columns.length; i++) {
            if (config.columns[i].xtype === 'richtext') {
                return true;
            }
        }

        return false;

    };

    var Table = function (config, stores, target, controller, method) {
        this.config = config || {};
        this.stores = stores || {};
        this.target = target || null;
        this.controller = controller || $.noop;
        this.method = method || {};
        this.search = {};
        this.params = {};
    };

    Table.prototype = {

        add: function (config) {
            var me = this;
            config = config || {};
            $.extend(true, config, me.config);
            config.success = config.success || $.noop;
            columnsStore(config.columns).done(function (columns) {

                config.columns = columns;

                var renderAfter = function (dom, data) {
                    // 渲染编辑器
                    renderEditor(dom, config);
                    if (config.renderAfter) {
                        config.renderAfter(me, dom, data);
                    }
                };

                var bindForm = function (form, dialog) {
                    var option = {submit: {}};
                    $.extend(true, option, config.form || {});
                    $.extend(true, option, formatCheck(config.columns));
                    option.submit.success = function () {
                        if (config.form && config.form.submit && config.form.submit.success) {
                            config.form.submit.success.apply(this, arguments)
                        }
                        var arg = Array.prototype.slice.call(arguments);
                        arg.unshift(me, dialog);
                        config.success.apply(this, arg);
                    };
                    option.submit.error = function (data) {
                        if (config.form && config.form.submit && config.form.submit.error) {
                            config.form.submit.error.apply(this, arguments)
                        }
                        if (config.error) {
                            var arg = Array.prototype.slice.call(arguments);
                            arg.unshift(me, dialog);
                            config.error.apply(this, arg);
                        } else {
                            Widgets.dialog.alert(data.message);
                        }

                    };
                    Form.bind(form, option);
                };

                // 渲染，如果没有TARGET则默认弹窗显示
                Template(config.view || 'simple!table/add', null, null, {config: config}, config.method).render().done(function (obj, dom, data, item) {
                    console.log(config);
                    if (config.target) {
                        if (typeof config.target === 'function') {
                            var domObj = config.target(dom);
                        } else {
                            domObj = $(config.target).html(dom);
                        }

                        renderAfter(domObj, data);
                        bindForm(domObj.item('form'));
                        bindFile(config, domObj);

                        // 渲染插件
                        bindPlugin(config, domObj, data.data);
                    } else {
                        Widgets.dialog.html(config.title || '添加', dom, {
                            width: config.width || 600,
                            backdrop: 'static',
                            form: {
                                store: config.store
                            },
                            done: checkRichtext(config) ? 'show' : 'load',
                            buttons: [
                                {
                                    name: '保存',
                                    xtype: 'submit',
                                    class: 'primary'
                                },
                                {
                                    name: '重填',
                                    xtype: 'reset'
                                }
                            ]
                        }).done(function (dialog) {
                            bindForm(dialog.form, dialog);
                            renderAfter(dialog.body, data);
                            bindFile(config, dialog.body);
                            // 渲染插件
                            bindPlugin(config, dialog.body, data.data);
                        });
                    }
                });

            }).fail(function (data) {
                Widgets.dialog.alert(data.message);
                console.log(arguments);
            });
        },

        edit: function (line) {

            var me = this;
            var config = $.extend(true, {}, arguments[1] || {});
            var store = config.store;
            var lineData = typeof line === 'object' ? line : me.data.data[line];
            config.success = config.success || $.noop;
            var render = function (saveStore, lineData) {

                columnsStore(config.columns).done(function (columns) {

                    config.columns = columns;

                    var renderAfter = function (dom, data) {
                        // 渲染编辑器
                        renderEditor(dom, config);
                        if (config.renderAfter) {
                            config.renderAfter(me, dom, data);
                        }
                    };

                    var bindForm = function (form, dialog) {
                        var option = {submit: {}};
                        $.extend(true, option, config.form || {});
                        $.extend(true, option, formatCheck(config.columns));
                        option.submit.success = function () {
                            if (config.form && config.form.submit && config.form.submit.success) {
                                config.form.submit.success.apply(this, arguments)
                            }
                            var arg = Array.prototype.slice.call(arguments);
                            arg.unshift(me, dialog);
                            config.success.apply(this, arg);
                        };
                        option.submit.error = function (data) {
                            if (config.form && config.form.submit && config.form.submit.error) {
                                config.form.submit.error.apply(this, arguments)
                            }
                            if (config.error) {
                                var arg = Array.prototype.slice.call(arguments);
                                arg.unshift(me, dialog);
                                config.error.apply(this, arg);
                            } else {
                                Widgets.dialog.alert(data.message);
                            }
                        };
                        Form.bind(form, option); // form
                    };

                    // 渲染，如果没有TARGET则默认弹窗显示
                    Template(config.view || 'simple!table/edit', null, null, {
                        data: lineData,
                        config: config
                    }, config.method).render().done(function (obj, dom, data, item) {

                        // 如果有TARGET则绑定的FORM需要带data-item="form"
                        if (config.target) {

                            if (typeof config.target === 'function') {
                                var domObj = config.target(dom);
                            } else {
                                domObj = $(config.target).html(dom);
                            }
                            renderAfter(domObj, data);
                            bindForm(domObj.item('form'));
                            bindFile(config, domObj);

                            // 渲染插件
                            bindPlugin(config, domObj, data.data);

                        } else {
                            Widgets.dialog.html(config.title || '编辑', dom, {
                                width: config.width || 600,
                                backdrop: 'static',
                                form: {
                                    store: saveStore
                                },
                                done: checkRichtext(config) ? 'show' : 'load',
                                buttons: [
                                    {
                                        name: '保存',
                                        xtype: 'submit',
                                        class: 'primary'
                                    },
                                    {
                                        name: '重填',
                                        xtype: 'reset'
                                    }
                                ]
                            }).done(function (dialog) {

                                bindForm(dialog.form, dialog);
                                renderAfter(dialog.body, data);
                                bindFile(config, dialog.body);

                                // 渲染插件
                                bindPlugin(config, dialog.body, data.data);

                            }); // dialog
                        }

                    }); // template

                });

            };

            // 如果有STORE则从STORE读取数据，否则取行内数据
            // 如果STORE是字符串则恒定是保存STORE，否则如果是对象则有 load, save 键值，则对应取数据和存数据
            if (typeof store === 'string') {
                render(store, lineData);
            } else {
                Store([store.load]).get().done(function (obj, data, getStore) {
                    data = getStore(store.load);
                    var primary = data.store.primary;
                    var option = {params: {}};
                    $.extend(option, config);
                    option.params[data.store.primaryName || primary] = lineData[primary];
                    obj.load([option]).done(function (obj, data, getStore) {
                        render(store.save, getStore(store.load).data);
                    }).fail(function (data) {
                        Widgets.dialog.alert(data.message);
                    });
                });
            }
        },
        view: function (line, config) {
            config = config || {};
            var me = this;
            var lineData = typeof line === 'object' ? line : me.data.data[line];
            var render = function (data) {
                var renderAfter = function (dom, data) {
                    if (config.renderAfter) {
                        config.renderAfter(me, dom, data);
                    }
                };
                // 渲染，如果没有TARGET则默认弹窗显示
                Template(config.view || 'simple!table/view', null, null, {
                    data: data,
                    config: config
                }, config.method).render().done(function (obj, dom, data, item) {
                    if (config.target) {
                        if (typeof config.target === 'function') {
                            var domObj = config.target(dom);
                        } else {
                            domObj = $(config.target).html(dom);
                        }
                        renderAfter(domObj, data);
                        // 渲染插件
                        bindPlugin(config, domObj, data.data);
                    } else {
                        Widgets.dialog.html(config.title || '查看', dom, {
                            width: config.width || 600
                        }).done(function (dialog) {
                            renderAfter(dialog.body, data);
                            // 渲染插件
                            bindPlugin(config, dialog.body, data.data);
                        });
                    }
                });
            };

            // 如果有STORE则从STORE读取数据，否则取行内数据
            if (config.store)
                Store([config.store]).get([$.extend(true, {}, config.storeConfig || {}, {aliases: 'viewData'})]).done(function (obj, data, getStore) {
                    data = data.viewData;
                    var primary = data.store.primary;
                    data.store.params = data.store.params || {};
                    data.store.params[data.store.primaryName || primary] = lineData[primary];
                    obj.load().done(function (obj, data) {
                        render(data.viewData.data);
                    }).fail(function (data) {
                        Widgets.dialog.alert(data.message);
                    });
                });
            else
                render(lineData);
        },

        delete: function (line, config) {
            var me = this;
            config = config || {};
            var text = '确认删除 “' + me.config.columns[0].header + '” 为 “' + me.data.data[line][me.config.columns[0].dataIndex] + '” 的信息吗？';
            Widgets.dialog.confirm(text, function (e, state) {
                if (state) {
                    var store = config.store;
                    var lineData = me.data.data[line];
                    Store([store]).get().done(function (obj, data, getStore) {
                        store = getStore(store);
                        store.data = store.data || {};
                        store.data[store.store.primary] = lineData[store.store.primary];
                        obj.save().done(function () {
                            if (config.success) {
                                config.success(me, me.data.data[line], me.config, line);
                            } else {
                                me.render();
                            }
                        }).fail(function (data) {
                            Widgets.dialog.alert(data.message);
                        });
                    });
                }
            });
        },

        render: function (params, config) {
            var obj = this;
            params = params || {};
            config = config || {};
            config.params = config.params || {};
            config.params = $.extend(true, {}, config.params, {sort: JSON.stringify(getOrder(obj.config.columns))});
            $.extend(true, config.params, params);
            $.extend(true, config.params, obj.search);
            $.extend(true, obj.config, config);
            obj.config.params.page = obj.config.params.page || 1;
            obj.config.params.limit = obj.config.params.limit || 25;
            obj.hasRendered = false;
            if (obj.config.keepScroll) {
                obj.target.off('scroll.table-scroll').on('scroll.table-scroll', function () {
                    if (obj.hasRendered) {
                        obj.scrollTop = obj.target.scrollTop();
                    }
                })
            }
            var dtdTemplate = function (dtd) {
                if (obj.config.keepScroll) {
                    obj.target.css('minHeight', obj.target.height());
                }
                $(obj.target).html('<div class="loader"></div>');
                var search = searchStore(obj.config.search);
                if (obj.store) {
                    var stores = obj.store.load([obj.config]);
                } else {
                    stores = Store(obj.stores).load([obj.config]);
                }
                $.when(search, stores).done(function (search, store) {
                    obj.config.search = search;
                    if ($.isArray(obj.stores)) {
                        var data = store[2](obj.stores[0]).data;
                        var paging = store[2](obj.stores[0]).paging;
                        var json = store[2](obj.stores[0]).json;
                    } else {
                        data = obj.stores.data;
                        paging = obj.stores.paging;
                        json = obj.stores.json;
                    }
                    obj.store = store[0];

                    Template(obj.config.view || 'simple!table/main', obj.target, function (dom, data, item) {
                        obj.target.css('minHeight', 0);
                        dom = $(dom);
                        obj.dom = dom;
                        obj.data = data;
                        dtd.resolve(obj, dom, data, item);
                        obj.hasRendered = true;
                        if (obj.config.keepScroll) {
                            obj.target.scrollTop(obj.scrollTop);
                        }
                    }, {data: data, paging: paging, json: json, config: obj.config, table: obj}, obj.method).render();
                }).fail(function () {
                    Template('simple!table/error', obj.target, function (dom, data, item) {

                    }, {data: arguments[0].message, url: arguments[3].url}).render();
                    dtd.reject.call(this, arguments);
                });
                obj.page = config.params.page;
                return dtd.promise();
            };
            return $.Deferred(function (dtd) {
                $.Deferred(dtdTemplate).done(function (obj, dom, data, item) {
                    bindSearch(dom.item('search'), obj);
                    bindOrder(obj, dom, data);
                    bindOperations(obj, dom, data);
                    bindGroupOperations(obj, dom, data);
                    bindColumns(obj, dom, data);
                    bindButtons(obj, dom, data);
                    bindPaging(obj, dom, data);
                    bindGotoPage(obj, dom, data);
                    bindLimit(obj, dom, data);
                    bindDownload(obj, dom, data);
                    obj.controller(dom, data, item, obj);
                    dtd.resolve(obj, dom, data, item);
                }).fail(dtd.reject);
                return dtd.promise();
            });
        }
    };

    Table.prototype.constructor = Table;

    return function (config, stores, target, controller, method) {
        return new Table(config, stores, target, controller, method);
    }

});
