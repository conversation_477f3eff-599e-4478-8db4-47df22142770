/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-lottery/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '用户id：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '用户id'
                },
                {
                    header: '直播的id：',
                    dataIndex: 'liveId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '直播的id'
                },
                {
                    header: '订单号：',
                    dataIndex: 'orderNumber',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '订单号'
                },
                {
                    header: '抽奖券的code：',
                    dataIndex: 'code',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '抽奖券的code'
                },
                {
                    header: '是否分享获取：',
                    dataIndex: 'share',
                    xtype: 'radio',
                    store: [{
                            key: true,
                            value: '是'
                        },
                        {
                            key: false,
                            value: '否'
                        }
                    ]
                },
                {
                    header: '创建人id：',
                    dataIndex: 'createUserId',
                    xtype: 'text',
                    placeholder: '创建人id'
                },
                {
                    header: 'createUserName：',
                    dataIndex: 'createUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'createUserName'
                },
                {
                    header: '更新时间：',
                    dataIndex: 'updateTime',
                    xtype: 'date',
                    placeholder: '更新时间'
                },
                {
                    header: '更新人id：',
                    dataIndex: 'updateUserId',
                    xtype: 'text',
                    placeholder: '更新人id'
                },
                {
                    header: 'updateUserName：',
                    dataIndex: 'updateUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'updateUserName'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '抽奖券列表列表',
            title: '抽奖券列表列表',
            search: [{
                    header: '用户id',
                    dataIndex: 'userId',
                    placeholder:'用户id',
                    xtype:'text',
                },
                {
                    header: '直播的id',
                    dataIndex: 'liveId',
                    xtype:'text',
                    placeholder:'直播的id'

                },
                {
                    header: '订单号',
                    dataIndex: 'orderNumber',
                    xtype:'text',
                    placeholder:'订单号'

                },
            ],
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-lottery/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '直播的id：',
                            dataIndex: 'liveId'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber'
                        },
                        {
                            header: '抽奖券的code：',
                            dataIndex: 'code'
                        },
                        {
                            header: '是否分享获取：',
                            render: function (data) {
                                if (data) {
                                    return '是';
                                } else {
                                    return '否';
                                }
                            },
                            dataIndex: 'share'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-lottery/data/view',
                        save: 'jiakao-misc!xueche-lottery/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '用户id'
                        },
                        {
                            header: '直播的id：',
                            dataIndex: 'liveId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '直播的id'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '订单号'
                        },
                        {
                            header: '抽奖券的code：',
                            dataIndex: 'code',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '抽奖券的code'
                        },
                        {
                            header: '是否分享获取：',
                            dataIndex: 'share',
                            xtype: 'radio',
                            store: [{
                                    key: true,
                                    value: '是'
                                },
                                {
                                    key: false,
                                    value: '否'
                                }
                            ]
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId',
                            xtype: 'text',
                            placeholder: '创建人id'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            dataIndex: 'updateTime',
                            xtype: 'date',
                            placeholder: '更新时间'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId',
                            xtype: 'text',
                            placeholder: '更新人id'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-lottery/data/delete'
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户id',
                    dataIndex: 'userId'
                },
                {
                    header: '直播的id',
                    dataIndex: 'liveId'
                },
                {
                    header: '订单号',
                    dataIndex: 'orderNumber'
                },
                {
                    header: '抽奖券的code',
                    dataIndex: 'code'
                },
                {
                    header: '是否分享获取',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'share'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },

                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },


            ]
        }, ['jiakao-misc!xueche-lottery/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});