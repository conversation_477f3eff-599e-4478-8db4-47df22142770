/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function(Template, Table, Utils, Widgets, Store, Form) {

    var add = function(table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-notify/data/insert',
            success: function(obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
             {
                 header: '直播id：',
                 dataIndex: 'liveId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '直播id'
             },
             {
                 header: '是否已通知：',
                 dataIndex: 'notify',
                 xtype: 'radio',
                 store: [
                     {
                         key: true,
                         value: '是'
                     },
                     {
                         key: false,
                         value: '否'
                     }
                 ]
             },
             {
                 header: '加密手机号：',
                 dataIndex: 'phoneEncrypt',
                 xtype: 'textarea',
                 maxlength: 1024,
                 placeholder: '加密手机号'
             },
             {
                 header: '手机号掩码：',
                 dataIndex: 'phoneMask',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '手机号掩码'
             },
             {
                 header: '手机号标识符：',
                 dataIndex: 'phoneIdentifier',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '手机号标识符'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             }

            ]
        }).add();
    }

    var list = function(panel) {
        Table({
            description: '直播提醒列表',
            title: '直播提醒列表',

            buttons: {
                top: [
                     {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function(obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-notify/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                     {
                         header: '直播id：',
                         dataIndex: 'liveId'
                     },
                     {
                         header: '是否已通知：',
                         render: function (data) {
                             if (data) {
                                 return '是';
                             } else {
                                 return '否';
                             }
                         },
                         dataIndex: 'notify'
                     },
                     {
                         header: '加密手机号：',
                         dataIndex: 'phoneEncrypt'
                     },
                     {
                         header: '手机号掩码：',
                         dataIndex: 'phoneMask'
                     },
                     {
                         header: '手机号标识符：',
                         dataIndex: 'phoneIdentifier'
                     },
                     {
                         header: 'createTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                     {
                         header: 'createUserId：',
                         dataIndex: 'createUserId'
                     },
                     {
                         header: 'createUserName：',
                         dataIndex: 'createUserName'
                     },
                     {
                         header: 'updateTime：',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     {
                         header: 'updateUserId：',
                         dataIndex: 'updateUserId'
                     },
                     {
                         header: 'updateUserName：',
                         dataIndex: 'updateUserName'
                     }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function(obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-notify/data/view',
                        save: 'jiakao-misc!xueche-notify/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
             {
                 header: '直播id：',
                 dataIndex: 'liveId',
                 xtype: 'text',
                 check: 'required',
                 placeholder: '直播id'
             },
             {
                 header: '是否已通知：',
                 dataIndex: 'notify',
                 xtype: 'radio',
                 store: [
                     {
                         key: true,
                         value: '是'
                     },
                     {
                         key: false,
                         value: '否'
                     }
                 ]
             },
             {
                 header: '加密手机号：',
                 dataIndex: 'phoneEncrypt',
                 xtype: 'textarea',
                 maxlength: 1024,
                 placeholder: '加密手机号'
             },
             {
                 header: '手机号掩码：',
                 dataIndex: 'phoneMask',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '手机号掩码'
             },
             {
                 header: '手机号标识符：',
                 dataIndex: 'phoneIdentifier',
                 xtype: 'text',
                 maxlength: 45,
                 placeholder: '手机号标识符'
             },
             {
                 header: 'createUserId：',
                 dataIndex: 'createUserId',
                 xtype: 'text',
                 placeholder: 'createUserId'
             },
             {
                 header: 'createUserName：',
                 dataIndex: 'createUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'createUserName'
             },
             {
                 header: 'updateTime：',
                 dataIndex: 'updateTime',
                 xtype: 'date',
                 placeholder: 'updateTime'
             },
             {
                 header: 'updateUserId：',
                 dataIndex: 'updateUserId',
                 xtype: 'text',
                 placeholder: 'updateUserId'
             },
             {
                 header: 'updateUserName：',
                 dataIndex: 'updateUserName',
                 xtype: 'text',
                 maxlength: 32,
                 placeholder: 'updateUserName'
             }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-notify/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                     {
                         header: '直播id',
                         dataIndex: 'liveId'
                     },
                     {
                         header: '是否已通知',
                         render: function (data) {
                             if (data) {
                                 return '是';
                             } else {
                                 return '否';
                             }
                         },
                         dataIndex: 'notify'
                     },
              
                     {
                         header: '手机号掩码',
                         dataIndex: 'phoneMask'
                     },
                
                     {
                         header: 'createTime',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'createTime'
                     },
                    
                     {
                         header: 'updateTime',
                         render: function (data) {
                             return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                         },
                         dataIndex: 'updateTime'
                     },
                     
            ]
        }, ['jiakao-misc!xueche-notify/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});