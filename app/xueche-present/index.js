/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    //：1-大奖；2-普通奖品；
    //：1-大奖；2-优惠券；3-待扩展；

    var typeArr = [
        {
            key: 1,
            value: '大奖'
        },
        {
            key: 2,
            value: '普通奖品'
        }
    ];

    var typeMap = {
        1: '大奖',
        2: '普通奖品'
    }

    var categoryArr = [
        {
            key: 1,
            value: '大奖'
        },
        {
            key: 2,
            value: 'VIP优惠券'
        },
        {
            key: 3,
            value: '路线视频优惠券'
        }
    ];

    var categoryMap = {
        1: '大奖',
        2: 'VIP优惠券',
        3: '路线视频优惠券'
    }

    var autoRefundArr = [
        {
            key: true,
            value: '是'
        },
        {
            key: false,
            value: '否'
        }
    ];

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-present/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '奖品类别：',
                    dataIndex: 'type',
                    xtype: 'select',
                    store: typeArr,
                    placeholder: '奖品类别'
                },
                {
                    header: '奖品种类：',
                    dataIndex: 'category',
                    xtype: 'select',
                    store: categoryArr,
                    placeholder: '奖品种类'
                },
                {
                    header: '奖品标识：',
                    dataIndex: 'key',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: '奖品标识'
                },
                {
                    header: '奖品名称：',
                    dataIndex: 'name',
                    xtype: 'text',
                    maxlength: 32,
                    check: 'required',
                    placeholder: '奖品名称'
                },
                {
                    header: '奖品描述：',
                    dataIndex: 'description',
                    xtype: 'textarea',
                    maxlength: 255,
                    placeholder: '奖品描述'
                },
                {
                    header: '奖品总数：',
                    dataIndex: 'count',
                    xtype: 'text',
                    placeholder: '奖品总数'
                },
                {
                    header: '金额：',
                    dataIndex: 'amount',
                    xtype: 'number',
                    placeholder: '金额'
                },
                {
                    header: '排序：',
                    dataIndex: 'sorter',
                    xtype: 'text',
                    placeholder: '排序'
                },
                {
                    header: '有效期天数：',
                    dataIndex: 'expiredDay',
                    xtype: 'number',
                    placeholder: '有效期天数'
                },
                {
                    header: '点击跳转地址：',
                    dataIndex: 'actionUrl',
                    xtype: 'textarea',
                    placeholder: '点击跳转地址'
                },
                {
                    header: '是否支持自动退款：',
                    dataIndex: 'supportAutoRefund',
                    xtype: 'radio',
                    store: autoRefundArr
                }
            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '奖品表列表',
            title: '奖品表列表',

            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            operations: [
                {
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-present/data/view',
                    columns: [
                        {
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '奖品类别：',
                            dataIndex: 'type'
                        },
                        {
                            header: '奖品种类：',
                            dataIndex: 'category'
                        },
                        {
                            header: '奖品标识：',
                            dataIndex: 'key'
                        },
                        {
                            header: '奖品名称：',
                            dataIndex: 'name'
                        },
                        {
                            header: '奖品描述：',
                            dataIndex: 'description'
                        },
                        {
                            header: '奖品总数：',
                            dataIndex: 'count'
                        },
                        {
                            header: '金额：',
                            dataIndex: 'amount'
                        },
                        {
                            header: '有效期天数：',
                            dataIndex: 'expiredDay'
                        },
                        {
                            header: '是否支持自动退款',
                            dataIndex: 'supportAutoRefund',
                            render: function (data) {
                                return data ? '是' : '否';
                            }
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-present/data/view',
                        save: 'jiakao-misc!xueche-present/data/update'
                    },
                    columns: [
                        {
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '奖品类别：',
                            dataIndex: 'type',
                            xtype: 'select',
                            store: typeArr,
                            placeholder: '奖品类别'
                        },
                        {
                            header: '奖品种类：',
                            dataIndex: 'category',
                            xtype: 'select',
                            store: categoryArr,
                            placeholder: '奖品种类'
                        },
                        {
                            header: '奖品标识：',
                            dataIndex: 'key',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: '奖品标识'
                        },
                        {
                            header: '奖品名称：',
                            dataIndex: 'name',
                            xtype: 'text',
                            maxlength: 32,
                            check: 'required',
                            placeholder: '奖品名称'
                        },
                        {
                            header: '奖品描述：',
                            dataIndex: 'description',
                            xtype: 'textarea',
                            maxlength: 255,
                            placeholder: '奖品描述'
                        },
                        {
                            header: '奖品总数：',
                            dataIndex: 'count',
                            xtype: 'text',
                            placeholder: '奖品总数'
                        },
                        {
                            header: '金额：',
                            dataIndex: 'amount',
                            xtype: 'number',
                            placeholder: '金额'
                        },
                        // {
                        //     header: '开奖时间：',
                        //     dataIndex: 'openTime',
                        //     xtype: 'text',
                        //     placeholder: '开奖时间'
                        // },
                        {
                            header: '排序：',
                            dataIndex: 'sorter',
                            xtype: 'text',
                            placeholder: '排序'
                        },
                        {
                            header: '有效期天数：',
                            dataIndex: 'expiredDay',
                            xtype: 'number',
                            placeholder: '有效期天数'
                        },
                        {
                            header: '点击跳转地址：',
                            dataIndex: 'actionUrl',
                            xtype: 'textarea',
                            placeholder: '点击跳转地址'
                        },
                        {
                            header: '是否支持自动退款：',
                            dataIndex: 'supportAutoRefund',
                            xtype: 'radio',
                            store: autoRefundArr
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-present/data/delete'
                }
            ],
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '奖品类别',
                    dataIndex: 'type',
                    render: function (data) {
                        return typeMap[data]
                    }
                },
                {
                    header: '奖品种类',
                    dataIndex: 'category',
                    render: function (data) {
                        return categoryMap[data]
                    }
                },
                {
                    header: '奖品标识',
                    dataIndex: 'key'
                },
                {
                    header: '奖品名称',
                    dataIndex: 'name'
                },
                {
                    header: '奖品描述',
                    dataIndex: 'description'
                },
                {
                    header: '奖品总数',
                    dataIndex: 'count'
                },
                {
                    header: '金额',
                    dataIndex: 'amount'
                },
                // {
                //     header: '开奖时间',
                //     dataIndex: 'openTime'
                // },
                {
                    header: '排序：',
                    dataIndex: 'sorter'
                },
                {
                    header: '有效期天数',
                    dataIndex: 'expiredDay'
                },
                {
                    header: '点击跳转地址',
                    dataIndex: 'actionUrl',
                    render: function (data) {
                        if(data){
                            return '<a href="'+data+'" target="_blank">'+data+'</a>'
                        }
                    }
                },
                {
                    header: '是否支持自动退款',
                    dataIndex: 'supportAutoRefund',
                    render: function (data) {
                        return data ? '是' : '否';
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人',
                    dataIndex: 'updateUserName'
                }

            ]
        }, ['jiakao-misc!xueche-present/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
