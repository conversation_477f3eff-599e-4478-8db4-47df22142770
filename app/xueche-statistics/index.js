/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-statistics/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [
                {
                    header: '统计日期：',
                    dataIndex: 'date',
                    xtype: 'text',
                    maxlength: 10,
                    check: 'required',
                    placeholder: '统计日期'
                },
                {
                    header: '直播ID：',
                    dataIndex: 'liveId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '直播ID'
                },
                {
                    header: '渠道：',
                    dataIndex: 'channel',
                    xtype: 'text',
                    maxlength: 10,
                    check: 'required',
                    placeholder: '渠道'
                },
                {
                    header: '活动首页pv量：',
                    dataIndex: 'indexPv',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '活动首页pv量'
                },
                {
                    header: '活动首页uv量：',
                    dataIndex: 'indexUv',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '活动首页uv量'
                },
                {
                    header: '分享人数1：',
                    dataIndex: 'indexShareNum1',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享人数1'
                },
                {
                    header: '分享率1：',
                    dataIndex: 'indexShareRate1',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享率1'
                },
                {
                    header: '分享人数2：',
                    dataIndex: 'indexShareNum2',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享人数2'
                },
                {
                    header: '分享率2：',
                    dataIndex: 'indexShareRate2',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享率2'
                },
                {
                    header: '分享人数3：',
                    dataIndex: 'indexShareNum3',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享人数3'
                },
                {
                    header: '分享率3：',
                    dataIndex: 'indexShareRate3',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享率3'
                },
                {
                    header: '点击付款按钮1人数：',
                    dataIndex: 'indexPayNum1',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '点击付款按钮1人数'
                },
                {
                    header: '点击付款按钮2人数：',
                    dataIndex: 'indexPayNum2',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '点击付款按钮2人数'
                },
                {
                    header: '渠道页pv量：',
                    dataIndex: 'channelPagePv',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '渠道页pv量'
                },
                {
                    header: '渠道页uv量：',
                    dataIndex: 'channelPageUv',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '渠道页uv量'
                },
                {
                    header: '渠道页点击付款按钮数量：',
                    dataIndex: 'channelPagePayNum',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '渠道页点击付款按钮数量'
                },
                {
                    header: '分享页pv量：',
                    dataIndex: 'sharePagePv',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享页pv量'
                },
                {
                    header: '分享页uv量：',
                    dataIndex: 'sharePageUv',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享页uv量'
                },
                {
                    header: '分享页点击付款按钮数量：',
                    dataIndex: 'sharePagePayNum',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '分享页点击付款按钮数量'
                },
                {
                    header: '参与活动人数：',
                    dataIndex: 'joinNum',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '参与活动人数'
                },
                {
                    header: '留资人数：',
                    dataIndex: 'retainNum',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '留资人数'
                },
                {
                    header: '留资率：',
                    dataIndex: 'retainRate',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '留资率'
                },
                {
                    header: '实际付款人数：',
                    dataIndex: 'paySuccessNum',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '实际付款人数'
                },
                {
                    header: '平均抽奖券数量：',
                    dataIndex: 'avgLotteryNum',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '平均抽奖券数量'
                },
                {
                    header: '创建人id：',
                    dataIndex: 'createUserId',
                    xtype: 'text',
                    placeholder: '创建人id'
                },
                {
                    header: 'createUserName：',
                    dataIndex: 'createUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'createUserName'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '学车节数据统计表列表',
            title: '学车节数据统计表列表',
            search: [
                {
                    dataIndex: 'startTime',
                    xtype: 'date',
                    placeholder: '开始时间'
                },
                {
                    dataIndex: 'endTime',
                    xtype: 'date',
                    placeholder: '结束时间'
                },
                {
                    dataIndex: 'liveId',
                    xtype: 'text',
                    placeholder: '直播ID'
                },
                {
                    dataIndex: 'channel',
                    xtype: 'text',
                    placeholder: '渠道'
                }
            ],
            buttons: {
                top: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [
                    {
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    }
                ]
            },
            columns: [
                {
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '统计日期',
                    dataIndex: 'date'
                },
                {
                    header: '直播ID',
                    dataIndex: 'liveId'
                },
                {
                    header: '渠道',
                    dataIndex: 'channel'
                },
                {
                    header: '活动首页pv量',
                    dataIndex: 'indexPv'
                },
                {
                    header: '活动首页uv量',
                    dataIndex: 'indexUv'
                },
                {
                    header: '分享人数1',
                    dataIndex: 'indexShareNum1'
                },
                {
                    header: '分享率1',
                    dataIndex: 'indexShareRate1'
                },
                {
                    header: '分享人数2',
                    dataIndex: 'indexShareNum2'
                },
                {
                    header: '分享率2',
                    dataIndex: 'indexShareRate2'
                },
                {
                    header: '分享人数3',
                    dataIndex: 'indexShareNum3'
                },
                {
                    header: '分享率3',
                    dataIndex: 'indexShareRate3'
                },
                {
                    header: '点击付款按钮1人数',
                    dataIndex: 'indexPayNum1'
                },
                {
                    header: '点击付款按钮2人数',
                    dataIndex: 'indexPayNum2'
                },
                {
                    header: '渠道页pv量',
                    dataIndex: 'channelPagePv'
                },
                {
                    header: '渠道页uv量',
                    dataIndex: 'channelPageUv'
                },
                {
                    header: '渠道页点击付款按钮数量',
                    dataIndex: 'channelPagePayNum'
                },
                {
                    header: '分享页pv量',
                    dataIndex: 'sharePagePv'
                },
                {
                    header: '分享页uv量',
                    dataIndex: 'sharePageUv'
                },
                {
                    header: '分享页点击付款按钮数量',
                    dataIndex: 'sharePagePayNum'
                },
                {
                    header: '参与活动人数',
                    dataIndex: 'joinNum'
                },
                {
                    header: '留资人数',
                    dataIndex: 'retainNum'
                },
                {
                    header: '留资率',
                    dataIndex: 'retainRate'
                },
                {
                    header: '实际付款人数',
                    dataIndex: 'paySuccessNum'
                },
                {
                    header: '平均抽奖券数量',
                    dataIndex: 'avgLotteryNum'
                }

            ]
        }, ['jiakao-misc!xueche-statistics/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
