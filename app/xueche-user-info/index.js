/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-user-info/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '用户id：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '用户id'
                },
                {
                    header: '用户姓名：',
                    dataIndex: 'userName',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '用户姓名'
                },
                {
                    header: '直播id：',
                    dataIndex: 'liveId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '直播id'
                },
                {
                    header: '订单号：',
                    dataIndex: 'orderNumber',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '订单号'
                },
                {
                    header: '加密手机号：',
                    dataIndex: 'phoneEncrypt',
                    xtype: 'textarea',
                    maxlength: 1024,
                    placeholder: '加密手机号'
                },
                {
                    header: '手机号掩码：',
                    dataIndex: 'phoneMask',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '手机号掩码'
                },
                {
                    header: '手机号标识符：',
                    dataIndex: 'phoneIdentifier',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '手机号标识符'
                },
                {
                    header: '驾照类型：',
                    dataIndex: 'carType',
                    xtype: 'text',
                    maxlength: 45,
                    placeholder: '驾照类型'
                },
                {
                    header: '出发地点：',
                    dataIndex: 'address',
                    xtype: 'textarea',
                    maxlength: 256,
                    placeholder: '出发地点'
                },
                {
                    header: '创建人id：',
                    dataIndex: 'createUserId',
                    xtype: 'text',
                    placeholder: '创建人id'
                },
                {
                    header: 'createUserName：',
                    dataIndex: 'createUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'createUserName'
                },
                {
                    header: '更新时间：',
                    dataIndex: 'updateTime',
                    xtype: 'date',
                    placeholder: '更新时间'
                },
                {
                    header: '更新人id：',
                    dataIndex: 'updateUserId',
                    xtype: 'text',
                    placeholder: '更新人id'
                },
                {
                    header: 'updateUserName：',
                    dataIndex: 'updateUserName',
                    xtype: 'text',
                    maxlength: 32,
                    placeholder: 'updateUserName'
                }

            ]
        }).add();
    }

    var quickGenerate = function(table, row, lineData){       
        Table({
            title: '生成优惠券',
            width: 500,
            store: 'jiakao-misc!xueche-user-info/data/genCode',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden',
                    value: lineData.id
                },
                {
                    header: '数量：',
                    dataIndex: 'num',
                    xtype: 'number',
                    placeholder: '数量'
                }
            ]
        }).add();
    }


    var list = function (panel) {
        Table({
            description: '用户报名信息列表',
            title: '用户报名信息列表',
            search: [{
                    placeholder: '直播id：',
                    dataIndex: 'liveId',
                    xtype: 'text'
                },
                {
                    placeholder: '订单号：',
                    dataIndex: 'orderNumber',
                    xtype: 'text'

                },
                {
                    placeholder: '优惠卷',
                    xtype: 'text',
                    dataIndex: 'code'
                },
            ],
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    {
                        name: '添加',
                        class: 'primary',
                        click: add
                    }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                    name: '查看',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-user-info/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '用户姓名：',
                            dataIndex: 'userName'
                        },
                        {
                            header: '直播id：',
                            dataIndex: 'liveId'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber'
                        },
                        {
                            header: '加密手机号：',
                            dataIndex: 'phoneEncrypt'
                        },
                        {
                            header: '手机号掩码：',
                            dataIndex: 'phoneMask'
                        },
                        {
                            header: '手机号标识符：',
                            dataIndex: 'phoneIdentifier'
                        },
                        {
                            header: '驾照类型：',
                            dataIndex: 'carType'
                        },
                        {
                            header: '出发地点：',
                            dataIndex: 'address'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'updateTime'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-user-info/data/view',
                        save: 'jiakao-misc!xueche-user-info/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '用户id：',
                            dataIndex: 'userId',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '用户id'
                        },
                        {
                            header: '用户姓名：',
                            dataIndex: 'userName',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '用户姓名'
                        },
                        {
                            header: '直播id：',
                            dataIndex: 'liveId',
                            xtype: 'text',
                            check: 'required',
                            placeholder: '直播id'
                        },
                        {
                            header: '订单号：',
                            dataIndex: 'orderNumber',
                            xtype: 'text',
                            maxlength: 64,
                            placeholder: '订单号'
                        },
                        {
                            header: '加密手机号：',
                            dataIndex: 'phoneEncrypt',
                            xtype: 'textarea',
                            maxlength: 1024,
                            placeholder: '加密手机号'
                        },
                        {
                            header: '手机号掩码：',
                            dataIndex: 'phoneMask',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '手机号掩码'
                        },
                        {
                            header: '手机号标识符：',
                            dataIndex: 'phoneIdentifier',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '手机号标识符'
                        },
                        {
                            header: '驾照类型：',
                            dataIndex: 'carType',
                            xtype: 'text',
                            maxlength: 45,
                            placeholder: '驾照类型'
                        },
                        {
                            header: '出发地点：',
                            dataIndex: 'address',
                            xtype: 'textarea',
                            maxlength: 256,
                            placeholder: '出发地点'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId',
                            xtype: 'text',
                            placeholder: '创建人id'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: 'createUserName'
                        },
                        {
                            header: '更新时间：',
                            dataIndex: 'updateTime',
                            xtype: 'date',
                            placeholder: '更新时间'
                        },
                        {
                            header: '更新人id：',
                            dataIndex: 'updateUserId',
                            xtype: 'text',
                            placeholder: '更新人id'
                        },
                        {
                            header: 'updateUserName：',
                            dataIndex: 'updateUserName',
                            xtype: 'text',
                            maxlength: 32,
                            placeholder: 'updateUserName'
                        }

                    ]
                },
                {
                    name: '删除',
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-user-info/data/delete'
                },
                {
                    name: "生成优惠券",
                    class: 'primary',
                    click: quickGenerate
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                // {
                //     header: '用户id：',
                //     dataIndex: 'userId'
                // },
                {
                    header: '用户姓名：',
                    dataIndex: 'userName'
                },
                {
                    header: '直播id：',
                    dataIndex: 'liveId'
                },
                {
                    header: '订单号：',
                    dataIndex: 'orderNumber'
                },

                {
                    header: '手机号掩码：',
                    dataIndex: 'phoneMask',
                    render: function (data, ) {
                        return data ? '<a>' + data + '</a>' : ''
                    },
                    click: function (table, rows, lineData) {
                        Store(['jiakao-misc!xueche-user-info/data/getPhone?id='+lineData.id], [{
                            aliases: 'list'
                        }]).load().done(function (store) {
                   Widgets.dialog.alert(       store.data.list.data)
                        }).fail(function () {});
                    }

                },
                {
                    header: '优惠券',
                    dataIndex: 'code',
                    render: function (data) {
                        return '<div style="width: 200px; word-break: break-all">' + data + '</div>'
                    }
                },

                {
                    header: '驾照类型',
                    dataIndex: 'carType'
                },
                {
                    header: '城市名称',
                    dataIndex: 'cityName'
                },
                {
                    header: '出发地点',
                    dataIndex: 'address'
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '更新时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
            ]
        }, ['jiakao-misc!xueche-user-info/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
