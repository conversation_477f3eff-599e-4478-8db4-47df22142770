/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form'], function (Template, Table, Utils, Widgets, Store, Form) {

    var statusArr = [{
            key: 0,
            value: '未生效'
        },
        {
            key: 1,
            value: '未领取'
        },
        {
            key: 2,
            value: '已领取'
        },
        {
            key: 3,
            value: '已放弃'
        },
        {
            key: 4,
            value: '已过期'
        }
    ];

    var statusMap = {
        0: '未生效',
        1: '未领取',
        2: '已领取',
        3: '已放弃',
        4: '已过期'
    }

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueche-win-prize-record/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                    header: '用户mucangId：',
                    dataIndex: 'userId',
                    xtype: 'text',
                    maxlength: 64,
                    check: 'required',
                    placeholder: '用户mucangId'
                },
                {
                    header: '直播ID：',
                    dataIndex: 'liveId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '直播ID'
                },
                {
                    header: '抽奖券ID：',
                    dataIndex: 'lotteryId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '抽奖券ID'
                },
                {
                    header: '订单编号：',
                    dataIndex: 'orderNumber',
                    xtype: 'text',
                    maxlength: 64,
                    placeholder: '订单编号'
                },
                {
                    header: '奖励ID：',
                    dataIndex: 'presentId',
                    xtype: 'text',
                    check: 'required',
                    placeholder: '奖励ID'
                },
                {
                    header: '奖品金额：',
                    dataIndex: 'presentAmount',
                    xtype: 'text',
                    placeholder: '奖品金额'
                },
                {
                    header: '奖品有效期秒数：',
                    dataIndex: 'presentExpire',
                    xtype: 'text',
                    placeholder: '奖品有效期秒数'
                },
                {
                    header: '奖励领取状态：',
                    dataIndex: 'status',
                    xtype: 'select',
                    store: statusArr,
                    placeholder: '奖励领取状态'
                }

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '中奖记录表列表',
            title: '中奖记录表列表',
            buttons: {
                top: [{
                        name: '刷新',
                        class: 'info',
                        click: function (obj) {
                            obj.render();
                        }
                    },
                    // {
                    //     name: '',
                    //     class: 'primary',
                    //     click: add
                    // }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            search: [
                {
                    xtype: 'text',
                    dataIndex: 'presentId',
                    placeholder: '奖品ID'
                },
                {
                    xtype: 'text',
                    dataIndex: 'liveId',
                    placeholder: '直播ID'
                },
                {
                    xtype: 'text',
                    dataIndex: 'orderNumber',
                    placeholder: '订单号'
                }
            ],
            operations: [
                {
                    name: '',
                    xtype: 'view',
                    width: 400,
                    class: 'success',
                    title: '查看',
                    store: 'jiakao-misc!xueche-win-prize-record/data/view',
                    columns: [{
                            header: '#',
                            dataIndex: 'id'
                        },
                        {
                            header: '用户mucangId：',
                            dataIndex: 'userId'
                        },
                        {
                            header: '直播ID：',
                            dataIndex: 'liveId'
                        },
                        {
                            header: '抽奖券ID：',
                            dataIndex: 'lotteryId'
                        },
                        {
                            header: '订单编号：',
                            dataIndex: 'orderNumber'
                        },
                        {
                            header: '奖励ID：',
                            dataIndex: 'presentId'
                        },
                        {
                            header: '奖品金额：',
                            dataIndex: 'presentAmount'
                        },
                        {
                            header: '奖品有效期天数：',
                            dataIndex: 'presentExpire'
                        },
                        {
                            header: '奖励领取状态：',
                            dataIndex: 'status'
                        },
                        {
                            header: '创建时间：',
                            render: function (data) {
                                return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                            },
                            dataIndex: 'createTime'
                        },
                        {
                            header: '创建人id：',
                            dataIndex: 'createUserId'
                        },
                        {
                            header: 'createUserName：',
                            dataIndex: 'createUserName'
                        }

                    ]
                },
                {
                    name: '编辑',
                    xtype: 'edit',
                    width: 500,
                    class: 'warning',
                    title: '编辑',
                    render: function (name, arr, index) {
                        var lineData = arr[index];
                        if (lineData.status == 0) {
                            return name;
                        }
                    },
                    success: function (obj, dialog, e) {
                        dialog.close();
                        obj.render();
                    },
                    store: {
                        load: 'jiakao-misc!xueche-win-prize-record/data/view',
                        save: 'jiakao-misc!xueche-win-prize-record/data/update'
                    },
                    columns: [{
                            dataIndex: 'id',
                            xtype: 'hidden'
                        },
                        {
                            header: '用户ID：',
                            dataIndex: 'userId',
                            xtype: 'text',
                            placeholder: '用户ID'
                        },
                        {
                            header: '用户名称：',
                            dataIndex: 'userName',
                            xtype: 'text',
                            placeholder: '用户名称'
                        },
                        {
                            header: '电话：',
                            dataIndex: 'phone',
                            xtype: 'text',
                            placeholder: '电话'
                        },
                        {
                            header: '直播ID：',
                            dataIndex: 'liveId',
                            xtype: 'text',
                            placeholder: '直播ID'
                        },
                        {
                            header: '订单编号',
                            dataIndex: 'orderNumber',
                            xtype: 'text',
                            placeholder: '订单编号'
                        },
                        {
                            header: '奖品名称',
                            dataIndex: 'presentName',
                            xtype: 'text',
                            placeholder: '奖品名称'
                        },
                        {
                            header: '奖品金额',
                            dataIndex: 'presentAmount',
                            xtype: 'text',
                            placeholder: '奖品金额'
                        },
                        {
                            header: '奖品类型',
                            dataIndex: 'presentType',
                            xtype: 'radio',
                            store: [{
                                    key: 1,
                                    value: '大奖'
                                },
                                {
                                    key: 2,
                                    value: '普通奖品'
                                }
                            ]
                        },
                        {
                            header: '奖品有效期天数',
                            dataIndex: 'presentExpire',
                            xtype: 'text',
                            placeholder: '奖品有效期天数'
                        },
                        {
                            header: '奖励ID',
                            dataIndex: 'presentId',
                            xtype: 'text',
                            placeholder: '奖励ID'
                        },
                        {
                            header: '奖励领取状态',
                            dataIndex: 'status',
                            xtype: 'select',
                            store: statusArr
                        }
                    ]
                },
                {
                    name: '删除',
                    render: function (name, arr, index) {
                        var lineData = arr[index];
                        if (lineData.status == 0) {
                            return name;
                        }
                    },
                    class: 'danger',
                    xtype: 'delete',
                    store: 'jiakao-misc!xueche-win-prize-record/data/delete'
                },
                {
                    name: '退款',
                    class: 'danger',
                    render: function (name, arr, index) {
                        var lineData = arr[index];
                        if (lineData.presentType == 1 && lineData.status == 1) {
                            return name;
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认退款吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!xueche-win-prize-record/data/refund?id=' + lineData.id]).save().done(function (store) {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message)
                                });
                            }
                        })
                    }
                },
                {
                    name: '确认领取',
                    class: 'warning',
                    render: function (name, arr, index) {
                        var lineData = arr[index];
                        if (lineData.presentType == 1 && lineData.status == 1) {
                            return name;
                        }
                    },
                    click: function (table, row, lineData) {
                        Widgets.dialog.confirm('确认领取吗？', function (ev, status) {
                            if (status) {
                                Store(['jiakao-misc!xueche-win-prize-record/data/confirm?id=' + lineData.id]).save().done(function (store) {
                                    table.render();
                                }).fail(function (ret) {
                                    Widgets.dialog.alert(ret.message)
                                });
                            }
                        })
                    }
                },
                {
                    name: '查看电话',
                    class: 'success',
                    click: function (table, row, lineData) {
                        Store(['jiakao-misc!xueche-win-prize-record/data/viewPhone']).load([{
                            aliases: 'phone',
                            params: {
                                id: lineData.id
                            }
                        }]).done(function (store, data) {
                            Widgets.dialog.alert(data.phone.data);
                        })
                    }
                }
            ],
            columns: [{
                    header: '#',
                    dataIndex: 'id',
                    width: 20
                },
                {
                    header: '用户ID',
                    dataIndex: 'userId'
                },
                {
                    header: '用户名称',
                    dataIndex: 'userName'
                },
                {
                    header: '电话',
                    dataIndex: 'phone'
                },
                {
                    header: '直播ID',
                    dataIndex: 'liveId'
                },
                {
                    header: '订单编号',
                    dataIndex: 'orderNumber'
                },
                {
                    header: '奖品名称',
                    dataIndex: 'presentName'
                },
                {
                    header: '奖品金额',
                    dataIndex: 'presentAmount'
                },
                {
                    header: '奖品类型',
                    dataIndex: 'presentType',
                    render: function (data) {
                        if (data == 1) {
                            return '大奖'
                        }
                        return '普通奖品';
                    }
                },
                {
                    header: '奖品有效期天数',
                    dataIndex: 'presentExpire'
                },
                {
                    header: '抽奖券码',
                    dataIndex: 'lotteryCode'
                },
                {
                    header: '奖励ID',
                    dataIndex: 'presentId'
                },
                // {
                //     header: '奖品是否发放成功',
                //     dataIndex: 'sendSuccess',
                //     render: function (data) {
                //         if(data){
                //             return '成功';
                //         }else{
                //             return '失败';
                //         }
                //     }
                // },
                // {
                //     header: '奖品发放失败原因',
                //     dataIndex: 'failReason'
                // },
                {
                    header: '奖励领取状态',
                    dataIndex: 'status',
                    render: function (data) {
                        return statusMap[data];
                    }
                },
                {
                    header: '创建时间',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                }

            ]
        }, ['jiakao-misc!xueche-win-prize-record/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});
