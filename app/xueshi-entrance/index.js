/*
 * index v0.0.1
 *
 * name: xiaojia
 * date: 2013/8/22
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/utils', 'simple!core/widgets', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Table, Utils, Widgets, Store, Form, Plugin) {

    var add = function (table) {
        Table({
            title: '添加',
            width: 500,
            store: 'jiakao-misc!xueshi-entrance/data/insert',
            success: function (obj, dialog) {
                dialog.close();
                table.render();
            },
            columns: [{
                header: '城市编码：',
                dataIndex: 'cityCode',
                xtype: Plugin('jiakao-misc!select-district', {
                    name: 'cityCode',
                    areaName: 'areaCode',
                    hideArea: true,
                    insert: {
                        province: [{
                            code: '',
                            name: '请选择省份'
                        }],
                        city: [{
                            code: '',
                            name: '请选择市'
                        }],
                        area: [{
                            code: '',
                            name: '请选择区域'
                        }]
                    }
                }, function (plugin, code) {

                }),
            },

            {
                header: '是否支持学时：',
                dataIndex: 'showEntrance',
                xtype: 'radio',
                store: [{
                    key: true,
                    value: '是'
                },
                {
                    key: false,
                    value: '否'
                }
                ]
            },
            {
                header: '科目',
                dataIndex: 'kemu',
                xtype: 'text',
                maxlength: 200,
                placeholder: '科目'
            },
            {
                header: '车型:',
                dataIndex: 'carType',
                xtype: 'checkbox',
                store: [{
                    key: 'car',
                    value: '小车'
                },
                {
                    key: 'bus',
                    value: '客车'
                },
                {
                    key: 'truck',
                    value: '货车'
                },
                {
                    key: 'moto',
                    value: '摩托车'
                }]
            },

            ]
        }).add();
    }

    var list = function (panel) {
        Table({
            description: '学时的配置列表',
            title: '学时的配置列表',
            search:[
                {
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district2', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },
                {
                    dataIndex: 'status',
                    xtype: 'select',
                    store: [
                        {
                            key: '',
                            value: "选择状态"
                        },
                        {
                            key: 0,
                            value: '未发布'
                        },
                        {
                            key: 1,
                            value: '测试发布'
                        },
                        {
                            key: 2,
                            value: '正式发布'
                        }
                    ]
                }
            ],
            buttons: {
                top: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                },
                {
                    name: '添加',
                    class: 'primary',
                    click: add
                }
                ],
                bottom: [{
                    name: '刷新',
                    class: 'info',
                    click: function (obj) {
                        obj.render();
                    }
                }]
            },
            operations: [{
                name: '查看',
                xtype: 'view',
                width: 400,
                class: 'success',
                title: '查看',
                store: 'jiakao-misc!xueshi-entrance/data/view',
                columns: [{
                    header: '#',
                    dataIndex: 'id'
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode'
                },
                {
                    header: '城市：',
                    dataIndex: 'cityName'
                },
                {
                    header: '是否支持学时：',
                    render: function (data) {
                        if (data) {
                            return '是';
                        } else {
                            return '否';
                        }
                    },
                    dataIndex: 'showEntrance'
                },
                {
                    header: '创建时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'createTime'
                },
                {
                    header: '创建人Id：',
                    dataIndex: 'createUserId'
                },
                {
                    header: '创建人：',
                    dataIndex: 'createUserName'
                },
                {
                    header: '更新时间：',
                    render: function (data) {
                        return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                    },
                    dataIndex: 'updateTime'
                },
                {
                    header: '修改人Id：',
                    dataIndex: 'updateUserId'
                },
                {
                    header: '修改人：',
                    dataIndex: 'updateUserName'
                }

                ]
            },
            {
                name: '编辑',
                xtype: 'edit',
                width: 500,
                class: 'warning',
                title: '编辑',
                success: function (obj, dialog, e) {
                    dialog.close();
                    obj.render();
                },
                store: {
                    load: 'jiakao-misc!xueshi-entrance/data/view',
                    save: 'jiakao-misc!xueshi-entrance/data/update'
                },
                columns: [{
                    dataIndex: 'id',
                    xtype: 'hidden'
                },
                {
                    header: '城市编码：',
                    dataIndex: 'cityCode',
                    xtype: Plugin('jiakao-misc!select-district', {
                        name: 'cityCode',
                        areaName: 'areaCode',
                        hideArea: true,
                        insert: {
                            province: [{
                                code: '',
                                name: '请选择省份'
                            }],
                            city: [{
                                code: '',
                                name: '请选择市'
                            }],
                            area: [{
                                code: '',
                                name: '请选择区域'
                            }]
                        }
                    }, function (plugin, code) {

                    }),
                },

                {
                    header: '是否支持学时：',
                    dataIndex: 'showEntrance',
                    xtype: 'radio',
                    store: [{
                        key: true,
                        value: '是'
                    },
                    {
                        key: false,
                        value: '否'
                    }
                    ]
                },
                {
                    header: '科目',
                    dataIndex: 'kemu',
                    xtype: 'text',
                    maxlength: 200,
                    placeholder: '科目'
                },
                {
                    header: '车型:',
                    dataIndex: 'carType',
                    xtype: 'checkbox',
                    store: [{
                        key: 'car',
                        value: '小车'
                    },
                    {
                        key: 'bus',
                        value: '客车'
                    },
                    {
                        key: 'truck',
                        value: '货车'
                    },
                    {
                        key: 'moto',
                        value: '摩托车'
                    }]
                },

                ]
            },
            {
                name: '删除',
                class: 'danger',
                xtype: 'delete',
                store: 'jiakao-misc!xueshi-entrance/data/delete'
            },
            {
                name: '上线',
                class: 'primary',
                render: function (name, arr, i) {
                    return arr[i].status == 1 ? name : '';

                },
                click: function (table, dom, lineData) {
                    Widgets.dialog.confirm('确认正式发布吗', function (e, stat) {
                        if (stat) {
                            Store(['jiakao-misc!xueshi-entrance/data/update?id=' + lineData.id + '&status=2']).save().done(data => {
                                console.log(data)
                                table.render();
                            }).fail();
                        }
                    })
                }
            },
            {
                name: '下线',
                class: 'info',
                render: function (name, arr, i) {
                    return arr[i].status == 2 ? name : '';

                },
                click: function (table, dom, lineData) {
                    Widgets.dialog.confirm('确认测试发布吗', function (e, stat) {
                        if (stat) {
                            Store(['jiakao-misc!xueshi-entrance/data/update?id=' + lineData.id + '&status=1']).save().done(data => {
                                console.log(data)
                                table.render();
                            }).fail();
                        }
                    })
                }
            }
            ],
            columns: [{
                header: '#',
                dataIndex: 'id',
                width: 20
            },
            {
                header: '城市编码',
                dataIndex: 'cityCode'
            },
            {
                header: '城市',
                dataIndex: 'cityName'
            },
            {
                header: '是否支持学时',
                render: function (data) {
                    if (data) {
                        return '是';
                    } else {
                        return '否';
                    }
                },
                dataIndex: 'showEntrance'
            },
            {
                header: '状态',
                render: function (data) {
                    switch (data) {
                        case 0:
                            return '未发布';
                        case 1:
                            return '测试发布';
                        case 2:
                            return '正式发布'
                    }
                },
                dataIndex: 'status'

            },
            {
                header: '科目',
                dataIndex: 'kemu'
            },
            {
                header: '车型',
                dataIndex: 'carType'
            },
            {
                header: '创建时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'createTime'
            },
            {
                header: '创建人Id',
                dataIndex: 'createUserId'
            },
            {
                header: '创建人',
                dataIndex: 'createUserName'
            },
            {
                header: '更新时间',
                render: function (data) {
                    return Utils.format.date(data, 'yyyy-MM-dd HH:mm:ss');
                },
                dataIndex: 'updateTime'
            },
            {
                header: '修改人Id',
                dataIndex: 'updateUserId'
            },
            {
                header: '修改人',
                dataIndex: 'updateUserName'
            }

            ]
        }, ['jiakao-misc!xueshi-entrance/data/list'], panel, null).render();
    }

    return {
        list: list
    }

});