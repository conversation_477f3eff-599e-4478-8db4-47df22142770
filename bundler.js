const fs = require('fs-extra')
const fg = require('fast-glob');
const dirs = fg.sync('./**', {deep: 1, onlyDirectories: true, ignore: ['**/node_modules/**', '**/bundler/**'], });
const files = fg.sync('./**', {deep: 1, onlyFiles: true, ignore: ['**/node_modules/**', '**/bundler/**'], });

fs.removeSync('bundler')

dirs.forEach(dir => {
	console.log(dir)
	fs.copySync(dir, './bundler/'+dir)
})
files.forEach(file => {
	fs.copySync(file, './bundler/'+file)
})