<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>加载中...</title>
    <link rel="stylesheet" type="text/css" href="resources/codemirror/lib/codemirror.css" />
    <link rel="stylesheet" href="resources/codemirror/addon/lint/lint.css">
    <link rel="stylesheet" type="text/css" href="resources/codemirror/theme/3024-day.css" />
    <link rel="stylesheet" href="resources/codemirror/addon/merge/merge.css">

    <script src="resources/codemirror/lib/codemirror.js"></script>
    <!-- <script src="resources/codemirror/mode/javascript/javascript.js"></script> -->
    <script src="resources/codemirror/mode/xml/xml.js"></script>
    <script src="resources/codemirror/addon/display/autorefresh.js"></script>
    <script src="resources/codemirror/addon/lint/lint.js"></script>
    <script src="resources/codemirror/addon/lint/json-lint.js"></script>
    <script src="resources/codemirror/addon/merge/merge.js"></script>
    <script src="resources/codemirror/lib/diff_match_patch.js"></script>
    <script src="resources/exif.js"></script>
   <script src="resources/three.js"></script>
   <script src="./html2canvas.min.js"></script>


</head>

<body>
    <style>
        .CodeMirror-merge-l-chunk+.CodeMirror-gutter-wrapper+.CodeMirror-line {
            background-color: #e3d7d7;
        }

        .CodeMirror-merge-l-chunk+.CodeMirror-gutter-wrapper+.CodeMirror-line span {
            background-image: none;
        }

        .CodeMirror-merge-l-chunk+.CodeMirror-gutter-wrapper+.CodeMirror-line .CodeMirror-merge-l-deleted {
            background-color: #fdffdd;
            color: red;
            border-radius: 5px;
            font-weight: 400;
            padding-left: 5px;
            padding-right: 5px;
        }

        .CodeMirror-merge-l-chunk+.CodeMirror-gutter-wrapper+.CodeMirror-line .CodeMirror-merge-l-inserted {
            background-color: #fdffdd;
            border-radius: 5px;
            font-weight: 100;
            padding-left: 5px;
            padding-right: 5px;
        }

        .CodeMirror-merge {
            height: 400px;
        }

        div[data-item="meeting-container"] tr td {
            vertical-align: middle
        }

        div[data-item="meeting-container"] .progress {
            margin-bottom: 0
        }

        div[data-item="meeting-container"] .progress p {
            margin: 0
        }

        div[data-item="meeting-container"] .progress-bar.timeShaft {
            text-align: left;
            line-height: 18px;
        }

        div[data-item="meeting-container"] .meeting-progress {
            position: relative;
            height: 70px;
            margin-top: 10px;
            background-color: #D1CECE
        }

        div[data-item="meeting-container"] .meeting-progress .progress-bar {
            position: absolute;
            display: -webkit-box;
            -webkit-box-pack: center;
            -webkit-box-align: center;
            -webkit-box-orient: vertical;
            display: box;
            box-pack: center;
            box-align: center;
            box-orient: vertical;
        }

        div[data-item="meeting-container"] .glyphicon-edit,
        div[data-item="meeting-container"] .glyphicon-remove {
            position: absolute;
            display: none;
            font-size: 16px;
            top: 5px;
            right: 10px;
        }

        div[data-item="meeting-container"] .glyphicon-remove {
            top: 30px
        }

        div[data-item="meeting-container"] .progress-bar:hover {
            background: #FF9900;
        }

        div[data-item="meeting-container"] .progress-bar:hover .glyphicon-edit,
        div[data-item="meeting-container"] .progress-bar:hover .glyphicon-remove {
            display: block;
        }

        div[data-item="meeting-container"] input[type="range"] {
            -webkit-appearance: none;
            width: 100%;
            background-color: #5bc0de;
            border-radius: 10px;
            padding: 0;
            border: none
        }

        div[data-item="meeting-container"] input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            cursor: default;
            top: 1px;
            height: 20px;
            width: 20px;
            background: none repeat scroll 0 0 #0099FF;
            border-radius: 10px
        }

        option:disabled {
            background: #ededed;
        }

        .amap-icon img {
            width: 20px;
            height: 28px;
        }

        .amap-marker-label {
            border: 0;
            height: 21px;
            background: #ffffff;
            border-radius: 4px;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
            padding: 0 5px;
        }
        .amap-sug-result{
            z-index: 99999;
         }
        .map-label-info {
            font-size: 12px;
            text-align: center;
            color: #1c67ff;
            line-height: 21px;
        }
    </style>
    <script>
        var getCityData = function (call) {
            var src = 'https://web-resource.mucang.cn/public-data/city/city-area.js';
            var script = document.createElement('script');
            var old = document.getElementById('scriptcitydata');
            script.src = src;
            script.id = 'scriptcitydata';
            if (old) {
                old.parentNode.removeChild(old);
            }
            document.head.appendChild(script);
            window.getCDNCityData = call;

        }

        // 所有配置的路径最后都不以 "/" 结尾，程序会自动增加
        ! function () {

            // 加载成功之后执行的方法
            var success = function () {
                getCityData(function (data) {

                    console.log(data.initial);
                    var arr1 = [];

                    for (let i = 0, len = data.initial.length; i < len; ++i) {
                        let arr = data.initial[i][1]

                        for (let j = 0, len = arr.length; j < len; ++j) {
                            for (let k in arr[j].citys) {
                                if (arr[j].citys[k].counties == void 0) {
                                    arr[j].citys[k].counties = []
                                }
                            }
                            arr1.push({
                                name: arr[j].province.name,
                                code: arr[j].province.code,
                                pinyin: arr[j].province.pinyin,
                                cities: arr[j].citys
                            })
                        }

                    }

                    Object.defineProperty(Simple, "DISTRICT", {
                        value: arr1,
                        writable: false,
                        configurable: false
                    });
                })

                console.log(Simple);
            }

            var config = {

                // 产品标题
                title: '驾考混合项目',
                name: 'jiakao-misc',

                // true 则加载本地文件，默认从当前目录开始， false则从framework.root加载
                debug: false,

                // ajax.js判断host，如果找到匹配则替换，例：
                // host: {
                //     local: 'http://w3c.org'
                // }
                // Ajax.require('local://m.json') > Ajax.require('http://w3c.org/m.json');
                // PS：此配置从v1.1版本开始生效
                host: {
                    // API地址
                    local: 'https://jiakao-misc-task.kakamobi.cn',
                    "jiakao-misc": 'https://jiakao-misc-task.kakamobi.cn',
                    "jiakao-misc2": 'https://jiakao-misc-task.kakamobi.cn',
                    "danmu": 'https://danmu.kakamobi.cn',
                    "monkey": 'https://monkey.kakamobi.cn',
                    'saturn': 'https://admin-cheyouquan.kakamobi.com',
                    "elephant": 'https://elephant.kakamobi.cn',
                    "swallow": 'https://swallow-admin.kakamobi.cn',
                    "meeting": 'https://meeting.mucang.cn',

                    "jiakao-search": 'https://jiakao-search-admin.kakamobi.cn',
                    "panda": "https://panda-admin.kakamobi.cn",
                    "jk-knowledge": 'https://jk-knowledge.kakamobi.cn',
                    "mc-pan": "https://mc-pan.mucang.cn",

                    "jiakao-td": 'https://jiakao-3d-admin.kakamobi.cn',
                    "athena": 'https://athena-admin.mucang.cn',

                    "jk-tiku": 'https://jk-tiku-res.kakamobi.cn',
                    sso: 'https://sso.kakamobi.com',
                    "jiakao-3d": 'https://jiakao-3d.kakamobi.cn'

                },

                // 前端文件根目录，最后没有 "/"
                root: {
                    "jiakao-misc": window.location.href.replace(/\/[^/]*$/ig, ''),
                    "squirrel": 'https://admin.mucang.cn/squirrel.kakamobi.cn',
                    "danmu": 'https://admin.mucang.cn/danmu.kakamobi.cn',
                    "jiakao-search": 'https://admin.mucang.cn/jiakao-search.kakamobi.cn/#header=systemadmin!main%2Faside%3Fproject%3Djiakao-search&aside=systemadmin!app%2Fsystem-config%2Findex%2Flist',
                    "saturn": "https://admin.mucang.cn/admin-cheyouquan.kakamobi.com",
                    "swallow": 'https://admin.mucang.cn/swallow.kakamobi.cn',
                    "shortVideo": "https://admin.mucang.cn/short-video.kakamobi.cn",

                    "jiakao-td": 'https://admin.mucang.cn/jiakao-3d.kakamobi.cn',
                    "athena": 'https://admin.mucang.cn/athena.mucang.cn',
                    "panda": 'https://admin.mucang.cn/panda.kakamobi.cn',
                    "jk-knowledge": 'https://admin.mucang.cn/jk-knowledge.kakamobi.cn',
                },

                // 框架远程文件配置
                framework: {
                    // 远程文件路径，PS：不带版本目录
                    root: 'https://static.kakamobi.cn/simple-framework',
                    // 版本目录，{version: 1} 代表 "/v1/"
                    version: '1.1'
                },

                // 入口文件需要的JS和CSS
                file: {
                    js: [],
                    css: []
                }
            }

            var script = document.createElement('script');
            script.src = config.framework.root + '/v' + config.framework.version + '/resources/js/require.min.js';
            document.body.appendChild(script);

            script.onload = function () {
                require([config.framework.root + '/v' + config.framework.version + '/app/main.js'], function (
                    app) {
                    app.init(config, success);
                });
            }


            window.hashParams = (() => {
                const ret = {};
                const querystring = window.location.hash.split('#')[1];
                if (!querystring) {
                    return ret;
                }

                querystring.split('&').forEach(pair => {
                    const [key, value] = pair.split('=');
                    ret[decodeURIComponent(key)] = decodeURIComponent(value);
                });
                return ret;
            })();


        }();
    </script>

</body>

</html>