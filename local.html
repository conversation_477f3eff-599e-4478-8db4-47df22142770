<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>加载中...</title>
</head>

<body>

    <script>
        // 所有配置的路径最后都不以 "/" 结尾，程序会自动增加
        ! function() {

            // 加载成功之后执行的方法
            var success = function() {

            }

            var config = {

                // 产品标题
                title: '驾考混合项目',
                name: 'jiakao-misc',

                // true 则加载本地文件，默认从当前目录开始， false则从framework.root加载
                debug: false,

                // ajax.js判断host，如果找到匹配则替换，例：
                // host: {
                //     local: 'http://w3c.org'
                // }
                // Ajax.require('local://m.json') > Ajax.require('http://w3c.org/m.json');
                // PS：此配置从v1.1版本开始生效
                host: {
                    // API地址
                    "jiakao-misc": 'http://*************:8080',
                    local: 'http://*************:8080',
                    'saturn': 'https://admin-cheyouquan.kakamobi.com',
                    // "jiakao-misc": 'https://jiakao-misc.ttt.mucang.cn',
                    // local: 'https://jiakao-misc.ttt.mucang.cn',

                    // "jiakao-misc": 'https://jiakao-misc.mucang.cn',
                    // local: 'https://jiakao-misc.mucang.cn',

                },

                // 前端文件根目录，最后没有 "/"
                root: {
                    "jiakao-misc": window.location.href.replace(/\/[^/]*$/ig, '')
                },

                // 框架远程文件配置
                framework: {

                    // 远程文件路径，PS：不带版本目录
                    root: 'https://static.kakamobi.cn/simple-framework',

                    // 版本目录，{version: 1} 代表 "/v1/"
                    version: '1.1'

                },

                // 入口文件需要的JS和CSS
                file: {
                    js: [],
                    css: []
                }

            }

            var script = document.createElement('script');
            script.src = config.framework.root + '/v' + config.framework.version + '/resources/js/require.min.js';
            document.body.appendChild(script);

            script.onload = function() {
                require([config.framework.root + '/v' + config.framework.version + '/app/main.js'], function(app) {
                    app.init(config, success);
                });
            }

        }();
    </script>

</body>

</html>