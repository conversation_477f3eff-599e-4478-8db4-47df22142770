{"name": "admin-bug-mucang-cn", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "webpack --watch", "build": "webpack && node bundler.js", "build:test": "webpack && node bundler.js", "build:dev": "webpack && node bundler.js"}, "repository": {"type": "git", "url": "http://git.mucang.cn/internal-webfront/admin-bug-mucang-cn.git"}, "author": "", "license": "ISC", "dependencies": {"@simplex/admin-vue": "1.3.17", "ant-design-vue": "^3.2.17", "js-audio-recorder": "^1.0.7", "less": "^4.1.3", "pinia": "^2.0.32", "vue": "^3.2.31"}, "devDependencies": {"@babel/core": "^7.17.8", "@babel/preset-env": "^7.17.8", "@babel/preset-typescript": "^7.17.8", "@types/jquery": "^3.5.14", "@types/node": "^17.0.43", "@types/webpack": "^5.28.0", "babel-loader": "^8.2.4", "clean-webpack-plugin": "^4.0.0", "css-loader": "^6.7.1", "fast-glob": "^3.2.11", "fs-extra": "^10.1.0", "less-loader": "^11.0.0", "vue-loader": "^17.0.0", "vue-style-loader": "^4.1.3", "webpack": "^5.70.0", "webpack-cli": "^4.9.2", "webpack-sources": "^3.2.3"}}