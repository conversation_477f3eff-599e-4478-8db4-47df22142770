﻿<style>

    .bug-manage-plugin-select-input {
        position: relative;
        /* z-index: 100000; */
    }

    .bug-manage-plugin-select-input .data-list {
        position: absolute;
        top:0px;
        width: 100%;
        max-height: 200px;
        overflow: auto;
        box-sizing: border-box;
        border: solid 1px #ccc;
        background: #fff;
        z-index: 10;
        display: none;
        border-radius:4px;
    }

    .bug-manage-plugin-select-input .data-list > ul > li {
        padding: 5px;
        color: #000;
    }

    .bug-manage-plugin-select-input .data-list li.highlight{
        background: #dedede;
    }

    .bug-manage-plugin-select-input .data-list > ul > li:hover {
        background: #dedede;
    }
    .bug-manage-plugin-select-input .divider-div{
        font-weight:bold;
        padding: 2px 3px;
        border-bottom:1px solid #cccccc;
        border-top:1px solid #cccccc;
    }

    .bug-manage-plugin-select-input .selected-div{
        width: 100%;
        padding: 0;
        border: 1px solid #ccc;
        border-radius: 20px;
        cursor: text;
    }
    .bug-manage-plugin-select-input .selected-div input{
        width: 100%;
        margin: 0;
        border: none;
        height: 32px;
        outline: none;
        border-radius: 20px;
        background: none;
    }
    .bug-manage-plugin-select-input .select-list{
        margin: 0;
        padding: 0;
        list-style: none;
        display: inline;
        -webkit-box-sizing:border-box;
    }
    .bug-manage-plugin-select-input .select-list li{
 
        padding: 2px 6px 2px 5px;
        margin: 3px 0 0 5px;
        background-color:#99ccff;
        display: inline-block;
        border-radius: 2px;
        border: 1px solid white;
        -webkit-box-sizing:border-box;
    }
    .bug-manage-plugin-select-input .select-list li.bordered{
        -webkit-box-sizing:border-box;
        border: 1px solid red;
    }

    .bug-manage-plugin-select-input .select-list li span{
        -webkit-box-sizing:border-box;
        margin-left: 10px;
        cursor: pointer;
    }
</style>
<!--自动提示模板-->
<script id="jiakao-misc-plugin-auto-prompt-template-main-index" type="text/html">


    <div class="bug-manage-plugin-select-input">
        <div class="selected-div clearfix" data-item="select-div">
            <ul class="select-list" data-item="select-list">

            </ul>
            <input type="text" data-item="input-prompt" placeholder="<?=config.placeholder?>">
            <input data-item="hiddenInput" type="hidden" name="<?=config.dataIndex?>" value="<?=config.value?>"/>
            <?
            var data = config.store;
            var groupIndex = config.groupIndex ? config.groupIndex : '';
            ?>
            <div style="position:relative;z-index: 1000;">
                <div data-item="data-list" class="data-list">

                    <?if(groupIndex && groupIndex.length > 0){?>
                        <?var groupNames = config.groupNames?>
                        <?for(var i=0; i<groupIndex.length;i++){?>
                            <div class="divider-div"><?=groupNames[i]?></div>
                            <?var groupData = data[groupIndex[i]]?>
                            <ul>
                                <? for(var j=0;j<groupData.length;j++){ ?>
                                    <li data-key="<?=groupData[j].key?>" data-value="<?=groupData[j].value?>" data-search="<?=groupData[j].value?><?=groupData[j].search?>"><?=groupData[j].value?></li>
                                <?}?>
                            </ul>
                        <?}?>
                    <?}else{?>
                        <ul>
                            <? for(var j=0;j<data.length;j++){ ?>
                                <li data-key="<?=data[j].key?>" data-value="<?=data[j].value?>" data-search="<?=data[j].value?><?=data[j].search?>"><?=data[j].value?></li>
                            <?}?>
                        </ul>
                    <?}?>
                </div>
            </div>
        </div>



    </div>
</script>
