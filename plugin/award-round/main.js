/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Store, Form, Plugin) {

    var awardRound = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    awardRound.prototype = {

        render: function () {
            var me = this;
            try{
                var value = JSON.parse(me.config.value);
            }catch ( e ) {
                value = []
            }
            Store(['jiakao-misc!xueche-present/data/list']).load([{
                aliases: 'presents'
            }]).done(function (store,data) {
                var presents = data.presents.data;

                Template(me.plugin.path + '/template/main/index', me.target, function (dom, data, item) {

                    var wrapDom = dom.item('all-wrap');

                    dom.item('add-tag').on('click', function () {
                        var div = $('<div class="clearfix tag-map" data-item="tag-map"></div>');
                        wrapDom.append(div);
                        Template(me.plugin.path + '/template/main/item',div, function (dom, data, item) {
                            dom.item('del-tag').on('click', function () {
                                $(this).parents('.tag-map').remove();
                            })

                        }, {data: [], presents: presents}).render();
                    });

                    dom.item('del-tag').on('click', function () {
                        $(this).parents('.tag-map').remove();
                    })

                }, {data: value, presents: presents, config: me.config}).render();
            })
        }
    }

    awardRound.prototype.constructor = awardRound;

    return function (plugin, success) {
        return new awardRound(plugin, success);
    }

});
