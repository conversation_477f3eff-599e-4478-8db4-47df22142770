<!--匹配标签-->
<style>
    .tag-map {
        padding: 5px 0;
        display: flex;
    }

    .tag-map input {
        height: 32px;
        border-radius: 15px;
        margin-right: 5px;
    }

    .tag-map a {
        font-weight: bold;
        font-size: 18px;
        padding: 0 8px;
        height: 30px;
        width: 30px;
    }

    .tag-map .editBtn {
        width: inherit;
        font-size: 15px;
        line-height: 29px;
        margin-left: 10px;
    }

    .tag-map .tag-key-wrap {
        float: left;
        width: 43%;
        margin-right: 10px;
        height: 32px;
    }
</style>
<script id="jiakao-misc-plugin-award-round-template-main-index" type="text/html">
    <div class="all-wrap" data-item="all-wrap">
        <input type="hidden" name="<?=config.dataIndex?>" value="<?=config.value?>">
        <div class="clearfix tag-map" data-item="tag-map">
            <select class="form-control">
                <option value="">选择奖品</option>
                <?for(var i=0; i<presents.length; i++){?>
                    <option value="<?=presents[i].id?>" <?=(data[0]&&data[0].presentId == presents[i].id) ? 'selected': ''?> ><?=presents[i].name?></option>
                <?}?>
            </select>
            <input class="form-control" data-item="tag-value" type="text" placeholder="value" value="<?=data[0]&&data[0].count?>">
            <a class="btn btn-sm btn-info" data-item="add-tag">+</a>
        </div>
        <?for(var i=1; i<data.length; i++){?>
        <div class="clearfix tag-map" data-item="tag-map">
            <?include('jiakao-misc-plugin-award-round-template-main-item', {data: data[i], presents: presents})?>
        </div>
        <?}?>
    </div>
</script>

<script id="jiakao-misc-plugin-award-round-template-main-item" type="text/html">
    <select class="form-control">
        <option value="">选择奖品</option>
        <?for(var i=0; i<presents.length; i++){?>
        <option value="<?=presents[i].id?>" <?=(data&&data.presentId == presents[i].id) ? 'selected': ''?> ><?=presents[i].name?></option>
        <?}?>
    </select>
    <input class="form-control" data-item="tag-value" type="text" value="<?=data&&data.count?>" placeholder="value">
    <a class="btn btn-sm btn-danger" data-item="del-tag">-</a>
</script>
