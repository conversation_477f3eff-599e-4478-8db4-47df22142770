/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Store, Form, Plugin) {

    var banner = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    banner.prototype = {

        render: function () {
            var me = this;

            Template(me.plugin.path + '/template/main/index', me.target, function (dom, data, item) {
                var $op = item('operation');
                Plugin('jiakao-misc!upload', {
                            //插件定义中，通过 this.plugin.config 访问此对象

                            dataIndex: 'image',
                            uploadIndex: 'image',
                            bucket: "jiakao-web",
                            isSingle: true,
                            placeholder: '请选择上传文件',
                            url: 'simple-upload3://upload/file.htm',

                            target: item('upload')
                        },
                        //这是插件加载并绘制成功后的回调函数
                        function (plugin) {}
                    )
                    //加载定义插件的js文件并执行该文件返回的函数
                    .render({})
                    .done(function (plugin) {
                        //必须确保执行了 插件定义函数 的第二个参数
                    })
                    .fail(function (errmsg) {
                        //加载定义plugin的js异常后执行此函数
                    });

                item('add').on('click', function () {

                    $op.before('<div class="line"><input  data-item="url1" type="text" class="url1" placeholder="url"><div class="upload"></div></div>');
                    console.log($(this).prev().find('.upload'));
                    Plugin('jiakao-misc!upload', {
                                //插件定义中，通过 this.plugin.config 访问此对象


                                uploadIndex: 'image',
                                bucket: "jiakao-web",
                                isSingle: true,
                                placeholder: '请选择上传文件',
                                url: 'simple-upload3://upload/file.htm',

                                target: $op.prev().find('.upload')
                            },
                            //这是插件加载并绘制成功后的回调函数
                            function (plugin) {}
                        )
                        //加载定义插件的js文件并执行该文件返回的函数
                        .render({})
                        .done(function (plugin) {
                            //必须确保执行了 插件定义函数 的第二个参数
                        })
                        .fail(function (errmsg) {
                            //加载定义plugin的js异常后执行此函数
                        });

                });


                item('del').on('click', function () {
                    if ($op.prev().prev().attr('class') != "top") {
                        $op.prev().remove();

                    }
                })
                if (me.config.value) {
                    item('first-line').remove();
                    var bannerData = JSON.parse(me.config.value);
                    for (let i in bannerData) {
                        $op.before(`<div class="line"><input value="${bannerData[i].url}"  data-item="url1" type="text" class="url1" placeholder="url"><div class="upload"></div></div>`);

                        Plugin('jiakao-misc!upload', {
                                    //插件定义中，通过 this.plugin.config 访问此对象


                                    uploadIndex: 'image',
                                    bucket: "jiakao-web",
                                    isSingle: true,
                                    placeholder: '请选择上传文件',
                                    url: 'simple-upload3://upload/file.htm',
                                    value: bannerData[i].image,
                                    target: $op.prev().find('.upload')
                                },
                                //这是插件加载并绘制成功后的回调函数
                                function (plugin) {}
                            )
                            //加载定义插件的js文件并执行该文件返回的函数
                            .render({})

                    }
                }


                me.success(); //这个函数一定要执行，否则无法调用回调函数
            }).render();



        }
    }

    banner.prototype.constructor = banner;

    return function (plugin, success) {
        return new banner(plugin, success);
    }

});