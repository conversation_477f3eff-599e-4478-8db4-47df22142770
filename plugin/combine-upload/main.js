﻿/*
 * select-UploadFile v0.0.1
 *
 * name: GG
 * date: 2013/12/25
 */

"use strict";

define(['simple!core/template', 'simple!core/ajax', 'simple!core/widgets', 'simple!core/store', 'simple!core/plugin', 'jiakao-misc!resources/minio-js'], function (Template, Ajax, Widgets, Store, Plugin, MinioJS) {

    console.log(MinioJS)
    var UploadFile = function (plugin, success) {

        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.render();
    };

    UploadFile.prototype = {

        render: function () {
            var me = this;

            me.getUploadParams(function (minioConfig) {
                console.log('minioConfig', minioConfig)
                Template(me.plugin.path + '/template/main/content', me.config.target, function (dom, data, item, obj) {
                    var config = me.config;
                    var content1 = dom.item('content1');
                    var content2 = dom.item('content2');
                    var uploading = false;
                    if (minioConfig.accessKey) {
                        content2.removeClass('hide');
                    } else {
                        content1.removeClass('hide');
                        config.target = content1;
                        Plugin('simple!select-file-part', config).render();
                    }
                    $
                    dom.on("change", '#minio-file', function (e) {
                        console.log('change')
                        var that = $(this);
                        $('.process-txt').text('上传中...');
                        uploading = true;
                        //把文件以ArrayBuffer的形式读取后给Minio上传
                        var f = this.files[0];
                        var regex = /\.[^.]+$/;
                        var extension = (f.name).match(regex);
                        var nowDate = new Date();
                        var year = nowDate.getFullYear();
                        var month = nowDate.getMonth() + 1;
                        var date = nowDate.getDate();
                        var newFileName = me.randomString() + extension[0];
                        var filePath = year + '/' + month + '/' + date + '/' + newFileName;
                        // let reader = new FileReader();
                        // reader.readAsArrayBuffer(f);
                        // reader.onload = function (e) {
                        //     let res = e.target.result; //ArrayBuffer
                            MinioJS.initMinio(minioConfig);
                            //再上传
                            MinioJS.putObject("video-editor", f, filePath, f.size, function (err, data) {
                                that.val('');
                                if (err) {
                                    $('.process-txt').text('上传失败');
                                    uploading = false;
                                }
                                else {
                                    var remoteFilePath = 'https://minio-video-editor-idc-api.mucang.cn/video-editor/' + filePath;
                                    Ajax.request(config.updateUrl, {
                                        data: {
                                            miniMapVideoUrl: remoteFilePath
                                        },
                                        method: 'get',
                                        // contentType: false,
                                        // processData: false,
                                        success: function (data) {
                                            console.log(data)
                                            uploading = false;
                                            $('.process-txt').text('上传成功');
                                        },
                                        error: function (err) {
                                            uploading = false;
                                            Widgets.dialog.alert(err.message);
                                        }
                                    })
                                }
                            });
                        // };
                    });

                    dom.item("complete-btn").on('click', function () {
                        console.log('uploading', uploading);
                        if (uploading) {
                            Widgets.dialog.alert('文件上传中... 请稍后');
                        } else {
                            me.success();
                            $(this).parents().find('.modal-content').find('.close').trigger('click')
                        }
                    });

                }, {
                    config: me.config
                }).render();
            });


        },

        randomString: function (len) {
            len = len || 32;
            var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';    /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
            var maxPos = $chars.length;
            var pwd = '';
            for (var i = 0; i < len; i++) {
                pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
            }
            return pwd;
        },

        getUploadParams: function (callback) {
            var minioConfig = {
                useSSL: true
            };
            window.fetch('https://network.mucang.cn/api/h5/file-upload/get-upload-params.htm')
                .then((response) => {
                    console.log(response)
                    if (response.ok) {
                        response.json().then((data) => {
                            minioConfig = data.data;
                            callback(minioConfig);
                        })
                    }
                }).catch(() => {
                    callback(minioConfig);
                });

        },

        close: function () {
            this.dialog.close();
        }
    };

    UploadFile.prototype.constructor = UploadFile;

    return function (plugin, success) {
        return new UploadFile(plugin, success);
    }

});
