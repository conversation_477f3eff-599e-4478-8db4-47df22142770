﻿/*
 * drag-sort v0.0.1
 *
 * name: GG
 * date: 2014/02/28
 */

"use strict";

define(['simple!core/template', 'simple!core/widgets', 'simple!core/store'], function (Template, Widgets, Store) {

    var Sort = function (plugin, success) {
        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    Sort.prototype = {

        render: function () {
            var me = this;
            
            Template(me.plugin.path + '/template/main/list', this.target, function (dom, data, item, obj) {
                var value = item('drag-sort-value');
                var listData = JSON.parse(me.config.value)
                value.val(me.config.value);

                function exchangeData(originIndex, targetIndex) {
                    var originItem = listData[originIndex]
                    if(originIndex > targetIndex) {
                        originIndex = originIndex+1
                    }else {
                        targetIndex = targetIndex+1

                    }
                    listData.splice(targetIndex, 0, originItem)
                    listData.splice(originIndex, 1)
                    value.val(JSON.stringify(listData));
                }
                var index;
                dom.find('ul').sortable({
                    start: function (event, list) {
                        index = $(list.item[0]).index()
                    },
                    update: function (event, list) {
                        var target = $(list.item[0]).index()
                        exchangeData(index, target)
                    },
                }).disableSelection();

                dom.delegate('[data-item="goto-btn"]', 'click', function () {
                    $(this).addClass('hidden').next().removeClass('hidden').next().removeClass('hidden');
                });

                dom.delegate('[data-item="go-btn"]', 'click', function () {
                    var $me = $(this).parents('li');
                    var index = $($me).index()
                    var target = parseInt($me.find('input').val()) - 1
                    $me.item('goto-btn').removeClass('hidden');
                    $me.find('.edit').addClass('hidden');

                    var $target = dom.find('li').eq(target);
                    if ($target.length > 0) {
                        if ($target.index() < $me.index()) {
                            $me.insertBefore($target)
                        } else {
                            $me.insertAfter($target);
                        }
                    }
                    exchangeData(index, target)
                });

            }, {config: this.config, list: JSON.parse(me.config.value)}).render();
        }
    }

    Sort.prototype.constructor = Sort;

    return function (plugin, success) {
        return new Sort(plugin, success);
    }

});