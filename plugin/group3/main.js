﻿/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form'], function (Template, Store, Form) {

    var _group = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    _group.prototype = {

        // 渲染
        render: function () {
            var me = this;
            var initValue = me.config.value;
            var valueArr = [];
            if (initValue) {
                valueArr = JSON.parse(initValue);
            } else {
                valueArr = [{ lessonId: '', recommendation: '',sort:'' }]
            }
            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    dom.item('add-goods-session-group-highlights').on('click', function () {
                        valueArr.push({ lessonId: '', recommendation: '',sort:'' })
                        renderTemplate()
                    });

                    dom.item('remove-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        valueArr.splice(index, 1)
                        renderTemplate()
                    });
                    dom.item('recommendation').on('input', function () {
                        var index = $(this).attr('data-index')
                        console.log($(this).val(),'$(this).val()$(this).val()');
                        valueArr[index].recommendation = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('lessonId').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].lessonId = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('sort').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].sort = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        dataIndex: me.config.dataIndex
                    }
                }).render()
            }

        }
    }

    _group.prototype.constructor = _group;

    return function (plugin, success) {
        console.log(plugin, "pluginplugin");
        return new _group(plugin, success);
    }

});