﻿<script id="jiakao-misc-plugin-group3-template-main-index" type="text/html">
    <?
      var valueArr = config.valueArr;
    

    ?>
<style>
    input {
        border-radius: 20px !important;
    }
</style>
<input type="hidden" data-item="hidden-input" name="<?=config.dataIndex?>" value="<?=config.value?>">
<?for(var index=0; index<valueArr.length;index++){?>
<div class="form-group" style="margin-bottom: 10px;">
    <div class="col-sm-10">

        <div class="input-group clearfix"  style="display:flex;">
            <input type="text" data-item="lessonId" data-index="<?=index?>" 
                class="form-control group-name<?=index?> group-input" id="lessonId" placeholder="课程ID"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].lessonId?>">

            <input type="text" data-item="sort"  data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="排序"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].sort?>" id="sort">
        </div>
        
        <textarea type="text" data-item="recommendation"  style="margin-top: 10px; " data-index="<?=index?>"
            class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="推荐语"
            value="<?=valueArr&&valueArr[index]&&valueArr[index].recommendation?>" id="recommendation" ><?=valueArr&&valueArr[index]&&valueArr[index].recommendation?></textarea>
      
    </div>
    <?if(index==0){?>
    <div class="col-sm-2"><button data-index="<?=index?>" class="btn btn-primary"
            data-item="add-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-plus"></span></button></div>
    <?}else{?>
    <div class="col-sm-2"><button data-index="<?=index?>" class="btn btn-danger"
            data-item="remove-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-remove"></span></button></div>
    <?}?>

</div>
<?}?>



</script>