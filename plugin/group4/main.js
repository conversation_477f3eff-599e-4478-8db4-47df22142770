/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Store, Form, Plugin) {

    var _group4 = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    _group4.prototype = {

        // 渲染
        render: function () {
            var me = this;
            var initValue = me.config.value;
            var valueArr = [];
            if (initValue) {
                valueArr = JSON.parse(initValue);
                if (!Array.isArray(valueArr)) {
                    valueArr = [valueArr]
                }
            } else {
                let valueObejct = {
                    actionUrl: '', imageUrl: ''
                }
                if (me.config.isShowTitle) {
                    valueObejct.title = ''
                } 
                if (me.config.isShowTopicId){
                    valueObejct.topicId = ''

                }
                if (me.config.isShowFragmentName) {
                    valueObejct.fragmentName = ''
                }
                if (me.config.hideImage) {
                    delete valueObejct['imageUrl']
                }
                valueArr = [valueObejct]

            }
            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    dom.item('add-goods-session-group-highlights').on('click', function () {
                        let valueObejct = {
                            actionUrl: '', imageUrl: ''
                        }
                        if (me.config.isShowTitle) {
                            valueObejct.title = ''
                        }
                        if (me.config.isShowTopicId) {
                            valueObejct.topicId = ''

                        }
                        if (me.config.isShowFragmentName) {
                            valueObejct.fragmentName = ''
                        }
                        if (me.config.hideImage) {
                            delete valueObejct['imageUrl']
                        }
                        valueArr.push(valueObejct)
                        renderTemplate()
                    });

                    dom.item('remove-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        valueArr.splice(index, 1)
                        renderTemplate()
                    });
                    dom.item('up-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        var ele = valueArr[index]
                        valueArr.splice(index, 1)
                        valueArr.splice(index - 1, 0, ele);
                        renderTemplate()
                    });
                    dom.item('down-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        var ele = valueArr[index]
                        valueArr.splice(index, 1)
                        valueArr.splice(index + 1, 0, ele);
                        renderTemplate()
                    });
                    dom.item('actionUrl').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].actionUrl = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('topicId').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].topicId = $(this).val().trim()
                        if ($(this).val().trim()){
                            valueArr[index].actionUrl = 'http://saturn.nav.mucang.cn/topic/detail?topicId=' + valueArr[index].topicId;
                        }else{
                            valueArr[index].actionUrl = ''
                        }
                      
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('title').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].title = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('fragmentName').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].fragmentName = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    if (!me.config.hideImage) {
                        for (var i = 0; i < valueArr.length; i++) {
                            Plugin(
                                'jiakao-misc!upload',
                                {
                                    dataIndex: 'new-img',
                                    uploadIndex: 'new-img',
                                    bucket: 'jiakao-web',
                                    target: dom.item('new-img-plugin' + i),
                                    isSingle: true,
                                    value: valueArr[i].imageUrl,
                                    placeholder: '请选择上传文件',
                                    url: 'simple-upload3://upload/file.htm',
                                    deleteImgInfo: function (plugin, value) {
                                        var index = $(plugin.config.target[0]).attr('data-index')
                                        valueArr[index].imageUrl = value
                                        hiddenInput.val(JSON.stringify(valueArr));
                                    }
                                },
                                function (plugin, tag, value) {
                                    var index = $(plugin.config.target[0]).attr('data-index')
                                    valueArr[index].imageUrl = value
                                    hiddenInput.val(JSON.stringify(valueArr));
                                }
                            ).render()
                        }
                    }

                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        isShowTitle: me.config.isShowTitle,
                        isShowTopicId: me.config.isShowTopicId,
                        isShowFragmentName: me.config.isShowFragmentName,
                        dataIndex: me.config.dataIndex,
                        isSingleConfigItem: me.config.isSingleConfigItem,
                        hideImage: me.config.hideImage
                    }
                }).render()
            }

        }
    }

    _group4.prototype.constructor = _group4;

    return function (plugin, success) {
        console.log(plugin, "pluginplugin");
        return new _group4(plugin, success);
    }

});