<script id="jiakao-misc-plugin-group4-template-main-index" type="text/html">
    <?
      var valueArr = config.valueArr;
      var isShowTitle = config.isShowTitle;
      var isShowTopicId = config.isShowTopicId;
      var isShowFragmentName = config.isShowFragmentName;
    

    ?>
<style>
    input {
        border-radius: 20px !important;
    }
</style>
<input type="hidden" data-item="hidden-input" name="<?=config.dataIndex?>" value="<?=config.value?>">
<?for(var index=0; index<valueArr.length;index++){?>
<div class="form-group" style="margin-bottom: 10px;">
    <div class="col-sm-10">
       
            <?if(isShowTopicId){?>
                <input type="hidden" data-item="actionUrl" data-index="<?=index?>" 
                class="form-control group-name<?=index?> group-input" id="actionUrl" placeholder="跳转链接"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].actionUrl?>">
            <input type="text" data-item="topicId" data-index="<?=index?>" 
                class="form-control group-name<?=index?> group-input" id="topicId" placeholder="帖子ID"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].topicId?>">
            <?}else{?>
                <input type="text" data-item="actionUrl" data-index="<?=index?>" 
                class="form-control group-name<?=index?> group-input" id="actionUrl" placeholder="跳转链接"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].actionUrl?>">
            <?}?>

            <?if(isShowTitle){?>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <input type="text" data-item="title" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="标题"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].title?>" id="title">
        </div>
        <?}?>
            <?if(isShowFragmentName){?>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <input type="text" data-item="fragmentName" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="打点名称" title="打点名称"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].fragmentName?>" id="fragmentName">
        </div>
        <?}?>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
             <div data-index="<?=index?>" style="width:100%" data-item="new-img-plugin<?=index?>"></div>
        </div>
    </div>
    <?if(!config.isSingleConfigItem){?>
        <div class="col-sm-2">
            <?if(index==0){?>
                <button
                    data-index="<?=index?>" class="btn btn-primary"
                    data-item="add-goods-session-group-highlights" type="button"
                >
                    <span class="glyphicon glyphicon-plus"></span>
                </button>
            <?}else{?>
                <button
                    data-index="<?=index?>" class="btn btn-danger"
                    data-item="remove-goods-session-group-highlights" type="button"
                >
                    <span class="glyphicon glyphicon-remove"></span>
                </button>
            <?}?>
            <button
                data-index="<?=index?>" class="btn" <? if (index === 0) { ?>disabled<? } ?>
                data-item="up-goods-session-group-highlights" type="button"
            >
                <span class="glyphicon glyphicon-arrow-up"></span>
            </button>
            <button
                data-index="<?=index?>" class="btn" <? if (index + 1 === valueArr.length) { ?>disabled<? } ?>
                data-item="down-goods-session-group-highlights" type="button"
            >
                <span class="glyphicon glyphicon-arrow-down"></span>
            </button>
        </div>
    <?}?>
</div>
<?}?>



</script>