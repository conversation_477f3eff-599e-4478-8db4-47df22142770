/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form','simple!core/plugin'], function (Template, Store, Form,Plugin) {

    var _group5 = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    _group5.prototype = {

        // 渲染
        render: function () {
            var me = this;
            var initValue = me.config.value;
            var valueArr = [];
            if (initValue) {
                valueArr = initValue
            } else {
                valueArr = [{ knowledgeId: '', knowledgeIconUrl: '',knowledgeName:'' }]
               
            }
            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    dom.item('add-goods-session-group-highlights').on('click', function () {
                          valueArr.push({ knowledgeId: '', knowledgeIconUrl: '',knowledgeName:'' })
                        renderTemplate()
                    });

                    dom.item('remove-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        valueArr.splice(index, 1)
                        renderTemplate()
                    });
                    dom.item('knowledgeId').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].knowledgeId = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('knowledgeName').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].knowledgeName = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    for(var i=0;i<valueArr.length;i++){
                        Plugin(
						'jiakao-misc!upload',
						{
							dataIndex: 'new-img',
							uploadIndex: 'new-img',
							bucket: 'jiakao-web',
                            target:dom.item('new-img-plugin'+i),
							isSingle: true,
                            value:valueArr[i].knowledgeIconUrl,
							placeholder: '请选择上传文件',
							url: 'simple-upload3://upload/file.htm',
                            deleteImgInfo:function(plugin,value){
                             var index = $(plugin.config.target[0]).attr('data-index')
                             valueArr[index].knowledgeIconUrl = value
                             hiddenInput.val(JSON.stringify(valueArr));
                            }
						},
						function (plugin,tag,value) {
                            var index = $(plugin.config.target[0]).attr('data-index')
                             valueArr[index].knowledgeIconUrl = value
                        hiddenInput.val(JSON.stringify(valueArr));
                        }
                    ).render()
                    }
                     
                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        dataIndex: me.config.dataIndex
                    }
                }).render()
            }

        }
    }

    _group5.prototype.constructor = _group5;

    return function (plugin, success) {
        console.log(plugin, "pluginplugin");
        return new _group5(plugin, success);
    }

});