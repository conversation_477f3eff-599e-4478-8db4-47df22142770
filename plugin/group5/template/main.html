<script id="jiakao-misc-plugin-group5-template-main-index" type="text/html">
    <?
      var valueArr = config.valueArr;
    
    ?>
<style>
    input {
        border-radius: 20px !important;
    }
</style>
<input type="hidden" data-item="hidden-input" name="<?=config.dataIndex?>" value="<?=config.value?>">
<?for(var index=0; index<valueArr.length;index++){?>
<div class="form-group" style="margin-bottom: 10px;">
    <div class="col-sm-10">
        <input type="text" data-item="knowledgeId" data-index="<?=index?>" 
            class="form-control group-name<?=index?> group-input" id="knowledgeId" placeholder="知识点ID"
            value="<?=valueArr&&valueArr[index]&&valueArr[index].knowledgeId?>">
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <input type="text" data-item="knowledgeName" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="知识点名称"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].knowledgeName?>" id="knowledgeName">
        </div>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
             <div data-index="<?=index?>" style="width:100%" data-item="new-img-plugin<?=index?>"></div>
        </div>
    </div>
    <?if(index==0){?>
    <div class="col-sm-2"><button data-index="<?=index?>" class="btn btn-primary"
            data-item="add-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-plus"></span></button></div>
    <?}else{?>
    <div class="col-sm-2"><button data-index="<?=index?>" class="btn btn-danger"
            data-item="remove-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-remove"></span></button></div>
    <?}?>

</div>
<?}?>



</script>