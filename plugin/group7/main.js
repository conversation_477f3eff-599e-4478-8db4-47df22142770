/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form','simple!core/plugin'], function (Template, Store, Form,Plugin) {

    var _group7 = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    _group7.prototype = {

        // 渲染
        render: function () {
            var me = this;
            var initValue = me.config.value;
            var valueArr = [];
            if (initValue) {
                valueArr = JSON.parse(initValue);
                if(!Array.isArray(valueArr)){
                  valueArr = [valueArr]
                }
            } else {
                valueArr = [{ name: '', introduce: '', imageUrl: '', channelCode: '', videoId: '', anchorItemList: []}]
            }
            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    dom.item('add-config-list-highlights').on('click', function () {
                        valueArr.push({ name: '', second: ''})
                        renderTemplate()
                    });

                    dom.item('remove-config-list-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        valueArr.splice(index, 1)
                        renderTemplate()
                    });
                    dom.item('name').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].name = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('introduce').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].introduce = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('imageUrl').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].imageUrl = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('channelCode').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].channelCode = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('videoId').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].videoId = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    for(var i=0;i<valueArr.length;i++){
                        Plugin('jiakao-misc!upload', {
                            dataIndex: 'imageUrl',
                            uploadIndex: 'imageUrl',
                            bucket: 'jiakao-web',
                            target: dom.item('new-img-plugin'+i),
                            isSingle: true,
                            value: valueArr[i].imageUrl,
                            placeholder: '请选择上传文件',
                            url: 'simple-upload3://upload/file.htm',
                            deleteImgInfo: function(plugin,value){
                                var index = $(plugin.config.target[0]).attr('data-index')
                                valueArr[index].imageUrl = value
                                hiddenInput.val(JSON.stringify(valueArr));
                            }
                        }, function (plugin,tag,value) {
                            var index = $(plugin.config.target[0]).attr('data-index')
                            valueArr[index].imageUrl = value
                            hiddenInput.val(JSON.stringify(valueArr));
                        }).render();
                        Plugin('jiakao-misc!group6', {
                            dataIndex: 'anchorPoint',
                            target: dom.item('anchor-point'+i),
                            value: JSON.stringify(valueArr[i]&&valueArr[i].anchorItemList)
                        }, function (plugin, value) {
                            var index = $(plugin.config.target[0]).attr('data-index')
                            var pointArr = $(plugin.config.target[0]).item('hidden-input')
                            valueArr[index].anchorItemList = JSON.parse(pointArr.val())
                            hiddenInput.val(JSON.stringify(valueArr));
                        }).render();
                    }
                     
                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        dataIndex: me.config.dataIndex
                    }
                }).render()
            }

        }
    }

    _group7.prototype.constructor = _group7;

    return function (plugin, success) {
        console.log(plugin, "pluginplugin");
        return new _group7(plugin, success);
    }

});