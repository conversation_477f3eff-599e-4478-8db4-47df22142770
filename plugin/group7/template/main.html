<script id="jiakao-misc-plugin-group7-template-main-index" type="text/html">
    <?
      var valueArr = config.valueArr;

    ?>
<style>
    input {
        border-radius: 20px !important;
    }
    .goods-config:nth-child(even) {
        background-color: #e5e5e5;
    }
</style>
<input type="hidden" data-item="hidden-input" name="<?=config.dataIndex?>" value="<?=config.value?>">
<?for(var index=0; index<valueArr.length;index++){?>
<div class="form-group goods-config" style="margin-bottom: 10px; width: 520px;">
    <div class="col-sm-10">
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <label for="name" style="padding-left: 0;" class="col-sm-4 control-label">商品名称：</label>
            <input type="text" data-item="name" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="商品名称"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].name?>" id="name">
        </div>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <label for="introduce" style="padding-left: 0;" class="col-sm-4 control-label">商品简介：</label>
            <input type="text" data-item="introduce" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="商品简介"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].introduce?>" id="introduce">
        </div>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <label for="imageUrl" style="padding-left: 0;" class="col-sm-4 control-label">商品图：</label>
            <div data-index="<?=index?>" style="width:100%" data-item="new-img-plugin<?=index?>"></div>
        </div>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <label for="channelCode" style="padding-left: 0;" class="col-sm-4 control-label">商品key：</label>
            <input type="text" data-item="channelCode" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="商品key"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].channelCode?>" id="channelCode">
        </div>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <label for="videoId" style="padding-left: 0;" class="col-sm-4 control-label">介绍视频id：</label>
            <input type="text" data-item="videoId" data-index="<?=index?>"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="介绍视频id"
                value="<?=valueArr&&valueArr[index]&&valueArr[index].videoId?>" id="videoId">
        </div>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
            <label for="configList" style="padding-left: 0;" class="col-sm-4 control-label">视频锚点：</label>
            <div data-index="<?=index?>" style="width:100%" data-item="anchor-point<?=index?>"></div>
       </div>
    </div>
    <?if(valueArr.length!==1){?>
    <div style="margin-top: 10px;"><button data-index="<?=index?>" class="btn btn-danger"
            data-item="remove-config-list-highlights" type="button"><span
                class="glyphicon glyphicon-remove"></span></button></div>
    <?}?>

</div>
<?}?>
<div class="form-group" style="margin-bottom: 10px; width: 520px;">
    <div class="col-sm-10"></div>
    <div><button class="btn btn-primary"
            data-item="add-config-list-highlights" type="button"><span
                class="glyphicon glyphicon-plus"></span></button></div>
</div>

</script>