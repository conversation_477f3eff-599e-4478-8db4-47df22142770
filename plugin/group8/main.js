/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2024/11/1
 */
"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, store, form, Plugin) {
    var _group8 = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        console.log(this.config);
        
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.store = this.config.store || [];
        this.render();
    }
    _group8.prototype = {
        render: function(){
            var me = this;
            var valueArr = Array.isArray(me.config.value) ? me.config.value : [me.config.value];
            const isTwoArray = me.config.options.some(function(item){
                return Array.isArray(item)
            }) ? true : false
            var option =  isTwoArray ? me.config.options : [me.config.options]
            console.log(option);
            
            const valueKey = (()=>{
                const value = {}
                if(Object.keys(Array.isArray(me.config.value) ? me.config.value[0] : me.config.value).length > 0){
                    for(let key in Array.isArray(me.config.value) ? me.config.value[0] : me.config.value){
                        value[key] = ''
                    }
                }
                return value
            })()
            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    dom.item('inputDom').on('input', function () {
                        var index = $(this).attr('data-index')
                        var j = $(this).attr('data-j')
                        valueArr[j][option[j][index].dataIndex] = $(this).val().trim()
                        console.log(valueArr[j][option[j][index].dataIndex]);
                        
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    console.log();
                    
                    dom.find('.selectDom').on('change',function (){
                        var index = $(this).attr('data-index')
                        var j = $(this).attr('data-j')
                        const item = option[j][index]
                        valueArr[j][item.dataIndex] = $(this).val()
                        hiddenInput.val(JSON.stringify(valueArr));
                        if(item.render){
                            const res = item.render($(this).val(), deepClone(option[j]),deepClone(valueArr[j]))
                            option[j] = res.option;
                            valueArr[j] = res.dataObj
                            renderTemplate()
                        }
                    })
                    dom.item('add-goods-session-group-highlights').on('click', function () {
                        valueArr.push(deepClone(valueKey))
                        option.push(deepClone(isTwoArray ? me.config.options[0] : me.config.options) )
                        option.forEach((row,i) => {
                            row.forEach((cell,j) => {
                                if(cell.addRender){
                                    cell.addRender(cell,option)
                                }
                            })
                        }) 
                        renderTemplate()
                    });
                    dom.item('remove-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')          
                        valueArr.splice(index, 1)
                        option.splice(index,1)
                        option.forEach((row,i) => {
                            row.forEach((cell,j) => {
                                if(cell.addRender){
                                    cell.removeRender(cell,option)
                                }
                            })
                        }) 
                        renderTemplate()
                    });
                    for(let i = 0; i < option.length; i++){
                        for(let j = 0; j < option[i].length; j++){
                            if(option[i][j].xtype === 'fileUpload'){
                                Plugin(
                                    'jiakao-misc!upload',
                                    {
                                        dataIndex: 'new-img',
                                        uploadIndex: 'new-img',
                                        bucket: 'jiakao-web',
                                        target: dom.item('new-img-plugin' + i + '' + j),
                                        isSingle: true,
                                        value: valueArr[i][option[i][j].dataIndex],
                                        placeholder: '请选择上传文件',
                                        url: 'simple-upload3://upload/file.htm',
                                        deleteImgInfo: function (plugin, value) {
                                            var index = $(plugin.config.target[0]).attr('data-index')
                                            valueArr[i][option[i][j].dataIndex] = value
                                            hiddenInput.val(JSON.stringify(valueArr));
                                        }
                                    },
                                    function (plugin, tag, value) {
                                        var index = $(plugin.config.target[0]).attr('data-index')
                                        valueArr[i][option[i][j].dataIndex] = value
                                        hiddenInput.val(JSON.stringify(valueArr));
                                        
                                    }
                                ).render()
                            }
                        }
                    }

                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        dataIndex: me.config.dataIndex,
                        options: option
                    }
                }).render()
            }
        }

    }
    _group8.prototype.constructor = _group8;
    return function (plugin, success) {
        return new _group8(plugin, success);
    }
})


function deepClone(obj, hash = new WeakMap()) {
    if (obj === null) return null;
    if (typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof RegExp) return new RegExp(obj);
    if (hash.has(obj)) return hash.get(obj);
  
    let cloneObj = new obj.constructor();
    hash.set(obj, cloneObj);
  
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloneObj[key] = deepClone(obj[key], hash);
      }
    }
    return cloneObj;
  }