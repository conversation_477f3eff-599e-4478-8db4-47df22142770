<script id="jiakao-misc-plugin-group8-template-main-index" type="text/html">
    <?
      var options = config.options;
      var valueArr = config.valueArr;
    ?>
    <style>
        .group-container{
            display: flex;
            border: 1px solid #CDD0D6;
            padding: 5px 5px;
            .main{
                flex: 1 auto;
            }
        }
    </style>
    <input type="hidden" data-item="hidden-input" name="<?=config.dataIndex?>" value="<?=config.value?>">
    <?for(var j = 0; j<options.length;j++){?>
        <div class="group-container">
            <div class="main">
                <?for(var index=0; index<options[j].length;index++){?>
                    <div class="form-group" style="margin-bottom: 10px;">
                        <div class="col-sm-10">
                            <?if(options[j][index].xtype == "text"){?>
                                <input type="<?=options[j][index].type?>"  <? if (options[j][index].disabled) { ?>disabled<? } ?>  data-item="inputDom" data-j="<?=j?>" data-index="<?=index?>" value="<?=valueArr[j][options[j][index].dataIndex]?>" class="form-control group-name<?=index?> group-input" placeholder="<?=options[j][index].placeholder?>">
                            <?}else if(options[j][index].xtype == "select"){?>
                                <select class="form-control group-name<?=index?> group-input selectDom" data-item="<?=options[j][index].dataIndex?>"  id="select-<?=options[j][index].dataIndex?>" data-j="<?=j?>" data-index="<?=index?>">
                                    <?for(var i=0; i<options[j][index].store.length; i++){?>
                                        <option value="<?=options[j][index].store[i].key?>"  <?=(valueArr[j][options[j][index].dataIndex] == options[j][index].store[i].key ) ? 'selected': ''?>>
                                            <?=options[j][index].store[i].value?>
                                        </option>
                                        <?}?>
                                </select>
                            <?}else if(options[j][index].xtype == "fileUpload"){?>
                                <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
                                    <div data-index="<?=index?>" style="width:100%" data-item="new-img-plugin<?=j?><?=index?>"></div>
                               </div>
                            <?}?>
                        </div>
                    </div>
                <?}?>
            </div>
            <div >
                <?if(j==0){?>
                    <button
                        data-index="<?=j?>" class="btn btn-primary"
                        data-item="add-goods-session-group-highlights" type="button"
                    >
                        <span class="glyphicon glyphicon-plus"></span>
                    </button>
                <?}else{?>
                    <button
                        data-index="<?=j?>" class="btn btn-danger"
                        data-item="remove-goods-session-group-highlights" type="button"
                    >
                        <span class="glyphicon glyphicon-remove"></span>
                    </button>
                <?}?>
            </div>
        </div>
    <?}?>

 
    
</script>

<!--  -->
