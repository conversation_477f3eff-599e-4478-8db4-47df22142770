/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin', 'simple!core/utils'], function (Template, Store, Form, Plugin, Utils) {

    var imagesGroup = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    imagesGroup.prototype = {

        render: function () {
            var me = this;

            Template(me.plugin.path + '/template/main/index', me.target, function (dom, data, item) {
                var $op = item('operation');
                Plugin('jiakao-misc!select-images-outer', Utils.object.clone({}, {
                    dataIndex: 'resourceUrl',
                    target: item('upload1')
                }))
                .render({})
                Plugin('jiakao-misc!select-images-outer', Utils.object.clone({}, {
                    dataIndex: 'compareResourceUrl',
                    target: item('upload2')
                }))
                .render({})

                item('add').on('click', function () {

                    $op.before('<div class="line"><div data-item="upload1" class="upload"></div><div data-item="upload2" class="upload"></div></div>');
                    Plugin('jiakao-misc!select-images-outer', Utils.object.clone({}, {
                        dataIndex: 'resourceUrl',
                        target: $op.prev().item('upload1')
                    }))
                    .render({})
                    Plugin('jiakao-misc!select-images-outer', Utils.object.clone({}, {
                        dataIndex: 'compareResourceUrl',
                        target: $op.prev().item('upload2')
                    }))
                    .render({})
                });


                item('del').on('click', function () {
                    if ($op.prev().prev().attr('class') != "top") {
                        $op.prev().remove();

                    }
                })
                if (me.config.value) {
                    item('first-line').remove();
                    var imagesData = JSON.parse(me.config.value);
                    for (let i in imagesData) {
                        $op.before(`<div class="line"><div data-item="upload1" class="upload"></div><div data-item="upload2" class="upload"></div></div>`);

                        Plugin('jiakao-misc!select-images-outer', Utils.object.clone({}, {
                            dataIndex: 'resourceUrl',
                            value: JSON.stringify(imagesData[i].resourceUrl ? [{url: imagesData[i].resourceUrl}] : ''),
                            target: $op.prev().item('upload1')
                        }))
                        .render({})
                        Plugin('jiakao-misc!select-images-outer', Utils.object.clone({}, {
                            dataIndex: 'compareResourceUrl',
                            value: JSON.stringify(imagesData[i].compareResourceUrl ? [{url: imagesData[i].compareResourceUrl}] : ''),
                            target: $op.prev().item('upload2')
                        }))
                        .render({})
                    }
                }

                me.success(); //这个函数一定要执行，否则无法调用回调函数
            }).render();

        }
    }

    imagesGroup.prototype.constructor = imagesGroup;

    return function (plugin, success) {
        return new imagesGroup(plugin, success);
    }

});