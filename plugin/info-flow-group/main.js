/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form'], function (Template, Store, Form) {

    var infoFlowGroup = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    infoFlowGroup.prototype = {

        // 渲染
        render: function () {
            var me = this;
            var initValue = me.config.value;
            var valueArr = [];
            if (initValue) {
                valueArr = JSON.parse(initValue);
            } else {
                valueArr = [{ dataType: '1', tags: '', sameCity: false }]
            }
            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    dom.item('add-goods-session-group-highlights').on('click', function () {
                        valueArr.push({ dataType: '1', tags: '', sameCity: false })
                        renderTemplate()
                    });

                    dom.item('remove-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        valueArr.splice(index, 1)
                        renderTemplate()
                    });
                    valueArr && valueArr.forEach((res, index) => {
                        var value = res.dataType
                        renderDom(dom, value, index)
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    function renderDom(dom, value, index, type) {
                        if (type) {
                            valueArr[index].tags = ''
                            valueArr[index].sameCity = false
                            $(dom.item('tags')[index]).val('')
                            $(dom.item('sameCity')[index]).removeAttr('checked')
                        }
                        if (value == 2 || value == 3 || value == 4) {
                            $(dom.item('tags')[index]).css('opacity', 0)
                            $(dom.item('sameCityDiv')[index]).css('opacity', 1)
                        } else if (value == 9 || value == 10 || value == 7 || value == 8) {
                            $(dom.item('tags')[index]).css('opacity', 0)
                            $(dom.item('sameCityDiv')[index]).css('opacity', 0)

                        } else {
                            $(dom.item('tags')[index]).css('opacity', 1)
                            $(dom.item('sameCityDiv')[index]).css('opacity', 1)
                        }
                    }
                    dom.item('dataType').on('change', function () {
                        var index = $(this).attr('data-index')
                        var value = $(this).val()
                        valueArr[index].dataType = $(this).val()
                        renderDom(dom, value, index, 'clear')
                        hiddenInput.val(JSON.stringify(valueArr));

                    })
                    dom.item('tags').on('input', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].tags = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                    dom.item('sameCity').on('click', function () {
                        var index = $(this).attr('data-index')
                        valueArr[index].sameCity = $(this).prop('checked')
                        hiddenInput.val(JSON.stringify(valueArr));
                    })
                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        dataIndex: me.config.dataIndex,
                        tieziArray: me.config.tieziArray
                    }
                }).render()
            }

        }
    }

    infoFlowGroup.prototype.constructor = infoFlowGroup;

    return function (plugin, success) {
        console.log(plugin, "pluginplugin");
        return new infoFlowGroup(plugin, success);
    }

});