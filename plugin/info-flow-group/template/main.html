<script id="jiakao-misc-plugin-info-flow-group-template-main-index" type="text/html">
    <?
      var valueArr = config.valueArr;
      var tieziArray = config.tieziArray||[];
    

    ?>
<style>
    input {
        border-radius: 20px !important;
    }
    .group-checkbox{
        vertical-align: text-top !important;
        margin-right: 5px !important;
    }
    .info-container-group{
        display: flex;
        align-items: center;
    }
    .tiezilei{
      display: inline-block;
      width: 200px;
      margin-right: 5px;
    }
    .biaoqianlei{
       display: inline-block;
    }
    .tiezicontainer{
        display: flex;
    }
</style>
<input type="hidden" data-item="hidden-input"  name="dataTypes" value="<?=config.value?>">
<?for(var index=0; index<valueArr.length;index++){?>
<div class="form-group info-container-group" style="margin-bottom: 10px;">
    <div class="col-sm-8 tiezicontainer">
        <select class="form-control tiezilei" data-item="dataType" data-index="<?=index?>">
            <?for(var i=0; i<tieziArray.length-1; i++){?>
            <option value="<?=tieziArray[i].key?>" <?=(valueArr[index].dataType == tieziArray[i].key) ? 'selected': ''?>  >
                <?=tieziArray[i].value?>
            </option>
            <?}?>
        </select>
        <input placeholder="以逗号分割"  type="text" data-item="tags" data-index="<?=index?>"
            class="form-control biaoqianlei goods-session-group-highlights<?=index?> highlights-input" 
            value="<?=valueArr&&valueArr[index]&&valueArr[index].tags?>">
    </div>
    <div class="col-sm-3">
        <div data-item="sameCityDiv">
            <input data-index="<?=index?>" name="sameCity"   data-item="sameCity" class="group-checkbox" type="checkbox" <?=valueArr[index].sameCity ? 'checked': ''?> >限制为同城
        </div>
        
    </div>
    <?if(index==0){?>
    <div class="col-sm-1"><button data-index="<?=index?>" class="btn btn-primary"
            data-item="add-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-plus"></span></button></div>
    <?}else{?>
    <div class="col-sm-1"><button data-index="<?=index?>" class="btn btn-danger"
            data-item="remove-goods-session-group-highlights" type="button"><span
                class="glyphicon glyphicon-remove"></span></button></div>
    <?}?>

</div>
<?}?>


</script>