/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Store, Form, Plugin) {

    var banner = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    banner.prototype = {

        render: function () {
            var me = this;

            Template(me.plugin.path + '/template/main/index', me.target, function (dom, data, item) {
                var $op = item('operation');
                item('add').on('click', function () {

                    $op.before('<div class="line"><input data-item="conmment-text" type="text" class="cmtext" placeholder="评论内容"></div>');

                });


                item('del').on('click', function () {
                    if ($op.prev().prev().attr('class') != "top") {
                        $op.prev().remove();

                    }
                })
                if (me.config.value) {
                    item('first-line').remove();
                    var bannerData = JSON.parse(me.config.value);
                    for (let i in bannerData) {
                        $op.before(`<div class="line"><input value="${bannerData[i].comment}" data-item="conmment-text" type="text" class="cmtext" placeholder="评论内容"></div>`);
                    }
                }

                me.success(); //这个函数一定要执行，否则无法调用回调函数
            }).render();



        }
    }

    banner.prototype.constructor = banner;

    return function (plugin, success) {
        return new banner(plugin, success);
    }

});