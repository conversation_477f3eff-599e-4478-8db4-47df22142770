﻿/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form'], function (Template, Store, Form) {

    var autoPrompt = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }


    // 画当前城市所有考场
    function drawKaochang(me, cityCode, address) {
        var placeSearch = new AMap.PlaceSearch({
            // city 指定搜索所在城市，支持传入格式有：城市名、citycode和adcode
            city: cityCode
        });
        address = address.replace('考场', '') + '科目三考场';
        placeSearch.search(address, function (status, result) {

            // 查询成功时，result即对应匹配的POI信息
            var pois = result.poiList.pois;

            var poi = pois[0];
            var marker = new AMap.Marker({
                icon: "https://webapi.amap.com/images/dd-via.png",
                position: poi.location,   // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
                title: poi.name
            });

            marker.setLabel({
                content: address, //设置文本标注内容
                direction: 'top' //设置文本标注方位
            })
            // 将创建的点标记添加到已有的地图实例：
            me.map.add(marker);

        })
    }

    // 画标识并将标识放到对应的对象中
    var drawMarker = function (me, map) {
        var points = me.config.mapData.points;
        for (let i = 0; i < points.length; i++) {
            let item = me.config.mapData.points[i];
            // 重绘删除之前的点
            if (item.marker) {
                item.marker.setMap(null)
            }

            var marker = new AMap.Marker({
                icon: "https://webapi.amap.com/images/dd-via.png",
                position: [item.lng, item.lat],
                draggable: true,
                offset: new AMap.Pixel(-3,-3),
                cursor: 'move',
                map: map
            });
            marker.setLabel({
                content: item.resName, //设置文本标注内容
                direction: 'top' //设置文本标注方位
            })
            item.marker = marker;

            marker.on('dragend', function (e) {
                item.lng = e.lnglat.R;
                item.lat = e.lnglat.Q;
            })
            // 双击表示右边的项目
            marker.on('dblclick', function (e) {
                var $nowProject = me.target.find('.kaoshi-box .kaoshi-item:eq(' + i + ')');
                $nowProject.trigger('click');
            })
            // 右键添加辅助点
            marker.on('rightclick', function (e) {
                var $nowProject = me.target.find('.kaoshi-box .kaoshi-item:eq(' + i + ')');
                $nowProject.trigger('click');

                var $addProject = me.target.find('.all-kaoshi-box .select-kaoshi-item');
                $addProject.val(24);
                $addProject.trigger('change');
                $addProject.val(-2);
            })
        }

    }
    // 画线
    var drawLine = function (me, map) {
        var path = me.config.mapData.points.map(item => [item.lng, item.lat])
        var polyline = new AMap.Polyline({
            path: path,
            outlineColor: '#ffeeff',
            borderWeight: 6,
            strokeColor: "#3366FF",
            strokeOpacity: 1,
            strokeWeight: 6,
            // 折线样式还支持 'dashed'
            strokeStyle: "solid",
            // strokeStyle是dashed时有效
            strokeDasharray: [10, 5],
            lineJoin: 'round',
            lineCap: 'round',
            zIndex: 50,
            showDir: true,
        });
        polyline.setMap(map);
        return polyline
    }

    // 加载地图
    var loadMap = function (me) {
        me.map = new AMap.Map('mapContainer' + me.config.id, {
            resizeEnable: true,
        });

        me.config.kaoshiList.forEach(function (item) {
            drawKaochang(me, item.cityCode, item.name);
        })

        drawMarker(me, me.map);

        // 折线
        me.line = drawLine(me, me.map);

        me.map.setFitView();
    }

    autoPrompt.prototype = {
        getData: function () {
            var me = this;
            var id = me.config.id;
            var mapData = new Promise(function (resolve, reject) {
                Simple.Store(['jiakao-misc!editMap/data/edit?id=' + id]).load([{
                    aliases: 'mapData'
                }]).then(function (data) {
                    resolve(data.data.mapData.data);
                })
            });
            var allProjectData = new Promise(function (resolve, reject) {
                Simple.Store(['jiakao-misc!editMap/data/allProject']).load([{
                    aliases: 'allProjectData'
                }]).then(function (data) {
                    resolve(data.data.allProjectData.data);
                })
            });
            return Promise.all([mapData, allProjectData])

        },
        saveData: function (dom, line) {
            var me = this;
            var tempData = $.extend(true, {}, this.config.mapData, { publish: line });
            var points = [];
            var traceList = [];
            if (me.config.selectKaochang) {
                var kaochang = {
                    placeId: dom.find('.select-kaochang').val(),
                    placeName: dom.find('.select-kaochang option:selected').text()
                };
                var luxian = {
                    routeId: dom.find('.select-luxian').val(),
                    routeName: dom.find('.select-luxian option:selected').text()
                }

                if (!kaochang.placeId) {
                    Simple.Dialog.toast('请先选择考场');
                    return
                }
                if (!luxian.routeId) {
                    Simple.Dialog.toast('请先选择路线');
                    return
                }

                tempData.placeId = kaochang.placeId;
                tempData.placeName = kaochang.placeName;
                tempData.routeId = luxian.routeId;
                tempData.routeName = luxian.routeName;
            }

            tempData.points.forEach(function (item, index) {
                points.push({
                    lat: item.lat,
                    lng: item.lng,
                    resourceId: item.resourceId,
                    resName: item.resName,
                    order: index
                });
                traceList.push(`${index + 1},${item.lng},${item.lat}`);
            });

            tempData.points = JSON.stringify(points);
            tempData.traceList = JSON.stringify(traceList);

            Simple.Store(['jiakao-misc!editMap/data/saveProject']).save([{
                aliases: 'saveStatus',
                params: tempData
            }]).then(function (data, store, c, json) {
                if (json.json.data === true) {
                    Simple.Dialog.toast('保存成功');
                }
            })

        },
        getKaochangList: function (cityCode) {
            return new Promise(function (resolve, reject) {
                Simple.Store(['jiakao-misc!editMap/data/kaochangList']).load([{
                    aliases: 'kaochangList',
                    params: {
                        cityCode: cityCode
                    }
                }]).then(function (data) {
                    resolve(data.data.kaochangList.data);
                })
            })
        },
        getLuxianList: function (placeId, dom) {
            var me = this;
            Simple.Store(['jiakao-misc!editMap/data/luxianList']).load([{
                aliases: 'luxianList',
                params: {
                    placeId: placeId
                }
            }]).then(function (data) {
                var luxianList = data.data.luxianList.data;
                var $luxianList = dom.find('.show-luxian-list');
                var $selectLuxian = dom.find('.select-luxian-box .select-luxian');
                $luxianList.html('');
                $selectLuxian.html('');

                luxianList.forEach(function (item) {
                    var $luxianItem = $(`<div data-src=${item.videoUrl} class="show-luxian-item">${item.name}</div>`);
                    // 点击播放视频
                    $luxianItem.on('click', function () {
                        Simple.Dialog.html('路线视频', {}).done(function (dialog) {
                            var $video = $('<video style="width: 100%;" autoplay="autoplay" src="' + item.videoUrl + '" controls></video>');
                            $video[0].currentTime = 30;

                            $(dialog.body).html($video);
                            setTimeout(function () {
                                $video[0].play();
                            }, 200)
                        })
                    });

                    $luxianList.append($luxianItem);

                    var $selectLuxianItem = $(`<option value="${item.id}">${item.name}</option>`);
                    $selectLuxian.append($selectLuxianItem);

                    if (me.config.mapData.routeId) {
                        $selectLuxian.val(me.config.mapData.routeId)
                    }
                });
            })
        },
        renderTemplate: function () {
            var me = this;
            var configData = me.config;

            Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                if (!configData.selectKaochang) {
                    dom.find('.kaochang-box').hide();
                }

                var $selectKaochang = dom.find('.select-kaochang');
                var $selectWeizhi = dom.find('.select-weizhi-box .select-weizhi');

                // 改变选择的考场
                $selectKaochang.on('change', function () {
                    var value = $(this).val();
                    me.getLuxianList(value, dom);
                })
                if (configData.mapData.placeId) {
                    $selectKaochang.val(configData.mapData.placeId);
                    $selectKaochang.trigger('change');
                }

                // 选中的项目
                var selectProject = {};
                // 选择项目
                var $projectBox = dom.find('.kaoshi-box');

                $projectBox.on('click', '.kaoshi-item', function () {
                    $(this).siblings().removeClass('active').end().addClass('active');
                    selectProject = {
                        target: $(this),
                        index: $(this).index()
                    };

                })
                // 新增项目
                var $addProject = dom.find('.all-kaoshi-box .select-kaoshi-item');
                $addProject.on('change', function () {
                    var mapCenter = me.map.getCenter();
                    if (!selectProject.target) {
                        Simple.Dialog.toast('请先选择项目');
                        $(this).val(-2);
                        return
                    }
                    var $selectOption = $(this).find('option:selected');
                    var index = selectProject.target.index();
                    var $Item = $(`<div data-id="${$selectOption.attr('value')}" class="kaoshi-item">${$selectOption.text()}</div>`);

                    if ($selectWeizhi.filter(':checked').val() == 'prev') {
                        selectProject.target.before($Item);

                        configData.mapData.points.splice(index, 0, {
                            resourceId: $(this).val(),
                            resName: $selectOption.text(),
                            lat: mapCenter.Q,
                            lng: mapCenter.R,
                        })
                    } else {
                        selectProject.target.after($Item);

                        configData.mapData.points.splice(index + 1, 0, {
                            resourceId: $(this).val(),
                            resName: $selectOption.text(),
                            lat: mapCenter.Q,
                            lng: mapCenter.R,
                        })
                    }

                    drawMarker(me, me.map);
                    // 清空选项
                    $(this).val(-2);
                })

                // 删除项目
                var $removeButton = dom.find('.all-kaoshi-box .remove-kaoshi-item');
                $removeButton.on('click', function () {
                    if (!selectProject.target) {
                        Simple.Dialog.toast('请先选择项目');
                        return
                    }
                    var index = selectProject.target.index();
                    var removeItem = configData.mapData.points.splice(index, 1)[0];
                    removeItem.marker.setMap(null)
                    drawMarker(me, me.map);
                    // 移除dom
                    selectProject.target.remove();
                    selectProject = {};

                })

                // 重新绘制
                var $reWrite = dom.find('.jingweidu-box .rewhite');
                $reWrite.on('click', function () {
                    me.line.setPath(configData.mapData.points.map(item => [item.lng, item.lat]));

                });



                // 保存
                var $save = dom.find('.active-box .save');
                $save.on('click', function () {
                    me.saveData(dom);
                })

                // 保存并上线
                var $saveAndLine = dom.find('.active-box .saveAndLine');
                $saveAndLine.on('click', function () {
                    me.saveData(dom, true);
                })
            }, { config: configData }).render()
        },

        // 渲染
        render: function () {
            var me = this;
            this.getData().then(function (data) {
                var mapData = data[0];
                var allProject = data[1];

                me.config.mapData = mapData;
                me.config.allProject = allProject;

                if (true) {
                    me.getKaochangList(mapData.cityCode).then(function (kaoshiList) {
                        me.config.kaoshiList = kaoshiList;
                        me.renderTemplate();

                        setTimeout(function () {
                            loadMap(me)
                        }, 500)
                    })
                } else {
                    me.renderTemplate();
                }




            })
        }
    }

    autoPrompt.prototype.constructor = autoPrompt;

    return function (plugin, success) {
        return new autoPrompt(plugin, success);
    }

});