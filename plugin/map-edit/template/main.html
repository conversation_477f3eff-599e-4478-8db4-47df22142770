﻿<style>
    /* .mapContainer .amap-icon img,
    .mapContainer.amap-marker-content img{
        width: 25px;
        height: 34px;
    } */
    .map-edit-box{
        float:right;
        height:100%;
        width:300px;
        padding: 0 15px;
        background-color: #fff;
        box-sizing: border-box;
    }
    .kaochang-box .show-city-box{
        display: flex;
    }
    .kaochang-box .show-item{
        margin: 6px 0; 
        width: 50%;
    }
    .kaochang-box .show-luxian-list{
        margin: 10px 0;
    }
    .kaochang-box .show-luxian-list .show-luxian-item{
        color: cornflowerblue;
    }
    .kaochang-box .select-luxian-title{
        margin: 0 0 6px;
    }
    .kaoshi-box .kaoshi-list{
        border: 1px solid #333;
        height: 200px;
        overflow: auto;
    }
    .kaoshi-box .kaoshi-list .kaoshi-item{
        line-height: 20px;
        padding: 0 10px;
    }
    .kaoshi-box .kaoshi-list .kaoshi-item.active{
        background-color: #999;
    }
    
    .kaoshi-box .all-kaoshi-box{
        margin: 10px 0;
    }
    .all-kaoshi-box .select-weizhi-box .select-weizhi{
        vertical-align: middle;
        margin: 0;
    }
    .kaoshi-box .all-kaoshi-box .select-kaoshi-box{
        position: relative;
    }
    .kaoshi-box .all-kaoshi-box .select-kaoshi-item{
        width: 150px;
        color: cornflowerblue;
        opacity: 0;
    }
    .kaoshi-box .all-kaoshi-box .add-kaoshi-item-sign{
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        color: cornflowerblue;
    }
    .kaoshi-box .all-kaoshi-box .remove-kaoshi-item{
        float: right;
        color: cornflowerblue;
    }
    .jingweidu-box .jingdu input,.jingweidu-box .weidu input{
        margin-bottom: 8px;
        width: 100%;
        height: 30px;
    }
    .jingweidu-box .active-box{
        display: flex;
        justify-content: space-between;
        color: cornflowerblue;
        margin-bottom: 10px;
    }
    
    .xmms .xmms-text textarea{
        margin-top: 10px;
        width: 100%;
        height: 150px;
    }
</style>
<!--自动提示模板-->
<script id="jiakao-misc-plugin-map-edit-template-main-index" type="text/html">
    <div id="mapContainer<?=config.id?>" class="mapContainer" style="float:left;width:calc(100% - 300px);height:100%;"></div>
    <div class='map-edit-box'>
         <?
            var mapData = config.mapData;
            var kaoshiList = config.kaoshiList;
        ?>
        <div><?=mapData.name?></div>
        <div class="kaochang-box">
            <div class="kaochang-title">判断考场</div>
           
            <div class="show-city-box">
                <div class="show-province show-item"><?=mapData.provinceName?></div>
                <div class="show-city show-item"><?=mapData.cityName?></div>
            </div>
            <div class="select-kaochang-box">
                <select class="select-kaochang">
                    <option value="-2" style="display: none;"></option>
                    <?for(var i=0; i<kaoshiList.length;i++){?>
                        <option value="<?=kaoshiList[i].id?>"><?=kaoshiList[i].name?></option>
                    <?}?>
                </select>
                <div class="select-kaochang-dec">
                    当前考场路线视频，你可以根据视频中的缩略图判断考场线路
                </div>
            </div>

            <div class="show-luxian-list">
                
            </div>
            <div class="select-luxian-box">
                <div class="select-luxian-title">判断考场路线</div>
                <select class="select-luxian">
                   
                </select>
            </div>
        </div>
        <div class="kaoshi-box">
            <div class="kaoshi-title">考试项目</div>
            <div class="kaoshi-list">
                <?
                    var projectArr = mapData.points;
                ?>
                 <?for(var i=0; i<projectArr.length;i++){?>
                    <div data-id="<?=projectArr[i].resourceId?>" class="kaoshi-item"><?=projectArr[i].resName?></div>
                 <?}?>
                  
            </div>
            <div class="all-kaoshi-box">
                <div class="select-weizhi-box">
                    <div class="select-weizhi-title">选择插入位置</div>
                    <label><input class="select-weizhi" type="radio" name="weizhi" value="next" checked><span>后方</span></label>
                    <label><input class="select-weizhi" type="radio" name="weizhi" value="prev"><span>前方</span></label>
                </div>
                <label class="select-kaoshi-box">
                    <span class="add-kaoshi-item-sign">在当前项目下插入项目</span>
                     <?
                        var allProject = config.allProject;
                    ?>
                    <select class="select-kaoshi-item">
                        <option value="-2" style='display: none'>-2</option>
                         <?for(var i=0; i<allProject.length;i++){?>
                            <option value="<?=allProject[i].id?>"><?=allProject[i].title?></option>
                        <?}?>
                    </select>
                </label>
                
                <div class="remove-kaoshi-item">删除当前项目</div>
            </div>
        </div>
        
        <div class="jingweidu-box">
            <!-- <div class="jingweidu-title">经纬度</div> -->
            <!-- <div class="jingdu">
                <input data-item="jindu" type="text">
            </div>
            <div class="weidu">
                <input data-item="weidu" type="text">
            </div> -->
            <div class="active-box">
                <!-- <div class="repotion">重新定位(暂无)</div> -->
                <div class="rewhite">重绘路线</div>
            </div>
        </div>

        <!-- <div class="xmms">
            <div class="xmms-title">项目描述</div>
            <div class="xmms-text">
                <textarea data-item="xmms"></textarea>
            </div>
        </div>

        <div class="xmyy">
            <div class="xmyy-title">项目语音地址</div>
            <div class="address-upload">
                <div class="address"></div>
                <input type="file" class="upload-mp3" value="上传mp3">
            </div>
        </div>

        <div class="lxrkt">
            <div class="lxrkt-title"></div>
            
        </div> -->
        <div class="active-box">
            <button class="saveAndLine">保存并上线</button>
            <button class="save">保存</button>
        </div>

    </div>
</script>