/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Store, Form, Plugin) {

    var NavbarGroup = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    NavbarGroup.prototype = {

        // 渲染
        render: function () {
            var me = this;
            var initValue = me.config.value;
            var valueArr = [];
            if (initValue) {
                valueArr = JSON.parse(initValue);
                if (!Array.isArray(valueArr)) {
                    valueArr = [valueArr]
                }
            } else {
                valueArr = [{type:'',sort:'0',itemList:[{url:'',title:''}]}]

            }
            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item('hidden-input');
                    hiddenInput.val(JSON.stringify(valueArr));
                    setTimeout(() => {
                       
                        valueArr.forEach(function (item, sonIndex) {
                            item.itemList = item.itemList || [{ url: item.data?.actionUrl || '', title: item.title||"" }]
                            if (item.type == 'web') {
                               
                                webTemplate(sonIndex, dom)
                            }

                        })
                        hiddenInput.val(JSON.stringify(valueArr));
                      
                    }, 500)
                    dom.item('new-sort').on('change',function(){
                        var index = $(this).attr('data-index')
                        valueArr && valueArr.forEach(function(res){
                            res.sort = ''
                        })
                        valueArr[index].sort = index+''
                        hiddenInput.val(JSON.stringify(valueArr));

                    })
                    dom.item('add-goods-session-group-highlights').on('click', function () {
                        valueArr.push({ type: '', sort:'', itemList :[{url:'',title:''}]})
                        renderTemplate()
                        setTimeout(()=>{
                           
                            valueArr.forEach(function (item, sonIndex) {
                            
                                if(item.type=='web'){
                                    webTemplate(sonIndex,dom)
                                }
                              
                            })
                        },500)
                       
                        
                    });

                    dom.item('remove-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        if (valueArr[index].sort){
                            valueArr[0].sort = '0'
                        } 
                        valueArr.splice(index, 1)
                        renderTemplate()
                        setTimeout(() => {
                            valueArr.forEach(function (item, sonIndex) {
                                if (item.type == 'web') {
                                    webTemplate(sonIndex,dom)
                                }

                            })
                        }, 500)
                      
                    });
                    dom.item('up-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        var ele = valueArr[index]
                        valueArr.splice(index, 1)
                        valueArr.splice(index - 1, 0, ele);
                        valueArr && valueArr.forEach((function(item,si){
                          
                            item.sort = item.sort+'' ? si+'':''
                        }))
                        renderTemplate()
                        setTimeout(() => {
                            valueArr.forEach(function (item, sonIndex) {
                                if (item.type == 'web') {
                                    webTemplate(sonIndex,dom)
                                }

                            })
                        }, 500)
                    });
                    dom.item('down-goods-session-group-highlights').on('click', function () {
                        var index = $(this).attr('data-index')
                        var ele = valueArr[index]
                        valueArr.splice(index, 1)
                        valueArr.splice(index + 1, 0, ele);
                        valueArr && valueArr.forEach((function (item, si) {
                          
                            item.sort = item.sort ? si+'' : ''
                        }))
                        renderTemplate()
                        setTimeout(() => {
                            valueArr.forEach(function (item, sonIndex) {
                                if (item.type == 'web') {
                                    webTemplate(sonIndex,dom)
                                }

                            })
                        }, 500)
                    });
                        for (var i = 0; i < valueArr.length; i++) {
                            Plugin(
                                'jiakao-misc!auto-prompt',
                                {
                                    store: me.config.typeArray,
                                    dataIndex: 'new-img',
                                    uploadIndex: 'new-img',
                                    target: dom.item('new-img-plugin' + i),
                                    isMulti: false,
                                    defaultVal: false,
                                    value: valueArr[i].type
                                },
                                function (plugin, value) {
                                    var index = $(plugin.config.target[0]).attr('data-index')
                                    valueArr[index].type = value
                                    hiddenInput.val(JSON.stringify(valueArr));
                                    if (value =='web'){
                                        webTemplate(index,dom)
                                     
                                    }else{
                                        dom.item('web-plugin' + index).html('')
                                    }
                                 }
                            ).render()
                           
                        }
                        
                    

                }, {
                    config: {
                        value: me.config.value,
                        valueArr: valueArr,
                        dataIndex: me.config.dataIndex,
                        isSingleConfigItem: me.config.isSingleConfigItem,
                    }
                }).render()
            }
            function webTemplate(parentOrder,parentDom) {

                var renderDom = parentDom.item('web-plugin' + parentOrder)
            
                Template(me.plugin.path + '/template/main/web', renderDom, function (dom, data, getItem) {
                    dom.item('webValue').on('change', function () {
                        var index = $(this).attr('data-index')
                       
                        valueArr[parentOrder].itemList[index].url = $(this).val().trim()
                        parentDom.item('hidden-input').val(JSON.stringify(valueArr));
                    
                    })
                    dom.item('webTitle').on('change', function () {
                        var index = $(this).attr('data-index')

                        valueArr[parentOrder].itemList[index].title = $(this).val().trim()
                        parentDom.item('hidden-input').val(JSON.stringify(valueArr));

                    })

                }, {
                    config: {
                        webDataArray: valueArr[parentOrder].itemList,
                        dataIndex: 'webdataIndex'
                    }
                }).render()
            }


        }
    }

    NavbarGroup.prototype.constructor = NavbarGroup;

    return function (plugin, success) {
        console.log(plugin, "pluginplugin");
        return new NavbarGroup(plugin, success);
    }

});