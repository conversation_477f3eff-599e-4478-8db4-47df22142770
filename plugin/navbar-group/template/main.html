<script id="jiakao-misc-plugin-navbar-group-template-main-index" type="text/html">
    <?
      var valueArr = config.valueArr;
    ?>
<style>
    input {
        border-radius: 20px !important;
    }
</style>
<input type="hidden" data-item="hidden-input" name="<?=config.dataIndex?>" value="<?=config.value?>">
<?for(var index=0; index<valueArr.length;index++){?>
<div class="form-group" style="margin-bottom: 10px;">
    <div class="col-sm-10">
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;flex-direction: column;">
             <div data-index="<?=index?>" style="width:100%" data-item="new-img-plugin<?=index?>"></div>
             <div style="margin-top: 10px;">
                <?
                 var checked = ''
                 if(valueArr[index].sort==index){
                    checked = 'checked'
                 }
                ?>
                <label  for="sort<?=index?>" style="cursor: pointer;vertical-align: middle;">默认选中</label><input id="sort<?=index?>"  style="vertical-align: top;margin-left: 5px;cursor: pointer;" type="radio" data-index="<?=index?>" name="sort" data-item="new-sort" <?=checked ?>   value="<?=valueArr[index].sort?>"/>
             </div>
        </div>
        <div class="input-group clearfix" style="margin-top: 10px; display:flex;">
             <div data-index="<?=index?>" style="width:100%" data-item="web-plugin<?=index?>"></div>
        </div>
    </div>
    <?if(!config.isSingleConfigItem){?>
        <div class="col-sm-2">
            <?if(index==0){?>
                <button
                    data-index="<?=index?>" class="btn btn-primary"
                    data-item="add-goods-session-group-highlights" type="button"
                >
                    <span class="glyphicon glyphicon-plus"></span>
                </button>
            <?}else{?>
                <button
                    data-index="<?=index?>" class="btn btn-danger"
                    data-item="remove-goods-session-group-highlights" type="button"
                >
                    <span class="glyphicon glyphicon-remove"></span>
                </button>
            <?}?>
            <button
                data-index="<?=index?>" class="btn" <? if (index === 0) { ?>disabled<? } ?>
                data-item="up-goods-session-group-highlights" type="button"
            >
                <span class="glyphicon glyphicon-arrow-up"></span>
            </button>
            <button
                data-index="<?=index?>" class="btn" <? if (index + 1 === valueArr.length) { ?>disabled<? } ?>
                data-item="down-goods-session-group-highlights" type="button"
            >
                <span class="glyphicon glyphicon-arrow-down"></span>
            </button>
        </div>
    <?}?>
</div>
<?}?>



</script>

<script id="jiakao-misc-plugin-navbar-group-template-main-web" type="text/html">
    <?
      var webDataArray = config.webDataArray;
    ?>
<style>
    input {
        border-radius: 20px !important;
    }
</style>
<input type="hidden" data-item="hidden-input" name="<?=config.dataIndex?>" value="<?=config.value?>">
<?for(var index=0; index<webDataArray.length;index++){?>
<div class="form-group" style="margin-bottom: 10px;">
    <div class="col-sm-10">
           <input style="margin-bottom: 10px;" type="text" data-item="webTitle" data-index="<?=index?>" 
            class="form-control group-name<?=index?> group-input" id="webTitle" placeholder="标题"
            value="<?=webDataArray&&webDataArray[index]&&webDataArray[index].title?>">
        <input  type="text" data-item="webValue" data-index="<?=index?>" 
            class="form-control group-name<?=index?> group-input" id="webValue" placeholder="跳转链接"
            value="<?=webDataArray&&webDataArray[index]&&webDataArray[index].url?>">
          
    </div>
</div>
<?}?>



</script>