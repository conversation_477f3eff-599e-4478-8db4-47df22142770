/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Store, Form, Plugin) {

    var banner = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    banner.prototype = {

        render: function () {
            var me = this;

            Template(me.plugin.path + '/template/main/index', me.target, function (dom, data, $item) {
                console.log($item)

                function lineDom(item) {
                    item = item || {}
                    return `<div class="line">
                        课程ID:<input data-item="lessonGroupId" placeholder="课程ID" type="text" value="${item.lessonGroupId || ''}" style="width:150px">
                        url:<input data-item="url" type="text" placeholder="url" value="${item.url || ''}" style="width:380px">
                        <button type="button" class="btn-danger btn" style="float:right" onclick="$(this).parent().remove()">删除</button>
                    </div>`
                }

                var $v = $item('view')
                $item('add').on('click', function () {
                    var type = $(this).data('type')
                    $item(type).append(lineDom())
                });

                Store(['jiakao-misc!top-lesson-group/data/getQuestionPopData'], [{
                    aliases: 'list'
                }]).load().done(function (store) {
                    var data = store.data.list.data
                    $item('title').val(data.title)
                    $item('subtitle').val(data.subtitle)
                    data.vip100LessonConfigs.forEach((item, i) => {
                        $item('vip100LessonConfigs').append(lineDom(item))
                    })
                    data.vip500LessonConfigs.forEach((item, i) => {
                        $item('vip500LessonConfigs').append(lineDom(item))
                    })
                }).fail(function () {});

                me.success(); //这个函数一定要执行，否则无法调用回调函数
            }).render();

        }
    }

    banner.prototype.constructor = banner;

    return function (plugin, success) {
        return new banner(plugin, success);
    }

});