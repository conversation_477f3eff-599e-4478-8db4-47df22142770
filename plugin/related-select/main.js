﻿/*
 * product-filter v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/store', 'simple!core/form', 'simple!core/utils', 'simple!core/ajax', 'simple!core/widgets'], function (Template, Table, Store, Form, Utils, Ajax, Widgets) {
    var relatedSelect = function (plugin, success) {
        this.plugin = plugin;
        this.success = success;
        this.config = this.plugin.config;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        // 加载控件
        this.render();
    }
    relatedSelect.prototype = {

        // 渲染内容
        render: function () {
            var me = this;
            console.log(me.config);
            Template(this.plugin.path + '/template/main/index', this.target, function (dom, data, item) {
                var value = item('related-select-value');
                if(me.config.value) {
                    dom.find('.close-icon').show()
                    value.val(me.config.value)
                }
                dom.find('.close-icon').on('click', function () {
                    $(this).hide()
                    value.val('')
                });
                dom.find('button').on('click', function () {

                    Widgets.dialog.html('请选择', {
                        width: 700
                    }).done(function (dialog) {
                        Table({
                            title: '',
                            columns: me.config.columns,
                            operations: [{
                                    name: '选择',
                                    class: 'info',
                                    click: function (oTable, line, lineData) {
                                        var id = lineData[me.config.selectIndex]
                                        var name = lineData[me.config.selectName]
                                        // value.val(id+':'+name);
                                        value.val(id)
                                        dom.find('.close-icon').show()
                                        dialog.close()
                                    }
                                }
                            ],
                        }, [me.config.store], dialog.body, function () {}).render();
                    });
                });
            }, { config: this.config }).render();
        }
    }



    relatedSelect.prototype.constructor = relatedSelect;

    return function (plugin, success) {
        return new relatedSelect(plugin, success);
    }

});
