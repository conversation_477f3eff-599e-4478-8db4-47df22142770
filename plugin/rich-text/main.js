/*
 * editor-images-drag-upload only use UE in template v0.0.1
 *
 * name: sunbin
 * date: 2015/05/06
 */

"use strict";

define(['simple!core/template', 'simple!core/ajax', 'simple!core/widgets', 'simple!core/plugin'], function (Template, Ajax, Widgets, Plugin) {
    console.log('richchchchchhchchh');
    var update = function (me, files, bucket, progressEle) {

        var placeholder, fileType, isGif = false;

        for (var i = 0; i < files.length; i++) {

            ! function (file) {

                // 判断类型
                switch (file.type) {
                    case 'image/jpeg':
                    case 'image/jpg':
                    case 'image/png':
                    case 'image/gif':
                        fileType = 'image';
                        if (file.type == 'image/gif') {
                            isGif = true
                        }
                        placeholder = file.name + '_' + Math.random().toString(16).substring(2);
                        me.execCommand('insertimage', {
                            src: window.URL.createObjectURL(file),
                            title: placeholder
                        });

                        $(me.document.body).find('img[title="' + placeholder + '"]').css({
                            maxWidth: '100%',
                            opacity: '0.2'
                        });


                        break;
                    default:
                        fileType = 'file';
                        placeholder = file.name + '_' + Math.random().toString(16).substring(2);
                        me.execCommand('link', {
                            url: 'javascript:;',
                            textValue: '[' + file.name + '，正在上传]',
                            title: placeholder,
                            target: '_blank'
                        });
                        break;
                }

                (function (me, placeholder, fileType, fileName, isGif) {

                    uploadImages(file, function (data) {

                        if (fileType === 'image') {
                            $(me.document.body).find('img[title="' + placeholder + '"]').css({
                                opacity: ''
                            }).attr({
                                'data-type': isGif ? 'gif' : 'image',
                                'data-width': data[0].width,
                                'data-height': data[0].height,
                                src: data[0].url,
                                _src: data[0].url
                            }).removeAttr('title');
                        } else {
                            $(me.document.body).find('a[title="' + placeholder + '"]').html(fileName).attr('href', url).removeAttr('title');
                        }


                    }, function () {

                        if (fileType === 'image') {
                            $(me.document.body).find('img[title="' + placeholder + '"]').attr({
                                src: window.j.framework.root + '/v' + window.j.framework.version + '/' + '/resources/css/images/img-error.png',
                                _src: window.j.framework.root + '/v' + window.j.framework.version + '/' + '/resources/css/images/img-error.png'
                            });
                        } else {
                            $(me.document.body).find('a[title="' + placeholder + '"]').html('<span style="color: #f00;">' + fileName + '上传失败</span>');
                        }

                    }, function (percentage) {

                        var item = progressEle.find('[data-item="' + placeholder + '"]');

                        if (percentage >= 100) {
                            item.remove();
                            return;
                        }

                        if (item.size() > 0) {
                            item.html(file.name + '：' + percentage + '%');
                        } else {
                            progressEle.append('<div data-item="' + placeholder + '">' + (file.name || '截图') + '：' + percentage + '%' + '</div>');
                        }

                    }, bucket);

                })(me, placeholder, fileType, file.name, isGif);

            }(files[i]);

        }

    };

    var uploadImages = function (file, success, error, progress, bucket, type, url) {
        var me = this;
        var formdata = new FormData();
        formdata.append('bucket', bucket);
        formdata.append('type', 'image');
        formdata.append('files', file);
        //url || 'simple-upload://upload.htm'
        // http://upload.image.kakamobi.cn/api/open
        Ajax.request('simple-upload3://upload/file.htm', {
            data: formdata,
            type: 'post',
            contentType: false,
            processData: false,
            success: function (data) {
                success.call(this, data);
            },
            error: function (data) {
                error.call(this, data);
            },
            progress: function (percentage) {
                progress.call(this, percentage);
            }
        });
    };

    var dragImage = function (plugin, success) {
        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        console.log(this.target,'this.target');
        this.render();
    };

    dragImage.prototype = {
        render: function () {
            var dragObj = this;

            Simple.Utils.waitVal('UE.getEditor').then(function () {
                dragObj.target.parent().css('position', 'relative');
                if (!dragObj.target.attr('id')) {

                    dragObj.target.attr('id', 'textarea' + Math.random().toString(16).substring(2));

                    console.log(dragObj.target.attr('id'));
                    setTimeout(() => {
                        var editor = UE.getEditor(dragObj.target.attr('id'));
                        dragObj.target.data('UE', editor);
                        this.isModified = void 0;
                        dragObj.target.before('<div data-item="upload-progress" style="text-align: right; z-index: 10000; position: absolute; right: 19px; bottom: 26px; line-height: 1.5;"></div><div data-item="drop-masks" style="z-index: 10000; display: none; top: 0; bottom: 0; left: 16px; right: 13px; border-radius: 5px; position: absolute; background: rgba(0, 0, 0, 0.1); font-size: 30px; text-align: center; line-height: 414px;">松开鼠标开始上传图片</div>');
                        (function (dom, textarea, editor) {

                            editor.ready(function () {

                                var me = editor;

                                var drop = $(me.document);
                                var dropMasks = dom.item('drop-masks');
                                var progressEle = dom.item('upload-progress');
                                if (dragObj.config.value) {
                                    me.setContent(dragObj.config.value)
                                }
                                Plugin('simple!clipboard', {
                                    input: drop,
                                    callback: function (files) {
                                        update(me, files, dragObj.config.bucket, progressEle);
                                    }
                                }).render();

                                $(window).on('dragover', false);
                                $(window).on('drop', false);

                                drop.on('click', function (e) {
                                    dragObj.isModified = true;
                                    dragObj.success();
                                });


                                // 拖拽上传
                                drop.on('dragenter drag dragover', function (e) {
                                    dropMasks.show();
                                    e.stopPropagation();
                                    e.preventDefault();
                                });


                                dropMasks.on('dragenter', function () {
                                    dropMasks.show();
                                });

                                dropMasks.on('dragleave', function () {
                                    dropMasks.hide();
                                });

                                dropMasks.on('dragover', false);

                                dropMasks.on('drop', function (e) {


                                    dropMasks.hide();
                                    var files = e.originalEvent.dataTransfer.files;

                                    if (files.length < 11) {
                                        update(me, files, dragObj.config.bucket, progressEle);
                                    } else {
                                        Widgets.dialog.alert('最多支持拖拽10个文件');
                                    }


                                    e.stopPropagation();
                                    e.preventDefault();

                                });

                            });

                        })(dragObj.target.parent(), dragObj.target, editor);
                    }, 500);
                }

            })

        }

    };

    dragImage.prototype.constructor = dragImage;

    return function (plugin, success) {
        return new dragImage(plugin, success);
    }
});