﻿/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
define(['simple!core/template', 'simple!core/store', 'simple!core/form'], function (Template, Store, Form) {

    var _group = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    _group.prototype = {
        // 渲染
        render: function () {
            var me = this;
            var initValue = '';
            if (me.config.valuePath) {
                const paths = me.config.valuePath.split('.');
                const data = me.config.data[paths[0]] && JSON.parse(me.config.data[paths[0]]);
                initValue = data && data[paths[1]];
            } else {
                initValue = me.config.value;
            }
            var valueObj = {};
            if (initValue) {
                valueObj = initValue
            } else {
                valueObj = { from: '', to: ''}
            }

            renderTemplate()
            function renderTemplate() {
                Template(me.plugin.path + '/template/main/index', me.config.target, function (dom, data, getItem) {
                    var hiddenInput = dom.item(me.config.dataIndex);
                    hiddenInput.val(JSON.stringify(valueObj));

                    dom.item('from').on('input', function () {
                        valueObj.from = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueObj));
                    })
                    dom.item('to').on('input', function () {
                        valueObj.to = $(this).val().trim()
                        hiddenInput.val(JSON.stringify(valueObj));
                    })
                }, {
                    config: {
                        value: me.config.value,
                        valueObj: valueObj,
                        dataIndex: me.config.dataIndex
                    }
                }).render()
            }

        }
    }

    _group.prototype.constructor = _group;

    return function (plugin, success) {
        return new _group(plugin, success);
    }

});