﻿<script id="jiakao-misc-plugin-section2-template-main-index" type="text/html">
    <?
      var valueObj = config.valueObj;
    
    ?>
<style>
    input {
        border-radius: 20px !important;
    }
</style>
<input type="hidden" data-item="<?=config.dataIndex?>" name="<?=config.dataIndex?>" value="<?=config.value?>">
 
<div class="form-group" style="margin-bottom: 10px;">
    <div class="col-sm-10" style="display: flex; align-items: center;">
        <input type="text" data-item="from" style="width:150px" 
            class="form-control group-name<?=index?> group-input" id="from" placeholder="最小值"
            value="<?=valueObj.from?>"> -
    
            <input type="text" data-item="to" style="width:150px"
                class="form-control goods-session-group-highlights<?=index?> highlights-input" placeholder="最大值"
                value="<?=valueObj.to?>" id="to">

 
    </div>
</div>



</script>