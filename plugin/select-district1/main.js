/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";

define(['simple!core/template', 'simple!core/widgets', 'jiakao-misc!plugin/select-district1/district', 'jiakao-misc!plugin/select-district1/district2', 'simple!core/form', 'simple!core/store'],
    function (Template, Widgets, District, District2, Form, Store) {

        var district = function (plugin, success) {
            this.id = plugin.id;
            this.plugin = plugin;
            this.success = success || $.noop;
            this.config = plugin.config;
            this.config.name = this.config.name || [];
            this.config.change = this.config.change || $.noop;
            if (this.config.special) {
                District = District2;
            }
            if (this.config.value) {
                var data = District.getProAndCityByAreaCode(this.config.value)

                // var province = District.getProvinceOfCity(this.config.value);
                // if (province && province.code) {
                this.proCode = data.provinceCode;
                this.cityCode = data.cityCode;
                // } else {
                //     this.proCode = this.config.value;
                //     this.cityCode = '';
                // }
            }

            this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');

            this.render();

        }

        district.prototype = {

            // 渲染省份
            render: function () {
                var me = this;
                var config = me.config;
                var insert = config.insert && config.insert.province;
                var select = me.province = me.createDom(District.provinces(), 'province', insert, me.proCode).select;
                var dom = (config.render && config.render(select)) || select.wrap('<div class="col-sm-4" style="padding-left: 0; padding-right: 2.5px;"></div>').parent();

                select.on('change', function (ev) {
                    me.renderCity(this.value);

                });

                me.target.empty();
                me.target.append(dom);

                me.renderCity(select.val());

            },

            // 渲染城市
            renderCity: function (code) {
                console.log('renderCity', code)
                var me = this;
                var config = me.config;
                var insert = config.insert && config.insert.city;
                var elem = me.createDom(District.getCitysOfProvince(code), 'city', insert, me.cityCode);
                var city = me.target.item(me.id + '-city');
                if (city.size() > 0) {
                    city.html(elem.option).trigger('change');
                    me.city = city;
                } else {

                    var select = me.city = elem.select;
                    var dom = (config.render && config.render(select)) || select.wrap('<div class="col-sm-4" style="padding-left: 0; padding-right: 2.5px;"></div>').parent();
                    select.on('change', function (ev) {
                        me.createName(this.value);
                        config.change.call(this, ev, this.value);
                        me.success.call(me, $('select[name="cityCode"]').val());
                        if (!me.config.hideArea) {
                            me.renderArea(this.value);
                        }

                    });

                    me.target.append(dom);

                    if (me.config.value) {

                        setTimeout(function () {
                            select.trigger('change');
                        }, 300)
                    }

                    if (!me.config.hideArea) {
                        me.renderArea(this.value);
                    }
                }

                me.createName(me.city.val());

            },

            // 渲染区域

            renderArea: function (code) {
                var me = this;
                var config = me.config;
                var insert = config.insert && config.insert.area;
                var elem = me.createDom(District.getAreasOfCity(code), 'area', insert, me.config.value);
                var area = me.target.item(me.id + '-area');
                if (area.size() > 0) {
                    area.html(elem.option).trigger('change');
                    me.area = area;
                } else {
                    var select = me.area = elem.select;
                    var dom = (config.render && config.render(select)) || select.wrap('<div class="col-sm-4" style="padding-left: 1.5px; padding-right: 0;"></div>').parent();
                    dom.append('<input name="areaName" type="hidden">');
                    me.target.append(dom);
                    select.on('change', function (ev) {
                        me.createName(this.value);
                        config.change.call(this, ev, this.value);
                        me.success.call(me, $('select[name="areaCode"]').val());
                        dom.find('[name="areaName"]').val($('select[name="areaCode"]').val() ? select.find("option:selected").text() : '');
                    });

                    if (me.config.value) {
                        setTimeout(function () {
                            select.trigger('change');
                        }, 300)

                    }

                }
                me.createName(me.area.val());

            },


            // 创建DOM
            createDom: function (list, type, insert, defCode) {
                list = list || [];
                insert = insert || [];

                var me = this;
                var config = me.config;
                var option = '';

                for (var i = 0; i < insert.length; i++) {
                    var selected = '';
                    if (defCode == insert[i].code) {
                        selected = 'selected';
                    }
                    option += '<option ' + selected + ' value="' + insert[i].code + '" data-name="' + insert[i].name + '">' + insert[i].name + '</option>';
                }

                for (var i = 0; i < list.length; i++) {
                    var selected = '';
                    if (defCode == list[i].code) {
                        selected = 'selected';
                    }
                    option += '<option ' + selected + ' value="' + list[i].code + '" data-name="' + list[i].name + '">' + list[i].name + '</option>';
                }

                var select = $('<select id="' + type + '"' + (me.config.disabled ? 'disabled="true"' : '') + 'data-item="' + me.id + '-' + type + '" class="form-control"></select>');
                config.style && select.css(config.style);
                select.html(option);

                return {
                    option: option,
                    select: select
                };

            },

            createName: function (value) {
                var me = this;
                if (value) {
                    me.province.removeAttr('name');
                    me.city.attr('name', me.config.name);
                } else {
                    me.province.attr('name', me.config.name);
                    me.city.removeAttr('name');
                }

                if (!me.config.hideArea) {

                    if (!me.area.attr('name')) {
                        me.area.attr('name', me.config['areaName']);
                    }
                }

            }

        }

        district.prototype.constructor = district;

        return function (plugin, success) {
            return new district(plugin, success);
        }

    });