/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";

define(['simple!core/template', 'simple!core/widgets', 'jiakao-misc!plugin/select-district1/district', 'simple!core/form'], function (Template, Widgets, District, Form) {

    var district = function (plugin, success) {
        this.id = plugin.id;
        this.plugin = plugin;
        this.config = plugin.config;
        this.config.name = this.config.name || [];
        this.config.change = this.config.change || $.noop;

        if (this.config.value) {
            this.config.value = this.config.value.toString();
            var province = District.getProvinceOfCity(this.config.value);
            if (province && province.code) {
                this.proCode = province.code;
                this.cityCode = this.config.value;
            } else {
                this.proCode = this.config.value;
                this.cityCode = '';
            }
        }

        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');

        this.render();

        success();

    }

    district.prototype = {

        // 渲染省份
        render: function () {

            var me = this;

            var config = me.config;
            var insert = config.insert && config.insert.province;
            var name = config.pDataIndex || (me.id + '-province'); //查询字段名称
            var select = me.province = me.createDom(District.provinces(), name, insert, me.proCode).select;
            var dom = (config.render && config.render(select)) || select.wrap('<div class="col-sm-6" style="padding-left: 0; padding-right: 2.5px;"></div>').parent();

            select.on('change', function () {
                me.renderCity(this.value);
            });

            me.target.empty();
            me.target.append(dom);

            me.renderCity(select.val());

        },

        // 渲染城市
        renderCity: function (code) {

            var me = this;
            var config = me.config;
            var insert = config.insert && config.insert.city;
            var name = config.cDataIndex || (me.id + '-citys'); //查询字段名称
            var cityData = District.getCitysOfProvince(code);
            District.getCitysOfProvince(code).unshift({ name: '请选择', code, counties: [] })
            var elem = me.createDom(cityData, name, insert, me.cityCode);
            var city = me.target.item(name);
            if (city.size() > 0) {
                city.html(elem.option).trigger('change');
                me.city = city;
            } else {

                var select = me.city = elem.select;
                var dom = (config.render && config.render(select)) || select.wrap('<div class="col-sm-6" style="padding-left: 1.5px; padding-right: 0;"></div>').parent();

                select.on('change', function () {
                    me.createName(this.value);
                    config.change.apply(this, arguments);
                });

                me.target.append(dom);

            }

            me.createName(me.city.val());

        },

        // 创建DOM
        createDom: function (list, eleName, insert, defCode) {

            list = list || [];
            insert = insert || [];

            var me = this;
            var config = me.config;
            var option = '';

            //eleName = eleName ||

            for (var i = 0; i < insert.length; i++) {
                var selected = '';
                if (defCode === insert[i].code) {
                    selected = 'selected';
                }
                option += '<option ' + selected + ' value="' + insert[i].code + '">' + insert[i].name + '</option>';
            }

            for (var i = 0; i < list.length; i++) {
                var selected = '';
                if (defCode === list[i].code) {
                    selected = 'selected';
                }
                option += '<option ' + selected + ' value="' + list[i].code + '">' + list[i].name + '</option>';
            }

            //me.id + '-' + type
            var select = $('<select id="' + eleName + '" name="' + eleName + '" data-item="' + eleName + '" class="form-control"></select>');
            config.style && select.css(config.style);
            select.html(option);

            return {
                option: option,
                select: select
            };

        },

        createName: function (value) {
            var me = this;
            if (me.config.cDataIndex || me.config.pDataIndex) return;
            if (value) {
                me.province.removeAttr('name');
                me.city.attr('name', me.config.name);
            } else {
                me.province.attr('name', me.config.name);
                me.city.removeAttr('name');
            }
        }

    }

    district.prototype.constructor = district;

    return function (plugin, success) {
        return new district(plugin, success);
    }

});