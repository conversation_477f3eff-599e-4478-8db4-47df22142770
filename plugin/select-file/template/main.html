﻿<!--
    * main v1.0
    *
    * name: GG
    * date: 2013/12/25
-->

<script id="jiakao-misc-plugin-select-file-template-main-btn" type="text/html">

    <style>

        .plugin-select-file button {
            position: relative;
        }

        .plugin-select-file button input {
            width: 100%;
            height: 100%;
            position: absolute; 
            top: 0; 
            left: 0;
            opacity: 0;
        }

        .plugin-select-file .file-xxx{ margin-top: 20px; }
        .plugin-select-file .file-xxx h3{ font-size: 15px; font-weight: normal; }
        .plugin-select-file .file-xxx .jindu{ height: 5px; width: 100%; background: #ccc; }
        .plugin-select-file .file-xxx .jindu i{ display: block; width: 0%; height: 100%; background:#5cb85c; transition: width .6s;  }

    </style>

    <div class="plugin-select-file">
        <button type="button" class="btn btn-info" data-item="file-input-btn-qita">
            <i>选择试看视频</i>
           
        </button>
        <input id="file0" type="file" accept="video/*" style="display: none;" />
        <button type="button" style="margin-left: 50px;background: #C35714;border:0;" data-item="go0" class="btn btn-info">上传</button>
        <div class="file-xxx" data-item="file-list0">
            <!--<h3>文件x</h3>
            <div class="jindu"><i></i></div>-->
        </div>
    </div>
    <br/><br/><br/><br/>
    <div class="plugin-select-file">
        <button type="button" class="btn btn-info" data-item="file-input-btn">
            <i>选择完整视频</i>
        </button>
        <input id="file1" type="file" accept="video/*" style="display: none;"/>
        <button type="button" style="margin-left: 50px;background: #C35714;border:0;" data-item="go1" class="btn btn-info">上传</button>
        <div class="file-xxx" data-item="file-list1">
            <!--<h3>文件x</h3>
            <div class="jindu"><i></i></div>-->
        </div>
    </div>
    <div class="plugin-select-file">
        <button type="button" class="btn btn-info" data-item="file-input-btn-hddt">
            <i>选择互动视频地图</i>
        </button>
        <input id="file10" type="file" accept="video/*" style="display: none;"/>
        <button type="button" style="margin-left: 50px;background: #C35714;border:0;" data-item="go10" class="btn btn-info">上传</button>
        <div class="file-xxx" data-item="file-list10">
            <!--<h3>文件x</h3>
            <div class="jindu"><i></i></div>-->
        </div>
    </div>
    <div class="plugin-select-file">
        <button type="button" class="btn btn-info" data-item="file-input-btn-hd">
            <i>选择互动视频</i>
        </button>
        <input id="file11" type="file" accept="video/*" style="display: none;"/>
        <button type="button" style="margin-left: 50px;background: #C35714;border:0;" data-item="go11" class="btn btn-info">上传</button>
        <div class="file-xxx" data-item="file-list11">
            <!--<h3>文件x</h3>
            <div class="jindu"><i></i></div>-->
        </div>
    </div>
    
    <br/><br/><br/><br/>
    <label>
        关联教练视频ID：<input id="fromUploadVideoId" placeholder="关联对应的教练视频id，官方视频填0" style="width: 300px;">
    </label>
    <label style="display:flex">
        视频更新原因：<select id="fromUploadVideoReason" class="form-control" style="width: 300px;">
            <option value="路线更新">路线更新</option>
            <option value="路线错误">路线错误</option>
        </select>
    </label>
    <label tyle="display:flex">
    是否清除旧的难点：<input value="true" type="radio" name="deleteTimeline"><span style="margin-left:5px;margin-right:15px;vertical-align: middle">清除</span>
    <input checked type="radio" value="false" name="deleteTimeline"><span style="margin-left:5px;vertical-align: middle">不清除</span>
    </label>
   
</script>



<script id="video-upload-plugin-select-file-template-main-dup" type="text/html">

    <style>

        .plugin-select-file button {
            position: relative;
        }

        .plugin-select-file button input {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
        }

        .plugin-select-file .file-xxx{ margin-top: 20px; }
        .plugin-select-file .file-xxx h3{ font-size: 15px; font-weight: normal; }
        .plugin-select-file .file-xxx .jindu{ height: 5px; width: 100%; background: #ccc; }
        .plugin-select-file .file-xxx .jindu i{ display: block; width: 0%; height: 100%; background:#5cb85c; transition: width .6s;  }

    </style>

 
      
        <button type="button" style="margin-left: 50px;background: darkolivegreen;border:0;" data-item="go" class="btn btn-info">点击查看</button>

</script>