/*
 * select-images v0.0.1
 *
 * name: GG
 * date: 2013/12/25
 */

"use strict";

define(['simple!core/template', 'simple!core/ajax', 'simple!core/utils', 'simple!core/plugin'], function (Template, Ajax, Utils, Plugin) {

    var images = function (plugin, success) {

        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;

        this.imagePlugin = this.init(plugin.config)
        this.render();
    }

    images.prototype = {
        init: function(config) {
            var _config = {
                dataIndex: config.dataIndex,
                uploadIndex: config.dataIndex,
                bucket: "jiakao-image",
                multiple: config.multiple,
                appSpaceId: '02fb648802fb886a5a5c',
                fileType: 'image',
                isImg: true,
                saveUploadUrl: window.j.host.panda + '/api/admin/upload/save.htm',
                imageSrc: {
                    dataIndex: 'url',
                    suffix: this.config.value && this.config.value.indexOf('jiakao-image') !== -1 ? '!default' : ''
                },
                useSecureUpload: true,
                placeholder: '请选择上传文件',
            }
            $.extend(_config, config);
            return Plugin('jiakao-misc!select-images', _config)
        },
        render: function () {
            this.imagePlugin.render();
        },

    }

    images.prototype.constructor = images;

    return function (plugin, success) {
        return new images(plugin, success);
    }

});
