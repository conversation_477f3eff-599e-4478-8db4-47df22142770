/*
 * select-images v0.0.1
 *
 * name: GG
 * date: 2013/12/25
 */

"use strict";

define(['simple!core/template', 'simple!core/ajax', 'simple!core/utils', 'jiakao-misc!app/common/mc-pan'], function (Template, Ajax, Utils, MCPan) {

    function joinStr(url) {
        return url.match(/\?/gi) ? '&' : '?'
    }

    var images = function (plugin, success) {

        this.plugin = plugin;
        this.success = success;
        this.config = plugin.config;
        this.images = [];
        if (plugin.config.value) {
            if (plugin.config.parse) {
                this.images = plugin.config.parse(plugin.config.value);
            } else {
                this.images = JSON.parse(plugin.config.value);
            }
        }
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');

        // 如果不传，则默认为图片类型：支持文件和图片类型2种选项
        this.config.fileType = this.config.fileType || 'image';
        this.config.isImg = this.config.fileType === 'image';
        // 提交的数据是否需要文件名。当为 true 时，结果必须为 object
        this.config.imageSrc = this.config.imageSrc || {};
        this.config.fileName = this.config.imageSrc.fileNameIndex || '';

        // 是否启用第三版更安全的文件上传功能
        this.isSecureUpload = this.config.useSecureUpload;
        // 是否直接保存文件编码，而不是保存文件真实url
        this.useEncodeData = this.isSecureUpload && !this.config.saveUploadUrl;

        if (this.isSecureUpload) {
            this.config.uploadUrl = this.config.uploadUrl || 'https://cyclops.mucang.cn/api/admin/upload-file/upload.htm';
            this.config.decodeUrl = this.config.decodeUrl || 'https://cyclops.mucang.cn/api/admin/upload-file/decode.htm';
        } else {
            this.config.uploadUrl = this.config.uploadUrl || 'simple-upload3://upload/file.htm';

        }

        // 初始化请求防止上传文件时登录导致的重复上传
        this.initFileReq();
        this.render();
    }

    images.prototype = {

        initFileReq: function () {
            var me = this;

            Ajax.request(this.config.uploadUrl, {
                data: {
                    bucket: 'simple-slect-images-pre-request'
                },
                type: 'get'
            });

            // 处理安全上传和普通上传的图片回显问题
            me.config.formatImgShow = function (imgUrl) {
                imgUrl = imgUrl || '';

                if (me.isSecureUpload && me.config.isImg && me.config.imageSrc.suffix) {
                    imgUrl = imgUrl.replace(/\!.*/gi, '') + me.config.imageSrc.suffix;
                }

                return imgUrl;
            }
        },

        /**
         * 检测文件是否合法
         * @param {*} files 文件列表
         * @returns 
         */
        checkValid: function (files) {
            var me = this;
            var status = true;
            var i;
            var len;
            var item;
            var itemSize;

            if (files.length) {
                if (typeof me.config.sizeLimit === 'number' && me.config.sizeLimit) {
                    for (i = 0, len = files.length; i < len; i++) {
                        item = files[i];
                        itemSize = item.size / 1024;

                        if (itemSize > me.config.sizeLimit) {
                            status = false;
                            Simple.Widgets.dialog.toast('文件大小超出 ' + Utils.format.byte(me.config.sizeLimit * 1024) + ' 限制');

                            break;
                        }
                    }
                }
            }

            return status;
        },

        render: function () {
            var me = this;

            Template(me.plugin.path + '/template/main/btn', me.target, function (dom, data, item, obj) {
                var imageList = item('plugin-image-list');
                var imageInput = item('plugin-image-input');
                var panBtn = item('pan-btn');

                function getLiDom(curFileName, className, tag, imageText) {
                    var li = $('<li data-success="false"><p data-name=' + curFileName + ' class="' + className + '">' + tag + '</p><span></span>' + imageText + '<i type="button" class="close">&times;</i></li>').appendTo(imageList);
                    return li;
                }

                imageList.sortable({
                    stop: function (event, item) {
                        me.handleInput(imageList, imageInput);
                    }
                });

                panBtn.on('click', function() {
                    Simple.Widgets.dialog.html('木仓网盘', {
                        width: 1000,
                        height: 600,
                        class: 'mc-pan-wrap'
                    }).done(function (dialog) {
                        MCPan.renderPan({
                            panel: dialog,
                            type: 'share',
                            multiple: me.config.multiple
                        }, function(itemList) {
                            if (!me.config.multiple) {
                                imageList.empty();
                            }
                            itemList.forEach(tmpUrl => {
                                var curFileName = '';
                                var imageText = '';
                                var className = me.config.isImg ? 'imgp' : 'filep'
                                var tag = me.config.isImg ? '<img />' : ''
                                var li = getLiDom(curFileName, className, tag, imageText)
                                li.find('span').hide();
                                li.attr('data-success', true);
    
                                if (me.config.isImg) {
                                    li.find('img').attr('src', me.config.formatImgShow(tmpUrl)).attr('data-src', tmpUrl);
                                } else {
                                    li.find('.filep').attr('data-src', tmpUrl).text(tmpFileName ? tmpFileName : ('...' + tmpUrl.substring(tmpUrl.length - 30, tmpUrl.length)));
                                }
                            })
                            me.handleInput(imageList, imageInput);
                        });
                    })
                })

                dom.find('input:file').on('change', function () {
                    if (!me.config.multiple && this.files.length > 0) {
                        imageList.empty();
                    }

                    if (!me.checkValid(this.files)) {
                        return false
                    }

                    for (var i = 0; i < this.files.length; i++) {
                        var curFile = this.files[i];
                        var curFileName = curFile.name || '';
                        var imageText = '';

                        if (me.config.imageText) {
                            imageText = '<input type="text" placeholder="' + me.config.imageText.placeholder + '"/>';
                        }
                        var className = me.config.isImg ? 'imgp' : 'filep'
                        var tag = me.config.isImg ? '<img />' : ''
                        var li = getLiDom(curFileName, className, tag, imageText)
                        me.uploadImages(curFile, function (data, li) {
                            data = data || [];
                            data[0] = data[0] || {};
                            var tmpUrl = data[0].url || '';
                            var tmpFileEncode = data[0].encodedData || '';
                            var tmpFileMd5 = data[0].fileMd5 || '';
                            // 第三版文件上车原图 url 无法访问，所有新增一个缩略图，用于界面显示，插件返回的数据还是用原图
                            var tmpFileThumb = data[0][me.config.imageSrc.previewIndex || 'bigStyleUrl'] || data[0].url || '';
                            var tmpFileName = li.find('p').attr('data-name') || '';

                            li.find('span').hide();
                            li.attr('data-success', tmpUrl ? 'true' : 'false');

                            if (!tmpUrl) {
                                console.error('上传失败')

                                return false;
                            }

                            if (me.config.isImg) {
                                li.find('img').attr('src', me.config.formatImgShow(tmpFileThumb)).attr('data-code', tmpFileEncode).attr('data-src', tmpUrl);
                                li.find('.imgp').attr('data-md5', tmpFileMd5);
                            } else {
                                li.find('.filep').attr('data-code', tmpFileEncode).attr('data-src', tmpUrl).attr('data-md5', tmpFileMd5).text(tmpFileName ? tmpFileName : ('...' + tmpUrl.substring(tmpUrl.length - 30, tmpUrl.length)));
                            }

                            me.handleInput(imageList, imageInput);
                        }, function (data, li) {
                            var errMsg = (data && data.message) || '';

                            if (errMsg) {
                                Simple.Widgets.dialog.toast(errMsg);
                            }

                            li.find('span').html('上传失败');
                        }, function (n, li) {
                            li.find('span').html(n + '%');
                        }, li);
                    }
                });

                imageList.delegate('input', 'change', function () {
                    me.handleInput(imageList, imageInput);
                });

                imageList.delegate('.close', 'click', function () {
                    $(this).parent().remove();
                    me.handleInput(imageList, imageInput);
                });

                me.handleInput(imageList, imageInput);
            }, { data: me.images, config: me.config }).render();
        },

        // 提交保存文件
        saveUpload: function (data, cb) {
            var me = this;

            data = (data && data.itemList && data.itemList[0]) || {};
            data.encodedData = data.encodedData || '';

            if (me.useEncodeData) {
                cb && cb([
                    {
                        url: data.previewUrl || '',
                        encodedData: data.encodedData || ''
                    }
                ])
            } else {
                if (data.encodedData) {
                    var saveParams = {}

                    // 第三版文件上传 commit 文件时支持参数自定义 saveUploadKey
                    saveParams[me.config.saveUploadKey || 'encodedDataList'] = data.encodedData

                    Ajax.request(me.config.saveUploadUrl, {
                        data: saveParams,
                        type: 'post',
                        success: function (sRes) {
                            var tmpMd5 = '';

                            if (sRes && typeof sRes === 'string') {
                                tmpMd5 = sRes || '';
                            } else {
                                tmpMd5 = (sRes && sRes[me.config.imageSrc.md5Index]) || '';
                            }

                            me.decodeUpload(data, cb, tmpMd5);
                        },
                        error: function (eRes) {
                            me.decodeUpload(null, cb);
                        }
                    });
                } else {
                    me.decodeUpload(null, cb);
                }
            }
        },

        decodeUpload: function (data, cb, dataMd5) {
            var me = this

            data = data || {}

            if (data.encodedData) {
                Ajax.request(me.config.decodeUrl, {
                    data: {
                        encodedDataList: data.encodedData
                    },
                    type: 'post',
                    success: function (sRes) {
                        var tmpList = (sRes && sRes.itemList) || [];
                        var tmpListFirst = tmpList[0] || {};

                        if (!tmpListFirst.fileMd5) {
                            tmpListFirst.fileMd5 = tmpListFirst[me.config.imageSrc.md5Index] || dataMd5 || '';
                        }
                        if (!tmpListFirst.encodedData) {
                            tmpListFirst.encodedData = data.encodedData
                        }

                        tmpList[0] = tmpListFirst;
                        cb && cb(tmpList);
                    },
                    error: function (eRes) {
                        cb && cb();
                    }
                });
            } else {
                cb && cb();
            }
        },

        uploadImages: function (file, success, error, progress, liDom) {
            var me = this;
            var formdata = new FormData();
            var uploadUrl = me.config.uploadUrl
            uploadUrl = uploadUrl + joinStr(uploadUrl) + 'appSpaceId=' + (me.config.appSpaceId || '')

            // 普通上传
            if (!me.isSecureUpload) {
                formdata.append('bucket', me.config.bucket);
                formdata.append('type', me.config.fileType);

                if (me.config.appSpaceId) {
                    formdata.append('appSpaceId', me.config.appSpaceId);
                }
            }

            formdata.append('files', file);

            Ajax.request(uploadUrl, {
                data: formdata,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    if (me.isSecureUpload) {
                        me.saveUpload(data, function (sData) {
                            success.call(this, sData, liDom);
                        })
                    } else {
                        success.call(this, data, liDom);
                    }
                },
                error: function (data) {
                    error.call(this, data, liDom);
                },
                progress: function (percentage) {
                    progress.call(this, percentage, liDom);
                }
            });
        },

        handleInput: function (imageList, imageInput) {
            var me = this;
            var value = [];

            imageList.find('li[data-success=true]').each(function () {
                var o = {};
                o[me.config.imageSrc.dataIndex] = $(this).find(me.config.isImg ? 'img' : '.filep').attr(me.useEncodeData ? 'data-code' : 'data-src');

                // 指定是否返回 encodedData
                if (me.config.imageSrc.encodedDataIndex) {
                    o[me.config.imageSrc.encodedDataIndex] = $(this).find(me.config.isImg ? 'img' : '.filep').attr('data-code');
                }

                if (me.config.imageText) {
                    o[me.config.imageText.dataIndex] = $(this).find('input').val().trim();
                }
                if (me.config.fileName) {
                    o[me.config.fileName] = $(this).find(me.config.isImg ? '.imgp' : '.filep').attr('data-name') || '';
                }
                if (me.config.imageSrc.md5Index) {
                    o[me.config.imageSrc.md5Index] = $(this).find(me.config.isImg ? '.imgp' : '.filep').attr('data-md5') || '';
                }
                value.push(o);
            });

            if (me.config.stringify) {
                imageInput.val(me.config.stringify(value));
            } else {
                imageInput.val((value && value.length) ? JSON.stringify(value) : '');
            }

        }
    }

    images.prototype.constructor = images;

    return function (plugin, success) {
        return new images(plugin, success);
    }

});
