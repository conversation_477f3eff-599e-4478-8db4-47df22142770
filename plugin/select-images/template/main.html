﻿<!--
    * main v1.0
    *
    * name: GG
    * date: 2013/12/25
-->

<!-- 用法：

xtype: Plugin('simple!select-images', {
    imageSrc: {
        dataIndex: 'img',
    },
    // 是否需要多个上传
    multiple: false,
    // 如果不传，则默认为图片类型
    fileType: 'file',
    // bucket 必须有
    bucket: 'xxx',
    // 值回显：key 和  imageSrc 里的 dataIndex 对应
    value: '[{"img":"https://question-manager-image.baodianjiaoyu.com.cn/question-manager/2021/06/10/18/1843cf83acc544158e8d31394b7046f2.xlsx"}]'
}) -->

<script id="jiakao-misc-plugin-select-images-template-main-btn" type="text/html" >

    <style>

        .plugin-select-images button {
            position: relative;
        }

        .plugin-select-images button input {
            width: 100%;
            height: 100%;
            position: absolute; 
            top: 0; 
            left: 0;
            opacity: 0;
        }

        .plugin-select-images ul li {
            width: 120px;
            height: 90px;
            float: left;
            margin: 10px 10px 0 0;
            overflow: hidden;
            position: relative;
            border: 1px solid #808080;
        }

        .plugin-select-images ul li p {
            line-height: 90px;
        }

        .plugin-select-images ul li .filep{
            position: absolute;
            top: 50%;
            left: 5px;
            right: 5px;
            max-height: 48px;
            line-height: 16px;
            transform: translate3d(0, -50%, 0);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .plugin-select-images ul li p img {
            width: 100%;
            height: 90px;
            object-fit: cover;
        }

        .plugin-select-images ul li span {
            line-height: 90px;
            width: 120px;
            text-align: center;
            color: #000;
            position: absolute;
            top: 0;
            left: 0;
        }

        .plugin-select-images ul li > input {
            position: absolute;
            bottom: 0;
            left: 0;
            border: 0;
            width: 120px;
            box-sizing: border-box;
            padding: 2px;
        }

        .plugin-select-images ul li > .close {
            position: absolute;
            top: 3px;
            right: 3px;
        }

    </style>

    <div class="plugin-select-images">
        <button type="button" class="btn btn-info">
            <? if(!config.isImg) { ?>
                <i>选择文件</i>
                <input type="file" accept="*" <? if(config.multiple){ ?>multiple<? } ?> />
            <? } else { ?>
                <i>选择图片</i>
                <input type="file" accept="image/gif, image/jpeg, image/jpg, image/png" <? if(config.multiple){ ?>multiple<? } ?> />
            <? } ?>
        </button>
         <? if(!config.isNoWangpan) { ?>
        <button type="button" data-item="pan-btn" class="btn btn-danger">网盘上传</button>
        <? } ?>
        <ul data-item="plugin-image-list" class="clearfix">
            <? for(var i = 0 ; i < data.length; i++){ ?>
                <li data-success="true">
                    <? 
                        var tmpUrl = data[i][config.imageSrc.dataIndex] || '';
                        var tmpName = data[i][config.imageSrc.fileNameIndex] || '';
                        var tmpMd5 = data[i][config.imageSrc.md5Index] || '';
                        var tmpCode = data[i][config.imageSrc.encodedDataIndex] || '';
                        if(!config.isImg) { 
                    ?>
                        <p class="filep" data-name="<?= tmpName ?>" data-md5="<?= tmpMd5 ?>" data-src="<?= tmpUrl ?>"><?= tmpName ? tmpName : ('...' + tmpUrl.substring(tmpUrl.length - 30, tmpUrl.length)) ?></p>
                    <? } else { ?>
                        <p class="imgp" data-name="<?= tmpName ?>" data-md5="<?= tmpMd5 ?>"><img data-src="<?= tmpUrl ?>" data-code="<?= tmpCode ?>" src="<?= config.formatImgShow(tmpUrl) ?>"></p>
                    <? } ?>
                    
                    <span></span>

                    <? if(config.imageText){ ?>
                        <input type="text" placeholder="<?=config.imageText.placeholder ?>" value="<?=data[i][config.imageText.dataIndex] ?>" />
                    <? } ?>
                    
                    <i type="button" class="close">&times;</i>
                </li>
            <? } ?>
        </ul>
        <input type="hidden" data-item="plugin-image-input" name="<?= config.dataIndex || (config.column && config.column.dataIndex) || (config.imageSrc && config.imageSrc.dataIndex) ?>" value="<?= config.value ?>" />
    </div>
</script>