﻿/*
 * select-UploadFile v0.0.1
 *
 * name: GG
 * date: 2013/12/25
 */

"use strict";

define(['simple!core/template', 'simple!core/ajax', 'simple!core/widgets', 'simple!core/store'], function (Template, Ajax, Widgets, Store) {


    var DEFAULT_CONFIG = {
        uploadUrl: "", //上传的服务器地址
        fileFiledName: "file", //文件字段名称
        fragmentSize: '3', //每个片段文件的大小，默认为3M
        processCount: 4, //同时进行上传的进程数

        postData: {}, //上传需要提交的其它字段数据

        tryTimes: 3, //片段上传失败重试的次数

        //全部上传完毕
        complete: $.noop,
        //单个片段上传完毕
        processComplete: $.noop
    };

    var statusMap = {};

    var UploadFile = function (plugin, success) {

        this.plugin = plugin;
        this.success = success;
        this.config = $.extend(true, {}, DEFAULT_CONFIG, plugin.config);
        this.UploadFile = [];
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');

        this.config.complete = this.config.complete || $.noop;
        this.config.processComplete = this.config.processComplete || $.noop;

        this.status = "init";

        this.render();
    };

    UploadFile.prototype = {

        render: function () {
            var me = this;

            Widgets.dialog.html("上传文件", '', {
                backdrop: 'static',
                buttons: [
                    {
                        name: '关闭',
                        xtype: 'danger',
                        click: function () {
                            this.close();
                        }
                    }
                ]
            }).done(function (dialog) {
                var filter = 'mp4,avi,rmbv,rm,wmv,mp3'.split(',');

                me.dialog = dialog;
                Store(['jiakao-misc!video-upload/data/bizTypeList']).load([{
                    aliases: 'list'
                }]).done(function (store, data) {
                    var bizTypeList = data.list.data;
                    if (me.config.postData.languageType === 'WEIYU') {
                        bizTypeList = bizTypeList && bizTypeList.filter((res) => {
                            return me.config.postData.bizTypeFilterArray.indexOf(res.key) !== -1
                        })
                    }
                    Template(me.plugin.path + '/template/main/btn', dialog.body, function (dom, data, item, obj) {
                        var imageList0 = item('file-list0');
                        var $fileInput0 = dom.find('#file0');
                        var bizIdDom = dom.item('bizId');
                        var bizTypeDom = dom.item('bizType');

                        function onChange(type) {
                            var li = '';

                            if (!this.files || !this.files[0]) return;
                            for (var i = 0; i < this.files.length; i++) {
                                var fileObj = this.files[i];
                                var format = fileObj.name.match(/\.([^.]+)$/)[1].toLowerCase();
                                // bizIdDom.val(fileObj.name.substr(0,fileObj.name.length-4))
                                if (filter.indexOf(format) >= 0) {
                                    li += '<h3>' + fileObj.name + '(' + parseInt(fileObj.size / 1024 / 1024) + 'MB)</h3><div class="jindu jindu' + i + '"><i></i></div>';
                                }
                            }
                            console.log(li)
                            item('file-list' + type).html(li);

                            me.status = "ready";
                        }

                        function go(type) {
                            console.log(statusMap[type])
                            if (statusMap[type] === "working") return;
                            if (type == 0) {
                                var files = $fileInput0[0].files;
                                var imageList = imageList0;
                            } else if (type == 1) {
                                files = $fileInput1[0].files;
                                imageList = imageList1;
                            }

                            if (!files || !files[0]) {
                                return Widgets.dialog.alert("先选个文件");
                            }

                            if (files.length > me.config.limit) {
                                Widgets.dialog.alert(`最多一次上传${me.config.limit}个视频`);
                                return;
                            }


                            statusMap[type] = "working";
                            console.log("aaa")
                            dom.item("go" + type).prop("disable", true).html("上传中...");

                            var successCount = 0;

                            window.uploadAllData = {};

                            for (var i = 0; i < files.length; i++) {
                                (function (files, i) {
                                    var format = files[i].name.match(/\.([^.]+)$/)[1].toLowerCase();
                                    var $jinduI = imageList.find(".jindu" + i + " i");
                                    if (filter.indexOf(format) === -1) {
                                        return;
                                    }
                                    var name = files[i].name;
                                    Store(['jiakao-misc!video-upload/data/getUploadKey']).save([
                                        {
                                            params: Object.assign({
                                                fileSize: files[i].size,
                                                name: name,
                                                format: format,
                                                bizId: name.substr(0, name.length - 4),
                                                bizType: $('#bizType-inpt').val(),
                                                sceneCode: $('#sceneCode-inpt').val()
                                            }, me.config.postData)
                                        }
                                    ]).done(function (a, b, c, d) {
                                        me.uploadUploadFile(files[i], function (data, li) {
                                            successCount++;

                                            if (successCount === files.length) {
                                                dom.item("go" + type).html('上传成功').attr('disabled', me.config.postData.languageType === 'WEIYU' ? false : true);
                                                $jinduI.width('100%').css('opacity', '1');
                                                me.config.complete.call(me, true, data);
                                            }

                                        }, function (data) {
                                            successCount++;
                                            dom.item("go" + type).html('上传失败');
                                            if (successCount === files.length) {
                                                me.config.complete.call(me, false, data);
                                            }
                                        }, function (n) {

                                            $jinduI.width(n * 100 + '%').css('opacity', '0.5');
                                        }, d.data, files[i].name, type);

                                    }).fail(function (ret) {
                                        dom.item("go" + type).prop("disable", '').html("上传");
                                        statusMap[type] = "";
                                        Widgets.dialog.alert(ret.message)
                                    });


                                })(files, i)
                            }
                        }

                        $fileInput0.on('change', function () {
                            onChange.call(this, 0);
                        });
                        if (me.config.postData.languageType === 'WEIYU') {
                            dom.item('sceneCode').val('');
                            dom.item('sceneCode-group').css('display', 'none');
                        }
                        bizTypeDom.on('change', function () {
                            console.log(bizTypeList, 'bizTypeDatabizTypeData');
                            var value = $(this).val();
                            if (value.includes('video-practice')) {
                                $('#sceneCode').show();
                            } else {
                                $('#sceneCode-inpt').val('');
                                $('#sceneCode').hide();
                            }
                        })

                        dom.item("go0").click(function () {
                            go(0);
                        });

                    }, { data: me.UploadFile, config: me.config, bizTypeList: bizTypeList }).render();

                })

            });
        },

        uploadUploadFile: function (fileObj, success, error, progress, fileKey, fileName, type) {
            var me = this;

            var fileSize = fileObj.size;
            var eFragmentSize = me.config.fragmentSize * 1024 * 1024;
            var fragmentCount = Math.ceil(fileSize / eFragmentSize);

            var todoList = [];
            for (var i = 0; i < fragmentCount; i++) {
                todoList.push(i);
            }
            var successCount = 0;


            //上传单个片段成功
            var oneComplete = function (isSuccess, data) {
                console.log(arguments)
                if (isSuccess) {
                    successCount++;
                    progress(successCount / fragmentCount);

                    if (successCount == fragmentCount) {
                        statusMap[type] = "success";
                        return success(data);
                    }

                    if (todoList.length) {
                        uploadOne(todoList.shift());
                    }
                }
                //失败
                else {
                    error(data);
                }
            };

            //上传单个片段
            var uploadOne = function (fragmentIndex) {

                if (fragmentIndex >= fragmentCount) return;
                var fragmentSize = Math.min(fileSize, (fragmentIndex + 1) * eFragmentSize);
                var fragment = fileObj.slice(fragmentIndex * eFragmentSize, fragmentSize);

                me.uploadFileFragment(fragment, fragmentIndex, fragmentSize, oneComplete, 0, fileKey, fileName, fileSize, type);
            };

            for (var i = 0; i < me.config.processCount; i++) {
                if (todoList.length) {
                    uploadOne(todoList.shift());
                }
            }
        },

        uploadFileFragment: function (fragment, fragmentIndex, fragmentSize, callback, _tryTimes, fileKey, fileName, fileSize, type) {
            var me = this;

            if (statusMap[type] === "fail") return;

            _tryTimes = _tryTimes || 0;

            var formdata = new FormData();
            formdata.append(me.config.fileFiledName, fragment);
            formdata.append("offset", fragmentIndex * me.config.fragmentSize * 1024 * 1024);
            formdata.append("fileSize", fileSize);
            formdata.append("fileKey", fileKey);
            formdata.append("bizId", $('#bizId-inpt').val());
            formdata.append("bizType", $('#bizType-inpt').val());
            formdata.append("sceneCode", $('#sceneCode-inpt').val());


            // formdata.append("type", type);

            for (var key in me.config.postData) {
                formdata.append(key, me.config.postData[key]);
            }

            var handleDuplicate = function (dupId) {

                Widgets.dialog.html("这个" + fileName + "视频已被上传过").done(function (dialog) {

                    me.originDialog = me.dialog;
                    me.dialog = dialog;

                    Template(me.plugin.path + '/template/main/dup', dialog.body, function (dom, data, item, obj) {



                        var id = data.data[0].id;
                        dom.item("go").click(function () {
                            var $idInput = $('[data-item=video][data-frame-parent=true]').find("#id");
                            var $searchBtn = $('[data-item=video][data-frame-parent=true]').find("[data-item=search]").find("button[type=submit]");

                            //me.close();
                            $idInput.val(id);
                            $searchBtn.click();
                            me.close();
                        });


                    }, { data: [{ id: dupId }] }).render();

                });
            };

            Ajax.request(me.config.uploadUrl, {
                data: formdata,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    var duplicateId = data.finish ? data.id : void 0;
                    var str = [];

                    if (data.id && !window.uploadAllData[data.id]) {
                        window.uploadAllData[data.id] = fileName;
                    }

                    if (duplicateId) {
                        // me.config.complete.call(me, true, data);
                        // handleDuplicate(duplicateId);
                        // Widgets.dialog.alert('这个'+fileName+'视频已被上传过，视频ID为 ' + duplicateId);

                        // return ;
                    }
                    callback(true, fragmentIndex, data);
                },
                error: function (data) {
                    if (_tryTimes >= me.config.tryTimes) {
                        console.log("片段" + fragmentIndex + "已经失败" + _tryTimes + "次, 上传任务失败！");
                        statusMap[type] = "fail";
                        return callback(false, fragmentIndex, data);
                    }
                    _tryTimes += 1;
                    me.uploadFileFragment(fragment, fragmentIndex, fragmentSize, callback, _tryTimes, fileKey, fileName, fileSize, type);
                },
                progress: function (percentage) {
                    //progress.call(this, percentage, index);
                }
            });
        },

        close: function () {
            this.dialog.close();
        }
    };

    UploadFile.prototype.constructor = UploadFile;

    return function (plugin, success) {
        return new UploadFile(plugin, success);
    }

});
