﻿<!--
    * main v1.0
    *
    * name: GG
    * date: 2013/12/25
-->

<script id="jiakao-misc-plugin-select-single-file-template-main-btn" type="text/html" >

    <style>

        .plugin-select-file {
            padding: 10px 55px 10px 55px;
        }
        .plugin-select-file button {
            position: relative;
        }

        .plugin-select-file button input {
            width: 100%;
            height: 100%;
            position: absolute; 
            top: 0; 
            left: 0;
            opacity: 0;
        }

        .plugin-select-file .file-xxx{ margin-top: 20px; }
        .plugin-select-file .file-xxx h3{ font-size: 15px; font-weight: normal; }
        .plugin-select-file .file-xxx .jindu{ height: 5px; width: 100%; background: #ccc; }
        .plugin-select-file .file-xxx .jindu i{ display: block; width: 0%; height: 100%; background:#5cb85c; transition: width .6s;  }

    </style>
<div class="clearfix form-horizontal">
    <!--<div class="form-group " data-item="bizType-group">-->
        <!--<label for="bizId" class="col-sm-3 control-label">业务ID：</label>-->
        <!--<div class="col-sm-8">-->
            <!--<input data-item="bizId" type="text" id="bizId-inpt" name="bizType" class="form-control " placeholder="业务ID">-->
        <!--</div>-->
    <!--</div>-->
    <div class="form-group" data-item="bizType-group">
        <label for="bizType" class="col-sm-3 control-label">业务类型：</label>
        <div class="col-sm-8">

            <select data-item="bizType" type="text" id="bizType-inpt" name="bizType" class="form-control">
                <? for(var i=0; i<bizTypeList.length; i++){?>
                    <option value="<?=bizTypeList[i].key?>"><?=bizTypeList[i].value?></option>
                <?}?>
            </select>
        </div>
    </div>

    <div class="form-group" id="sceneCode" data-item="sceneCode-group">
        <label for="sceneCode" class="col-sm-3 control-label">场景:</label>
        <div class="col-sm-8">
            <select data-item="sceneCode" type="text" id="sceneCode-inpt" name="sceneCode" class="form-control">
                    <option value="101">基础场景</option>
                    <option value="102">扣满12分</option>
                    <option value="103">维语场景</option>
            </select>
        </div>
    </div>

    <div class="plugin-select-file">
        <button type="button" class="btn btn-info">
            <i>选择视频</i>
            <input id="file0" type="file" multiple accept="video/*" />
        </button>
        <button type="button" style="margin-left: 50px;background: #C35714;border:0;" data-item="go0" class="btn btn-info">上传</button>
        <div class="file-xxx" data-item="file-list0">
            <!--<h3>文件x</h3>
            <div class="jindu"><i></i></div>-->
        </div>
    </div>

</div>


</script>



<script id="video-upload-plugin-select-file-template-main-dup" type="text/html" >

    <style>

        .plugin-select-file button {
            position: relative;
            padding-left: 55px;
        }

        .plugin-select-file button input {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
        }

        .plugin-select-file .file-xxx{ margin-top: 20px; }
        .plugin-select-file .file-xxx h3{ font-size: 15px; font-weight: normal; }
        .plugin-select-file .file-xxx .jindu{ height: 5px; width: 100%; background: #ccc; }
        .plugin-select-file .file-xxx .jindu i{ display: block; width: 0%; height: 100%; background:#5cb85c; transition: width .6s;  }

    </style>

 
      
        <button type="button" style="margin-left: 50px;background: darkolivegreen;border:0;" data-item="go" class="btn btn-info">点击查看</button>

</script>
