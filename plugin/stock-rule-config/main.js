/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";
//输入文字，自动提示匹配字符
define(['simple!core/template', 'simple!core/store', 'simple!core/form', 'simple!core/plugin'], function (Template, Store, Form, Plugin) {

    var banner = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = plugin.config;
        this.store = this.config.store || [];
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    }

    banner.prototype = {

        render: function () {
            var me = this;

            Template(me.plugin.path + '/template/main/index', me.target, function (dom, data, $item) {
                function lineDom(item) {
                    item = item || {}
                    return `<div class="line">
                                <div class="title">
                                    <p>时段<span class="index"></span></p>
                                </div>
                                <div class="data">
                                    直播开始后
                                    <input data-item="start" type="text" value="${item.start || ''}" style="width:60px;padding: 0;"> ~
                                    <input data-item="end" type="text" value="${item.end || ''}" style="width:60px;padding: 0;">
                                </div>
                                <div class="data">
                                    每30S，自动售卖
                                    <input data-item="lower" type="text" value="${item.lower || ''}" style="width:60px;padding: 0;"> ~
                                    <input data-item="upper" type="text" value="${item.upper || ''}" style="width:60px;padding: 0;"> 单
                                    <button type="button" class="btn-danger btn" style="float: right;" data-item="delete">删除</button>
                                </div>
                                
                    </div>`
                }
                var $v = $item('view')

                function setIndex() {
                    $v.find('.index').each(function(index, item){
                        $(item).html(index+1)
                    })
                }

                $item('add').on('click', function () {
                    $item('config').append(lineDom())
                    setIndex()
                });
                $v.on('click', "[data-item='delete']", function () {
                    $(this).parents('.line').remove()
                    setIndex()
                });

                Store(['jiakao-misc!live-stock-rule/data/view'], [{
                    aliases: 'list',
                    params: {
                        id: me.config.id
                    }
                }]).load().done(function (store) {
                    var data = store.data.list.data
                    var autoDecrConfig = JSON.parse(data.autoDecrConfig || '[]')
                    if(autoDecrConfig && autoDecrConfig.length){
                        autoDecrConfig.forEach((item, i) => {
                            $item('config').append(lineDom({
                                start: item.durationStart,
                                end: item.durationEnd,
                                lower: item.autoDecrMin,
                                upper: item.autoDecrMax,
                            }))
                        })
                    }else{
                        $item('config').append(lineDom())
                    }
                    setIndex()
                }).fail(function () {});

                me.success(); //这个函数一定要执行，否则无法调用回调函数
            }).render();

        }
    }

    banner.prototype.constructor = banner;

    return function (plugin, success) {
        return new banner(plugin, success);
    }

});