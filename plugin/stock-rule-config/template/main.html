<script id="jiakao-misc-plugin-stock-rule-config-template-main-index" type="text/html">
    <style>
        .stock-rule{
            margin-top: -20px;
        }
        .stock-rule .line {
            border-bottom: 1px solid #dfdfdf;
            padding: 10px 0 20px 20px;
        }
        .stock-rule .title {
            position: relative;
            height: 35px;
        }
        .stock-rule .title::after {
            content: '';
            position: absolute;
            width: 236px;
            left: 50%;
            transform: translateX(-50%);
            top: 50%;
            border-bottom: 1px solid #000;

        }
        .stock-rule .title p {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            position: absolute;
            transform: translateX(-50%);
            left: 50%;
            width: 104px;
            background-color: #fff;
            z-index: 1;
        }
        .stock-rule .data {
            padding: 5px 0;
        }
    </style>
    <div class="stock-rule" data-item="view">
        <div data-item="config">
            
        </div>
        <button style="margin-top: 10px;" type='button' class="btn btn-primary" data-item='add'>新增时段</button>
        <p>区间内随机取数，库存扣完即止，不会出现负数</p>
    </div>
</script>