﻿/*
 * main v0.0.1
 *
 * name: caifuqing
 * date: 16.8.3
 */

"use strict";
define([], function () {

    var Store = Simple.Store;
    var Template = Simple.Template;

    var CONFIG = {
        value : '', //默认值
        width : '', //宽度,
        height : '', //高度,
        renderAfter : null
    };

    var UmClient = function (plugin, success) {
        this.plugin = plugin;
        this.success = success || $.noop;
        this.config = $.extend({}, CONFIG, plugin.config);
        this.target = $(this.config.target || '[data-plugin=' + plugin.id + ']');
        this.render();
    };

    UmClient.prototype = {

        // 渲染
        render: function () {
            var me = this;

            setTimeout(function(){

                if( !me.config.width ){
                    me.config.width = me.config.target.width() || 300;
                    me.config.height = me.config.width * .4;
                }

                return Simple.UED.create(me.target, me.config).then( me.config.renderAfter);

            }, 200);

        }

    };

    UmClient.prototype.constructor = UmClient;

    return function (plugin, success) {
        return new UmClient(plugin, success);
    }

});