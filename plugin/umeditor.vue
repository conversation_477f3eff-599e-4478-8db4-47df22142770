<template>
    <div class="clearfix">
        <div class="col-sm-9" style="padding-left: 0;">
            <div style="position: relative">
                <input :name="props.config.dataIndex" :value="value" type="hidden">
                <div id="myEditor"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, nextTick } from 'vue';
const emit = defineEmits(['update:value'])

const props = defineProps({
    /** 这个属性是自定义组件本身需要的配置 */
    options: {
        ueConfig: Object,
    },
    /** 这个是该组件对应的表单列配置，不需要传 */
    config: Object,
    /** 这个是表单字段的值，不需要传 */
    value: String
});

onMounted(() => {
    Simple.UM.ready().then(() => {
        const um = window.UM.getEditor('myEditor', props.options.ueConfig)
        if (props.value) {
            um.ready(async function() {
                await nextTick();
                setTimeout(() => {
                    um.setContent(props.value);
                }, 0)
            });
        }
        um.addListener('contentchange',function(){
            const html = um.getContent();
            emit("update:value", html);
        });
    })
})

onUnmounted(() => {
    const um = window.UM.getEditor('myEditor');
    if (um) {
        um.destroy();
    }
})

</script>