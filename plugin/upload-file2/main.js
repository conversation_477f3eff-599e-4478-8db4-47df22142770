﻿/*
 * product-filter v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/store', 'simple!core/form', 'simple!core/utils', 'simple!core/ajax'], function (Template, Table, Store, Form, Utils, Ajax) {
    var me = null;
    var report = function (plugin, success) {
        me = this;
        this.plugin = plugin;
        this.success = success;
        this.config = this.plugin.config;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        // 加载控件
        this.render();
    }
    report.prototype = {

        // 渲染内容
        render: function () {
            Template(this.plugin.path + '/template/main/index', this.target, function (dom, data, item) {
                var process = item('server-manager-process');
                var value = item('server-manager-value');

                dom.find('input:file').on('change', function () {
                    if (me.ajax) {
                        me.ajax.abort();
                    }
                    process.attr('class', 'server-manager-upload-process').css('width', '0%').html('');
                    value.val('');
                    if (this.files.length > 0) {
                        console.log(this.files[0])
                        me.success(me, this.files[0]);
                        $(this).val('')
                        // me.upload(this.files[0], function (data, status) {
                        //     if (data) {
                        //         value.val(data.uuid);
                        //     }

                        //     process.addClass('success').text('文件上传成功！');
                        //     me.success(data, status);
                        // }, function (data) {
                        //     process.addClass('error').text('上传失败,请重新上传！');
                        // }, function (percentage) {
                        //     process.css('width', percentage + '%');
                        // });
                    }
                });
            }, { config: this.config }).render();
        },
        upload: function (file, success, error, progress) {
            console.log(this.plugin.config)
            var formData = new FormData();
            formData.append('bucket', me.config.bucket);
            formData.append('type', 'file');
            formData.append(this.plugin.config.dataIndex, file);
            console.log(formData)
            this.ajax = Ajax.request(me.config.url, {
                data: formData,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    success.call(this, data, true);
                },
                error: function (data) {
                    error.call(this, data, false);
                },
                progress: function (percentage) {
                    progress.call(this, percentage);
                }
            });
        }

    }

    report.prototype.constructor = report;

    return function (plugin, success) {
        return new report(plugin, success);
    }

});