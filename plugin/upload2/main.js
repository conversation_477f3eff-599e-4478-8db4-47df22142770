﻿/*
 * product-filter v0.0.1
 *
 * name: xiaojia
 * date: 2013/11/19
 */

"use strict";

define(['simple!core/template', 'simple!core/table', 'simple!core/store', 'simple!core/form', 'simple!core/utils', 'simple!core/ajax', 'simple!core/widgets'], function (Template, Table, Store, Form, Utils, Ajax, Widgets) {
    var report = function (plugin, success) {
        this.plugin = plugin;
        this.success = success;
        this.config = this.plugin.config;
        this.target = $(plugin.config.target || '[data-plugin=' + plugin.id + ']');
        // 加载控件
        this.render();
    }
    report.prototype = {

        // 渲染内容
        render: function () {
            var me = this;
            Template(this.plugin.path + '/template/main/index', this.target, function (dom, data, item) {
                var process = item('bug-manage-process');
                var value = item('bug-manage-value');
                var uploadDom = item('upload-successed');
                if (me.config.isSingle) {
                    var images = '';
                    if (me.config.value) {
                        images = me.config.value;
                        value.val(images);
                        var previewImage = $('<div class="image-wraper"><video class="upload-image" src="' + images + '"></video><i type="button" class="close-icon">x</i></div>').appendTo(uploadDom);
                        previewImage.find('.close-icon').on('click', function () {
                            value.val('');
                            previewImage.remove();
                        });
                    }
                } else {
                    images = {};
                    if (me.config.value) {
                        images = me.formatData(JSON.parse(me.config.value));
                        value.val(me.getValue(images));
                        if (images) {
                            for (var key in images) {
                                (function (key) {
                                    var previewImage = $('<div class="image-wraper"><video class="upload-image" src="' + images[key].url + '"></video><i type="button" class="close-icon">x</i></div>').appendTo(uploadDom);
                                    previewImage.find('.close-icon').on('click', function () {
                                        delete images[key];
                                        value.val(me.getValue(images));
                                        previewImage.remove();
                                    });
                                })(key)
                            }
                        }
                    }
                }
                dom.find('input:file').on('change', function () {
                    process.attr('class', 'bug-manage-upload-process').css('width', '0%').html('');
                    value.val('');
                    if ((!me.config.isSingle) && (this.files.length + me.getImageLength(images)) > me.config.maxnum) {
                        Widgets.dialog.alert('上传图片数量不得超过' + me.config.maxnum + '张！');
                        return;
                    }
                    if (this.files.length > 0) {
                        for (var i = 0; i < this.files.length; i++) {
                            var filename = this.files[i].name;
                            var size = parseInt(this.files[i].size / 1024) + 'KB';
                            var processDiv = $('<div style="float: left"></div>');
                            (function (processDiv, files, filename, size) {
                                me.upload(files[i], function (data) {
                                    var data = data[0];
                                    var size = data.length;
                                    if (me.config.isSingle) {
                                        var imageUrl = data;

                                        if (typeof imageUrl == 'object') {
                                            imageUrl = data.url;
                                        }
                                        uploadDom.html('');
                                        value.val(imageUrl);
                                    } else {
                                        var flag = (Math.random() * 100).toFixed(2);
                                        images[flag] = data;
                                        imageUrl = data.url;

                                        value.val(me.getValue(images))
                                    }
                                    processDiv.appendTo(uploadDom);
                                    var uploadSucc = $('<div class="image-wraper"><video class="upload-image" src="' + imageUrl + '"></video><i type="button" class="close-icon">x</i></div>').appendTo(processDiv);
                                    uploadSucc.find('.close-icon').on('click', function () {
                                        if (me.config.isSingle) {
                                            value.val('');
                                        } else {
                                            delete images[flag];
                                            value.val(me.getValue(images));
                                        }
                                        uploadSucc.parent().remove();
                                    });
                                    process.addClass('success').text('文件上传成功！');
                                    me.success.call && me.success.call(me, true, imageUrl, size);


                                }, function (data) {
                                    process.addClass('error').text('上传失败,请重新上传！');
                                    me.error.call && me.error.call(me, false);
                                }, function (percentage) {
                                    process.css('width', percentage + '%');
                                });
                            })(processDiv, this.files, filename, size);
                        }

                    }
                });
            }, {config: this.config}).render();
        },
        upload: function (file, success, error, progress) {
            var me = this;
            var formData = new FormData();
            formData.append('bucket', me.config.bucket);
            formData.append('type', 'file');
            formData.append(me.config.uploadIndex, file);
            this.ajax = Ajax.request(me.config.url, {
                data: formData,
                type: 'post',
                contentType: false,
                processData: false,
                success: function (data) {
                    success.call(this, data);
                },
                error: function (data) {
                    error.call(this, data);
                },
                progress: function (percentage) {
                    progress.call(this, percentage);
                }
            });
        },

        getImageLength: function (obj) {
            var count = 0;
            for (var key in obj) {
                count++;
            }
            return count;
        },

        formatData: function (obj) {

            if (obj.length == 0) {
                return {};
            }
            var temp = {};
            for (var i = 0; i < obj.length; i++) {
                var flag = (Math.random() * 100).toFixed(2);
                temp[flag] = obj[i];
            }
            return temp;
        },

        getValue: function (obj) {
            var temp = [];
            if (obj) {
                for (var key in obj) {
                    temp.push(obj[key]);
                }
                return JSON.stringify(temp);
            }
            return JSON.stringify(temp);
        }
    }


    report.prototype.constructor = report;

    return function (plugin, success) {
        return new report(plugin, success);
    }

});
