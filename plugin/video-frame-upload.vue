<template>
    <div class="clearfix">
        <div class="col-sm-9" style="padding-left: 0;">
            <div style="position: relative">
                <input :name="config.dataIndex" :value="value" type="hidden">
                <input type="text" readonly :placeholder="options.placeholder" class="form-control" style="border-radius: 2px;" />
            </div>
        </div>
        <button type="button" class="btn btn-default col-sm-3" style="position: relative;" @click="videoModelVisible = true">
          <span>选择截图</span>
          <inpu style="width: 100%; height: 34px; position: absolute; top: 0; left: 0; opacity: 0;" class="form-control" />
        </button>
    </div>
    <div class="form-group clearfix upload-success">
      <div class="image-wraper" v-if="value">
        <img class="upload-image" v-if="isImg" :src="value">
        <div style="margin-top: 18px; cursor: pointer;" v-else @click="toFile(value)">{{ getFileName(value) }}</div>
        <i type="button" @click="removeItem" class="close-icon">x</i>
      </div>
    </div>
    <a-modal v-model:visible="videoModelVisible" :width="860" :zIndex="1050" title="视频">
        <div class="video-wrap" v-if="videoModelVisible">
            <video crossOrigin ref="videoRef" controls :src="options.videoUrl" />
        </div>
        <template #footer>
          <a-button type="primary" :loading="loading" @click="onSumit">
            保存 <template v-if="loading">
                {{ process }}
            </template>
        </a-button>
        </template>
    </a-modal>
</template>

<script setup>
import { ref } from 'vue'
import Ajax from 'simple!core/ajax';
import { Modal as AModal, Button as AButton } from 'ant-design-vue';

const { options, value, config } = defineProps({
    /** 这个属性是自定义组件本身需要的配置 */
    options: {
        placeholder: String,
        videoUrl: String,
    },
    /** 这个是该组件对应的表单列配置，不需要传 */
    config: Object,
    /** 这个是表单字段的值，不需要传 */
    value: String
});
const fileType = 'image'
const videoRef = ref(null);
let videoModelVisible = ref(false)
let isImg = ref(fileType === 'image')
let loading = ref(false)
let process = ref(0)
function getVideoFrame() {
	return new Promise(resolve => {
        if (videoRef.value) {
            const video = videoRef.value
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
            canvas.toBlob(blob => {
                const file = new File([blob], 'xx.png', { type: 'image/png' });
                resolve({ file });
            });
        }
	});
}
function upload (file, success, error, progress) {
    var formData = new FormData();
    formData.append('bucket', options.bucket);
    formData.append('type', fileType);
    formData.append(config.dataIndex, file);

    Ajax.request('simple-upload3://upload/file.htm'+'?wenaho=1', {
        data: formData,
        type: 'post',
        contentType: false,
        processData: false,
        success: function (data) {
            success.call(this, data);
        },
        error: function (data) {
            error.call(this, data);
        },
        progress: function (percentage) {
            progress.call(this, percentage);
        }
    });
}
// 这个事件用来更新表单字段的值
const emit = defineEmits(['update:value'])

const getFileName = function (path) {
  var parts = path.split('\/');
  return parts[parts.length - 1];
}

async function onSumit(){
    const {file} = await getVideoFrame()
    loading.value = true
    process.value = 0
    upload(file, function(data) {
        var data = data[0];
        var imageUrl = data;
        if(typeof imageUrl == 'object'){
            imageUrl = data.url;
        }
        loading.value = false
        emit("update:value", imageUrl);
        videoModelVisible.value = false
    }, function(data) {
        loading.value = false
        console.log(data)
    }, function(data) {
        console.log(data)
        process.value = data
    })
}

function removeItem() {
    emit("update:value", '');
}

function toFile(file) {
    window.open(file)
}

</script>
<style scoped>
.bug-manage-upload-process {
    height: 100%;
    width: 0%;
    background: #b0a758;
    position: absolute;
    left: 0;
    padding: 8px 0;
    border-radius: 4px;
    top: 0;
    text-align: center;
    background: rgba(19,122,58,0.5);
    color:#fff;
    line-height: 100%;
}
.bug-manage-upload-process.success {
    background: rgba(19,122,58,0.9);
    width: 100%;
}
.bug-manage-upload-process.error {
    color: red;
    background: rgba(246,216,212,0.9);
    width: 100%;
}
.upload-success .image-wraper {
    width:110px;
    height:100px;
    position: relative;
    margin: 10px 0 0 15px;
    display: inline-block;
    background: #f9f9f9;
}
.upload-success .upload-image {
    width: 110px;
    height: 100px;
}
.upload-success .close-icon{
    position: absolute;
    top: 0;
    right: 0;
    color: white;
    font-weight: bold;
    width: 20px;
    height: 20px;
    background: #000;
    border-radius: 10px;
    opacity: 0.7;
    text-align: center;
    cursor: pointer
}
.video-wrap {
    video {
        width: 100%;
    }
}
</style>