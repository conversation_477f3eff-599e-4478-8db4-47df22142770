.tag {
    margin: 4px 2px;
    display: inline-block;
    padding: 1px 4px;
    font-size: 12px;
    color: #666;
    border: 1px solid #666;
    border-radius: 2px;
}

.tag-red {
    color: #f3630c;
    border: 1px solid #f3630c;
}

.tag-green {
    color: #29bf6d;
    border: 1px solid #29bf6d;
}

.tag-purple {
    color: #a922c1;
    border: 1px solid #a922c1;
}

td img {
    width: 30%;
}

.html-content img {
    width: 204px;
}

.topic-car-vote .vote-pk-img img {
    width: 60% !important;
    margin: 0 auto !important;
}

.topic-wraper .image-content img {
    width: 10% !important;
}

.icon-round {
    width: 50px;
    border-radius: 50%;
}

//.text-content{
//  padding: 10px 5px 0 10px;
//  border: 1px solid #ccc;
//  border-radius: 10px;
//}
.topic-getCar {
    padding: 10px !important;
    h4 {
        padding-bottom: 10px;
        font-size: 16px;
    }
    .topic-table-car {
        max-width: 400px;
        border: 1px solid #ccc;
        border-radius: 3px;
        > div {
            position: relative;
            padding: 10px;
            background: #f7f7f7 url("images/arr_r.png") no-repeat right center;
            -webkit-background-size: 26px auto;
            background-size: 26px auto;
            img {
                position: absolute;
                top: 50%;
                -webkit-transform: translate3d(0, -50%, 0);
                transform: translate3d(0, -50%, 0);
                height: 50px;
            }
            p {
                -webkit-box-sizing: border-box;
                box-sizing: border-box;
                padding: 10px 30px 10px 100px;
            }
        }
    }
    table {
        width: 100%;
        tr {
            border-top: 1px solid #ccc;
        }
        td {
            padding: 6px 10px;
            text-align: center;
            &:not(:last-child) {
                font-weight: 600;
                border-right: 1px solid #ccc;
            }
        }
        .td-in {
            padding: 0 10px;
            span {
                display: inline-block;
                margin: 0 2px 2px;
                padding: 3px 4px;
                line-height: 100%;
                color: #fff;
                background: #91b9f7;
                border-radius: 2px;
            }
        }
    }
}

.red-dot {
    &:before {
        content: "";
        position: absolute;
        display: block;
        width: 8px;
        height: 8px;
        margin: 8px 0 0 56px;
        background: red;
        border-radius: 50%;
        -webkit-animation: cFlash 1.1s ease-in-out infinite alternate;
        -o-animation: cFlash 1.1s ease-in-out infinite alternate;
        animation: cFlash 1.1s ease-in-out infinite alternate;
    }
}

.red-d {
    &:before {
        content: "";
        position: absolute;
        display: block;
        width: 8px;
        height: 8px;
        margin: 17px 0 0 63px;
        background: red;
        border-radius: 50%;
        -webkit-animation: cFlash 1.1s ease-in-out infinite alternate;
        -o-animation: cFlash 1.1s ease-in-out infinite alternate;
        animation: cFlash 1.1s ease-in-out infinite alternate;
    }
}

@-webkit-keyframes cFlash {
    from {
        background: red;
    }
    to {
        background: orange;
    }
}

@keyframes cFlash {
    from {
        background: red;
    }
    to {
        background: orange;
    }
}

.isRead {
    background: #333 !important;
    //background: #ccc!important;
    -webkit-filter: grayscale(100%);
}

.input-col {
    overflow: hidden;
    label {
        line-height: 34px;
    }
    input {
        border-radius: 20px
    }
}

.tailorImgBox {
    margin-bottom: 20px;
}

.fixedTip {
    z-index: 99;
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    padding: 10px;
    -webkit-transform: translate3D(0, -50%, 0);
    transform: translate3D(0, -50%, 0);
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
}

.disable{
    pointer-events: none;
}
.topic-audio-control{
    display: none;
}