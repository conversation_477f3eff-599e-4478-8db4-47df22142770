
<!DOCTYPE html>
<html dir="ltr" lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>ViewerJS</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0"/>

    <link rel="stylesheet" href="viewer.css">
    <style>
        *{
            margin: 0;
            padding: 0;
        }
        li{
            list-style-type:none;

        }
        li img{
            width: 100%;
            height: auto;
        }
    </style>
    <script src="jquery-2.0.3.min.js"></script>
    <script src="viewer.js"></script>

</head>

<body>
<ul id="viewer">
    <!--<li><img src="http://cheyouquan.image.mucang.cn/saturn-image/2016/09/06/11/a581abd9883a4c498074908c26bf8868" alt="图片1"></li>
    <li><img src="http://cheyouquan.image.mucang.cn/saturn-image/2016/09/06/11/78fe61712f364e41872eec7e5201ff66" alt="图片2"></li>
    <li><img src="http://cheyouquan.image.mucang.cn/saturn-image/2016/09/06/11/a581abd9883a4c498074908c26bf8868" alt="图片3"></li>-->
</ul>
</body>
</html>
<script>

    function createViewer(imgList){
        if(!imgList || imgList.length == 0) return;

        var html=[];
        var $dom = $('#viewer');
        for(var i=0;i<imgList.length;i++){
            html.push('<li><img src="'+imgList[i]+'"></li>')
        }
        $dom.html(html.join(''));

        $dom.viewer();
    }

</script>