﻿(function(n,t){function kt(n){var t=n.length,r=i.type(n);return i.isWindow(n)?!1:n.nodeType===1&&t?!0:r==="array"||r!=="function"&&(t===0||typeof t=="number"&&t>0&&t-1 in n)}function ku(n){var t=dt[n]={};return i.each(n.match(h)||[],function(n,i){t[i]=!0}),t}function c(){Object.defineProperty(this.cache={},0,{get:function(){return{}}}),this.expando=i.expando+Math.random()}function pi(n,i,r){var u;if(r===t&&n.nodeType===1)if(u="data-"+i.replace(yi,"-$1").toLowerCase(),r=n.getAttribute(u),typeof r=="string"){try{r=r==="true"?!0:r==="false"?!1:r==="null"?null:+r+""===r?+r:vi.test(r)?JSON.parse(r):r}catch(e){}f.set(n,i,r)}else r=t;return r}function ht(){return!0}function p(){return!1}function di(){try{return u.activeElement}catch(n){}}function nr(n,t){while((n=n[t])&&n.nodeType!==1);return n}function ni(n,t,r){if(i.isFunction(t))return i.grep(n,function(n,i){return!!t.call(n,i,n)!==r});if(t.nodeType)return i.grep(n,function(n){return n===t!==r});if(typeof t=="string"){if(uf.test(t))return i.filter(t,n,r);t=i.filter(t,n)}return i.grep(n,function(n){return et.call(t,n)>=0!==r})}function fr(n,t){return i.nodeName(n,"table")&&i.nodeName(t.nodeType===1?t:t.firstChild,"tr")?n.getElementsByTagName("tbody")[0]||n.appendChild(n.ownerDocument.createElement("tbody")):n}function af(n){return n.type=(n.getAttribute("type")!==null)+"/"+n.type,n}function vf(n){var t=cf.exec(n.type);return t?n.type=t[1]:n.removeAttribute("type"),n}function ti(n,t){for(var u=n.length,i=0;i<u;i++)r.set(n[i],"globalEval",!t||r.get(t[i],"globalEval"))}function er(n,t){var e,c,o,s,h,l,a,u;if(t.nodeType===1){if(r.hasData(n)&&(s=r.access(n),h=r.set(t,s),u=s.events,u)){delete h.handle,h.events={};for(o in u)for(e=0,c=u[o].length;e<c;e++)i.event.add(t,o,u[o][e])}f.hasData(n)&&(l=f.access(n),a=i.extend({},l),f.set(t,a))}}function o(n,r){var u=n.getElementsByTagName?n.getElementsByTagName(r||"*"):n.querySelectorAll?n.querySelectorAll(r||"*"):[];return r===t||r&&i.nodeName(n,r)?i.merge([n],u):u}function yf(n,t){var i=t.nodeName.toLowerCase();i==="input"&&rr.test(n.type)?t.checked=n.checked:(i==="input"||i==="textarea")&&(t.defaultValue=n.defaultValue)}function lr(n,t){if(t in n)return t;for(var r=t.charAt(0).toUpperCase()+t.slice(1),u=t,i=cr.length;i--;)if(t=cr[i]+r,t in n)return t;return u}function d(n,t){return n=t||n,i.css(n,"display")==="none"||!i.contains(n.ownerDocument,n)}function ct(t){return n.getComputedStyle(t,null)}function ar(n,t){for(var e,u,s,o=[],f=0,h=n.length;f<h;f++)(u=n[f],u.style)&&(o[f]=r.get(u,"olddisplay"),e=u.style.display,t?(o[f]||e!=="none"||(u.style.display=""),u.style.display===""&&d(u)&&(o[f]=r.access(u,"olddisplay",df(u.nodeName)))):o[f]||(s=d(u),(e&&e!=="none"||!s)&&r.set(u,"olddisplay",s?e:i.css(u,"display"))));for(f=0;f<h;f++)(u=n[f],u.style)&&(t&&u.style.display!=="none"&&u.style.display!==""||(u.style.display=t?o[f]||"":"none"));return n}function vr(n,t,i){var r=wf.exec(t);return r?Math.max(0,r[1]-(i||0))+(r[2]||"px"):t}function yr(n,t,r,u,f){for(var e=r===(u?"border":"content")?4:t==="width"?1:0,o=0;e<4;e+=2)r==="margin"&&(o+=i.css(n,r+v[e],!0,f)),u?(r==="content"&&(o-=i.css(n,"padding"+v[e],!0,f)),r!=="margin"&&(o-=i.css(n,"border"+v[e]+"Width",!0,f))):(o+=i.css(n,"padding"+v[e],!0,f),r!=="padding"&&(o+=i.css(n,"border"+v[e]+"Width",!0,f)));return o}function pr(n,t,r){var e=!0,u=t==="width"?n.offsetWidth:n.offsetHeight,f=ct(n),o=i.support.boxSizing&&i.css(n,"boxSizing",!1,f)==="border-box";if(u<=0||u==null){if(u=w(n,t,f),(u<0||u==null)&&(u=n.style[t]),ii.test(u))return u;e=o&&(i.support.boxSizingReliable||u===n.style[t]),u=parseFloat(u)||0}return u+yr(n,t,r||(o?"border":"content"),e,f)+"px"}function df(n){var r=u,t=sr[n];return t||(t=wr(n,r),t!=="none"&&t||(k=(k||i("<iframe frameborder='0' width='0' height='0'/>").css("cssText","display:block !important")).appendTo(r.documentElement),r=(k[0].contentWindow||k[0].contentDocument).document,r.write("<!doctype html><html><body>"),r.close(),t=wr(n,r),k.detach()),sr[n]=t),t}function wr(n,t){var r=i(t.createElement(n)).appendTo(t.body),u=i.css(r[0],"display");return r.remove(),u}function ri(n,t,r,u){var f;if(i.isArray(t))i.each(t,function(t,i){r||ne.test(n)?u(n,i):ri(n+"["+(typeof i=="object"?t:"")+"]",i,r,u)});else if(r||i.type(t)!=="object")u(n,t);else for(f in t)ri(n+"["+f+"]",t[f],r,u)}function iu(n){return function(t,r){typeof t!="string"&&(r=t,t="*");var u,f=0,e=t.toLowerCase().match(h)||[];if(i.isFunction(r))while(u=e[f++])u[0]==="+"?(u=u.slice(1)||"*",(n[u]=n[u]||[]).unshift(r)):(n[u]=n[u]||[]).push(r)}}function ru(n,t,r,u){function e(s){var h;return f[s]=!0,i.each(n[s]||[],function(n,i){var s=i(t,r,u);if(typeof s!="string"||o||f[s]){if(o)return!(h=s)}else return t.dataTypes.unshift(s),e(s),!1}),h}var f={},o=n===ei;return e(t.dataTypes[0])||!f["*"]&&e("*")}function oi(n,r){var u,f,e=i.ajaxSettings.flatOptions||{};for(u in r)r[u]!==t&&((e[u]?n:f||(f={}))[u]=r[u]);return f&&i.extend(!0,n,f),n}function oe(n,i,r){for(var o,f,e,s,h=n.contents,u=n.dataTypes;u[0]==="*";)u.shift(),o===t&&(o=n.mimeType||i.getResponseHeader("Content-Type"));if(o)for(f in h)if(h[f]&&h[f].test(o)){u.unshift(f);break}if(u[0]in r)e=u[0];else{for(f in r){if(!u[0]||n.converters[f+" "+u[0]]){e=f;break}s||(s=f)}e=e||s}if(e)return e!==u[0]&&u.unshift(e),r[e]}function se(n,t,i,r){var h,u,f,s,e,o={},c=n.dataTypes.slice();if(c[1])for(f in n.converters)o[f.toLowerCase()]=n.converters[f];for(u=c.shift();u;)if(n.responseFields[u]&&(i[n.responseFields[u]]=t),!e&&r&&n.dataFilter&&(t=n.dataFilter(t,n.dataType)),e=u,u=c.shift(),u)if(u==="*")u=e;else if(e!=="*"&&e!==u){if(f=o[e+" "+u]||o["* "+u],!f)for(h in o)if(s=h.split(" "),s[1]===u&&(f=o[e+" "+s[0]]||o["* "+s[0]],f)){f===!0?f=o[h]:o[h]!==!0&&(u=s[0],c.unshift(s[1]));break}if(f!==!0)if(f&&n.throws)t=f(t);else try{t=f(t)}catch(l){return{state:"parsererror",error:f?l:"No conversion from "+e+" to "+u}}}return{state:"success",data:t}}function fu(){return setTimeout(function(){b=t}),b=i.now()}function eu(n,t,i){for(var u,f=(tt[t]||[]).concat(tt["*"]),r=0,e=f.length;r<e;r++)if(u=f[r].call(i,t,n))return u}function ou(n,t,r){var e,o,s=0,l=vt.length,f=i.Deferred().always(function(){delete c.elem}),c=function(){if(o)return!1;for(var s=b||fu(),t=Math.max(0,u.startTime+u.duration-s),h=t/u.duration||0,i=1-h,r=0,e=u.tweens.length;r<e;r++)u.tweens[r].run(i);return f.notifyWith(n,[u,i,t]),i<1&&e?t:(f.resolveWith(n,[u]),!1)},u=f.promise({elem:n,props:i.extend({},t),opts:i.extend(!0,{specialEasing:{}},r),originalProperties:t,originalOptions:r,startTime:b||fu(),duration:r.duration,tweens:[],createTween:function(t,r){var f=i.Tween(n,u.opts,t,r,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(f),f},stop:function(t){var i=0,r=t?u.tweens.length:0;if(o)return this;for(o=!0;i<r;i++)u.tweens[i].run(1);return t?f.resolveWith(n,[u,t]):f.rejectWith(n,[u,t]),this}}),h=u.props;for(ve(h,u.opts.specialEasing);s<l;s++)if(e=vt[s].call(u,n,h,u.opts),e)return e;return i.map(h,eu,u),i.isFunction(u.opts.start)&&u.opts.start.call(n,u),i.fx.timer(i.extend(c,{elem:n,anim:u,queue:u.opts.queue})),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always)}function ve(n,t){var r,f,e,u,o;for(r in n)if(f=i.camelCase(r),e=t[f],u=n[r],i.isArray(u)&&(e=u[1],u=n[r]=u[0]),r!==f&&(n[f]=u,delete n[r]),o=i.cssHooks[f],o&&"expand"in o){u=o.expand(u),delete n[f];for(r in u)r in n||(n[r]=u[r],t[r]=e)}else t[f]=e}function ye(n,u,f){var e,a,p,v,s,w,l=this,y={},h=n.style,c=n.nodeType&&d(n),o=r.get(n,"fxshow");f.queue||(s=i._queueHooks(n,"fx"),s.unqueued==null&&(s.unqueued=0,w=s.empty.fire,s.empty.fire=function(){s.unqueued||w()}),s.unqueued++,l.always(function(){l.always(function(){s.unqueued--,i.queue(n,"fx").length||s.empty.fire()})})),n.nodeType===1&&("height"in u||"width"in u)&&(f.overflow=[h.overflow,h.overflowX,h.overflowY],i.css(n,"display")==="inline"&&i.css(n,"float")==="none"&&(h.display="inline-block")),f.overflow&&(h.overflow="hidden",l.always(function(){h.overflow=f.overflow[0],h.overflowX=f.overflow[1],h.overflowY=f.overflow[2]}));for(e in u)if(a=u[e],le.exec(a)){if(delete u[e],p=p||a==="toggle",a===(c?"hide":"show"))if(a==="show"&&o&&o[e]!==t)c=!0;else continue;y[e]=o&&o[e]||i.style(n,e)}if(!i.isEmptyObject(y)){o?"hidden"in o&&(c=o.hidden):o=r.access(n,"fxshow",{}),p&&(o.hidden=!c),c?i(n).show():l.done(function(){i(n).hide()}),l.done(function(){var t;r.remove(n,"fxshow");for(t in y)i.style(n,t,y[t])});for(e in y)v=eu(c?o[e]:0,e,l),e in o||(o[e]=v.start,c&&(v.end=v.start,v.start=e==="width"||e==="height"?1:0))}}function e(n,t,i,r,u){return new e.prototype.init(n,t,i,r,u)}function yt(n,t){var r,i={height:n},u=0;for(t=t?1:0;u<4;u+=2-t)r=v[u],i["margin"+r]=i["padding"+r]=n;return t&&(i.opacity=i.width=n),i}function su(n){return i.isWindow(n)?n:n.nodeType===9&&n.defaultView}var hi,it,rt=typeof t,hu=n.location,u=n.document,ci=u.documentElement,cu=n.jQuery,lu=n.$,ut={},ft=[],pt="2.0.3",li=ft.concat,wt=ft.push,a=ft.slice,et=ft.indexOf,au=ut.toString,bt=ut.hasOwnProperty,vu=pt.trim,i=function(n,t){return new i.fn.init(n,t,hi)},ot=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,h=/\S+/g,yu=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,ai=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,pu=/^-ms-/,wu=/-([\da-z])/gi,bu=function(n,t){return t.toUpperCase()},st=function(){u.removeEventListener("DOMContentLoaded",st,!1),n.removeEventListener("load",st,!1),i.ready()},dt,f,r,vi,yi,si,lt;i.fn=i.prototype={jquery:pt,constructor:i,init:function(n,r,f){var e,o;if(!n)return this;if(typeof n=="string"){if(e=n.charAt(0)==="<"&&n.charAt(n.length-1)===">"&&n.length>=3?[null,n,null]:yu.exec(n),e&&(e[1]||!r)){if(e[1]){if(r=r instanceof i?r[0]:r,i.merge(this,i.parseHTML(e[1],r&&r.nodeType?r.ownerDocument||r:u,!0)),ai.test(e[1])&&i.isPlainObject(r))for(e in r)i.isFunction(this[e])?this[e](r[e]):this.attr(e,r[e]);return this}return o=u.getElementById(e[2]),o&&o.parentNode&&(this.length=1,this[0]=o),this.context=u,this.selector=n,this}return!r||r.jquery?(r||f).find(n):this.constructor(r).find(n)}return n.nodeType?(this.context=this[0]=n,this.length=1,this):i.isFunction(n)?f.ready(n):(n.selector!==t&&(this.selector=n.selector,this.context=n.context),i.makeArray(n,this))},selector:"",length:0,toArray:function(){return a.call(this)},get:function(n){return n==null?this.toArray():n<0?this[this.length+n]:this[n]},pushStack:function(n){var t=i.merge(this.constructor(),n);return t.prevObject=this,t.context=this.context,t},each:function(n,t){return i.each(this,n,t)},ready:function(n){return i.ready.promise().done(n),this},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(n){var i=this.length,t=+n+(n<0?i:0);return this.pushStack(t>=0&&t<i?[this[t]]:[])},map:function(n){return this.pushStack(i.map(this,function(t,i){return n.call(t,i,t)}))},end:function(){return this.prevObject||this.constructor(null)},push:wt,sort:[].sort,splice:[].splice},i.fn.init.prototype=i.fn,i.extend=i.fn.extend=function(){var o,e,u,r,s,h,n=arguments[0]||{},f=1,l=arguments.length,c=!1;for(typeof n=="boolean"&&(c=n,n=arguments[1]||{},f=2),typeof n=="object"||i.isFunction(n)||(n={}),l===f&&(n=this,--f);f<l;f++)if((o=arguments[f])!=null)for(e in o)(u=n[e],r=o[e],n!==r)&&(c&&r&&(i.isPlainObject(r)||(s=i.isArray(r)))?(s?(s=!1,h=u&&i.isArray(u)?u:[]):h=u&&i.isPlainObject(u)?u:{},n[e]=i.extend(c,h,r)):r!==t&&(n[e]=r));return n},i.extend({expando:"jQuery"+(pt+Math.random()).replace(/\D/g,""),noConflict:function(t){return n.$===i&&(n.$=lu),t&&n.jQuery===i&&(n.jQuery=cu),i},isReady:!1,readyWait:1,holdReady:function(n){n?i.readyWait++:i.ready(!0)},ready:function(n){(n===!0?--i.readyWait:i.isReady)||(i.isReady=!0,n!==!0&&--i.readyWait>0)||(it.resolveWith(u,[i]),i.fn.trigger&&i(u).trigger("ready").off("ready"))},isFunction:function(n){return i.type(n)==="function"},isArray:Array.isArray,isWindow:function(n){return n!=null&&n===n.window},isNumeric:function(n){return!isNaN(parseFloat(n))&&isFinite(n)},type:function(n){return n==null?String(n):typeof n=="object"||typeof n=="function"?ut[au.call(n)]||"object":typeof n},isPlainObject:function(n){if(i.type(n)!=="object"||n.nodeType||i.isWindow(n))return!1;try{if(n.constructor&&!bt.call(n.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}return!0},isEmptyObject:function(n){var t;for(t in n)return!1;return!0},error:function(n){throw new Error(n);},parseHTML:function(n,t,r){if(!n||typeof n!="string")return null;typeof t=="boolean"&&(r=t,t=!1),t=t||u;var f=ai.exec(n),e=!r&&[];return f?[t.createElement(f[1])]:(f=i.buildFragment([n],t,e),e&&i(e).remove(),i.merge([],f.childNodes))},parseJSON:JSON.parse,parseXML:function(n){var r,u;if(!n||typeof n!="string")return null;try{u=new DOMParser,r=u.parseFromString(n,"text/xml")}catch(f){r=t}return(!r||r.getElementsByTagName("parsererror").length)&&i.error("Invalid XML: "+n),r},noop:function(){},globalEval:function(n){var t,r=eval;n=i.trim(n),n&&(n.indexOf("use strict")===1?(t=u.createElement("script"),t.text=n,u.head.appendChild(t).parentNode.removeChild(t)):r(n))},camelCase:function(n){return n.replace(pu,"ms-").replace(wu,bu)},nodeName:function(n,t){return n.nodeName&&n.nodeName.toLowerCase()===t.toLowerCase()},each:function(n,t,i){var u,r=0,f=n.length,e=kt(n);if(i){if(e){for(;r<f;r++)if(u=t.apply(n[r],i),u===!1)break}else for(r in n)if(u=t.apply(n[r],i),u===!1)break}else if(e){for(;r<f;r++)if(u=t.call(n[r],r,n[r]),u===!1)break}else for(r in n)if(u=t.call(n[r],r,n[r]),u===!1)break;return n},trim:function(n){return n==null?"":vu.call(n)},makeArray:function(n,t){var r=t||[];return n!=null&&(kt(Object(n))?i.merge(r,typeof n=="string"?[n]:n):wt.call(r,n)),r},inArray:function(n,t,i){return t==null?-1:et.call(t,n,i)},merge:function(n,i){var f=i.length,u=n.length,r=0;if(typeof f=="number")for(;r<f;r++)n[u++]=i[r];else while(i[r]!==t)n[u++]=i[r++];return n.length=u,n},grep:function(n,t,i){var u,f=[],r=0,e=n.length;for(i=!!i;r<e;r++)u=!!t(n[r],r),i!==u&&f.push(n[r]);return f},map:function(n,t,i){var u,r=0,e=n.length,o=kt(n),f=[];if(o)for(;r<e;r++)u=t(n[r],r,i),u!=null&&(f[f.length]=u);else for(r in n)u=t(n[r],r,i),u!=null&&(f[f.length]=u);return li.apply([],f)},guid:1,proxy:function(n,r){var f,e,u;return(typeof r=="string"&&(f=n[r],r=n,n=f),!i.isFunction(n))?t:(e=a.call(arguments,2),u=function(){return n.apply(r||this,e.concat(a.call(arguments)))},u.guid=n.guid=n.guid||i.guid++,u)},access:function(n,r,u,f,e,o,s){var h=0,l=n.length,c=u==null;if(i.type(u)==="object"){e=!0;for(h in u)i.access(n,r,h,u[h],!0,o,s)}else if(f!==t&&(e=!0,i.isFunction(f)||(s=!0),c&&(s?(r.call(n,f),r=null):(c=r,r=function(n,t,r){return c.call(i(n),r)})),r))for(;h<l;h++)r(n[h],u,s?f:f.call(n[h],h,r(n[h],u)));return e?n:c?r.call(n):l?r(n[0],u):o},now:Date.now,swap:function(n,t,i,r){var f,u,e={};for(u in t)e[u]=n.style[u],n.style[u]=t[u];f=i.apply(n,r||[]);for(u in t)n.style[u]=e[u];return f}}),i.ready.promise=function(t){return it||(it=i.Deferred(),u.readyState==="complete"?setTimeout(i.ready):(u.addEventListener("DOMContentLoaded",st,!1),n.addEventListener("load",st,!1))),it.promise(t)},i.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(n,t){ut["[object "+t+"]"]=t.toLowerCase()}),hi=i(u),function(n,t){function u(n,t,i,r){var p,u,f,l,w,a,k,c,g,d;if((t?t.ownerDocument||t:y)!==s&&nt(t),t=t||s,i=i||[],!n||typeof n!="string")return i;if((l=t.nodeType)!==1&&l!==9)return[];if(v&&!r){if(p=or.exec(n))if(f=p[1]){if(l===9)if(u=t.getElementById(f),u&&u.parentNode){if(u.id===f)return i.push(u),i}else return i;else if(t.ownerDocument&&(u=t.ownerDocument.getElementById(f))&&ot(t,u)&&u.id===f)return i.push(u),i}else{if(p[2])return b.apply(i,t.getElementsByTagName(n)),i;if((f=p[3])&&e.getElementsByClassName&&t.getElementsByClassName)return b.apply(i,t.getElementsByClassName(f)),i}if(e.qsa&&(!h||!h.test(n))){if(c=k=o,g=t,d=l===9&&n,l===1&&t.nodeName.toLowerCase()!=="object"){for(a=pt(n),(k=t.getAttribute("id"))?c=k.replace(cr,"\\$&"):t.setAttribute("id",c),c="[id='"+c+"'] ",w=a.length;w--;)a[w]=c+wt(a[w]);g=ti.test(n)&&t.parentNode||t,d=a.join(",")}if(d)try{return b.apply(i,g.querySelectorAll(d)),i}catch(tt){}finally{k||t.removeAttribute("id")}}}return pr(n.replace(vt,"$1"),t,i,r)}function ri(){function n(i,u){return t.push(i+=" ")>r.cacheLength&&delete n[t.shift()],n[i]=u}var t=[];return n}function c(n){return n[o]=!0,n}function l(n){var t=s.createElement("div");try{return!!n(t)}catch(i){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function ui(n,t){for(var u=n.split("|"),i=n.length;i--;)r.attrHandle[u[i]]=t}function bi(n,t){var i=t&&n,r=i&&n.nodeType===1&&t.nodeType===1&&(~t.sourceIndex||vi)-(~n.sourceIndex||vi);if(r)return r;if(i)while(i=i.nextSibling)if(i===t)return-1;return n?1:-1}function lr(n){return function(t){var i=t.nodeName.toLowerCase();return i==="input"&&t.type===n}}function ar(n){return function(t){var i=t.nodeName.toLowerCase();return(i==="input"||i==="button")&&t.type===n}}function rt(n){return c(function(t){return t=+t,c(function(i,r){for(var u,f=n([],i.length,t),e=f.length;e--;)i[u=f[e]]&&(i[u]=!(r[u]=i[u]))})})}function ki(){}function pt(n,t){var e,f,s,o,i,h,c,l=li[n+" "];if(l)return t?0:l.slice(0);for(i=n,h=[],c=r.preFilter;i;){(!e||(f=ir.exec(i)))&&(f&&(i=i.slice(f[0].length)||i),h.push(s=[])),e=!1,(f=rr.exec(i))&&(e=f.shift(),s.push({value:e,type:f[0].replace(vt," ")}),i=i.slice(e.length));for(o in r.filter)(f=yt[o].exec(i))&&(!c[o]||(f=c[o](f)))&&(e=f.shift(),s.push({value:e,type:o,matches:f}),i=i.slice(e.length));if(!e)break}return t?i.length:i?u.error(n):li(n,h).slice(0)}function wt(n){for(var t=0,r=n.length,i="";t<r;t++)i+=n[t].value;return i}function fi(n,t,i){var r=t.dir,u=i&&r==="parentNode",f=di++;return t.first?function(t,i,f){while(t=t[r])if(t.nodeType===1||u)return n(t,i,f)}:function(t,i,e){var h,s,c,l=p+" "+f;if(e){while(t=t[r])if((t.nodeType===1||u)&&n(t,i,e))return!0}else while(t=t[r])if(t.nodeType===1||u)if(c=t[o]||(t[o]={}),(s=c[r])&&s[0]===l){if((h=s[1])===!0||h===ht)return h===!0}else if(s=c[r]=[l],s[1]=n(t,i,e)||ht,s[1]===!0)return!0}}function ei(n){return n.length>1?function(t,i,r){for(var u=n.length;u--;)if(!n[u](t,i,r))return!1;return!0}:n[0]}function bt(n,t,i,r,u){for(var e,o=[],f=0,s=n.length,h=t!=null;f<s;f++)(e=n[f])&&(!i||i(e,r,u))&&(o.push(e),h&&t.push(f));return o}function oi(n,t,i,r,u,f){return r&&!r[o]&&(r=oi(r)),u&&!u[o]&&(u=oi(u,f)),c(function(f,e,o,s){var l,c,a,p=[],y=[],w=e.length,k=f||yr(t||"*",o.nodeType?[o]:o,[]),v=n&&(f||!t)?bt(k,p,n,o,s):k,h=i?u||(f?n:w||r)?[]:e:v;if(i&&i(v,h,o,s),r)for(l=bt(h,y),r(l,[],o,s),c=l.length;c--;)(a=l[c])&&(h[y[c]]=!(v[y[c]]=a));if(f){if(u||n){if(u){for(l=[],c=h.length;c--;)(a=h[c])&&l.push(v[c]=a);u(null,h=[],l,s)}for(c=h.length;c--;)(a=h[c])&&(l=u?it.call(f,a):p[c])>-1&&(f[l]=!(e[l]=a))}}else h=bt(h===e?h.splice(w,h.length):h),u?u(null,e,h,s):b.apply(e,h)})}function si(n){for(var s,u,i,e=n.length,h=r.relative[n[0].type],c=h||r.relative[" "],t=h?1:0,l=fi(function(n){return n===s},c,!0),a=fi(function(n){return it.call(s,n)>-1},c,!0),f=[function(n,t,i){return!h&&(i||t!==lt)||((s=t).nodeType?l(n,t,i):a(n,t,i))}];t<e;t++)if(u=r.relative[n[t].type])f=[fi(ei(f),u)];else{if(u=r.filter[n[t].type].apply(null,n[t].matches),u[o]){for(i=++t;i<e;i++)if(r.relative[n[i].type])break;return oi(t>1&&ei(f),t>1&&wt(n.slice(0,t-1).concat({value:n[t-2].type===" "?"*":""})).replace(vt,"$1"),u,t<i&&si(n.slice(t,i)),i<e&&si(n=n.slice(i)),i<e&&wt(n))}f.push(u)}return ei(f)}function vr(n,t){var f=0,i=t.length>0,e=n.length>0,o=function(o,h,c,l,a){var y,g,k,w=[],d=0,v="0",nt=o&&[],tt=a!=null,it=lt,ut=o||e&&r.find.TAG("*",a&&h.parentNode||h),rt=p+=it==null?1:Math.random()||.1;for(tt&&(lt=h!==s&&h,ht=f);(y=ut[v])!=null;v++){if(e&&y){for(g=0;k=n[g++];)if(k(y,h,c)){l.push(y);break}tt&&(p=rt,ht=++f)}i&&((y=!k&&y)&&d--,o&&nt.push(y))}if(d+=v,i&&v!==d){for(g=0;k=t[g++];)k(nt,w,h,c);if(o){if(d>0)while(v--)nt[v]||w[v]||(w[v]=nr.call(l));w=bt(w)}b.apply(l,w),tt&&!o&&w.length>0&&d+t.length>1&&u.uniqueSort(l)}return tt&&(p=rt,lt=it),nt};return i?c(o):o}function yr(n,t,i){for(var r=0,f=t.length;r<f;r++)u(n,t[r],i);return i}function pr(n,t,i,u){var s,f,o,c,l,h=pt(n);if(!u&&h.length===1){if(f=h[0]=h[0].slice(0),f.length>2&&(o=f[0]).type==="ID"&&e.getById&&t.nodeType===9&&v&&r.relative[f[1].type]){if(t=(r.find.ID(o.matches[0].replace(k,d),t)||[])[0],!t)return i;n=n.slice(f.shift().value.length)}for(s=yt.needsContext.test(n)?0:f.length;s--;){if(o=f[s],r.relative[c=o.type])break;if((l=r.find[c])&&(u=l(o.matches[0].replace(k,d),ti.test(f[0].type)&&t.parentNode||t))){if(f.splice(s,1),n=u.length&&wt(f),!n)return b.apply(i,u),i;break}}}return kt(n,h)(u,t,!v,i,ti.test(n)),i}var ut,e,ht,r,ct,hi,kt,lt,g,nt,s,a,v,h,tt,at,ot,o="sizzle"+-new Date,y=n.document,p=0,di=0,ci=ri(),li=ri(),ai=ri(),ft=!1,dt=function(n,t){return n===t?(ft=!0,0):0},st=typeof t,vi=-2147483648,gi={}.hasOwnProperty,w=[],nr=w.pop,tr=w.push,b=w.push,yi=w.slice,it=w.indexOf||function(n){for(var t=0,i=this.length;t<i;t++)if(this[t]===n)return t;return-1},gt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",f="[\\x20\\t\\r\\n\\f]",et="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",pi=et.replace("w","w#"),wi="\\["+f+"*("+et+")"+f+"*(?:([*^$|!~]?=)"+f+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+pi+")|)|)"+f+"*\\]",ni=":("+et+")(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|"+wi.replace(3,8)+")*)|.*)\\)|)",vt=new RegExp("^"+f+"+|((?:^|[^\\\\])(?:\\\\.)*)"+f+"+$","g"),ir=new RegExp("^"+f+"*,"+f+"*"),rr=new RegExp("^"+f+"*([>+~]|"+f+")"+f+"*"),ti=new RegExp(f+"*[+~]"),ur=new RegExp("="+f+"*([^\\]'\"]*)"+f+"*\\]","g"),fr=new RegExp(ni),er=new RegExp("^"+pi+"$"),yt={ID:new RegExp("^#("+et+")"),CLASS:new RegExp("^\\.("+et+")"),TAG:new RegExp("^("+et.replace("w","w*")+")"),ATTR:new RegExp("^"+wi),PSEUDO:new RegExp("^"+ni),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+f+"*(even|odd|(([+-]|)(\\d*)n|)"+f+"*(?:([+-]|)"+f+"*(\\d+)|))"+f+"*\\)|)","i"),bool:new RegExp("^(?:"+gt+")$","i"),needsContext:new RegExp("^"+f+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+f+"*((?:-\\d)?\\d*)"+f+"*\\)|)(?=[^-]|$)","i")},ii=/^[^{]+\{\s*\[native \w/,or=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,sr=/^(?:input|select|textarea|button)$/i,hr=/^h\d$/i,cr=/'|\\/g,k=new RegExp("\\\\([\\da-f]{1,6}"+f+"?|("+f+")|.)","ig"),d=function(n,t,i){var r="0x"+t-65536;return r!==r||i?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,r&1023|56320)};try{b.apply(w=yi.call(y.childNodes),y.childNodes),w[y.childNodes.length].nodeType}catch(wr){b={apply:w.length?function(n,t){tr.apply(n,yi.call(t))}:function(n,t){for(var i=n.length,r=0;n[i++]=t[r++];);n.length=i-1}}}hi=u.isXML=function(n){var t=n&&(n.ownerDocument||n).documentElement;return t?t.nodeName!=="HTML":!1},e=u.support={},nt=u.setDocument=function(n){var t=n?n.ownerDocument||n:y,i=t.defaultView;return t===s||t.nodeType!==9||!t.documentElement?s:(s=t,a=t.documentElement,v=!hi(t),i&&i.attachEvent&&i!==i.top&&i.attachEvent("onbeforeunload",function(){nt()}),e.attributes=l(function(n){return n.className="i",!n.getAttribute("className")}),e.getElementsByTagName=l(function(n){return n.appendChild(t.createComment("")),!n.getElementsByTagName("*").length}),e.getElementsByClassName=l(function(n){return n.innerHTML="<div class='a'><\/div><div class='a i'><\/div>",n.firstChild.className="i",n.getElementsByClassName("i").length===2}),e.getById=l(function(n){return a.appendChild(n).id=o,!t.getElementsByName||!t.getElementsByName(o).length}),e.getById?(r.find.ID=function(n,t){if(typeof t.getElementById!==st&&v){var i=t.getElementById(n);return i&&i.parentNode?[i]:[]}},r.filter.ID=function(n){var t=n.replace(k,d);return function(n){return n.getAttribute("id")===t}}):(delete r.find.ID,r.filter.ID=function(n){var t=n.replace(k,d);return function(n){var i=typeof n.getAttributeNode!==st&&n.getAttributeNode("id");return i&&i.value===t}}),r.find.TAG=e.getElementsByTagName?function(n,t){if(typeof t.getElementsByTagName!==st)return t.getElementsByTagName(n)}:function(n,t){var i,r=[],f=0,u=t.getElementsByTagName(n);if(n==="*"){while(i=u[f++])i.nodeType===1&&r.push(i);return r}return u},r.find.CLASS=e.getElementsByClassName&&function(n,t){if(typeof t.getElementsByClassName!==st&&v)return t.getElementsByClassName(n)},tt=[],h=[],(e.qsa=ii.test(t.querySelectorAll))&&(l(function(n){n.innerHTML="<select><option selected=''><\/option><\/select>",n.querySelectorAll("[selected]").length||h.push("\\["+f+"*(?:value|"+gt+")"),n.querySelectorAll(":checked").length||h.push(":checked")}),l(function(n){var i=t.createElement("input");i.setAttribute("type","hidden"),n.appendChild(i).setAttribute("t",""),n.querySelectorAll("[t^='']").length&&h.push("[*^$]="+f+"*(?:''|\"\")"),n.querySelectorAll(":enabled").length||h.push(":enabled",":disabled"),n.querySelectorAll("*,:x"),h.push(",.*:")})),(e.matchesSelector=ii.test(at=a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&l(function(n){e.disconnectedMatch=at.call(n,"div"),at.call(n,"[s!='']:x"),tt.push("!=",ni)}),h=h.length&&new RegExp(h.join("|")),tt=tt.length&&new RegExp(tt.join("|")),ot=ii.test(a.contains)||a.compareDocumentPosition?function(n,t){var r=n.nodeType===9?n.documentElement:n,i=t&&t.parentNode;return n===i||!!(i&&i.nodeType===1&&(r.contains?r.contains(i):n.compareDocumentPosition&&n.compareDocumentPosition(i)&16))}:function(n,t){if(t)while(t=t.parentNode)if(t===n)return!0;return!1},dt=a.compareDocumentPosition?function(n,i){if(n===i)return ft=!0,0;var r=i.compareDocumentPosition&&n.compareDocumentPosition&&n.compareDocumentPosition(i);return r?r&1||!e.sortDetached&&i.compareDocumentPosition(n)===r?n===t||ot(y,n)?-1:i===t||ot(y,i)?1:g?it.call(g,n)-it.call(g,i):0:r&4?-1:1:n.compareDocumentPosition?-1:1}:function(n,i){var r,u=0,o=n.parentNode,s=i.parentNode,f=[n],e=[i];if(n===i)return ft=!0,0;if(o&&s){if(o===s)return bi(n,i)}else return n===t?-1:i===t?1:o?-1:s?1:g?it.call(g,n)-it.call(g,i):0;for(r=n;r=r.parentNode;)f.unshift(r);for(r=i;r=r.parentNode;)e.unshift(r);while(f[u]===e[u])u++;return u?bi(f[u],e[u]):f[u]===y?-1:e[u]===y?1:0},t)},u.matches=function(n,t){return u(n,null,null,t)},u.matchesSelector=function(n,t){if((n.ownerDocument||n)!==s&&nt(n),t=t.replace(ur,"='$1']"),e.matchesSelector&&v&&(!tt||!tt.test(t))&&(!h||!h.test(t)))try{var i=at.call(n,t);if(i||e.disconnectedMatch||n.document&&n.document.nodeType!==11)return i}catch(r){}return u(t,s,null,[n]).length>0},u.contains=function(n,t){return(n.ownerDocument||n)!==s&&nt(n),ot(n,t)},u.attr=function(n,i){(n.ownerDocument||n)!==s&&nt(n);var f=r.attrHandle[i.toLowerCase()],u=f&&gi.call(r.attrHandle,i.toLowerCase())?f(n,i,!v):t;return u===t?e.attributes||!v?n.getAttribute(i):(u=n.getAttributeNode(i))&&u.specified?u.value:null:u},u.error=function(n){throw new Error("Syntax error, unrecognized expression: "+n);},u.uniqueSort=function(n){var r,u=[],t=0,i=0;if(ft=!e.detectDuplicates,g=!e.sortStable&&n.slice(0),n.sort(dt),ft){while(r=n[i++])r===n[i]&&(t=u.push(i));while(t--)n.splice(u[t],1)}return n},ct=u.getText=function(n){var r,i="",u=0,t=n.nodeType;if(t){if(t===1||t===9||t===11){if(typeof n.textContent=="string")return n.textContent;for(n=n.firstChild;n;n=n.nextSibling)i+=ct(n)}else if(t===3||t===4)return n.nodeValue}else for(;r=n[u];u++)i+=ct(r);return i},r=u.selectors={cacheLength:50,createPseudo:c,match:yt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(n){return n[1]=n[1].replace(k,d),n[3]=(n[4]||n[5]||"").replace(k,d),n[2]==="~="&&(n[3]=" "+n[3]+" "),n.slice(0,4)},CHILD:function(n){return n[1]=n[1].toLowerCase(),n[1].slice(0,3)==="nth"?(n[3]||u.error(n[0]),n[4]=+(n[4]?n[5]+(n[6]||1):2*(n[3]==="even"||n[3]==="odd")),n[5]=+(n[7]+n[8]||n[3]==="odd")):n[3]&&u.error(n[0]),n},PSEUDO:function(n){var r,i=!n[5]&&n[2];return yt.CHILD.test(n[0])?null:(n[3]&&n[4]!==t?n[2]=n[4]:i&&fr.test(i)&&(r=pt(i,!0))&&(r=i.indexOf(")",i.length-r)-i.length)&&(n[0]=n[0].slice(0,r),n[2]=i.slice(0,r)),n.slice(0,3))}},filter:{TAG:function(n){var t=n.replace(k,d).toLowerCase();return n==="*"?function(){return!0}:function(n){return n.nodeName&&n.nodeName.toLowerCase()===t}},CLASS:function(n){var t=ci[n+" "];return t||(t=new RegExp("(^|"+f+")"+n+"("+f+"|$)"))&&ci(n,function(n){return t.test(typeof n.className=="string"&&n.className||typeof n.getAttribute!==st&&n.getAttribute("class")||"")})},ATTR:function(n,t,i){return function(r){var f=u.attr(r,n);return f==null?t==="!=":t?(f+="",t==="="?f===i:t==="!="?f!==i:t==="^="?i&&f.indexOf(i)===0:t==="*="?i&&f.indexOf(i)>-1:t==="$="?i&&f.slice(-i.length)===i:t==="~="?(" "+f+" ").indexOf(i)>-1:t==="|="?f===i||f.slice(0,i.length+1)===i+"-":!1):!0}},CHILD:function(n,t,i,r,u){var s=n.slice(0,3)!=="nth",e=n.slice(-4)!=="last",f=t==="of-type";return r===1&&u===0?function(n){return!!n.parentNode}:function(t,i,h){var a,k,c,l,v,w,b=s!==e?"nextSibling":"previousSibling",y=t.parentNode,g=f&&t.nodeName.toLowerCase(),d=!h&&!f;if(y){if(s){while(b){for(c=t;c=c[b];)if(f?c.nodeName.toLowerCase()===g:c.nodeType===1)return!1;w=b=n==="only"&&!w&&"nextSibling"}return!0}if(w=[e?y.firstChild:y.lastChild],e&&d){for(k=y[o]||(y[o]={}),a=k[n]||[],v=a[0]===p&&a[1],l=a[0]===p&&a[2],c=v&&y.childNodes[v];c=++v&&c&&c[b]||(l=v=0)||w.pop();)if(c.nodeType===1&&++l&&c===t){k[n]=[p,v,l];break}}else if(d&&(a=(t[o]||(t[o]={}))[n])&&a[0]===p)l=a[1];else while(c=++v&&c&&c[b]||(l=v=0)||w.pop())if((f?c.nodeName.toLowerCase()===g:c.nodeType===1)&&++l&&(d&&((c[o]||(c[o]={}))[n]=[p,l]),c===t))break;return l-=u,l===r||l%r==0&&l/r>=0}}},PSEUDO:function(n,t){var f,i=r.pseudos[n]||r.setFilters[n.toLowerCase()]||u.error("unsupported pseudo: "+n);return i[o]?i(t):i.length>1?(f=[n,n,"",t],r.setFilters.hasOwnProperty(n.toLowerCase())?c(function(n,r){for(var u,f=i(n,t),e=f.length;e--;)u=it.call(n,f[e]),n[u]=!(r[u]=f[e])}):function(n){return i(n,0,f)}):i}},pseudos:{not:c(function(n){var i=[],r=[],t=kt(n.replace(vt,"$1"));return t[o]?c(function(n,i,r,u){for(var e,o=t(n,null,u,[]),f=n.length;f--;)(e=o[f])&&(n[f]=!(i[f]=e))}):function(n,u,f){return i[0]=n,t(i,null,f,r),!r.pop()}}),has:c(function(n){return function(t){return u(n,t).length>0}}),contains:c(function(n){return function(t){return(t.textContent||t.innerText||ct(t)).indexOf(n)>-1}}),lang:c(function(n){return er.test(n||"")||u.error("unsupported lang: "+n),n=n.replace(k,d).toLowerCase(),function(t){var i;do if(i=v?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return i=i.toLowerCase(),i===n||i.indexOf(n+"-")===0;while((t=t.parentNode)&&t.nodeType===1);return!1}}),target:function(t){var i=n.location&&n.location.hash;return i&&i.slice(1)===t.id},root:function(n){return n===a},focus:function(n){return n===s.activeElement&&(!s.hasFocus||s.hasFocus())&&!!(n.type||n.href||~n.tabIndex)},enabled:function(n){return n.disabled===!1},disabled:function(n){return n.disabled===!0},checked:function(n){var t=n.nodeName.toLowerCase();return t==="input"&&!!n.checked||t==="option"&&!!n.selected},selected:function(n){return n.parentNode&&n.parentNode.selectedIndex,n.selected===!0},empty:function(n){for(n=n.firstChild;n;n=n.nextSibling)if(n.nodeName>"@"||n.nodeType===3||n.nodeType===4)return!1;return!0},parent:function(n){return!r.pseudos.empty(n)},header:function(n){return hr.test(n.nodeName)},input:function(n){return sr.test(n.nodeName)},button:function(n){var t=n.nodeName.toLowerCase();return t==="input"&&n.type==="button"||t==="button"},text:function(n){var t;return n.nodeName.toLowerCase()==="input"&&n.type==="text"&&((t=n.getAttribute("type"))==null||t.toLowerCase()===n.type)},first:rt(function(){return[0]}),last:rt(function(n,t){return[t-1]}),eq:rt(function(n,t,i){return[i<0?i+t:i]}),even:rt(function(n,t){for(var i=0;i<t;i+=2)n.push(i);return n}),odd:rt(function(n,t){for(var i=1;i<t;i+=2)n.push(i);return n}),lt:rt(function(n,t,i){for(var r=i<0?i+t:i;--r>=0;)n.push(r);return n}),gt:rt(function(n,t,i){for(var r=i<0?i+t:i;++r<t;)n.push(r);return n})}},r.pseudos.nth=r.pseudos.eq;for(ut in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[ut]=lr(ut);for(ut in{submit:!0,reset:!0})r.pseudos[ut]=ar(ut);ki.prototype=r.filters=r.pseudos,r.setFilters=new ki,kt=u.compile=function(n,t){var r,u=[],f=[],i=ai[n+" "];if(!i){for(t||(t=pt(n)),r=t.length;r--;)i=si(t[r]),i[o]?u.push(i):f.push(i);i=ai(n,vr(f,u))}return i},e.sortStable=o.split("").sort(dt).join("")===o,e.detectDuplicates=ft,nt(),e.sortDetached=l(function(n){return n.compareDocumentPosition(s.createElement("div"))&1}),l(function(n){return n.innerHTML="<a href='#'><\/a>",n.firstChild.getAttribute("href")==="#"})||ui("type|href|height|width",function(n,t,i){if(!i)return n.getAttribute(t,t.toLowerCase()==="type"?1:2)}),e.attributes&&l(function(n){return n.innerHTML="<input/>",n.firstChild.setAttribute("value",""),n.firstChild.getAttribute("value")===""})||ui("value",function(n,t,i){if(!i&&n.nodeName.toLowerCase()==="input")return n.defaultValue}),l(function(n){return n.getAttribute("disabled")==null})||ui(gt,function(n,t,i){var r;if(!i)return(r=n.getAttributeNode(t))&&r.specified?r.value:n[t]===!0?t.toLowerCase():null}),i.find=u,i.expr=u.selectors,i.expr[":"]=i.expr.pseudos,i.unique=u.uniqueSort,i.text=u.getText,i.isXMLDoc=u.isXML,i.contains=u.contains}(n),dt={},i.Callbacks=function(n){n=typeof n=="string"?dt[n]||ku(n):i.extend({},n);var f,c,s,l,e,o,r=[],u=!n.once&&[],a=function(t){for(f=n.memory&&t,c=!0,o=l||0,l=0,e=r.length,s=!0;r&&o<e;o++)if(r[o].apply(t[0],t[1])===!1&&n.stopOnFalse){f=!1;break}s=!1,r&&(u?u.length&&a(u.shift()):f?r=[]:h.disable())},h={add:function(){if(r){var t=r.length;(function u(t){i.each(t,function(t,f){var e=i.type(f);e==="function"?n.unique&&h.has(f)||r.push(f):f&&f.length&&e!=="string"&&u(f)})})(arguments),s?e=r.length:f&&(l=t,a(f))}return this},remove:function(){return r&&i.each(arguments,function(n,t){for(var u;(u=i.inArray(t,r,u))>-1;)r.splice(u,1),s&&(u<=e&&e--,u<=o&&o--)}),this},has:function(n){return n?i.inArray(n,r)>-1:!!(r&&r.length)},empty:function(){return r=[],e=0,this},disable:function(){return r=u=f=t,this},disabled:function(){return!r},lock:function(){return u=t,f||h.disable(),this},locked:function(){return!u},fireWith:function(n,t){return r&&(!c||u)&&(t=t||[],t=[n,t.slice?t.slice():t],s?u.push(t):a(t)),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!c}};return h},i.extend({Deferred:function(n){var u=[["resolve","done",i.Callbacks("once memory"),"resolved"],["reject","fail",i.Callbacks("once memory"),"rejected"],["notify","progress",i.Callbacks("memory")]],f="pending",r={state:function(){return f},always:function(){return t.done(arguments).fail(arguments),this},then:function(){var n=arguments;return i.Deferred(function(f){i.each(u,function(u,e){var s=e[0],o=i.isFunction(n[u])&&n[u];t[e[1]](function(){var n=o&&o.apply(this,arguments);n&&i.isFunction(n.promise)?n.promise().done(f.resolve).fail(f.reject).progress(f.notify):f[s+"With"](this===r?f.promise():this,o?[n]:arguments)})}),n=null}).promise()},promise:function(n){return n!=null?i.extend(n,r):r}},t={};return r.pipe=r.then,i.each(u,function(n,i){var e=i[2],o=i[3];r[i[1]]=e.add,o&&e.add(function(){f=o},u[n^1][2].disable,u[2][2].lock),t[i[0]]=function(){return t[i[0]+"With"](this===t?r:this,arguments),this},t[i[0]+"With"]=e.fireWith}),r.promise(t),n&&n.call(t,t),t},when:function(n){var t=0,u=a.call(arguments),r=u.length,e=r!==1||n&&i.isFunction(n.promise)?r:0,f=e===1?n:i.Deferred(),h=function(n,t,i){return function(r){t[n]=this,i[n]=arguments.length>1?a.call(arguments):r,i===o?f.notifyWith(t,i):--e||f.resolveWith(t,i)}},o,c,s;if(r>1)for(o=new Array(r),c=new Array(r),s=new Array(r);t<r;t++)u[t]&&i.isFunction(u[t].promise)?u[t].promise().done(h(t,s,u)).fail(f.reject).progress(h(t,c,o)):--e;return e||f.resolveWith(s,u),f.promise()}}),i.support=function(t){var r=u.createElement("input"),e=u.createDocumentFragment(),f=u.createElement("div"),o=u.createElement("select"),s=o.appendChild(u.createElement("option"));return r.type?(r.type="checkbox",t.checkOn=r.value!=="",t.optSelected=s.selected,t.reliableMarginRight=!0,t.boxSizingReliable=!0,t.pixelPosition=!1,r.checked=!0,t.noCloneChecked=r.cloneNode(!0).checked,o.disabled=!0,t.optDisabled=!s.disabled,r=u.createElement("input"),r.value="t",r.type="radio",t.radioValue=r.value==="t",r.setAttribute("checked","t"),r.setAttribute("name","t"),e.appendChild(r),t.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,t.focusinBubbles="onfocusin"in n,f.style.backgroundClip="content-box",f.cloneNode(!0).style.backgroundClip="",t.clearCloneStyle=f.style.backgroundClip==="content-box",i(function(){var o,r,e=u.getElementsByTagName("body")[0];e&&(o=u.createElement("div"),o.style.cssText="border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px",e.appendChild(o).appendChild(f),f.innerHTML="",f.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%",i.swap(e,e.style.zoom!=null?{zoom:1}:{},function(){t.boxSizing=f.offsetWidth===4}),n.getComputedStyle&&(t.pixelPosition=(n.getComputedStyle(f,null)||{}).top!=="1%",t.boxSizingReliable=(n.getComputedStyle(f,null)||{width:"4px"}).width==="4px",r=f.appendChild(u.createElement("div")),r.style.cssText=f.style.cssText="padding:0;margin:0;border:0;display:block;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box",r.style.marginRight=r.style.width="0",f.style.width="1px",t.reliableMarginRight=!parseFloat((n.getComputedStyle(r,null)||{}).marginRight)),e.removeChild(o))}),t):t}({}),vi=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,yi=/([A-Z])/g,c.uid=1,c.accepts=function(n){return n.nodeType?n.nodeType===1||n.nodeType===9:!0},c.prototype={key:function(n){if(!c.accepts(n))return 0;var r={},t=n[this.expando];if(!t){t=c.uid++;try{r[this.expando]={value:t},Object.defineProperties(n,r)}catch(u){r[this.expando]=t,i.extend(n,r)}}return this.cache[t]||(this.cache[t]={}),t},set:function(n,t,r){var f,e=this.key(n),u=this.cache[e];if(typeof t=="string")u[t]=r;else if(i.isEmptyObject(u))i.extend(this.cache[e],t);else for(f in t)u[f]=t[f];return u},get:function(n,i){var r=this.cache[this.key(n)];return i===t?r:r[i]},access:function(n,r,u){var f;return r===t||r&&typeof r=="string"&&u===t?(f=this.get(n,r),f!==t?f:this.get(n,i.camelCase(r))):(this.set(n,r,u),u!==t?u:r)},remove:function(n,r){var f,u,e,s=this.key(n),o=this.cache[s];if(r===t)this.cache[s]={};else for(i.isArray(r)?u=r.concat(r.map(i.camelCase)):(e=i.camelCase(r),r in o?u=[r,e]:(u=e,u=u in o?[u]:u.match(h)||[])),f=u.length;f--;)delete o[u[f]]},hasData:function(n){return!i.isEmptyObject(this.cache[n[this.expando]]||{})},discard:function(n){n[this.expando]&&delete this.cache[n[this.expando]]}},f=new c,r=new c,i.extend({acceptData:c.accepts,hasData:function(n){return f.hasData(n)||r.hasData(n)},data:function(n,t,i){return f.access(n,t,i)},removeData:function(n,t){f.remove(n,t)},_data:function(n,t,i){return r.access(n,t,i)},_removeData:function(n,t){r.remove(n,t)}}),i.fn.extend({data:function(n,u){var s,o,e=this[0],h=0,c=null;if(n===t){if(this.length&&(c=f.get(e),e.nodeType===1&&!r.get(e,"hasDataAttrs"))){for(s=e.attributes;h<s.length;h++)o=s[h].name,o.indexOf("data-")===0&&(o=i.camelCase(o.slice(5)),pi(e,o,c[o]));r.set(e,"hasDataAttrs",!0)}return c}return typeof n=="object"?this.each(function(){f.set(this,n)}):i.access(this,function(r){var u,o=i.camelCase(n);if(e&&r===t)return(u=f.get(e,n),u!==t)?u:(u=f.get(e,o),u!==t)?u:(u=pi(e,o,t),u!==t)?u:void 0;this.each(function(){var i=f.get(this,o);f.set(this,o,r),n.indexOf("-")!==-1&&i!==t&&f.set(this,n,r)})},null,u,arguments.length>1,null,!0)},removeData:function(n){return this.each(function(){f.remove(this,n)})}}),i.extend({queue:function(n,t,u){var f;if(n)return t=(t||"fx")+"queue",f=r.get(n,t),u&&(!f||i.isArray(u)?f=r.access(n,t,i.makeArray(u)):f.push(u)),f||[]},dequeue:function(n,t){t=t||"fx";var r=i.queue(n,t),e=r.length,u=r.shift(),f=i._queueHooks(n,t),o=function(){i.dequeue(n,t)};u==="inprogress"&&(u=r.shift(),e--),u&&(t==="fx"&&r.unshift("inprogress"),delete f.stop,u.call(n,o,f)),!e&&f&&f.empty.fire()},_queueHooks:function(n,t){var u=t+"queueHooks";return r.get(n,u)||r.access(n,u,{empty:i.Callbacks("once memory").add(function(){r.remove(n,[t+"queue",u])})})}}),i.fn.extend({queue:function(n,r){var u=2;return(typeof n!="string"&&(r=n,n="fx",u--),arguments.length<u)?i.queue(this[0],n):r===t?this:this.each(function(){var t=i.queue(this,n,r);i._queueHooks(this,n),n==="fx"&&t[0]!=="inprogress"&&i.dequeue(this,n)})},dequeue:function(n){return this.each(function(){i.dequeue(this,n)})},delay:function(n,t){return n=i.fx?i.fx.speeds[n]||n:n,t=t||"fx",this.queue(t,function(t,i){var r=setTimeout(t,n);i.stop=function(){clearTimeout(r)}})},clearQueue:function(n){return this.queue(n||"fx",[])},promise:function(n,u){var f,o=1,s=i.Deferred(),e=this,h=this.length,c=function(){--o||s.resolveWith(e,[e])};for(typeof n!="string"&&(u=n,n=t),n=n||"fx";h--;)f=r.get(e[h],n+"queueHooks"),f&&f.empty&&(o++,f.empty.add(c));return c(),s.promise(u)}});var du,wi,gt=/[\t\r\n\f]/g,gu=/\r/g,nf=/^(?:input|select|textarea|button)$/i;i.fn.extend({attr:function(n,t){return i.access(this,i.attr,n,t,arguments.length>1)},removeAttr:function(n){return this.each(function(){i.removeAttr(this,n)})},prop:function(n,t){return i.access(this,i.prop,n,t,arguments.length>1)},removeProp:function(n){return this.each(function(){delete this[i.propFix[n]||n]})},addClass:function(n){var e,t,r,u,o,f=0,s=this.length,c=typeof n=="string"&&n;if(i.isFunction(n))return this.each(function(t){i(this).addClass(n.call(this,t,this.className))});if(c)for(e=(n||"").match(h)||[];f<s;f++)if(t=this[f],r=t.nodeType===1&&(t.className?(" "+t.className+" ").replace(gt," "):" "),r){for(o=0;u=e[o++];)r.indexOf(" "+u+" ")<0&&(r+=u+" ");t.className=i.trim(r)}return this},removeClass:function(n){var e,r,t,u,o,f=0,s=this.length,c=arguments.length===0||typeof n=="string"&&n;if(i.isFunction(n))return this.each(function(t){i(this).removeClass(n.call(this,t,this.className))});if(c)for(e=(n||"").match(h)||[];f<s;f++)if(r=this[f],t=r.nodeType===1&&(r.className?(" "+r.className+" ").replace(gt," "):""),t){for(o=0;u=e[o++];)while(t.indexOf(" "+u+" ")>=0)t=t.replace(" "+u+" "," ");r.className=n?i.trim(t):""}return this},toggleClass:function(n,t){var u=typeof n;return typeof t=="boolean"&&u==="string"?t?this.addClass(n):this.removeClass(n):i.isFunction(n)?this.each(function(r){i(this).toggleClass(n.call(this,r,this.className,t),t)}):this.each(function(){if(u==="string")for(var t,e=0,f=i(this),o=n.match(h)||[];t=o[e++];)f.hasClass(t)?f.removeClass(t):f.addClass(t);else(u===rt||u==="boolean")&&(this.className&&r.set(this,"__className__",this.className),this.className=this.className||n===!1?"":r.get(this,"__className__")||"")})},hasClass:function(n){for(var i=" "+n+" ",t=0,r=this.length;t<r;t++)if(this[t].nodeType===1&&(" "+this[t].className+" ").replace(gt," ").indexOf(i)>=0)return!0;return!1},val:function(n){var r,u,e,f=this[0];return arguments.length?(e=i.isFunction(n),this.each(function(u){var f;this.nodeType===1&&(f=e?n.call(this,u,i(this).val()):n,f==null?f="":typeof f=="number"?f+="":i.isArray(f)&&(f=i.map(f,function(n){return n==null?"":n+""})),r=i.valHooks[this.type]||i.valHooks[this.nodeName.toLowerCase()],r&&"set"in r&&r.set(this,f,"value")!==t||(this.value=f))})):f?(r=i.valHooks[f.type]||i.valHooks[f.nodeName.toLowerCase()],r&&"get"in r&&(u=r.get(f,"value"))!==t)?u:(u=f.value,typeof u=="string"?u.replace(gu,""):u==null?"":u):void 0}}),i.extend({valHooks:{option:{get:function(n){var t=n.attributes.value;return!t||t.specified?n.value:n.text}},select:{get:function(n){for(var e,t,o=n.options,r=n.selectedIndex,u=n.type==="select-one"||r<0,s=u?null:[],h=u?r+1:o.length,f=r<0?h:u?r:0;f<h;f++)if(t=o[f],(t.selected||f===r)&&(i.support.optDisabled?!t.disabled:t.getAttribute("disabled")===null)&&(!t.parentNode.disabled||!i.nodeName(t.parentNode,"optgroup"))){if(e=i(t).val(),u)return e;s.push(e)}return s},set:function(n,t){for(var u,r,f=n.options,e=i.makeArray(t),o=f.length;o--;)r=f[o],(r.selected=i.inArray(i(r).val(),e)>=0)&&(u=!0);return u||(n.selectedIndex=-1),e}}},attr:function(n,r,u){var f,e,o=n.nodeType;if(n&&o!==3&&o!==8&&o!==2){if(typeof n.getAttribute===rt)return i.prop(n,r,u);if(o===1&&i.isXMLDoc(n)||(r=r.toLowerCase(),f=i.attrHooks[r]||(i.expr.match.bool.test(r)?wi:du)),u!==t)if(u===null)i.removeAttr(n,r);else return f&&"set"in f&&(e=f.set(n,u,r))!==t?e:(n.setAttribute(r,u+""),u);else return f&&"get"in f&&(e=f.get(n,r))!==null?e:(e=i.find.attr(n,r),e==null?t:e)}},removeAttr:function(n,t){var r,u,e=0,f=t&&t.match(h);if(f&&n.nodeType===1)while(r=f[e++])u=i.propFix[r]||r,i.expr.match.bool.test(r)&&(n[u]=!1),n.removeAttribute(r)},attrHooks:{type:{set:function(n,t){if(!i.support.radioValue&&t==="radio"&&i.nodeName(n,"input")){var r=n.value;return n.setAttribute("type",t),r&&(n.value=r),t}}}},propFix:{"for":"htmlFor","class":"className"},prop:function(n,r,u){var e,f,s,o=n.nodeType;if(n&&o!==3&&o!==8&&o!==2)return s=o!==1||!i.isXMLDoc(n),s&&(r=i.propFix[r]||r,f=i.propHooks[r]),u!==t?f&&"set"in f&&(e=f.set(n,u,r))!==t?e:n[r]=u:f&&"get"in f&&(e=f.get(n,r))!==null?e:n[r]},propHooks:{tabIndex:{get:function(n){return n.hasAttribute("tabindex")||nf.test(n.nodeName)||n.href?n.tabIndex:-1}}}}),wi={set:function(n,t,r){return t===!1?i.removeAttr(n,r):n.setAttribute(r,r),r}},i.each(i.expr.match.bool.source.match(/\w+/g),function(n,r){var u=i.expr.attrHandle[r]||i.find.attr;i.expr.attrHandle[r]=function(n,r,f){var e=i.expr.attrHandle[r],o=f?t:(i.expr.attrHandle[r]=t)!=u(n,r,f)?r.toLowerCase():null;return i.expr.attrHandle[r]=e,o}}),i.support.optSelected||(i.propHooks.selected={get:function(n){var t=n.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null}}),i.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){i.propFix[this.toLowerCase()]=this}),i.each(["radio","checkbox"],function(){i.valHooks[this]={set:function(n,t){if(i.isArray(t))return n.checked=i.inArray(i(n).val(),t)>=0}},i.support.checkOn||(i.valHooks[this].get=function(n){return n.getAttribute("value")===null?"on":n.value})});var tf=/^key/,rf=/^(?:mouse|contextmenu)|click/,bi=/^(?:focusinfocus|focusoutblur)$/,ki=/^([^.]*)(?:\.(.+)|)$/;i.event={global:{},add:function(n,u,f,e,o){var p,l,b,w,k,a,c,v,s,d,g,y=r.get(n);if(y){for(f.handler&&(p=f,f=p.handler,o=p.selector),f.guid||(f.guid=i.guid++),(w=y.events)||(w=y.events={}),(l=y.handle)||(l=y.handle=function(n){return typeof i!==rt&&(!n||i.event.triggered!==n.type)?i.event.dispatch.apply(l.elem,arguments):t},l.elem=n),u=(u||"").match(h)||[""],k=u.length;k--;)(b=ki.exec(u[k])||[],s=g=b[1],d=(b[2]||"").split(".").sort(),s)&&(c=i.event.special[s]||{},s=(o?c.delegateType:c.bindType)||s,c=i.event.special[s]||{},a=i.extend({type:s,origType:g,data:e,handler:f,guid:f.guid,selector:o,needsContext:o&&i.expr.match.needsContext.test(o),namespace:d.join(".")},p),(v=w[s])||(v=w[s]=[],v.delegateCount=0,c.setup&&c.setup.call(n,e,d,l)!==!1||n.addEventListener&&n.addEventListener(s,l,!1)),c.add&&(c.add.call(n,a),a.handler.guid||(a.handler.guid=f.guid)),o?v.splice(v.delegateCount++,0,a):v.push(a),i.event.global[s]=!0);n=null}},remove:function(n,t,u,f,e){var p,k,c,v,w,s,l,a,o,b,d,y=r.hasData(n)&&r.get(n);if(y&&(v=y.events)){for(t=(t||"").match(h)||[""],w=t.length;w--;){if(c=ki.exec(t[w])||[],o=d=c[1],b=(c[2]||"").split(".").sort(),!o){for(o in v)i.event.remove(n,o+t[w],u,f,!0);continue}for(l=i.event.special[o]||{},o=(f?l.delegateType:l.bindType)||o,a=v[o]||[],c=c[2]&&new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"),k=p=a.length;p--;)s=a[p],(e||d===s.origType)&&(!u||u.guid===s.guid)&&(!c||c.test(s.namespace))&&(!f||f===s.selector||f==="**"&&s.selector)&&(a.splice(p,1),s.selector&&a.delegateCount--,l.remove&&l.remove.call(n,s));k&&!a.length&&(l.teardown&&l.teardown.call(n,b,y.handle)!==!1||i.removeEvent(n,o,y.handle),delete v[o])}i.isEmptyObject(v)&&(delete y.handle,r.remove(n,"events"))}},trigger:function(f,e,o,s){var b,h,l,k,v,y,a,w=[o||u],c=bt.call(f,"type")?f.type:f,p=bt.call(f,"namespace")?f.namespace.split("."):[];if((h=l=o=o||u,o.nodeType!==3&&o.nodeType!==8)&&!bi.test(c+i.event.triggered)&&(c.indexOf(".")>=0&&(p=c.split("."),c=p.shift(),p.sort()),v=c.indexOf(":")<0&&"on"+c,f=f[i.expando]?f:new i.Event(c,typeof f=="object"&&f),f.isTrigger=s?2:3,f.namespace=p.join("."),f.namespace_re=f.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,f.result=t,f.target||(f.target=o),e=e==null?[f]:i.makeArray(e,[f]),a=i.event.special[c]||{},s||!a.trigger||a.trigger.apply(o,e)!==!1)){if(!s&&!a.noBubble&&!i.isWindow(o)){for(k=a.delegateType||c,bi.test(k+c)||(h=h.parentNode);h;h=h.parentNode)w.push(h),l=h;l===(o.ownerDocument||u)&&w.push(l.defaultView||l.parentWindow||n)}for(b=0;(h=w[b++])&&!f.isPropagationStopped();)f.type=b>1?k:a.bindType||c,y=(r.get(h,"events")||{})[f.type]&&r.get(h,"handle"),y&&y.apply(h,e),y=v&&h[v],y&&i.acceptData(h)&&y.apply&&y.apply(h,e)===!1&&f.preventDefault();return f.type=c,s||f.isDefaultPrevented()||(!a._default||a._default.apply(w.pop(),e)===!1)&&i.acceptData(o)&&v&&i.isFunction(o[c])&&!i.isWindow(o)&&(l=o[v],l&&(o[v]=null),i.event.triggered=c,o[c](),i.event.triggered=t,l&&(o[v]=l)),f.result}},dispatch:function(n){n=i.event.fix(n);var s,h,o,f,u,c=[],l=a.call(arguments),v=(r.get(this,"events")||{})[n.type]||[],e=i.event.special[n.type]||{};if(l[0]=n,n.delegateTarget=this,!e.preDispatch||e.preDispatch.call(this,n)!==!1){for(c=i.event.handlers.call(this,n,v),s=0;(f=c[s++])&&!n.isPropagationStopped();)for(n.currentTarget=f.elem,h=0;(u=f.handlers[h++])&&!n.isImmediatePropagationStopped();)(!n.namespace_re||n.namespace_re.test(u.namespace))&&(n.handleObj=u,n.data=u.data,o=((i.event.special[u.origType]||{}).handle||u.handler).apply(f.elem,l),o!==t&&(n.result=o)===!1&&(n.preventDefault(),n.stopPropagation()));return e.postDispatch&&e.postDispatch.call(this,n),n.result}},handlers:function(n,r){var o,f,e,s,c=[],h=r.delegateCount,u=n.target;if(h&&u.nodeType&&(!n.button||n.type!=="click"))for(;u!==this;u=u.parentNode||this)if(u.disabled!==!0||n.type!=="click"){for(f=[],o=0;o<h;o++)s=r[o],e=s.selector+" ",f[e]===t&&(f[e]=s.needsContext?i(e,this).index(u)>=0:i.find(e,this,null,[u]).length),f[e]&&f.push(s);f.length&&c.push({elem:u,handlers:f})}return h<r.length&&c.push({elem:this,handlers:r.slice(h)}),c},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(n,t){return n.which==null&&(n.which=t.charCode!=null?t.charCode:t.keyCode),n}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(n,i){var o,r,f,e=i.button;return n.pageX==null&&i.clientX!=null&&(o=n.target.ownerDocument||u,r=o.documentElement,f=o.body,n.pageX=i.clientX+(r&&r.scrollLeft||f&&f.scrollLeft||0)-(r&&r.clientLeft||f&&f.clientLeft||0),n.pageY=i.clientY+(r&&r.scrollTop||f&&f.scrollTop||0)-(r&&r.clientTop||f&&f.clientTop||0)),n.which||e===t||(n.which=e&1?1:e&2?3:e&4?2:0),n}},fix:function(n){if(n[i.expando])return n;var f,e,o,r=n.type,s=n,t=this.fixHooks[r];for(t||(this.fixHooks[r]=t=rf.test(r)?this.mouseHooks:tf.test(r)?this.keyHooks:{}),o=t.props?this.props.concat(t.props):this.props,n=new i.Event(s),f=o.length;f--;)e=o[f],n[e]=s[e];return n.target||(n.target=u),n.target.nodeType===3&&(n.target=n.target.parentNode),t.filter?t.filter(n,s):n},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==di()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===di()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(this.type==="checkbox"&&this.click&&i.nodeName(this,"input"))return this.click(),!1},_default:function(n){return i.nodeName(n.target,"a")}},beforeunload:{postDispatch:function(n){n.result!==t&&(n.originalEvent.returnValue=n.result)}}},simulate:function(n,t,r,u){var f=i.extend(new i.Event,r,{type:n,isSimulated:!0,originalEvent:{}});u?i.event.trigger(f,null,t):i.event.dispatch.call(t,f),f.isDefaultPrevented()&&r.preventDefault()}},i.removeEvent=function(n,t,i){n.removeEventListener&&n.removeEventListener(t,i,!1)},i.Event=function(n,t){if(!(this instanceof i.Event))return new i.Event(n,t);n&&n.type?(this.originalEvent=n,this.type=n.type,this.isDefaultPrevented=n.defaultPrevented||n.getPreventDefault&&n.getPreventDefault()?ht:p):this.type=n,t&&i.extend(this,t),this.timeStamp=n&&n.timeStamp||i.now(),this[i.expando]=!0},i.Event.prototype={isDefaultPrevented:p,isPropagationStopped:p,isImmediatePropagationStopped:p,preventDefault:function(){var n=this.originalEvent;this.isDefaultPrevented=ht,n&&n.preventDefault&&n.preventDefault()},stopPropagation:function(){var n=this.originalEvent;this.isPropagationStopped=ht,n&&n.stopPropagation&&n.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=ht,this.stopPropagation()}},i.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(n,t){i.event.special[n]={delegateType:t,bindType:t,handle:function(n){var u,f=this,r=n.relatedTarget,e=n.handleObj;return r&&(r===f||i.contains(f,r))||(n.type=e.origType,u=e.handler.apply(this,arguments),n.type=t),u}}}),i.support.focusinBubbles||i.each({focus:"focusin",blur:"focusout"},function(n,t){var r=0,f=function(n){i.event.simulate(t,n.target,i.event.fix(n),!0)};i.event.special[t]={setup:function(){r++==0&&u.addEventListener(n,f,!0)},teardown:function(){--r==0&&u.removeEventListener(n,f,!0)}}}),i.fn.extend({on:function(n,r,u,f,e){var o,s;if(typeof n=="object"){typeof r!="string"&&(u=u||r,r=t);for(s in n)this.on(s,r,u,n[s],e);return this}if(u==null&&f==null?(f=r,u=r=t):f==null&&(typeof r=="string"?(f=u,u=t):(f=u,u=r,r=t)),f===!1)f=p;else if(!f)return this;return e===1&&(o=f,f=function(n){return i().off(n),o.apply(this,arguments)},f.guid=o.guid||(o.guid=i.guid++)),this.each(function(){i.event.add(this,n,f,u,r)})},one:function(n,t,i,r){return this.on(n,t,i,r,1)},off:function(n,r,u){var f,e;if(n&&n.preventDefault&&n.handleObj)return f=n.handleObj,i(n.delegateTarget).off(f.namespace?f.origType+"."+f.namespace:f.origType,f.selector,f.handler),this;if(typeof n=="object"){for(e in n)this.off(e,r,n[e]);return this}return(r===!1||typeof r=="function")&&(u=r,r=t),u===!1&&(u=p),this.each(function(){i.event.remove(this,n,u,r)})},trigger:function(n,t){return this.each(function(){i.event.trigger(n,t,this)})},triggerHandler:function(n,t){var r=this[0];if(r)return i.event.trigger(n,t,r,!0)}});var uf=/^.[^:#\[\.,]*$/,ff=/^(?:parents|prev(?:Until|All))/,gi=i.expr.match.needsContext,ef={children:!0,contents:!0,next:!0,prev:!0};i.fn.extend({find:function(n){var t,r=[],u=this,f=u.length;if(typeof n!="string")return this.pushStack(i(n).filter(function(){for(t=0;t<f;t++)if(i.contains(u[t],this))return!0}));for(t=0;t<f;t++)i.find(n,u[t],r);return r=this.pushStack(f>1?i.unique(r):r),r.selector=this.selector?this.selector+" "+n:n,r},has:function(n){var t=i(n,this),r=t.length;return this.filter(function(){for(var n=0;n<r;n++)if(i.contains(this,t[n]))return!0})},not:function(n){return this.pushStack(ni(this,n||[],!0))},filter:function(n){return this.pushStack(ni(this,n||[],!1))},is:function(n){return!!ni(this,typeof n=="string"&&gi.test(n)?i(n):n||[],!1).length},closest:function(n,t){for(var r,f=0,o=this.length,u=[],e=gi.test(n)||typeof n!="string"?i(n,t||this.context):0;f<o;f++)for(r=this[f];r&&r!==t;r=r.parentNode)if(r.nodeType<11&&(e?e.index(r)>-1:r.nodeType===1&&i.find.matchesSelector(r,n))){r=u.push(r);break}return this.pushStack(u.length>1?i.unique(u):u)},index:function(n){return n?typeof n=="string"?et.call(i(n),this[0]):et.call(this,n.jquery?n[0]:n):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(n,t){var r=typeof n=="string"?i(n,t):i.makeArray(n&&n.nodeType?[n]:n),u=i.merge(this.get(),r);return this.pushStack(i.unique(u))},addBack:function(n){return this.add(n==null?this.prevObject:this.prevObject.filter(n))}}),i.each({parent:function(n){var t=n.parentNode;return t&&t.nodeType!==11?t:null},parents:function(n){return i.dir(n,"parentNode")},parentsUntil:function(n,t,r){return i.dir(n,"parentNode",r)},next:function(n){return nr(n,"nextSibling")},prev:function(n){return nr(n,"previousSibling")},nextAll:function(n){return i.dir(n,"nextSibling")},prevAll:function(n){return i.dir(n,"previousSibling")},nextUntil:function(n,t,r){return i.dir(n,"nextSibling",r)},prevUntil:function(n,t,r){return i.dir(n,"previousSibling",r)},siblings:function(n){return i.sibling((n.parentNode||{}).firstChild,n)},children:function(n){return i.sibling(n.firstChild)},contents:function(n){return n.contentDocument||i.merge([],n.childNodes)}},function(n,t){i.fn[n]=function(r,u){var f=i.map(this,t,r);return n.slice(-5)!=="Until"&&(u=r),u&&typeof u=="string"&&(f=i.filter(u,f)),this.length>1&&(ef[n]||i.unique(f),ff.test(n)&&f.reverse()),this.pushStack(f)}}),i.extend({filter:function(n,t,r){var u=t[0];return r&&(n=":not("+n+")"),t.length===1&&u.nodeType===1?i.find.matchesSelector(u,n)?[u]:[]:i.find.matches(n,i.grep(t,function(n){return n.nodeType===1}))},dir:function(n,r,u){for(var f=[],e=u!==t;(n=n[r])&&n.nodeType!==9;)if(n.nodeType===1){if(e&&i(n).is(u))break;f.push(n)}return f},sibling:function(n,t){for(var i=[];n;n=n.nextSibling)n.nodeType===1&&n!==t&&i.push(n);return i}});var tr=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,ir=/<([\w:]+)/,of=/<|&#?\w+;/,sf=/<(?:script|style|link)/i,rr=/^(?:checkbox|radio)$/i,hf=/checked\s*(?:[^=]|=\s*.checked.)/i,ur=/^$|\/(?:java|ecma)script/i,cf=/^true\/(.*)/,lf=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,s={option:[1,"<select multiple='multiple'>","<\/select>"],thead:[1,"<table>","<\/table>"],col:[2,"<table><colgroup>","<\/colgroup><\/table>"],tr:[2,"<table><tbody>","<\/tbody><\/table>"],td:[3,"<table><tbody><tr>","<\/tr><\/tbody><\/table>"],_default:[0,"",""]};s.optgroup=s.option,s.tbody=s.tfoot=s.colgroup=s.caption=s.thead,s.th=s.td,i.fn.extend({text:function(n){return i.access(this,function(n){return n===t?i.text(this):this.empty().append((this[0]&&this[0].ownerDocument||u).createTextNode(n))},null,n,arguments.length)},append:function(){return this.domManip(arguments,function(n){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=fr(this,n);t.appendChild(n)}})},prepend:function(){return this.domManip(arguments,function(n){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=fr(this,n);t.insertBefore(n,t.firstChild)}})},before:function(){return this.domManip(arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this)})},after:function(){return this.domManip(arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling)})},remove:function(n,t){for(var r,f=n?i.filter(n,this):this,u=0;(r=f[u])!=null;u++)t||r.nodeType!==1||i.cleanData(o(r)),r.parentNode&&(t&&i.contains(r.ownerDocument,r)&&ti(o(r,"script")),r.parentNode.removeChild(r));return this},empty:function(){for(var n,t=0;(n=this[t])!=null;t++)n.nodeType===1&&(i.cleanData(o(n,!1)),n.textContent="");return this},clone:function(n,t){return n=n==null?!1:n,t=t==null?n:t,this.map(function(){return i.clone(this,n,t)})},html:function(n){return i.access(this,function(n){var r=this[0]||{},u=0,f=this.length;if(n===t&&r.nodeType===1)return r.innerHTML;if(typeof n=="string"&&!sf.test(n)&&!s[(ir.exec(n)||["",""])[1].toLowerCase()]){n=n.replace(tr,"<$1><\/$2>");try{for(;u<f;u++)r=this[u]||{},r.nodeType===1&&(i.cleanData(o(r,!1)),r.innerHTML=n);r=0}catch(e){}}r&&this.empty().append(n)},null,n,arguments.length)},replaceWith:function(){var t=i.map(this,function(n){return[n.nextSibling,n.parentNode]}),n=0;return this.domManip(arguments,function(r){var u=t[n++],f=t[n++];f&&(u&&u.parentNode!==f&&(u=this.nextSibling),i(this).remove(),f.insertBefore(r,u))},!0),n?this:this.remove()},detach:function(n){return this.remove(n,!0)},domManip:function(n,t,u){n=li.apply([],n);var h,v,s,c,f,y,e=0,l=this.length,w=this,b=l-1,a=n[0],p=i.isFunction(a);if(p||!(l<=1||typeof a!="string"||i.support.checkClone||!hf.test(a)))return this.each(function(i){var r=w.eq(i);p&&(n[0]=a.call(this,i,r.html())),r.domManip(n,t,u)});if(l&&(h=i.buildFragment(n,this[0].ownerDocument,!1,!u&&this),v=h.firstChild,h.childNodes.length===1&&(h=v),v)){for(s=i.map(o(h,"script"),af),c=s.length;e<l;e++)f=h,e!==b&&(f=i.clone(f,!0,!0),c&&i.merge(s,o(f,"script"))),t.call(this[e],f,e);if(c)for(y=s[s.length-1].ownerDocument,i.map(s,vf),e=0;e<c;e++)f=s[e],ur.test(f.type||"")&&!r.access(f,"globalEval")&&i.contains(y,f)&&(f.src?i._evalUrl(f.src):i.globalEval(f.textContent.replace(lf,"")))}return this}}),i.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(n,t){i.fn[n]=function(n){for(var u,f=[],e=i(n),o=e.length-1,r=0;r<=o;r++)u=r===o?this:this.clone(!0),i(e[r])[t](u),wt.apply(f,u.get());return this.pushStack(f)}}),i.extend({clone:function(n,t,r){var u,h,e,f,s=n.cloneNode(!0),c=i.contains(n.ownerDocument,n);if(!i.support.noCloneChecked&&(n.nodeType===1||n.nodeType===11)&&!i.isXMLDoc(n))for(f=o(s),e=o(n),u=0,h=e.length;u<h;u++)yf(e[u],f[u]);if(t)if(r)for(e=e||o(n),f=f||o(s),u=0,h=e.length;u<h;u++)er(e[u],f[u]);else er(n,s);return f=o(s,"script"),f.length>0&&ti(f,!c&&o(n,"script")),s},buildFragment:function(n,t,r,u){for(var f,e,y,l,p,a,h=0,w=n.length,c=t.createDocumentFragment(),v=[];h<w;h++)if(f=n[h],f||f===0)if(i.type(f)==="object")i.merge(v,f.nodeType?[f]:f);else if(of.test(f)){for(e=e||c.appendChild(t.createElement("div")),y=(ir.exec(f)||["",""])[1].toLowerCase(),l=s[y]||s._default,e.innerHTML=l[1]+f.replace(tr,"<$1><\/$2>")+l[2],a=l[0];a--;)e=e.lastChild;i.merge(v,e.childNodes),e=c.firstChild,e.textContent=""}else v.push(t.createTextNode(f));for(c.textContent="",h=0;f=v[h++];)if((!u||i.inArray(f,u)===-1)&&(p=i.contains(f.ownerDocument,f),e=o(c.appendChild(f),"script"),p&&ti(e),r))for(a=0;f=e[a++];)ur.test(f.type||"")&&r.push(f);return c},cleanData:function(n){for(var s,u,h,o,e,l,v=i.event.special,a=0;(u=n[a])!==t;a++){if(c.accepts(u)&&(e=u[r.expando],e&&(s=r.cache[e]))){if(h=Object.keys(s.events||{}),h.length)for(l=0;(o=h[l])!==t;l++)v[o]?i.event.remove(u,o):i.removeEvent(u,o,s.handle);r.cache[e]&&delete r.cache[e]}delete f.cache[u[f.expando]]}},_evalUrl:function(n){return i.ajax({url:n,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})}}),i.fn.extend({wrapAll:function(n){var t;return i.isFunction(n)?this.each(function(t){i(this).wrapAll(n.call(this,t))}):(this[0]&&(t=i(n,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var n=this;n.firstElementChild;)n=n.firstElementChild;return n}).append(this)),this)},wrapInner:function(n){return i.isFunction(n)?this.each(function(t){i(this).wrapInner(n.call(this,t))}):this.each(function(){var t=i(this),r=t.contents();r.length?r.wrapAll(n):t.append(n)})},wrap:function(n){var t=i.isFunction(n);return this.each(function(r){i(this).wrapAll(t?n.call(this,r):n)})},unwrap:function(){return this.parent().each(function(){i.nodeName(this,"body")||i(this).replaceWith(this.childNodes)}).end()}});var w,k,pf=/^(none|table(?!-c[ea]).+)/,or=/^margin/,wf=new RegExp("^("+ot+")(.*)$","i"),ii=new RegExp("^("+ot+")(?!px)[a-z%]+$","i"),bf=new RegExp("^([+-])=("+ot+")","i"),sr={BODY:"block"},kf={position:"absolute",visibility:"hidden",display:"block"},hr={letterSpacing:0,fontWeight:400},v=["Top","Right","Bottom","Left"],cr=["Webkit","O","Moz","ms"];i.fn.extend({css:function(n,r){return i.access(this,function(n,r,u){var e,o,s={},f=0;if(i.isArray(r)){for(e=ct(n),o=r.length;f<o;f++)s[r[f]]=i.css(n,r[f],!1,e);return s}return u!==t?i.style(n,r,u):i.css(n,r)},n,r,arguments.length>1)},show:function(){return ar(this,!0)},hide:function(){return ar(this)},toggle:function(n){return typeof n=="boolean"?n?this.show():this.hide():this.each(function(){d(this)?i(this).show():i(this).hide()})}}),i.extend({cssHooks:{opacity:{get:function(n,t){if(t){var i=w(n,"opacity");return i===""?"1":i}}}},cssNumber:{columnCount:!0,fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(n,r,u,f){if(n&&n.nodeType!==3&&n.nodeType!==8&&n.style){var o,s,e,h=i.camelCase(r),c=n.style;if(r=i.cssProps[h]||(i.cssProps[h]=lr(c,h)),e=i.cssHooks[r]||i.cssHooks[h],u!==t){if(s=typeof u,s==="string"&&(o=bf.exec(u))&&(u=(o[1]+1)*o[2]+parseFloat(i.css(n,r)),s="number"),u==null||s==="number"&&isNaN(u))return;s!=="number"||i.cssNumber[h]||(u+="px"),i.support.clearCloneStyle||u!==""||r.indexOf("background")!==0||(c[r]="inherit"),e&&"set"in e&&(u=e.set(n,u,f))===t||(c[r]=u)}else return e&&"get"in e&&(o=e.get(n,!1,f))!==t?o:c[r]}},css:function(n,r,u,f){var e,h,o,s=i.camelCase(r);return(r=i.cssProps[s]||(i.cssProps[s]=lr(n.style,s)),o=i.cssHooks[r]||i.cssHooks[s],o&&"get"in o&&(e=o.get(n,!0,u)),e===t&&(e=w(n,r,f)),e==="normal"&&r in hr&&(e=hr[r]),u===""||u)?(h=parseFloat(e),u===!0||i.isNumeric(h)?h||0:e):e}}),w=function(n,r,u){var s,h,c,o=u||ct(n),e=o?o.getPropertyValue(r)||o[r]:t,f=n.style;return o&&(e!==""||i.contains(n.ownerDocument,n)||(e=i.style(n,r)),ii.test(e)&&or.test(r)&&(s=f.width,h=f.minWidth,c=f.maxWidth,f.minWidth=f.maxWidth=f.width=e,e=o.width,f.width=s,f.minWidth=h,f.maxWidth=c)),e},i.each(["height","width"],function(n,t){i.cssHooks[t]={get:function(n,r,u){if(r)return n.offsetWidth===0&&pf.test(i.css(n,"display"))?i.swap(n,kf,function(){return pr(n,t,u)}):pr(n,t,u)},set:function(n,r,u){var f=u&&ct(n);return vr(n,r,u?yr(n,t,u,i.support.boxSizing&&i.css(n,"boxSizing",!1,f)==="border-box",f):0)}}}),i(function(){i.support.reliableMarginRight||(i.cssHooks.marginRight={get:function(n,t){if(t)return i.swap(n,{display:"inline-block"},w,[n,"marginRight"])}}),!i.support.pixelPosition&&i.fn.position&&i.each(["top","left"],function(n,t){i.cssHooks[t]={get:function(n,r){if(r)return r=w(n,t),ii.test(r)?i(n).position()[t]+"px":r}}})}),i.expr&&i.expr.filters&&(i.expr.filters.hidden=function(n){return n.offsetWidth<=0&&n.offsetHeight<=0},i.expr.filters.visible=function(n){return!i.expr.filters.hidden(n)}),i.each({margin:"",padding:"",border:"Width"},function(n,t){i.cssHooks[n+t]={expand:function(i){for(var r=0,f={},u=typeof i=="string"?i.split(" "):[i];r<4;r++)f[n+v[r]+t]=u[r]||u[r-2]||u[0];return f}},or.test(n)||(i.cssHooks[n+t].set=vr)});var gf=/%20/g,ne=/\[\]$/,br=/\r?\n/g,te=/^(?:submit|button|image|reset|file)$/i,ie=/^(?:input|select|textarea|keygen)/i;i.fn.extend({serialize:function(){return i.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var n=i.prop(this,"elements");return n?i.makeArray(n):this}).filter(function(){var n=this.type;return this.name&&!i(this).is(":disabled")&&ie.test(this.nodeName)&&!te.test(n)&&(this.checked||!rr.test(n))}).map(function(n,t){var r=i(this).val();return r==null?null:i.isArray(r)?i.map(r,function(n){return{name:t.name,value:n.replace(br,"\r\n")}}):{name:t.name,value:r.replace(br,"\r\n")}}).get()}}),i.param=function(n,r){var u,f=[],e=function(n,t){t=i.isFunction(t)?t():t==null?"":t,f[f.length]=encodeURIComponent(n)+"="+encodeURIComponent(t)};if(r===t&&(r=i.ajaxSettings&&i.ajaxSettings.traditional),i.isArray(n)||n.jquery&&!i.isPlainObject(n))i.each(n,function(){e(this.name,this.value)});else for(u in n)ri(u,n[u],r,e);return f.join("&").replace(gf,"+")},i.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(n,t){i.fn[t]=function(n,i){return arguments.length>0?this.on(t,null,n,i):this.trigger(t)}}),i.fn.extend({hover:function(n,t){return this.mouseenter(n).mouseleave(t||n)},bind:function(n,t,i){return this.on(n,null,t,i)},unbind:function(n,t){return this.off(n,null,t)},delegate:function(n,t,i,r){return this.on(t,n,i,r)},undelegate:function(n,t,i){return arguments.length===1?this.off(n,"**"):this.off(t,n||"**",i)}});var y,l,ui=i.now(),fi=/\?/,re=/#.*$/,kr=/([?&])_=[^&]*/,ue=/^(.*?):[ \t]*([^\r\n]*)$/mg,fe=/^(?:GET|HEAD)$/,ee=/^\/\//,dr=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,gr=i.fn.load,nu={},ei={},tu="*/".concat("*");try{l=hu.href}catch(pe){l=u.createElement("a"),l.href="",l=l.href}y=dr.exec(l.toLowerCase())||[],i.fn.load=function(n,r,u){if(typeof n!="string"&&gr)return gr.apply(this,arguments);var f,s,h,e=this,o=n.indexOf(" ");return o>=0&&(f=n.slice(o),n=n.slice(0,o)),i.isFunction(r)?(u=r,r=t):r&&typeof r=="object"&&(s="POST"),e.length>0&&i.ajax({url:n,type:s,dataType:"html",data:r}).done(function(n){h=arguments,e.html(f?i("<div>").append(i.parseHTML(n)).find(f):n)}).complete(u&&function(n,t){e.each(u,h||[n.responseText,t,n])}),this},i.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(n,t){i.fn[t]=function(n){return this.on(t,n)}}),i.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:l,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(y[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":tu,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":i.parseJSON,"text xml":i.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(n,t){return t?oi(oi(n,i.ajaxSettings),t):oi(i.ajaxSettings,n)},ajaxPrefilter:iu(nu),ajaxTransport:iu(ei),ajax:function(n,r){function k(n,r,h,l){var v,rt,k,y,w,a=r;o!==2&&(o=2,g&&clearTimeout(g),c=t,d=l||"",f.readyState=n>0?4:0,v=n>=200&&n<300||n===304,h&&(y=oe(u,f,h)),y=se(u,y,f,v),v?(u.ifModified&&(w=f.getResponseHeader("Last-Modified"),w&&(i.lastModified[e]=w),w=f.getResponseHeader("etag"),w&&(i.etag[e]=w)),n===204||u.type==="HEAD"?a="nocontent":n===304?a="notmodified":(a=y.state,rt=y.data,k=y.error,v=!k)):(k=a,(n||!a)&&(a="error",n<0&&(n=0))),f.status=n,f.statusText=(r||a)+"",v?tt.resolveWith(s,[rt,a,f]):tt.rejectWith(s,[f,a,k]),f.statusCode(b),b=t,p&&nt.trigger(v?"ajaxSuccess":"ajaxError",[f,u,v?rt:k]),it.fireWith(s,[f,a]),p&&(nt.trigger("ajaxComplete",[f,u]),--i.active||i.event.trigger("ajaxStop")))}typeof n=="object"&&(r=n,n=t),r=r||{};var c,e,d,w,g,a,p,v,u=i.ajaxSetup({},r),s=u.context||u,nt=u.context&&(s.nodeType||s.jquery)?i(s):i.event,tt=i.Deferred(),it=i.Callbacks("once memory"),b=u.statusCode||{},rt={},ut={},o=0,ft="canceled",f={readyState:0,getResponseHeader:function(n){var t;if(o===2){if(!w)for(w={};t=ue.exec(d);)w[t[1].toLowerCase()]=t[2];t=w[n.toLowerCase()]}return t==null?null:t},getAllResponseHeaders:function(){return o===2?d:null},setRequestHeader:function(n,t){var i=n.toLowerCase();return o||(n=ut[i]=ut[i]||n,rt[n]=t),this},overrideMimeType:function(n){return o||(u.mimeType=n),this},statusCode:function(n){var t;if(n)if(o<2)for(t in n)b[t]=[b[t],n[t]];else f.always(n[f.status]);return this},abort:function(n){var t=n||ft;return c&&c.abort(t),k(0,t),this}};if(tt.promise(f).complete=it.add,f.success=f.done,f.error=f.fail,u.url=((n||u.url||l)+"").replace(re,"").replace(ee,y[1]+"//"),u.type=r.method||r.type||u.method||u.type,u.dataTypes=i.trim(u.dataType||"*").toLowerCase().match(h)||[""],u.crossDomain==null&&(a=dr.exec(u.url.toLowerCase()),u.crossDomain=!!(a&&(a[1]!==y[1]||a[2]!==y[2]||(a[3]||(a[1]==="http:"?"80":"443"))!==(y[3]||(y[1]==="http:"?"80":"443"))))),u.data&&u.processData&&typeof u.data!="string"&&(u.data=i.param(u.data,u.traditional)),ru(nu,u,r,f),o===2)return f;p=u.global,p&&i.active++==0&&i.event.trigger("ajaxStart"),u.type=u.type.toUpperCase(),u.hasContent=!fe.test(u.type),e=u.url,u.hasContent||(u.data&&(e=u.url+=(fi.test(e)?"&":"?")+u.data,delete u.data),u.cache===!1&&(u.url=kr.test(e)?e.replace(kr,"$1_="+ui++):e+(fi.test(e)?"&":"?")+"_="+ui++)),u.ifModified&&(i.lastModified[e]&&f.setRequestHeader("If-Modified-Since",i.lastModified[e]),i.etag[e]&&f.setRequestHeader("If-None-Match",i.etag[e])),(u.data&&u.hasContent&&u.contentType!==!1||r.contentType)&&f.setRequestHeader("Content-Type",u.contentType),f.setRequestHeader("Accept",u.dataTypes[0]&&u.accepts[u.dataTypes[0]]?u.accepts[u.dataTypes[0]]+(u.dataTypes[0]!=="*"?", "+tu+"; q=0.01":""):u.accepts["*"]);for(v in u.headers)f.setRequestHeader(v,u.headers[v]);if(u.beforeSend&&(u.beforeSend.call(s,f,u)===!1||o===2))return f.abort();ft="abort";for(v in{success:1,error:1,complete:1})f[v](u[v]);if(c=ru(ei,u,r,f),c){f.readyState=1,p&&nt.trigger("ajaxSend",[f,u]),u.async&&u.timeout>0&&(g=setTimeout(function(){f.abort("timeout")},u.timeout));try{o=1,c.send(rt,k)}catch(et){if(o<2)k(-1,et);else throw et;}}else k(-1,"No Transport");return f},getJSON:function(n,t,r){return i.get(n,t,r,"json")},getScript:function(n,r){return i.get(n,t,r,"script")}}),i.each(["get","post"],function(n,r){i[r]=function(n,u,f,e){return i.isFunction(u)&&(e=e||f,f=u,u=t),i.ajax({url:n,type:r,dataType:e,data:u,success:f})}}),i.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(n){return i.globalEval(n),n}}}),i.ajaxPrefilter("script",function(n){n.cache===t&&(n.cache=!1),n.crossDomain&&(n.type="GET")}),i.ajaxTransport("script",function(n){if(n.crossDomain){var r,t;return{send:function(f,e){r=i("<script>").prop({async:!0,charset:n.scriptCharset,src:n.url}).on("load error",t=function(n){r.remove(),t=null,n&&e(n.type==="error"?404:200,n.type)}),u.head.appendChild(r[0])},abort:function(){t&&t()}}}}),si=[],lt=/(=)\?(?=&|$)|\?\?/,i.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var n=si.pop()||i.expando+"_"+ui++;return this[n]=!0,n}}),i.ajaxPrefilter("json jsonp",function(r,u,f){var e,s,o,h=r.jsonp!==!1&&(lt.test(r.url)?"url":typeof r.data=="string"&&!(r.contentType||"").indexOf("application/x-www-form-urlencoded")&&lt.test(r.data)&&"data");if(h||r.dataTypes[0]==="jsonp")return e=r.jsonpCallback=i.isFunction(r.jsonpCallback)?r.jsonpCallback():r.jsonpCallback,h?r[h]=r[h].replace(lt,"$1"+e):r.jsonp!==!1&&(r.url+=(fi.test(r.url)?"&":"?")+r.jsonp+"="+e),r.converters["script json"]=function(){return o||i.error(e+" was not called"),o[0]},r.dataTypes[0]="json",s=n[e],n[e]=function(){o=arguments},f.always(function(){n[e]=s,r[e]&&(r.jsonpCallback=u.jsonpCallback,si.push(e)),o&&i.isFunction(s)&&s(o[0]),o=s=t}),"script"}),i.ajaxSettings.xhr=function(){try{return new XMLHttpRequest}catch(n){}};var g=i.ajaxSettings.xhr(),he={0:200,1223:204},ce=0,nt={};if(n.ActiveXObject)i(n).on("unload",function(){for(var n in nt)nt[n]();nt=t});i.support.cors=!!g&&"withCredentials"in g,i.support.ajax=g=!!g,i.ajaxTransport(function(n){var r;if(i.support.cors||g&&!n.crossDomain)return{send:function(i,u){var e,o,f=n.xhr();if(f.open(n.type,n.url,n.async,n.username,n.password),n.xhrFields)for(e in n.xhrFields)f[e]=n.xhrFields[e];n.mimeType&&f.overrideMimeType&&f.overrideMimeType(n.mimeType),n.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest");for(e in i)f.setRequestHeader(e,i[e]);r=function(n){return function(){r&&(delete nt[o],r=f.onload=f.onerror=null,n==="abort"?f.abort():n==="error"?u(f.status||404,f.statusText):u(he[f.status]||f.status,f.statusText,typeof f.responseText=="string"?{text:f.responseText}:t,f.getAllResponseHeaders()))}},f.onload=r(),f.onerror=r("error"),r=nt[o=ce++]=r("abort"),f.send(n.hasContent&&n.data||null)},abort:function(){r&&r()}}});var b,at,le=/^(?:toggle|show|hide)$/,uu=new RegExp("^(?:([+-])=|)("+ot+")([a-z%]*)$","i"),ae=/queueHooks$/,vt=[ye],tt={"*":[function(n,t){var f=this.createTween(n,t),s=f.cur(),u=uu.exec(t),e=u&&u[3]||(i.cssNumber[n]?"":"px"),r=(i.cssNumber[n]||e!=="px"&&+s)&&uu.exec(i.css(f.elem,n)),o=1,h=20;if(r&&r[3]!==e){e=e||r[3],u=u||[],r=+s||1;do o=o||".5",r=r/o,i.style(f.elem,n,r+e);while(o!==(o=f.cur()/s)&&o!==1&&--h)}return u&&(r=f.start=+r||+s||0,f.unit=e,f.end=u[1]?r+(u[1]+1)*u[2]:+u[2]),f}]};i.Animation=i.extend(ou,{tweener:function(n,t){i.isFunction(n)?(t=n,n=["*"]):n=n.split(" ");for(var r,u=0,f=n.length;u<f;u++)r=n[u],tt[r]=tt[r]||[],tt[r].unshift(t)},prefilter:function(n,t){t?vt.unshift(n):vt.push(n)}}),i.Tween=e,e.prototype={constructor:e,init:function(n,t,r,u,f,e){this.elem=n,this.prop=r,this.easing=f||"swing",this.options=t,this.start=this.now=this.cur(),this.end=u,this.unit=e||(i.cssNumber[r]?"":"px")},cur:function(){var n=e.propHooks[this.prop];return n&&n.get?n.get(this):e.propHooks._default.get(this)},run:function(n){var t,r=e.propHooks[this.prop];return this.pos=this.options.duration?t=i.easing[this.easing](n,this.options.duration*n,0,1,this.options.duration):t=n,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),r&&r.set?r.set(this):e.propHooks._default.set(this),this}},e.prototype.init.prototype=e.prototype,e.propHooks={_default:{get:function(n){var t;return n.elem[n.prop]!=null&&(!n.elem.style||n.elem.style[n.prop]==null)?n.elem[n.prop]:(t=i.css(n.elem,n.prop,""),!t||t==="auto"?0:t)},set:function(n){i.fx.step[n.prop]?i.fx.step[n.prop](n):n.elem.style&&(n.elem.style[i.cssProps[n.prop]]!=null||i.cssHooks[n.prop])?i.style(n.elem,n.prop,n.now+n.unit):n.elem[n.prop]=n.now}}},e.propHooks.scrollTop=e.propHooks.scrollLeft={set:function(n){n.elem.nodeType&&n.elem.parentNode&&(n.elem[n.prop]=n.now)}},i.each(["toggle","show","hide"],function(n,t){var r=i.fn[t];i.fn[t]=function(n,i,u){return n==null||typeof n=="boolean"?r.apply(this,arguments):this.animate(yt(t,!0),n,i,u)}}),i.fn.extend({fadeTo:function(n,t,i,r){return this.filter(d).css("opacity",0).show().end().animate({opacity:t},n,i,r)},animate:function(n,t,u,f){var s=i.isEmptyObject(n),o=i.speed(t,u,f),e=function(){var t=ou(this,i.extend({},n),o);(s||r.get(this,"finish"))&&t.stop(!0)};return e.finish=e,s||o.queue===!1?this.each(e):this.queue(o.queue,e)},stop:function(n,u,f){var e=function(n){var t=n.stop;delete n.stop,t(f)};return typeof n!="string"&&(f=u,u=n,n=t),u&&n!==!1&&this.queue(n||"fx",[]),this.each(function(){var s=!0,t=n!=null&&n+"queueHooks",o=i.timers,u=r.get(this);if(t)u[t]&&u[t].stop&&e(u[t]);else for(t in u)u[t]&&u[t].stop&&ae.test(t)&&e(u[t]);for(t=o.length;t--;)o[t].elem===this&&(n==null||o[t].queue===n)&&(o[t].anim.stop(f),s=!1,o.splice(t,1));(s||!f)&&i.dequeue(this,n)})},finish:function(n){return n!==!1&&(n=n||"fx"),this.each(function(){var t,e=r.get(this),u=e[n+"queue"],o=e[n+"queueHooks"],f=i.timers,s=u?u.length:0;for(e.finish=!0,i.queue(this,n,[]),o&&o.stop&&o.stop.call(this,!0),t=f.length;t--;)f[t].elem===this&&f[t].queue===n&&(f[t].anim.stop(!0),f.splice(t,1));for(t=0;t<s;t++)u[t]&&u[t].finish&&u[t].finish.call(this);delete e.finish})}}),i.each({slideDown:yt("show"),slideUp:yt("hide"),slideToggle:yt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(n,t){i.fn[n]=function(n,i,r){return this.animate(t,n,i,r)}}),i.speed=function(n,t,r){var u=n&&typeof n=="object"?i.extend({},n):{complete:r||!r&&t||i.isFunction(n)&&n,duration:n,easing:r&&t||t&&!i.isFunction(t)&&t};return u.duration=i.fx.off?0:typeof u.duration=="number"?u.duration:u.duration in i.fx.speeds?i.fx.speeds[u.duration]:i.fx.speeds._default,(u.queue==null||u.queue===!0)&&(u.queue="fx"),u.old=u.complete,u.complete=function(){i.isFunction(u.old)&&u.old.call(this),u.queue&&i.dequeue(this,u.queue)},u},i.easing={linear:function(n){return n},swing:function(n){return.5-Math.cos(n*Math.PI)/2}},i.timers=[],i.fx=e.prototype.init,i.fx.tick=function(){var u,n=i.timers,r=0;for(b=i.now();r<n.length;r++)u=n[r],u()||n[r]!==u||n.splice(r--,1);n.length||i.fx.stop(),b=t},i.fx.timer=function(n){n()&&i.timers.push(n)&&i.fx.start()},i.fx.interval=13,i.fx.start=function(){at||(at=setInterval(i.fx.tick,i.fx.interval))},i.fx.stop=function(){clearInterval(at),at=null},i.fx.speeds={slow:600,fast:200,_default:400},i.fx.step={},i.expr&&i.expr.filters&&(i.expr.filters.animated=function(n){return i.grep(i.timers,function(t){return n===t.elem}).length}),i.fn.offset=function(n){if(arguments.length)return n===t?this:this.each(function(t){i.offset.setOffset(this,n,t)});var u,e,r=this[0],f={top:0,left:0},o=r&&r.ownerDocument;if(o)return(u=o.documentElement,!i.contains(u,r))?f:(typeof r.getBoundingClientRect!==rt&&(f=r.getBoundingClientRect()),e=su(o),{top:f.top+e.pageYOffset-u.clientTop,left:f.left+e.pageXOffset-u.clientLeft})},i.offset={setOffset:function(n,t,r){var e,o,s,h,u,c,v,l=i.css(n,"position"),a=i(n),f={};l==="static"&&(n.style.position="relative"),u=a.offset(),s=i.css(n,"top"),c=i.css(n,"left"),v=(l==="absolute"||l==="fixed")&&(s+c).indexOf("auto")>-1,v?(e=a.position(),h=e.top,o=e.left):(h=parseFloat(s)||0,o=parseFloat(c)||0),i.isFunction(t)&&(t=t.call(n,r,u)),t.top!=null&&(f.top=t.top-u.top+h),t.left!=null&&(f.left=t.left-u.left+o),"using"in t?t.using.call(n,f):a.css(f)}},i.fn.extend({position:function(){if(this[0]){var n,r,u=this[0],t={top:0,left:0};return i.css(u,"position")==="fixed"?r=u.getBoundingClientRect():(n=this.offsetParent(),r=this.offset(),i.nodeName(n[0],"html")||(t=n.offset()),t.top+=i.css(n[0],"borderTopWidth",!0),t.left+=i.css(n[0],"borderLeftWidth",!0)),{top:r.top-t.top-i.css(u,"marginTop",!0),left:r.left-t.left-i.css(u,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var n=this.offsetParent||ci;n&&!i.nodeName(n,"html")&&i.css(n,"position")==="static";)n=n.offsetParent;return n||ci})}}),i.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(r,u){var f="pageYOffset"===u;i.fn[r]=function(e){return i.access(this,function(i,r,e){var o=su(i);if(e===t)return o?o[u]:i[r];o?o.scrollTo(f?n.pageXOffset:e,f?e:n.pageYOffset):i[r]=e},r,e,arguments.length,null)}}),i.each({Height:"height",Width:"width"},function(n,r){i.each({padding:"inner"+n,content:r,"":"outer"+n},function(u,f){i.fn[f]=function(f,e){var o=arguments.length&&(u||typeof f!="boolean"),s=u||(f===!0||e===!0?"margin":"border");return i.access(this,function(r,u,f){var e;return i.isWindow(r)?r.document.documentElement["client"+n]:r.nodeType===9?(e=r.documentElement,Math.max(r.body["scroll"+n],e["scroll"+n],r.body["offset"+n],e["offset"+n],e["client"+n])):f===t?i.css(r,u,s):i.style(r,u,f,s)},r,o?f:t,o,null)}})}),i.fn.size=function(){return this.length},i.fn.andSelf=i.fn.addBack,typeof module=="object"&&module&&typeof module.exports=="object"?module.exports=i:typeof define=="function"&&define.amd&&define("jquery",[],function(){return i}),typeof n=="object"&&typeof n.document=="object"&&(n.jQuery=n.$=i)})(window);
//@ sourceMappingURL=jquery-2.0.3.min.js.map