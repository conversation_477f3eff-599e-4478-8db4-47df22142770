﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/auth-listener-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/auth-listener-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/auth-listener-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/auth-listener-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/auth-listener-config/view.htm',
            type: 'get'
        }
    }

    var onType = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/auth-listener-config/get-type-list.htm',
            type: 'get'
        }
    }
    var allList = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/auth-listener-config/users-by-project.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        onType:onType,
        allList:allList
    }

});
