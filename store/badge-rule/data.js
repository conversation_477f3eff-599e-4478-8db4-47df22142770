﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/badge-rule/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/badge-rule/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/badge-rule/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/badge-rule/update.htm',
            type: 'post'
        }
    }

    var save = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/badge-rule/save.htm',
            type: 'post'
        }
       
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/badge-rule/view-by-badge-config-id.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        save: save
    }

});