/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var getAppSpaceId = {
        load: {
            url: 'jk-knowledge://api/admin/upload/get-app-space-id.htm',
            type: 'get'
        }
    }

    var getMiscAppSpaceId = {
        load: {
            url: 'jiakao-misc://api/admin/upload/get-app-space-id.htm',
            type: 'get'
        }
    }


    return {
        getAppSpaceId: getAppSpaceId,
        getMiscAppSpaceId: getMiscAppSpaceId
       
    }

});
