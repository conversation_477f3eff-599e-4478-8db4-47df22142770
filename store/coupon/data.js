﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/coupon/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coupon/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/coupon/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coupon/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/coupon/view.htm',
            type: 'get'
        }
    }

    var send = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coupon/send.htm',
            type: 'post'
        }
    }

    var generate = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coupon/generate.htm',
            type: 'post'
        }
    }


    var bizList = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/coupon/biz-type-list.htm',
            type: 'get'
        }
    }

    var bizIdList = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/coupon/biz-id-type-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        send: send,
        generate: generate,
        bizList: bizList,
        bizIdList: bizIdList
    }

});
