/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {
    var baseUrl='jiakao-misc://api/admin/crawler-answer/'
    var list = {
        load: {
            url: baseUrl+'list.htm',
            type: 'get'
        }
    }

    var audit = {
        save: {
            url: baseUrl+'audit.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: baseUrl+'update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: baseUrl+'view.htm',
            type: 'get'
        }
    }

    var publish = {
        load: {
            url: baseUrl+'publish.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        update: update,
        audit: audit,
        view: view,
        publish:publish
    }

});