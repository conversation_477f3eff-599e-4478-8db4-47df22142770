/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {
    var baseUrl='jiakao-misc://api/admin/crawler-negative-word/'
    var list = {
        load: {
            url: baseUrl+'list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: baseUrl+'delete.htm',
            type: 'post'
        }
    }

    var add = {
        primary: 'word',
        save: {
            url: baseUrl+'create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: baseUrl+'update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: baseUrl+'view.htm',
            type: 'get'
        }
    }
    var publish = {
        load: {
            url: baseUrl+'publish.htm',
            type: 'get'
        }
    }
    var upload={
        save: {
            url: baseUrl+'upload.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        add: add,
        view: view,
        publish:publish,
        upload:upload,
    }

});