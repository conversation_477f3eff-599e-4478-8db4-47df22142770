﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/drive-car/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/drive-car/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/drive-car/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/drive-car/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/drive-car/view.htm',
            type: 'get'
        }
    }

    var carTypes = {

        load: {
            url: 'jiakao-misc://api/admin/drive-car/car-types.htm',
            type: 'get'
        }
    }
    var carName= {

        load: {
            url: 'jiakao-misc://api/admin/drive-car/car-name-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        carTypes: carTypes,
        carName:carName
    }

});