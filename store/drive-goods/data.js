﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/drive-goods/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/drive-goods/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/drive-goods/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/drive-goods/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/drive-goods/view.htm',
            type: 'get'
        }
    }

    var typeList = {

        load: {
            url: 'jiakao-misc://api/admin/drive-goods/type-list.htm',
            type: 'get'
        }
    }
    var permissionList = {

        load: {
            url: 'jiakao-misc://api/admin/drive-goods/permission-list.htm',
            type: 'get'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        typeList: typeList,
        permissionList: permissionList
    }

});