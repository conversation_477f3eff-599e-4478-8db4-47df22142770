﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/drive-permission/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/drive-permission/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/drive-permission/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/drive-permission/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/drive-permission/view.htm',
            type: 'get'
        }
    }
    var keyList = {

        load: {
            url: 'jiakao-misc://api/admin/drive-permission/key-list.htm',
            type: 'get'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        keyList:keyList
    }

});