/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var edit = {
        load: {
            url: 'jiakao-misc://api/admin/route-map/edit.htm',
            type: 'get'
        }
    }
    var allProject = {
        load: {
            url: 'jiakao-misc://api/admin/route-map/point-name-list.htm',
            type: 'get'
        }
    }

    var kaochangList = {
        load: {
            url: 'jiakao-misc://api/admin/route-map/place-list.htm',
            type: 'get'
        }
    }
    var luxianList = {
        load: {
            url: 'jiakao-misc://api/admin/route-map/route-list.htm',
            type: 'get'
        }
    }
   
    var saveProject = {
        save: {
            url: 'jiakao-misc://api/admin/route-map/save-map.htm',
            type: 'post'
        }
    }


    return {
        edit: edit,
        allProject: allProject,
        kaochangList: kaochang<PERSON>ist,
        luxianList: luxian<PERSON>ist,
        saveProject: saveProject
    }

});
