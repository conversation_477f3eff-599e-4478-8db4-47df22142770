﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-brand/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-brand/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/emulator-brand/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-brand/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/emulator-brand/view.htm',
            type: 'get'
        }
    }

    var brandList = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-brand/drop-down-list.htm',
            type: 'get',
            format: function (json) {
                var data = json.itemList || json
                data = data.map(item => {
                    return {
                        key: item.id,
                        value: item.name,
                    }
                })
                console.log(data)
                return data
            }
        }
    }
    

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        brandList,
    }

});