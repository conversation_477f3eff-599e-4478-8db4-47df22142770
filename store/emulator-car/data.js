﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-car/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-car/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/emulator-car/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-car/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/emulator-car/view.htm',
            type: 'get'
        }
    }
    var carList = { 
        load: {
            url: 'jiakao-misc://api/admin/emulator-car/drop-down-list.htm',
            type: 'get',
            format: function (json) {
                var data = json.itemList || json
                data = data.map(item => {
                    const {name, nameYear} = item
                    let value = name
                    if (nameYear) {
                        value += `(${item.nameYear})`
                    }
                    return {
                        key: item.id,
                        value
                    }
                })
                return data
            }
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        carList:carList,
        
    }

});