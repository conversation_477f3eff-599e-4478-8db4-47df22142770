﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-city/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-city/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/emulator-city/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-city/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/emulator-city/view.htm',
            type: 'get'
        }
    }

    
    var getConfig = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/emulator-city/get-light-pay-config.htm',
            type: 'get'
        }
    }

    var updateConfig = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-city/update-light-pay-config.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getConfig: getConfig,
        updateConfig:updateConfig
    }

});