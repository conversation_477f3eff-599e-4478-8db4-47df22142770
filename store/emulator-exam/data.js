﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-exam/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-exam/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/emulator-exam/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-exam/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/emulator-exam/view.htm',
            type: 'get'
        }
    }
    var createExam = {

        save: {
            url: 'jiakao-misc://api/admin/emulator-exam/create-exam-list.htm',
            type: 'post'
        }
    }
    var getMetaByCity = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-exam/get-meta-by-city.htm',
            type: 'get'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        createExam: createExam,
        getMetaByCity: getMetaByCity
    }

});