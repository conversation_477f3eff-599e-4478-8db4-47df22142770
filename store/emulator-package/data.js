﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-package/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-package/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/emulator-package/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-package/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/emulator-package/view.htm',
            type: 'get'
        }
    }
    var typeList = {
   
        load: {
            url: 'jiakao-misc://api/admin/emulator-type/type-list.htm',
            type: 'get'
        }
    }
    var createPackage = {
   
        load: {
            url: 'jiakao-misc://api/admin/emulator-package/create-package.htm',
            type: 'get'
        }
    }
    var uploadDefaultData = {
   
        save: {
            url: 'jiakao-misc://api/admin/emulator-package/upload-default-data.htm',
            type: 'post'
        }
    }
    
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        typeList:typeList,
        createPackage:createPackage,
        uploadDefaultData:uploadDefaultData
    }

});