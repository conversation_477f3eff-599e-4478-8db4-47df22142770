﻿/*
 * data v0.0.1
 *
 * name: xiaoji<PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'elephant://api/admin/emulator-practice/list.htm',
            type: 'get'
        }
    }

    var carList = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-car/get-car-list.htm',
            type: 'get'
        }
    }



    return {
        list: list,
        carList: carList
    }

});