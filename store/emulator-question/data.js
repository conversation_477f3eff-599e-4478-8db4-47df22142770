﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-question/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-question/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/emulator-question/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/emulator-question/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/emulator-question/view.htm',
            type: 'get'
        }
    }

    var refresh = {

        load: {
            url: 'jiakao-misc://api/admin/emulator-question/refresh.htm',
            type: 'get'
        }
    }

    var audio = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-question/textToSpeech.htm',
            type: 'get'
        }
    }
    var getNeedRelation = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-question/find-exclude-type-question-list.htm',
            type: 'get'
        }
    }
    var relationCreate = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-type-question/batch-create.htm',
            type: 'get'
        }
    }


    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        refresh: refresh,
        audio: audio,
        getNeedRelation: getNeedRelation,
        relationCreate: relationCreate
    }

});