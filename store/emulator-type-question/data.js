/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-type-question/list.htm',
            type: 'get'
        }
    }
    var deleteRelation = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-type-question/delete.htm',
            type: 'get'
        }
    }
    var getGuideList = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-type/guide-list.htm',
            type: 'get'
        }
    }
    var updateGuide = {
        load: {
            url: 'jiakao-misc://api/admin/emulator-type-question/update.htm',
            type: 'get'
        }
    }
    var sigleUpdate = {
        save: {
            url: 'jiakao-misc://api/admin/emulator-type-question/update.htm',
            type: 'post'
        }
    }


    return {
        list: list,
        deleteRelation: deleteRelation,
        getGuideList: getGuideList,
        updateGuide: updateGuide,
        sigleUpdate: sigleUpdate
    }

});