﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/entries/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/entries/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/entries/view.htm',
            type: 'get'
        }
    }

    var detailList = {
        load: {
            url: 'jiakao-misc://api/admin/entries-detail/list.htm',
            type: 'get'
        }
    }

    var detailDel = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries-detail/delete.htm',
            type: 'post'
        }
    }

    var detailInsert = {
        save: {
            url: 'jiakao-misc://api/admin/entries-detail/create.htm',
            type: 'post'
        }
    }

    var detailUpdate = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries-detail/update.htm',
            type: 'post'
        }
    }

    var detailView = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/entries-detail/view.htm',
            type: 'get'
        }
    }

    var functionsList = {
        load: {
            url: 'jiakao-misc://api/admin/entries-function/list.htm',
            type: 'get'
        }
    }

    var functionsDel = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries-function/delete.htm',
            type: 'post'
        }
    }

    var functionsInsert = {
        save: {
            url: 'jiakao-misc://api/admin/entries-function/create.htm',
            type: 'post'
        }
    }

    var functionsUpdate = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries-function/update.htm',
            type: 'post'
        }
    }

    var functionsView = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/entries-function/view.htm',
            type: 'get'
        }
    }
    
    var referenceMaterialList = {
        load: {
            url: 'jiakao-misc://api/admin/entries-material/list.htm',
            type: 'get'
        }
    }

    var referenceMaterialDel = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries-material/delete.htm',
            type: 'post'
        }
    }

    var referenceMaterialInsert = {
        save: {
            url: 'jiakao-misc://api/admin/entries-material/create.htm',
            type: 'post'
        }
    }

    var referenceMaterialUpdate = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/entries-material/update.htm',
            type: 'post'
        }
    }

    var referenceMaterialView = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/entries-material/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        del: del,
        update: update,
        insert: insert,
        view: view,
        detailList: detailList,
        detailDel: detailDel,
        detailInsert: detailInsert,
        detailUpdate: detailUpdate,
        detailView:detailView,
        functionsList: functionsList,
        functionsDel: functionsDel,
        functionsInsert: functionsInsert,
        functionsUpdate: functionsUpdate,
        functionsView: functionsView,
        referenceMaterialList: referenceMaterialList,
        referenceMaterialDel: referenceMaterialDel,
        referenceMaterialInsert: referenceMaterialInsert,
        referenceMaterialUpdate: referenceMaterialUpdate,
        referenceMaterialView: referenceMaterialView
    }

});