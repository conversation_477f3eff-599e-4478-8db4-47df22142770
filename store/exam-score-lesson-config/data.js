﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/exam-score-lesson-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/exam-score-lesson-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/exam-score-lesson-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/exam-score-lesson-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/exam-score-lesson-config/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});