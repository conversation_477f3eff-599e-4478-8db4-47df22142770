﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'panda://api/admin/first-lesson-video/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/first-lesson-video/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'panda://api/admin/first-lesson-video/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/first-lesson-video/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'panda://api/admin/first-lesson-video/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});