﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>ji<PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/good-student-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/good-student-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/good-student-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/good-student-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/good-student-config/view.htm',
            type: 'get'
        }
    }

    var getConfig = {
        primary: 'id',
        load: {
            url: 'swallow://api/abstracted/abstract-config/get-config.htm',
            type: 'get'
        }
    }

    var updateStatus = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/good-student-config/update-status.htm',
            type: 'post'
        }
    }


    var abTestList = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/ab-test/list.htm',
            type: 'get',
            format: function (o) {
                let newArray = []
                o && o.forEach((res) => {
                    newArray.push({
                        ...res,
                        testKey: res.testKey + '-' + res.id
                    })
                })
                return newArray
            }
        },
      
    }


    var viewAbTest = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/ab-test/view.htm',
            type: 'get'
        }
     }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getConfig,
        updateStatus,
        abTestList,
        viewAbTest
    }

});