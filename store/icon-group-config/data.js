﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/icon-group-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/icon-group-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/icon-group-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/icon-group-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/icon-group-config/view.htm',
            type: 'get'
        }
    }
    var groupIdslist = {
        load: {
            url: 'sirius://api/admin/traffic-icon-group/list.htm',
            type: 'get',
            format: function (o) {
                let newArray = []
                o && o.forEach((res) => {
                    newArray.push({
                        ...res,
                        showName: res.name.HANYU + '--' + res.id
                    })
                })
                return newArray
            }
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        groupIdslist: groupIdslist
    }

});