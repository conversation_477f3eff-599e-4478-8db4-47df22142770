﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/icon-item-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/icon-item-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/icon-item-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/icon-item-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/icon-item-config/view.htm',
            type: 'get'
        }
    }
    var itemlist = {
        load: {
            url: 'sirius://api/admin/traffic-icon-item/list.htm',
            type: 'get',
            format: function (o) {
                let newArray = []
                o && o.forEach((res) => {
                    newArray.push({
                        ...res,
                        showName: res.title.HANYU + '--' + res.id
                    })
                })
                return newArray
            }
        }
    }
    var templateDownload = {
        load: {
            url: 'jiakao-misc://api/admin/icon-item-config/template-download.htm',
            type: 'get'
        }
    }
    var excelImport = {
        load: {
            url: 'jiakao-misc://api/admin/icon-item-config/excel-import.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        itemlist: itemlist,
        templateDownload: templateDownload,
        excelImport: excelImport
    }

});