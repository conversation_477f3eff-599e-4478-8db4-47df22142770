﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-search://api/admin/image-recognition-record/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/image-recognition-record/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-search://api/admin/image-recognition-record/create.htm',
            type: 'post'
        }
    }
    var batchMark = {
        save: {
            url: 'jiakao-search://api/admin/image-recognition-record/batch-update-status.htm',
            type: 'post'
        }
    }
    
    
    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/image-recognition-record/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-search://api/admin/image-recognition-record/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        batchMark: batchMark,
        insert: insert,
        view: view
    }

});