﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(['jiakao-misc!app/common/constants'], function (Constants) {

    var list = {
        load: {
            url: 'jiakao-search://api/admin/info-flow-group/list.htm',
            type: 'get',
            format:function(o){
                let newArr = []
                o&&o.forEach((res)=>{
                    newArr.push({
                        ...res,
                        showGroup: Constants.kemuMap[res.course] + '-' + Constants.carTypeMap[res.carType] + '-' + res.title +'('+res.id+')'
                    })
                })
                return newArr

            }
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/info-flow-group/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-search://api/admin/info-flow-group/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/info-flow-group/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-search://api/admin/info-flow-group/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});