﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-search://api/admin/info-flow-item/get-admin-info-flow-list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/info-flow-item/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-search://api/admin/info-flow-item/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/info-flow-item/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-search://api/admin/info-flow-item/view.htm',
            type: 'get'
        }
    }
    var online = {
        primary: 'id',
        load: {
            url: 'jiakao-search://api/admin/info-flow-item/online.htm',
            type: 'get'
        }
    }
    var divisor = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/info-flow-item/divisor.htm',
            type: 'post'
        }
    }
    var queryDivisor = {
        primary: 'id',
        load: {
            url: 'jiakao-search://api/admin/info-flow-item/queryDivisor.htm',
            type: 'get'
        }
    }
    var weight = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/info-flow-item/weight.htm',
            type: 'post'
        }
    }
    var queryWeight = {
        primary: 'id',
        load: {
            url: 'jiakao-search://api/admin/info-flow-item/query-weight.htm',
            type: 'get'
        }
    }
    var hidden = {
        save: {
            url: 'jiakao-search://api/admin/info-flow-item/hidden.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        online: online,
        online: online,
        divisor: divisor,
        queryDivisor: queryDivisor,
        weight: weight,
        queryWeight: queryWeight,
        hidden: hidden
    }

});