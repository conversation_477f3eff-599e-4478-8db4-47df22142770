﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/international-drive-license/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/international-drive-license/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/international-drive-license/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/international-drive-license/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/international-drive-license/view.htm',
            type: 'get'
        }
    }
    var viewPhone = {
        load: {
            url: 'jiakao-misc://api/admin/international-drive-license/view-phone.htm',
            type: 'get'
        }
    }
    var handleBatch = {
        save: {
            url: 'jiakao-misc://api/admin/International-drive-license/handle-batch.htm?',
            type: 'post'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        viewPhone: viewPhone,
        handleBatch: handleBatch

    }

});