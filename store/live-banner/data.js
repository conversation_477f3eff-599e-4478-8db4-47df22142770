﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/live-banner/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-banner/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/live-banner/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-banner/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/live-banner/view.htm',
            type: 'get'
        }
    }

    var enable = {
        save: {
            url: 'jiakao-misc://api/admin/live-banner/enable.htm',
            type: 'post'
        }
    }

    var disable = {
        save: {
            url: 'jiakao-misc://api/admin/live-banner/disable.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        enable: enable,
        disable: disable
    }

});