﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/live-schedule/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-schedule/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/live-schedule/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-schedule/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/live-schedule/view.htm',
            type: 'get'
        }
    }

    var delDay = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-schedule-day/delete.htm',
            type: 'post'
        }
    }

    var insertDay = {
        save: {
            url: 'jiakao-misc://api/admin/live-schedule-day/create.htm',
            type: 'post'
        }
    }

    var delItem = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-schedule-item/delete.htm',
            type: 'post'
        }
    }

    var insertItem = {
        save: {
            url: 'jiakao-misc://api/admin/live-schedule-item/create.htm',
            type: 'post'
        }
    }

    var updateItem = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-schedule-item/update.htm',
            type: 'post'
        }
    }

    var viewItem = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/live-schedule-item/view.htm',
            type: 'get'
        }
    }

    var getLiveCalendar = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/live-schedule/get-live-calendar.htm',
            type: 'get'
        }
    }

    var arrangeSchedule = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/live-schedule/schedule-lesson.htm',
            type: 'post'
        }
    }
    var listRecord = {
        load: {
            url: 'jiakao-misc://api/admin/live-schedule-record/list.htm',
            type: 'get'
        }
    }
    var listRecordSchedule = {
        load: {
            url: 'jiakao-misc://api/admin/live-schedule-record/get-schedule-record-detail.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        delDay: delDay,
        insertDay: insertDay,
        delItem: delItem,
        insertItem: insertItem,
        updateItem: updateItem,
        viewItem: viewItem,
        getLiveCalendar: getLiveCalendar,
        arrangeSchedule: arrangeSchedule,
        listRecord: listRecord,
        listRecordSchedule: listRecordSchedule
    }

});