﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'monkey://api/admin/live-stock-rule/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-stock-rule/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'monkey://api/admin/live-stock-rule/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-stock-rule/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-stock-rule/view.htm',
            type: 'get'
        }
    }

    var updateEnlargeInfo = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-stock-rule/update-enlarge-info.htm',
            type: 'post'
        }
    }

    var updateAutoEecrConfig = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-stock-rule/update-auto-decr-config.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        updateEnlargeInfo,
        updateAutoEecrConfig
    }

});