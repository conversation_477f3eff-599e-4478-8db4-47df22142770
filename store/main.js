/*
 * main v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

'use strict';

define(function () {
  var header = {
    load: {
      type: "data",
      data: [
        {
          name: "驾考",
          item: "header-nav",
          aside: "jiakao-misc!main/aside",
        },
        {
          name: "灯光模拟",
          item: "header-nav",
          aside: "jiakao-misc!main/aside1",
        },
        // {
        //     name: '66学车节',
        //     item: 'header-nav',
        //     aside: 'jiakao-misc!main/aside5'
        // },
        {
          name: "问答数据采集",
          item: "header-nav",
          aside: "jiakao-misc!main/aside6",
        },
        {
          name: "负面词采集",
          item: "header-nav",
          aside: "jiakao-misc!main/aside7",
        },
        {
          name: "精品课程",
          item: "header-nav",
          aside: "jiakao-misc!main/aside8",
        },
        {
          name: "关键点管理",
          item: "header-nav",
          aside: "jiakao-misc!main/aside9",
        },
        {
          name: "帮助与反馈",
          item: "header-nav",
          aside: "jiakao-misc!main/aside10",
        },
        {
          name: "学车生涯",
          item: "header-nav",
          aside: "jiakao-misc!main/aside11",
        },
        {
          name: "交通强国",
          item: "header-nav",
          aside: "jiakao-misc!main/aside12",
        },
        {
          name: "搜索后台",
          item: "header-nav",
          aside: "jiakao-misc!main/aside13",
        },
        {
          name: "彩蛋活动",
          item: "header-nav",
          aside: "jiakao-misc!main/aside14",
        },
        {
          name: "驾考知识库",
          item: "header-nav",
          aside: "jiakao-misc!main/aside16",
        },
        {
          name: "运营位配置",
          item: "header-nav",
          aside: "jiakao-misc!main/aside17",
        },
        {
          name: '视频数据合成',
          item: 'header-nav',
          aside: 'jiakao-misc!main/aside18'
        },
        {
          name: '双清单',
          item: 'header-nav',
          aside: 'jiakao-misc!main/aside19'
        },
        {
          name: '3d',
          item: 'header-nav',
          aside: 'jiakao-misc!main/aside20'
        },
        {
          name: '同学会',
          item: 'header-nav',
          aside: 'jiakao-misc!main/aside21'
        }
      ].concat(window.j.userInfo.roles.indexOf('vip管理员') !== -1 ? [
        {
          name: '管理员界面',
          item: 'header-nav',
          aside: 'jiakao-misc!main/aside15'

        }
      ] : []),
    },
  };
  var aside18 = {
    load: {
      type: 'data',
      data: [
        {
          name: '视频数据合成',
          nodes: [
            {
              id: 'route-video-process',
              name: '初剪视频管理',
              app: 'jiakao-misc!app/route-video-process/index/list',
              item: 'aside-nav'
            },
            {
              id: 'route-video-process2',
              name: '路线视频成片管理',
              app: 'jiakao-misc!app/route-video-process2/index/list',
              item: 'aside-nav'
            },
            {
              id: 'route-video-process3',
              name: '互动视频时间轴编辑',
              app: 'jiakao-misc!app/route-video-process3/index/list',
              item: 'aside-nav'
            },
            {
              id: 'route-video-process4',
              name: '视频二次剪辑更新',
              app: 'jiakao-misc!app/route-video-process4/index/list',
              item: 'aside-nav'
            },
            // {
            //     id: 'film-video',
            //     name: "初剪视频管理",
            //     app: 'jiakao-misc!app/edit-video/index/list',
            //     item: 'aside-nav'
            // },
          ]
        }
      ]
    }
  }
  var aside17 = {
    load: {
      type: "data",
      type: "data",
      data: [
        {
          name: "运营位配置",
          nodes: [
            {
              id: 'operation-config2',
              name: '课程详情页配置',
              app: 'jiakao-misc!app/operation-config2/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config',
              name: '我的页面配置',
              app: 'jiakao-misc!app/operation-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config3',
              name: '考试/练习结果页配置',
              app: 'jiakao-misc!app/operation-config3/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config4',
              name: '科目首页配置',
              app: 'jiakao-misc!app/operation-config4/index/list',
              item: 'aside-nav'
            },
            {
              id: 'resource-group',
              name: '资源位管理',
              app: 'jiakao-misc!app/resource-group/index/list',
              item: 'aside-nav'
            },
            {
              id: 'resource-item',
              name: '资源详情管理',
              app: 'jiakao-misc!app/resource-item/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config5',
              name: 'pc直播运营位配置',
              app: 'jiakao-misc!app/operation-config5/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config6',
              name: '科二3D列表页配置',
              app: 'jiakao-misc!app/operation-config6/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config7',
              name: '科目2首页练考试项目运营位',
              app: 'jiakao-misc!app/operation-config7/index/list',
              item: 'aside-nav'
            },
            // {
            //     id: 'operation-config8',
            //     name: '分享页配置',
            //     app: 'jiakao-misc!app/operation-config8/index/list',
            //     item: 'aside-nav'
            //   },
            {
              id: "exam-rule-config",
              name: "考前辅导规则",
              app: "jiakao-misc!app/exam-rule-config/index/list",
              item: "aside-nav"
            },
            {
              id: 'good-student-config',
              name: '优秀学员配置',
              app: 'jiakao-misc!app/good-student-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'tiku-weekly-config',
              name: '题库周报配置',
              app: 'jiakao-misc!app/tiku-weekly-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'kemu-index-config',
              name: '科目首页课程配置',
              app: 'jiakao-misc!app/kemu-index-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'pinxuan-index-config',
              name: '品宣卡片配置',
              app: 'jiakao-misc!app/pinxuan-index-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config10',
              name: '科目合格率考试提醒配置',
              app: 'jiakao-misc!app/operation-config10/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config-k3-lighting',
              name: '科目三灯光模拟城市图片配置',
              app: 'jiakao-misc!app/operation-config-k3-lighting/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config-exam-practice',
              name: '考试/练习前置页配置',
              app: 'jiakao-misc!app/operation-config-exam-practice/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config11',
              name: '真实考场模拟前置页',
              app: 'jiakao-misc!app/operation-config11/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config-error-prone-point',
              name: '易错考点专练详情页配置',
              app: 'jiakao-misc!app/operation-config-error-prone-point/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config-error-prone-point-entry',
              name: '易错考点入口配置',
              app: 'jiakao-misc!app/operation-config-error-prone-point-entry/index/list',
              item: 'aside-nav'
            },
            {
              id: 'exam-page-config',
              name: '做题页配置',
              app: 'jiakao-misc!app/exam-page-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'goods-video',
              name: '商品介绍视频配置',
              app: 'jiakao-misc!app/goods-video/index/list',
              item: 'aside-nav'
            },
            {
              id: 'knowledge-detail-config',
              name: '考点详情页运营位配置',
              app: 'jiakao-misc!app/knowledge-detail-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'pass-rate-analysis-config',
              name: '通过率分析运营位配置',
              app: 'jiakao-misc!app/pass-rate-analysis-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'exam-transcript',
              name: '模拟考试成绩单',
              app: 'jiakao-misc!app/exam-transcript/index/list',
              item: 'aside-nav'
            },
            {
              id: 'jiaxiao-recommend-config',
              name: '驾校推荐配置',
              app: 'jiakao-misc!app/jiaxiao-recommend-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'saturn-homepage-config',
              name: '社区主页配置',
              app: 'jiakao-misc!app/saturn-homepage-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'navigation-bar-config',
              name: '导航栏配置',
              app: 'jiakao-misc!app/navigation-bar-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'new-age-education-guide-config',
              name: '新手引导-学历+年龄跳转配置',
              app: 'jiakao-misc!app/new-age-education-guide-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'restore_driver_license',
              name: '恢复驾驶证首页运营位',
              app: 'jiakao-misc!app/restore_driver_license/index/list',
              item: 'aside-nav'
            },
            {
              id: 'kemu_index_mentor_mentor',
              name: '科目首页私教运营位',
              app: 'jiakao-misc!app/kemu_index_mentor_mentor/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config12',
              name: '做题页答题卡运营位',
              app: 'jiakao-misc!app/operation-config12/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config13',
              name: '做题页底部悬浮运营位',
              app: 'jiakao-misc!app/operation-config13/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config14',
              name: '启动全局推荐',
              app: 'jiakao-misc!app/operation-config14/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config15',
              name: '答题背景配置',
              app: 'jiakao-misc!app/operation-config15/index/list',
              item: 'aside-nav'
            },
            {
              id: 'operation-config16',
              name: 'AI陪练的主入口配置',
              app: 'jiakao-misc!app/operation-config16/index/list',
              item: 'aside-nav'
            }
          ],
        }
      ],
    },
  }
  var aside11 = {
    load: {
      type: "data",
      type: "data",
      data: [
        {
          name: "驾考-里程碑",
          nodes: [
            {
              id: "user-milestone",
              name: "用户里程碑查询",
              app: "jiakao-misc!app/user-milestone/index/list",
              item: "aside-nav",
            },
            {
              id: "user-milestone-node",
              name: "里程碑列表",
              app: "jiakao-misc!app/user-milestone-node/index/list",
              item: "aside-nav",
            },
          ],
        },
      ],
    },
  };
  var aside10 = {
    load: {
      type: "data",
      data: [
        {
          name: "帮助与反馈",
          nodes: [
            {
              id: "help-self-service",
              name: "自助服务配置",
              app: "jiakao-misc!app/help-self-service/index/list",
              item: "aside-nav",
            },
            {
              id: "help-label",
              name: "标签配置",
              app: "jiakao-misc!app/help-label/index/list",
              item: "aside-nav",
            },
            {
              id: "help-question",
              name: "问答列表",
              app: "jiakao-misc!app/help-question/index/list",
              item: "aside-nav",
            },
            {
              id: "want-ask",
              name: "猜你想问页面入口",
              app: "jiakao-misc!app/want-ask/index/list",
              item: "aside-nav",
            },
          ],
        },
      ],
    },
  };
  var aside9 = {
    load: {
      type: "data",
      data: [
        {
          name: "关键点管理",
          nodes: [
            {
              id: "timeline-config",
              name: "时间轴业务配置列表",
              app: "jiakao-misc!app/timeline-config/index/list",
              item: "aside-nav",
            },
            {
              id: "timeline-keypoint",
              name: "时间轴关键点配置列表",
              app: "jiakao-misc!app/timeline-keypoint/index/list",
              item: "aside-nav",
            },
            {
              id: "timeline-res",
              name: "时间轴关键点资源信息",
              app: "jiakao-misc!app/timeline-res/index/list",
              item: "aside-nav",
            },
            {
              id: "timeline-res-rel",
              name: "timeline-res-rel列表 ",
              app: "jiakao-misc!app/timeline-res-rel/index/list",
              item: "aside-nav",
            },
          ],
        },
      ],
    },
  };
  var aside8 = {
    load: {
      type: "data",
      data: [
        {
          name: "精品课程",
          nodes: [
            // {
            //   id: "live-schedule",
            //   name: "课程表管理",
            //   app: "jiakao-misc!app/live-schedule/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "live-schedule-arranged",
            //   name: "排课记录",
            //   app: "jiakao-misc!app/live-schedule-arranged/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "top-lesson-sys-message",
            //   name: "直播间系统消息",
            //   app: "jiakao-misc!app/top-lesson-sys-message/index/list",
            //   item: "aside-nav",
            // },
            {
              id: "top-lesson-group",
              name: "名师精品课程",
              app: "jk-knowledge!app/top-lesson-group/index/list",
              item: "aside-nav",
            },
            // {
            //   id: "top-lesson-item",
            //   name: "名师精品子课程-直播",
            //   app: "jiakao-misc!app/top-lesson-item/index/list",
            //   item: "aside-nav",
            // },
            {
              id: "top-lesson-item-video",
              name: "名师精品子课程-视频",
              app: "jk-knowledge!app/top-lesson-item-video/index/list",
              item: "aside-nav",
            },
            {
              id: "knowledge-lesson-config",
              name: '课程知识点',
              app: "jk-knowledge!app/knowledge-lesson-config/index/list",
              item: "aside-nav",
            },
            {
              id: "lesson-rank-config",
              name: "课程排名配置",
              app: "panda!app/lesson-rank-config/index/list",
              item: "aside-nav",
            },
            // {
            //   id: "live-banner",
            //   name: "直播banner配置",
            //   app: "jiakao-misc!app/live-banner/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "top-lesson-detail",
            //   name: "子课程详情管理",
            //   app: "jiakao-misc!app/top-lesson-detail/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "top-lesson-teacher-rank",
            //   name: "讲师排名",
            //   app: "jiakao-misc!app/top-lesson-teacher-rank/index/list",
            //   item: "aside-nav",
            // },
            {
              id: "top-lesson-teacher",
              name: "讲师管理",
              app: "jk-knowledge!app/top-lesson-teacher/index/list",
              item: "aside-nav",
            },
            {
              id: "top-lesson-tag",
              name: "标签管理",
              app: "jk-knowledge!app/top-lesson-tag/index/list",
              item: "aside-nav",
            },
            // {
            //   id: "top-lesson-img",
            //   name: "图片素材库",
            //   app: "jiakao-misc!app/top-lesson-img/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "top-lesson-item-tpl",
            //   name: "子课程模板",
            //   app: "jiakao-misc!app/top-lesson-item-tpl/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "top-lesson-advert-tpl",
            //   name: "运营位模板",
            //   app: "jiakao-misc!app/top-lesson-advert-tpl/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "top-lesson-recommend-goods-tpl",
            //   name: "推荐商品弹窗模板",
            //   app: "jiakao-misc!app/top-lesson-recommend-goods-tpl/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "live-stock-rule",
            //   name: "优惠库存特殊规则配置",
            //   app: "jiakao-misc!app/live-stock-rule/index/list",
            //   item: "aside-nav",
            // },
            {
              id: "top-lesson-package",
              name: "名师精品课程打包售卖",
              app: "jiakao-misc!app/top-lesson-package/index/list",
              item: "aside-nav",
            },
            // {
            //   id: "draw-prize",
            //   name: "直播抽奖场次配置",
            //   app: "jiakao-misc!app/draw-prize/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "win-prize-user",
            //   name: "直播中奖人员名单",
            //   app: "jiakao-misc!app/win-prize-user/index/list",
            //   item: "aside-nav",
            // },
            {
              id: "top-lesson-ranklist",
              name: "名师精品课程排行榜",
              app: "panda!app/top-lesson-ranklist/index/list",
              item: "aside-nav",
            },
            {
              id: "top-lesson-step-config",
              name: "名师精品课四步抢分配置",
              app: "panda!app/top-lesson-step-config/index/list",
              item: "aside-nav",
            },
            // {
            //   id: "surprise-bag",
            //   name: "福袋抽奖-活动管理",
            //   app: "jiakao-misc!app/surprise-bag/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "surprise-bag-prize",
            //   name: "福袋抽奖-奖品管理",
            //   app: "jiakao-misc!app/surprise-bag-prize/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "surprise-bag-record",
            //   name: "福袋抽奖参与记录",
            //   app: "jiakao-misc!app/surprise-bag-record/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "surprise-bag-winner",
            //   name: "福袋中奖信息-实物",
            //   app: "jiakao-misc!app/surprise-bag-winner/index/list",
            //   item: "aside-nav",
            // },
            // {
            //   id: "surprise-bag-virtua-winner",
            //   name: "福袋中奖信息-虚拟",
            //   app: "jiakao-misc!app/surprise-bag-virtual-winner/index/list",
            //   item: "aside-nav",
            // },
            {
              id: "lesson-activity-config",
              name: "活动管理",
              app: "jiakao-misc!app/lesson-activity-config/index/list",
              item: "aside-nav",
            },
            {
              id: 'lesson-album',
              name: '专辑配置',
              app: 'panda!app/lesson-album/index/list',
              item: 'aside-nav'
            },
            {
              id: 'exam-score-lesson-config',
              name: '课程模拟考试',
              app: 'panda!app/exam-score-lesson-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'tag-album',
              name: '专项强化合集',
              app: 'panda!app/tag-album/index/list',
              item: 'aside-nav'
            },
            //   {
            //     id: 'top-lesson-share-config',
            //     name: '分享入口配置',
            //     app: 'jiakao-misc!app/top-lesson-share-config/index/list',
            //     item: 'aside-nav'
            //   },

            {
              id: 'recommend-config',
              name: '推荐数据配置',
              app: 'panda!app/recommend-config/index/list',
              item: 'aside-nav'
            }
          ],
        },
      ],
    },
  };
  var aside7 = {
    load: {
      type: "data",
      data: [
        {
          name: "负面词采集",
          nodes: [
            {
              id: "upload-negativewords",
              name: "上传负面词",
              app: "jiakao-misc!app/upload-negativewords/index/list",
              item: "aside-nav",
            },
            {
              id: "negative-list",
              name: "采集列表",
              app: "jiakao-misc!app/crawler-negativewords/index/list",
              item: "aside-nav",
            },
          ],
        },
      ],
    },
  };
  var aside6 = {
    load: {
      type: "data",
      data: [
        {
          name: "问答数据采集",
          nodes: [
            {
              id: "upload-keyword",
              name: "上传关键字",
              app: "jiakao-misc!app/upload-keyword/index/list",
              item: "aside-nav",
            },
            {
              id: "crawler-list",
              name: "采集列表",
              app: "jiakao-misc!app/crawler-list/index/list",
              item: "aside-nav",
            },
          ],
        },
      ],
    },
  };
  var aside5 = {
    load: {
      type: "data",
      data: [
        {
          name: "驾考",
          nodes: [
            {
              id: "xueche-live",
              name: "学车节直播",
              app: "jiakao-misc!app/xueche-live/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-lottery",
              name: "抽奖券列表",
              app: "jiakao-misc!app/xueche-lottery/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-user-info",
              name: "用户报名信息",
              app: "jiakao-misc!app/xueche-user-info/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-notify",
              name: "直播提醒",
              app: "jiakao-misc!app/xueche-notify/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-user-share",
              name: "用户分享",
              app: "jiakao-misc!app/xueche-user-share/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-present",
              name: "奖品列表",
              app: "jiakao-misc!app/xueche-present/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-win-prize-record",
              name: "中奖记录表",
              app: "jiakao-misc!app/xueche-win-prize-record/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-fantastic-video",
              name: "学车节精彩视频表",
              app: "jiakao-misc!app/xueche-fantastic-video/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-award-round",
              name: "中奖轮次",
              app: "jiakao-misc!app/xueche-award-round/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-statistics",
              name: "学车节数据统计表",
              app: "jiakao-misc!app/xueche-statistics/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-barrage",
              name: "学车节弹幕内容表",
              app: "jiakao-misc!app/xueche-barrage/index/list",
              item: "aside-nav",
            },
            {
              id: "xueche-cover",
              name: "学车节视频封面表",
              app: "jiakao-misc!app/xueche-cover/index/list",
              item: "aside-nav",
            },
          ],
        },
      ],
    },
  };
  var aside = {
    load: {
      type: "data",
      data: [
        {
          name: "驾考",
          nodes: [
            {
              id: 'permission',
              name: '用户权限',
              app: 'jiakao-misc!app/permission/index/list',
              item: 'aside-nav'
            },
            {
              id: "user-cloud-data",
              name: "用户做题数据",
              app: "jiakao-misc!app/user-cloud-data/index/list",
              item: "aside-nav",
            },
            {
              id: "exam-instruction-clue",
              name: "考场路线预约",
              app: "jiakao-misc!app/exam-instruction-clue/index/list",
              item: "aside-nav",
            },
            //  {
            //     id: 'auth-listener-config',
            //     name: '登陆监听配置',
            //     app: 'jiakao-misc!app/auth-listener-config/index/list',
            //     item: 'aside-nav'
            // },
            {
              id: "maiche-rec",
              name: "买车引流推荐",
              app: "jiakao-misc!app/maiche-rec/index/list",
              item: "aside-nav",
            },
            {
              id: "shake",
              name: "抖抖抖抽奖列表",
              app: "jiakao-misc!app/shake/index/list",
              item: "aside-nav",
            },
            {
              id: "clipboard-commend",
              name: "淘口令",
              app: "jiakao-misc!app/clipboard-commend/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-clew",
              name: "考试路线",
              app: "jiakao-misc!app/route-video-clew/index/list",
              item: "aside-nav",
            },
            {
              id: "must-exercise-list",
              name: "必刷题合集",
              app: "jiakao-misc!app/must-exercise-list/index/list",
              item: "aside-nav",
            },
            {
              id: "must-exercise-detail",
              name: "必刷题详情",
              app: "jiakao-misc!app/must-exercise-detail/index/list",
              item: "aside-nav",
            },
            {
              id: "illiteracy-voice",
              name: "文盲详解语音",
              app: "jiakao-misc!app/illiteracy-voice/index/list",
              item: "aside-nav",
            },
            {
              id: "video-practice",
              name: "视频刷题",
              app: "jiakao-misc!app/video-practice/index/list",
              item: "aside-nav",
            },
            {
              id: "scene-practice",
              name: "情景练习",
              app: "jiakao-misc!app/scene-practice/index/list",
              item: "aside-nav",
            },
            {
              id: "scene-practice-group",
              name: "情景练习分组",
              app: "jiakao-misc!app/scene-practice-group/index/list",
              item: "aside-nav",
            },
            {
              id: "video-explain",
              name: "视频详解",
              app: "jiakao-misc!app/video-explain/index/list",
              item: "aside-nav",
            },


            {
              id: "xueshi-entrance",
              name: "学时的配置",
              app: "jiakao-misc!app/xueshi-entrance/index/list",
              item: "aside-nav",
            },
            // {
            //   id: "remote-config-swallow",
            //   name: "远程配置(新)",
            //   app: "jiakao-misc!app/remote-config/index/list",
            //   item: "aside-nav",
            //   },
            {
              id: 'remote-config',
              name: '远程配置(新)',
              app: 'swallow!app/remote-config/index/list',
              item: 'aside-nav'
            },
            {
              id: "remote-config-misc",
              name: "远程配置(老-慎用)",
              app: "jiakao-misc!app/remote-config/index1/list",
              item: "aside-nav",
            },
            {
              id: "safe-drive",
              name: "安全驾驶",
              app: "jiakao-misc!app/safe-drive/index/list",
              item: "aside-nav",
            },
            {
              id: "map-point-resource",
              name: "路线地图打点的资源",
              app: "jiakao-misc!app/map-point-resource/index/list",
              item: "aside-nav",
            },
            {
              id: "international-drive-license",
              name: "国际驾照",
              app: "jiakao-misc!app/international-drive-license/index/list",
              item: "aside-nav",
            },
            {
              id: "faq",
              name: "常用faq",
              app: "jiakao-misc!app/faq/index/list",
              item: "aside-nav",
            },
            {
              id: "share-video-template",
              name: "分享视频模板",
              app: "jiakao-misc!app/share-video-template/index/list",
              item: "aside-nav",
            },
            {
              id: 'first-lesson-video',
              name: '第一课视频',
              app: 'jiakao-misc!app/first-lesson-video/index/list',
              item: 'aside-nav'
            },
            {
              id: "jiaxiao-store",
              name: "驾校门店",
              app: "jiakao-misc!app/jiaxiao-store/index/list",
              item: "aside-nav",
            },
          ],
        },
        {
          name: "考场路线",
          nodes: [
            {
              id: 'scene-supply',
              name: '科目二真实考场列表',
              app: 'jk-knowledge!app/scene-supply/index/list',
              item: 'aside-nav'
            },
            {
              id: "route-video-update-record",
              name: "待更新考场管理",
              app: "jiakao-misc!app/route-video-update-record/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-item",
              name: "考场路线视频-路线",
              app: "jiakao-misc!app/route-video-item/index/list",
              item: "aside-nav",
            },
            {
              id: "city-tip",
              name: "城市提示配置",
              app: "jiakao-misc!app/city-tip/index/list",
              item: "aside-nav",
            },
            {
              id: "exam-process",
              name: "考试流程配置",
              app: "jiakao-misc!app/exam-process/index/list",
              item: "aside-nav",
            },
            {
              id: "exam-room",
              name: "122考场列表",
              app: "jiakao-misc!app/exam-room/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-item-clear",
              name: "考场路线清洗",
              app: "jiakao-misc!app/route-video-item/route-video-item-clear/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-meta",
              name: "考场路线视频-考场",
              app: "jiakao-misc!app/route-video-meta/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-permission",
              name: "考场路线视频权限",
              app: "jiakao-misc!app/route-video-permission/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-config",
              name: "考场路线视频-城市",
              app: "jiakao-misc!app/route-video-config/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-audit",
              name: "教练视频审核",
              app: "jiakao-misc!app/route-video-audit/index/list",
              item: "aside-nav",
            },
            {
              id: "route-video-income",
              name: "分成流水",
              app: "jiakao-misc!app/route-video-income/index/list",
              item: "aside-nav",
            },
            {
              id: "route-coach",
              name: "教练信息",
              app: "jiakao-misc!app/route-coach/index/list",
              item: "aside-nav",
            },
            {
              id: "route-stat",
              name: "每日分成数据统计",
              app: "jiakao-misc!app/route-stat/index/list",
              item: "aside-nav",
            },
            {
              id: "route-exam-voice",
              name: "科三路考语音",
              app: "jiakao-misc!app/route-exam-voice/index/list",
              item: "aside-nav",
            },
            {
              id: "route-exam-voice",
              name: "考场数据管理",
              app: "jiakao-misc!app/exam-data-admin/index/list",
              item: "aside-nav",
            },
            {
              id: "route-item-operation-log",
              name: "考场路线删除记录",
              app: "jiakao-misc!app/route-item-operation-log/index/list",
              item: "aside-nav",
            },
          ],
        },
        {
          name: "优惠券",
          nodes: [
            {
              id: "order",
              name: "订单系统",
              app: "jiakao-misc!app/order/index/list",
              item: "aside-nav",
            },
            {
              id: "coupon",
              name: "优惠券列表",
              app: "jiakao-misc!app/coupon/index/list",
              item: "aside-nav",
            },
            {
              id: "coupon-user",
              name: "用户优惠券",
              app: "jiakao-misc!app/coupon-user/index/list",
              item: "aside-nav",
            },
          ],
        },
        {
          name: "视频上传",
          nodes: [
            {
              id: "video-upload",
              name: "视频上传记录表",
              app: "jiakao-misc!app/video-upload/index/list",
              item: "aside-nav",
            },
            {
              id: 'intro-video',
              name: '介绍视频',
              app: 'jiakao-misc!app/intro-video/index/list',
              item: 'aside-nav'
            }
          ],
        },
        {
          name: "朋友圈素材",
          nodes: [
            {
              id: "share-text",
              name: "分享文案",
              app: "jiakao-misc!app/share-text/index/list",
              item: "aside-nav",
            },
            {
              id: "share-img",
              name: "分享图片",
              app: "jiakao-misc!app/share-img/index/list",
              item: "aside-nav",
            },
            {
              id: "share-template",
              name: "分享模版",
              app: "jiakao-misc!app/share-template/index/list",
              item: "aside-nav",
            },
          ]
        }
      ]
    }
  }
  var aside1 = {
    load: {
      type: 'data',
      data: [{
        name: '灯光模拟',
        nodes: [{
          id: 'emulator-package',
          name: '打包',
          app: 'jiakao-misc!app/emulator-package/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-type',
          name: '类型',
          app: 'jiakao-misc!app/emulator-type/index/list',
          item: 'aside-nav'
        },

        {
          id: 'emulator-brand',
          name: '品牌',
          app: 'jiakao-misc!app/emulator-brand/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-car',
          name: '车型',
          app: 'jiakao-misc!app/emulator-car/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-city',
          name: '城市',
          app: 'jiakao-misc!app/emulator-city/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-question',
          name: '试题',
          app: 'jiakao-misc!app/emulator-question/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-exam',
          name: '考试',
          app: 'jiakao-misc!app/emulator-exam/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-video',
          name: '视频',
          app: 'jiakao-misc!app/emulator-video/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-question-video',
          name: '模拟器操作视频',
          app: 'jiakao-misc!app/emulator-question-video/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-practice',
          name: '灯光模拟练习数据',
          app: 'jiakao-misc!app/emulator-practice/index/list',
          item: 'aside-nav'
        },
        {
          id: 'emulator-banner',
          name: '灯光模拟轮播图',
          app: 'jiakao-misc!app/emulator-banner/index/list',
          item: 'aside-nav'
        }
        ]
      }]

    }
  }

  var aside12 = {
    load: {
      type: 'data',
      data: [
        {
          name: '交通强国',
          nodes: [
            {
              id: 'traffic-power-tag',
              name: '交通强国标签',
              app: 'jk-knowledge!app/traffic-power-tag/index/list',
              item: 'aside-nav'
            }

          ]
        }
      ]
    }
  }


  var aside13 = {
    load: {
      type: 'data',
      data: [
        {
          name: '搜索后台',
          nodes: [
            {
              id: 'search-keyword',
              name: '搜索关键词列表',
              app: 'jiakao-misc!app/search-keyword/index/list',
              item: 'aside-nav'
            },
            {
              id: 'keyword-icon',
              name: '关键词图标列表',
              app: 'jiakao-misc!app/keyword-icon/index/list',
              item: 'aside-nav'
            },
            {
              id: 'image-recognition-record',
              name: '图片识别记录',
              app: 'jiakao-misc!app/image-recognition-record/index/list',
              item: 'aside-nav'
            },
            {
              id: 'search-record',
              name: '搜索记录',
              app: 'jiakao-misc!app/search-record/index/list',
              item: 'aside-nav'
            },
            {
              id: 'info-flow-item',
              name: '信息流推荐管理',
              app: 'jiakao-misc!app/info-flow-item/index/list',
              item: 'aside-nav'
            },
            {
              id: 'info-flow-group',
              name: '信息流分组标签管理',
              app: 'jiakao-misc!app/info-flow-group/index/list',
              item: 'aside-nav'
            },
            {
              id: 'search-word-admin',
              name: '搜索词库管理',
              app: 'jiakao-misc!app/search-word-admin/index/list',
              item: 'aside-nav'
            }
          ]
        }
      ]
    }
  }
  var aside14 = {
    load: {
      type: 'data',
      data: [
        {
          name: '彩蛋活动',
          nodes: [
            {
              id: 'egg-entrance-config',
              name: '彩蛋管理',
              app: 'jiakao-misc!app/egg-entrance-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'badge-config',
              name: '徽章管理',
              app: 'jiakao-misc!app/badge-config/index/list',
              item: 'aside-nav'
            },
            {
              id: "icon-group-config",
              name: "新规分类管理",
              app: "jiakao-misc!app/icon-group-config/index/list",
              item: "aside-nav",
            },
            {
              id: "icon-item-config",
              name: "新规图标配置",
              app: "jiakao-misc!app/icon-item-config/index/list",
              item: "aside-nav",
            },
          ]
        }
      ]
    }
  }
  var aside15 = {
    load: {
      type: 'data',
      data: [
        {
          name: '数据操作审核表',
          nodes: [
            {
              id: 'operate-data-audit',
              name: '数据操作审核表',
              app: 'jiakao-misc!app/operate-data-audit/index/list',
              item: 'aside-nav'
            },
          ]
        }
      ]
    }
  }
  var aside16 = {
    load: {
      type: 'data',
      data: [
        {
          name: '驾考知识库',
          nodes: [
            {
              id: 'entries',
              name: '词条列表',
              app: 'jk-knowledge!app/entries/index/list',
              item: 'aside-nav'
            },
            {
              id: 'reference-material',
              name: '参考资料',
              app: 'jk-knowledge!app/reference-material/index/list',
              item: 'aside-nav'
            },
            {
              id: 'related-function',
              name: '相关功能',
              app: 'jk-knowledge!app/related-function/index/list',
              item: 'aside-nav'
            },
            {
              id: 'entries-classify',
              name: '分类',
              app: 'jk-knowledge!app/entries-classify/index/list',
              item: 'aside-nav'
            },
            {
              id: 'data-compare-record',
              name: '数据对比',
              app: 'jiakao-misc!app/data-compare-record/index/list',
              item: 'aside-nav'
            }
          ]
        }
      ]
    }
  }

  var aside19 = {
    load: {
      type: 'data',
      data: [
        {
          name: '配置',
          nodes: [
            {
              id: 'personal-info-group',
              name: '菜单配置',
              app: 'athena!app/personal-info-group/index/list',
              item: 'aside-nav'
            },
            {
              id: 'common-config',
              name: '文案配置',
              app: 'athena!app/common-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'tiku-search',
              name: '用户数据搜索',
              app: 'athena!app/search/index/search',
              item: 'aside-nav'
            }
          ]
        }
      ]
    }
  }

  var aside19 = {
    load: {
      type: 'data',
      data: [
        {
          name: '配置',
          nodes: [
            {
              id: 'personal-info-group',
              name: '菜单配置',
              app: 'athena!app/personal-info-group/index/list',
              item: 'aside-nav'
            },
            {
              id: 'common-config',
              name: '文案配置',
              app: 'athena!app/common-config/index/list',
              item: 'aside-nav'
            },
            {
              id: 'tiku-search',
              name: '用户数据搜索',
              app: 'athena!app/search/index/search',
              item: 'aside-nav'
            }
          ]
        }
      ]
    }
  }

  var aside20 = {
    load: {
      type: "data",
      data: [
        {
          name: "权限管理",
          nodes: [
            {
              id: "user-permission",
              name: "用户权限",
              app: "jiakao-td!app/user-permission/index/list",
              item: "aside-nav",
            },
          ]
        }
      ],
    },
  };

  var aside21 = {

    load: {
      type: 'data',
      data: [
        {
          name: '一起考驾照',
          nodes: [
            {
              id: 'operation-config9',
              name: '驾考状态配置',
              app: 'jiakao-misc!app/operation-config9/index/list',
              item: 'aside-nav'
            },
            {
              id: 'learning-union',
              name: '同学会配置信息',
              app: 'jiakao-misc!app/learning-union/index/list',
              item: 'aside-nav'
            },
            {
              id: 'learning-user-union',
              name: '同学会参与人',
              app: 'jiakao-misc!app/learning-user-union/index/list',
              item: 'aside-nav'
            }
          ]
        }
      ]
    }
  }

  if (window.j.userInfo.roles.indexOf('运营活动专员') > -1) {
    return {
      header: {
        load: {
          type: 'data',
          data: [
            {
              name: '精品课程',
              item: 'header-nav',
              aside: 'jiakao-misc!main/aside8'
            }
          ]
        }
      },
      aside8: aside8
    }
  } else {
    return {
      header: header,
      aside: aside,
      aside1: aside1,
      aside5: aside5,
      aside6: aside6,
      aside7: aside7,
      aside8: aside8,
      aside9: aside9,
      aside10: aside10,
      aside11: aside11,
      aside12: aside12,
      aside13: aside13,
      aside14: aside14,
      aside15: aside15,
      aside16: aside16,
      aside17: aside17,
      aside18: aside18,
      aside19: aside19,
      aside20: aside20,
      aside21: aside21
    }
  }
});
