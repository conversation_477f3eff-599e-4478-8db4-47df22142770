/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {
    var getmucangNetworkShare = {
        primary: 'id',
        load: {
            url: 'mc-pan://api/admin/external/file/share.htm',
            type: 'get',
            format: function (res) {
                return res.itemList
            }
        }
    }

    var findChild =  {
        primary:'id',
        load: {
            url: 'mc-pan://api/admin/external/file/child.htm',
            type: 'get'
        }
    }

    var mcMineRoot = {
        primary:'id',
        load: {
            url: 'mc-pan://api/admin/external/file/root.htm',
            type: 'get',
            format: function (res) {
                return res.itemList
            }
        }
    }

    var transfer = {
        save: {
            url: 'mc-pan://api/admin/external/file/transfer.htm',
            type: 'post'
        }
        
    }

    return {
        findChild,
        getmucangNetworkShare,
        mcMineRoot,
        transfer
    }

});