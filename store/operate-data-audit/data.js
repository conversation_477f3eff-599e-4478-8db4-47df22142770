﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/operate-data-audit/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operate-data-audit/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/operate-data-audit/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operate-data-audit/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/operate-data-audit/view.htm',
            type: 'get'
        }
    }
    var audit = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operate-data-audit/audit.htm',
            type: 'post'
        }
    }
    var queryOldValue = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/operate-data-audit/queryOldValue.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        audit: audit,
        queryOldValue: queryOldValue
    }

});