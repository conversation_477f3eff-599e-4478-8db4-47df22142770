﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/operation-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operation-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/operation-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operation-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/operation-config/view.htm',
            type: 'get'
        }
    }

       // 过滤条件
    var filter = {
        load: {
            url: 'jiakao-misc://api/admin/operation_config/conditions/view.htm',
            type: 'get'
        },
        save: {
            url: 'jiakao-misc://api/admin/operation_config/conditions/update.htm',
            type: 'post'
        }
    }
    
    var filter2 = {
        load: {
            url: 'panda://api/admin/operation_config/conditions/view.htm',
            type: 'get'
        },
        save: {
            url: 'panda://api/admin/operation_config/conditions/update.htm',
            type: 'post'
        } 
    }

    var codeList = {
        load: {
            url: 'jiakao-misc://api/admin/operation-config/code-list.htm',
            type: 'get'
        }
    }

    // 个人画像
    var personas = {
        load: {
            url: 'jiakao-misc://api/admin/operation_config/personas_conditions/view.htm',
            type: 'get'
        },
        save: {
            url: 'jiakao-misc://api/admin/operation_config/personas_conditions/update.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        filter,
        filter2,
        codeList,
        personas
    }

});