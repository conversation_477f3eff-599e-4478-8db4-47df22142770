﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON>a
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'panda://api/admin/operation-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/operation-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'panda://api/admin/operation-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/operation-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'panda://api/admin/operation-config/view.htm',
            type: 'get'
        }
    }

    // 过滤条件
    var filter = {
        load: {
            url: 'panda://api/admin/operation_config/conditions/view.htm',
            type: 'get'
        },
        save: {
            url: 'panda://api/admin/operation_config/conditions/update.htm',
            type: 'post'
        }
    }

    var codeList = {
        load: {
            url: 'panda://api/admin/operation-config-code/code-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        filter,
        codeList
    }

});