﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/order/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/order/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/order/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/order/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/order/view.htm',
            type: 'get'
        }
    }

    var refund = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/order/refund.htm',
            type: 'post'
        }
    }

    var deviceList = {
        load: {
            url: 'jiakao-misc://api/admin/order/device-list.htm',
            type: 'get'
        }
    }
    var updateBizId = {
        save: {
            url: 'jiakao-misc://api/admin/order/update-biz-id.htm',
            type: 'post'
        }
    }
    var deleteDeviceList = {
        save: {
            url: 'jiakao-misc://api/admin/order/delete-device-list.htm',
            type: 'post'
        }
    }
    var manualSetPaid = {
        save: {
            url: 'jiakao-misc://api/admin/order/manual-set-paid.htm',
            type: 'post'
        }
    }
    var bindUser = {
        save: {
            url: 'jiakao-misc://api/admin/order/bind.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        refund: refund,
        deviceList: deviceList,
        updateBizId: updateBizId,
        deleteDeviceList: deleteDeviceList,
        manualSetPaid: manualSetPaid,
        bindUser: bindUser
    }

});