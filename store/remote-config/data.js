﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'swallow://api/admin/remote-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/remote-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'swallow://api/admin/remote-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'swallow://api/admin/remote-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'swallow://api/admin/remote-config/view.htm',
            type: 'get'
        }
    }


    // 过滤条件
    var filter = {
        load: {
            url: 'swallow://api/admin/remote_config/conditions/view.htm',
            type: 'get'
        },
        save: {
            url: 'swallow://api/admin/remote_config/conditions/update.htm',
            type: 'post'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        filter: filter
    }

});