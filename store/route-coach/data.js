﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/coach/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coach/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/coach/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coach/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/coach/view.htm',
            type: 'get'
        }
    }

    var addCity = {
        save: {
            url: 'jiakao-misc://api/admin/coach/add-city.htm',
            type: 'post'
        }
    }

    var delCity = {
        save: {
            url: 'jiakao-misc://api/admin/coach/delete-city.htm',
            type: 'post'
        }
    }

    var getCityConfig = {
        load: {
            url: 'jiakao-misc://api/admin/coach/get-entrance-cityConfig.htm',
            type: 'get'
        }
    }

    var updateCity = {
        save: {
            url: 'jiakao-misc://api/admin/coach/update-city.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        addCity: addCity,
        delCity: delCity,
        getCityConfig: getCityConfig,
        updateCity: updateCity
    }

});