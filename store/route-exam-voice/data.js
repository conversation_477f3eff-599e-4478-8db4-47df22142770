﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'panda://api/admin/route-exam-voice/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/route-exam-voice/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'panda://api/admin/route-exam-voice/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/route-exam-voice/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'panda://api/admin/route-exam-voice/view.htm',
            type: 'get'
        }
    }

    var textTospeech = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-exam-voice/text-to-speech.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        textTospeech:textTospeech
    }

});