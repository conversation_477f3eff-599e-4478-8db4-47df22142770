﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jk-knowledge://api/admin/route-item-upload-log/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-item-upload-log/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jk-knowledge://api/admin/route-item-upload-log/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-item-upload-log/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jk-knowledge://api/admin/route-item-upload-log/view.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view
    }

});