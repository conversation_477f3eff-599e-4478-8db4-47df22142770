﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/coach-route-video/list.htm',
            type: 'get'
        }
    }

    var audit = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coach-route-video/audit.htm',
            type: 'post'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coach-route-video/delete.htm',
            type: 'post'
        }
    }

    var editCheckSource = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coach-route-video/edit-check-source.htm',
            type: 'post'
        }
    }
    var enableNewTemplate = {
        save: {
            url: 'jiakao-misc://api/admin/coach-route-video/enable-new-template.htm',
            type: 'post'
        }
    }

    var removeTrackInfo = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-item/remove-track-info.htm',
            type: 'post'
        }
    }

    var doUrgent = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coach-route-video/do-urgent.htm',
            type: 'post'
        }
    }
    var updateExtractStatus = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/coach-route-video/update-extract-status.htm',
            type: 'post'
        }
    }
    var importantCoachList = {
        load: {
            url: 'jiakao-misc://api/admin/coach-route-video/important-coach-list.htm',
            type: 'get'
        }
    }
    var updateImportantCoach = {
        save: {
            url: 'jiakao-misc://api/admin/coach-route-video/update-important-coach.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        audit: audit,
        delete: del,
        editCheckSource: editCheckSource,
        enableNewTemplate: enableNewTemplate,
        removeTrackInfo:removeTrackInfo,
        doUrgent: doUrgent,
        updateExtractStatus:updateExtractStatus,
        importantCoachList:importantCoachList,
        updateImportantCoach:updateImportantCoach
    }
});