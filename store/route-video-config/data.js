﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jk-knowledge://api/admin/route-video-config/view.htm',
            type: 'get'
        }
    }

    var upload = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-config/upload-route-data.htm',
            type: 'post'
        }
    }
    var verify = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-config/confirm.htm',
            type: 'get',
        }
    }

    var listItemByCity = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-config/list-item-by-city.htm',
            type: 'get',
        }
    }
    var updateActivityConfig = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-config/update-activity-config.htm',
            type: 'post'
        }
    }
    var enableNewTemplate = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-config/enable-new-template.htm',
            type: 'post'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        upload: upload,
        verify: verify,
        listItemByCity: listItemByCity,
        updateActivityConfig: updateActivityConfig,
        enableNewTemplate: enableNewTemplate
    }

});