/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var uploadMap = {
        save: {
            url: 'jiakao-misc://api/admin/route-map/import-data.htm',
            type: 'post'
        }
    }

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/route-map/list.htm',
            type: 'get'
        }
    }

    var line = {
        load: {
            url: 'jiakao-misc://api/admin/route-map/publish.htm',
            type: 'get'
        }
    }

    


    return {
        uploadMap: uploadMap,
        list: list,
        line: line
    }

});
