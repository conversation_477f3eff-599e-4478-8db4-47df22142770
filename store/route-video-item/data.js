﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(['simple!core/store'], function (Store) {

    var list = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-item/list.htm',
            type: 'get'
        }
    }

    var list2 = {
        load: {
            type: 'data',
            format: function (res, config, obj) {
                return Promise.resolve(Store(['jiakao-misc!route-video-item/data/list'], [{
                    aliases: 'list',
                    params:config.params
                }]).load().promise()).then(res => {
                    if (!obj.json) {
                        obj.json = {};
                    }
                    obj.json.paging = res.data.list.paging;

                    const listData = res.data.list.data;
                    const itemIdList = listData.map(item => item.id);
                    return Promise.resolve(Store(['jiakao-misc!route-video-item/data/getWeiyuVideo','jiakao-misc!route-video-item/data/getOuterItemIds?routeItemIds='+itemIdList.join(',')], [{
                        aliases: 'list',
                        params: {
                            itemIdList:itemIdList.join(',')
                        }
                    }]).load().promise()).then(store => { 
                        const outerItemIds = store.data['route-video-item'].data.getOuterItemIds.data;
                        const retData = store.data.list.data;
                        const newData = [];
                        for (const key in retData) {
                            const item = {
                                outerItemId:outerItemIds[key],
                                ...listData.filter(el => el.id == key)[0],
                                ...retData[key],
                            };
                            newData.push(item)
                        }
                        return newData;
                    })
                });
            }
        }
    }

    var getOuterItemIds = {
        load: {
            url: 'panda://api/admin/route-relative/find-outer-item-ids.htm',
            type: 'post',
        }
    }

    var getWeiyuVideo = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-item/get-weiyu-video.htm',
            type: 'post'
        }
    }


    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-item/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-item/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-item/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jk-knowledge://api/admin/route-video-item/view.htm',
            type: 'get'
        }
    }

    var getRealVideoUrl = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-item/get-auth-video-url.htm',
            type: 'get'
        }
    }

    var getUploadKey = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-item/get-upload-key.htm',
            type: 'post'
        }
    }


    var getUploadKey1 = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-practice/get-upload-key.htm',
            type: 'post'
        }
    }

    var updateCoachVideo = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-item/update-coach-video.htm',
            type: 'post'
        }
    }

    var enableK3Scene = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-item/enable-k3-scene.htm',
            type: 'post'
        }
    }

    var disableK3Scene = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-item/disable-k3-scene.htm',
            type: 'post'
        }
    }

    var saveOuterId = {
        save: {
            url: 'panda://api/admin/route-relative/save-rel.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        list2,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getUploadKey: getUploadKey,
        getUploadKey1,
        updateCoachVideo: updateCoachVideo,
        enableK3Scene,
        disableK3Scene,
        saveOuterId,
        getWeiyuVideo,
        getRealVideoUrl,
        getOuterItemIds
    }

});