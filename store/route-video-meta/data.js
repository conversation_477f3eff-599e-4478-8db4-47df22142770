﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(['simple!core/store'], function (Store) {
    var list = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-meta/list.htm',
            type: 'get'
        }
    } 

    var list2 = {
        load: {
            type: 'data',
            format: function (res, config, obj) {
                return Promise.resolve(Store(['jiakao-misc!route-video-meta/data/list'], [{
                    aliases: 'list',
                    params:config.params
                }]).load().promise()).then(res => {
                    if (!obj.json) {
                        obj.json = {};
                    }
                    obj.json.paging = res.data.list.paging;
                    
                    const listData = res.data.list.data;
                    const metaIds = listData.map(item => item.id);
                    return Promise.resolve(Store(['jiakao-misc!route-video-meta/data/getMetaId'], [{
                        aliases: 'list',
                        params: {
                            metaIds:metaIds.join(',')
                        }
                    }]).load().promise()).then(store =>{ 
                        const retData = store.data.list.data;
                        const newData = [];
                        for (const key in retData) {
                            const item = {
                                ...listData.filter(el => el.id == key)[0],
                                ...retData[key]
                            };
                            newData.push(item)
                        }
                        console.log(newData, 'newData');
                        return newData;
                    })
                });
            }
        }
    }

    var getMetaId = {
        primary: 'id',
        load: {
            url: 'panda://api/admin/scene-supply/get-route-video-meta-relative-id.htm',
            type:'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-meta/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-meta/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-meta/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jk-knowledge://api/admin/route-video-meta/view.htm',
            type: 'get'
        }
    }
    var listAll = {
        primary: 'id',
        load: {
            url: 'jk-knowledge://api/admin/route-video-meta/list-all.htm',
            type: 'get'
        }
    }

    var setEntrance = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/update-entrance-city.htm',
            type: 'post'
        }
    }

    var getEntrance = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-meta/get-entrance-city-config.htm',
            type: 'get'
        }
    }

    var addCity = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/add-city.htm',
            type: 'post'
        }
    }

    var delCity = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/delete-city.htm',
            type: 'post'
        }
    }

    var getCityConfig = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-meta/get-entrance-cityConfig.htm',
            type: 'get'
        }
    }

    var updateCity = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/update-city.htm',
            type: 'post'
        }
    }

    var updateStatus = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-meta/update-status.htm',
            type: 'get'
        }
    }

    var enableK3Scene = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/enable-k3-scene.htm',
            type: 'post'
        }
    }

    var disableK3Scene = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/disable-k3-scene.htm',
            type: 'post'
        }
    }

    var saveOuterId = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/save-outer-id.htm',
            type: 'post'
        }
    }
    var saveOuterKey = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-meta/save-outer-key.htm',
            type: 'post'
        }
    } 

    return {
        list: list,
        list2,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        listAll: listAll,
        setEntrance: setEntrance,
        getEntrance: getEntrance,
        addCity: addCity,
        delCity: delCity,
        getCityConfig: getCityConfig,
        updateCity: updateCity,
        updateStatus:updateStatus,
        enableK3Scene,
        disableK3Scene,
        saveOuterId,
        saveOuterKey: saveOuterKey,
        getMetaId
    }

});
