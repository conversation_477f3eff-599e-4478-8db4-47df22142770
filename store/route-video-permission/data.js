﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-permission/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-permission/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-permission/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-permission/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/route-video-permission/view.htm',
            type: 'get'
        }
    }

    var bind = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-permission/bind.htm',
            type: 'post'
        }
    }
    var bizIdTypeList = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-permission/biz-id-type-list.htm',
            type: 'get'
        }
    }

    var city = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-permission/update-user-permission-city.htm',
            type: 'post'
        }
    }

    var place = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-permission/update-user-permission-place.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        bind: bind,
        bizIdTypeList: bizIdTypeList,
        city: city,
        place: place
    }

});