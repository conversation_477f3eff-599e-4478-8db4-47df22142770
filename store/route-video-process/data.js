﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

'use strict';

define(['simple!core/store'], function (Store) {
    var list = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/list.htm',
            type: 'get',
        },
    };

    var list3 = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/list.htm',
            type: 'get',
        },
    };

    var list4 = {
        load: {
            type: 'data',
            format: function (res, config, obj) {
                return Promise.resolve(
                    Store(
                        ['jiakao-misc!route-video-process/data/list'],
                        [
                            {
                                aliases: 'list',
                                params: config.params,
                            },
                        ]
                    )
                        .load()
                        .promise()
                ).then((retData) => {
                    if (!obj.json) {
                        obj.json = {};
                    }
                    obj.json.paging = retData.data.list.paging;
                    retData = retData.data.list.data;
                    return Promise.all(
                        retData.map((item) => {
                            return Promise.all([
                                item.videoPracticeKey &&
                                    keyExchangeData(item.videoPracticeKey),
                            ]);
                        })
                    ).then((data) => {
                        data.forEach(([videoPracticeData = ''], i) => {
                            retData[i].videoPracticeData = videoPracticeData;
                        });
                        return retData;
                    });
                });
            },
        },
    };

    function keyExchangeData(key) {
        return Store(
            [`jiakao-misc!route-video-process/data/getByKey?videoKey=${key}`],
            [
                {
                    aliases: 'list',
                },
            ]
        )
            .load()
            .promise()
            .then((retData) => retData.data.list.data.value);
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/delete.htm',
            type: 'post',
        },
    };

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/create.htm',
            type: 'post',
        },
    };

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/update.htm',
            type: 'post',
        },
    };

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/view.htm',
            type: 'get',
        },
    };

    var processVideo = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/process-video-timeline.htm',
            type: 'post',
        },
    };

    var getByKey = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/get-video-url-by-key.htm',
            type: 'get',
        },
    };

    var genVideo = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/gen-video.htm',
            type: 'post',
        },
    };

    var syncTimeline = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/sync-timeline.htm',
            type: 'get',
        },
    };

    var syncVideoKey = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/sync-video-key.htm',
            type: 'get',
        },
    };

    var seveLightTip = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/save-light-tip.htm',
            type: 'post',
        },
    };

    var saveEndTip = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/save-end-tip.htm',
            type: 'post',
        },
    };

    var syncTrackInfo = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/sync-track-info.htm',
            type: 'get',
        },
    };
    var getFileUrl = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/get-file-url.htm',
            type: 'get',
        },
    };

    var checkCity = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/check-city.htm',
            type: 'get',
        },
    };
    var listUsers = {
        load: {
            url: 'sso://api/admin/user/list-users.htm',
            type: 'get',
        },
    };
    var updateLiableUser = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/update-liable-user.htm',
            type: 'post',
        },
    };
    var processAudit = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/audit.htm',
            type: 'post',
        },
    };
    var processList = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-process-audit/list.htm',
            type: 'get',
        },
    };
    var routeVideoProcessLog = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-process-log/list.htm',
            type: 'get',
        },
    };

    var viewPosterImage = {
        load: {
            url: 'jiakao-misc://api/admin/route-video-process/view-image.htm',
            type: 'get',
        },
    };

    var updatePosterImage = {
        save: {
            url: 'jiakao-misc://api/admin/route-video-process/update-image.htm',
            type: 'post',
        },
    };

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        processVideo,
        getByKey,
        genVideo,
        syncTimeline,
        syncVideoKey,
        list3,
        list4,
        seveLightTip,
        saveEndTip,
        getFileUrl,
        syncTrackInfo,
        checkCity,
        listUsers,
        updateLiableUser,
        processAudit,
        processList,
        routeVideoProcessLog,
        viewPosterImage,
        updatePosterImage
    };
});
