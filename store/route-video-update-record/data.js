﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jk-knowledge://api/admin/route-video-update-record/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-update-record/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-update-record/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-update-record/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jk-knowledge://api/admin/route-video-update-record/view.htm',
            type: 'get'
        }
    }

    var getUserInfo = {
        load: {
            url: 'sso://api/internal/manage/users-by-project.htm',
            type: 'get'
        }
    }

    var batchDelete = {
        primary: 'id',
        save: {
            url: 'jk-knowledge://api/admin/route-video-update-record/batch-delete.htm',
            type: 'post'
        }

    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getUserInfo: getUserInfo,
        batchDelete: batchDelete
    }

});