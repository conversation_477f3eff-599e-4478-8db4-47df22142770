﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/scene-practice/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/scene-practice/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/scene-practice/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/scene-practice/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/scene-practice/view.htm',
            type: 'get'
        }
    }
    var groupList = {

        load: {
            url: 'jiakao-misc://api/admin/scene-practice-group/group-list.htm',
            type: 'get'
        }
    }
    var groupList2 = {

        load: {
            url: 'jiakao-misc://api/admin/scene-practice/group-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        groupList: groupList,
        groupList2: groupList2
    }

});