﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'panda://api/admin/scene-supply/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/scene-supply/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'panda://api/admin/scene-supply/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/scene-supply/update.htm',
            type: 'post'
        }
    }

    var updateSI = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/scene-supply/update-scene-introduce.htm',
            type: 'post'
            
        }
    }

    var updateBI = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/scene-supply/update-base-info.htm',
            type: 'post'
        }
    }

    var updateSR = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/scene-supply/update-scene-res.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'panda://api/admin/scene-supply/view.htm',
            type: 'get',
            format: function(o) {

                return o ? o : {}
            }

        }
    }

    var viewTopicId = {
        primary: 'id',
        save: {
            url: 'saturn://api/admin/topic/view.htm',
            type: 'post'
        }
    }

    var updateStatus = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/scene-supply/update-status.htm',
            type: 'post'
        }
        
    }

    var getVideoInfo = {
        primary: 'id',
        load: {
            url: 'panda://api/admin/scene-supply/get-video-info.htm',
            type: 'get',
        }
    }


    var carList = {
        primary: 'id',
        load: {
            url: 'panda://api/admin/car-brand/get-car-list.htm',
            type: 'get',
        }
    }

    var uploadFile = {
        primary: 'id',
        save: {
            url: 'panda://api/admin/scene-supply/import-base-info.htm',
            type: 'post',
        }
    }

    
 
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        viewTopicId: viewTopicId,
        updateSI,
        updateBI,
        updateSR,
        updateStatus,
        getVideoInfo,
        carList,
        uploadFile
    }

});