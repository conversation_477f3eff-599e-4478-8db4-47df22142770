/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var getDic = {
        
        load: {
            url: 'jiakao-search://api/admin/search-keyword/get-dic.htm',
            type: 'get'
        }
    }
 
    var setDic = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/search-keyword/set-dic.htm',
            type: 'post'
        }
    }

    var getSynonymDic = {
        load: {
            url: 'jiakao-search://api/admin/search-keyword/get-synonym-dic.htm',
            type: 'get'
        }
    }
 
    var setSynonymDic = {
        primary: 'id',
        save: {
            url: 'jiakao-search://api/admin/search-keyword/write-synonym-dic.htm',
            type: 'post'
        }
    }


    return {
        getDic,
        setDic,
        getSynonymDic,
        setSynonymDic
    }

});