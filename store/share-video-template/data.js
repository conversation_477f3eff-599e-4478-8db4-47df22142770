/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/share-video-template/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/share-video-template/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/share-video-template/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/share-video-template/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/share-video-template/view.htm',
            type: 'get'
        }
    }

    var batchUpdate = {
        load: {
            url: 'jiakao-misc://api/admin/share-video-template/batch-update.htm',
            type: 'get'
        }
    }

    var updateImageConfig = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/share-video-template/update-image-config.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        batchUpdate: batchUpdate,
        updateImageConfig: updateImageConfig
    }

});