﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'monkey://api/admin//live-activity-lottery-config/list-by-activity-id.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin//live-activity-lottery-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'monkey://api/admin//live-activity-lottery-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin//live-activity-lottery-config/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin//live-activity-lottery-config/view.htm',
            type: 'get'
        }
    }

    

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
    }

});