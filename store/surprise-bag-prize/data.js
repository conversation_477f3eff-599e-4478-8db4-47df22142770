﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'monkey://api/admin/live-activity-prize/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity-prize/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'monkey://api/admin/live-activity-prize/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity-prize/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-activity-prize/view.htm',
            type: 'get'
        }
    }

    

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
    }

});