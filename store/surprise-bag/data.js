﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'monkey://api/admin/live-activity/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'monkey://api/admin/live-activity/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-activity/view.htm',
            type: 'get'
        }
    }

    var prizeAdressList = {
        load: {
            url: 'monkey://api/admin/live-activity-prize-adress/list.htm',
            type: 'get'
        }
    }

    var virtualRecordList = {
        load: {
            url: 'monkey://api/admin/activity-virtual-prize-send-record/list.htm',
            type: 'get'
        }
    }

    var prizeAdressUpdate = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity-prize-adress/update.htm',
            type: 'post'
        }
    }

    var prizeAdressView = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-activity-prize-adress/view.htm',
            type: 'get'
        }
    }

    var prizeAdressDeliver = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity-prize-adress/deliver.htm',
            type: 'post'
        }
    }

    var prizeAddRemark = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity-prize-adress/add-remark.htm',
            type: 'post'
        }
    }

    var activityUserList = {
        load: {
            url: 'monkey://api/admin/live-activity-user/list.htm',
            type: 'get'
        }
    }

    var activityUserDel = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity-user/delete.htm',
            type: 'post'
        }
    }

    var activityUserView = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-activity-user/view.htm',
            type: 'get'
        }
    }

    var getPhone = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-activity-prize-adress/get-phone.htm',
            type: 'get'
        }
    }

    var getUserInfo = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-activity-prize-adress/get-user-info.htm',
            type: 'get'
        }
    }

    var batchDeliver = {
        save: {
            url: 'monkey://api/admin/live-activity-prize-adress/batch-deliver.htm',
            type: 'post'
        }
    }

    var updateStatus=  {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-activity-prize-adress/batch-update-status.htm',
            type: 'get'
        }
    }

    var importExpressNumber  = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-activity-prize-adress/batch-update-express-number.htm',
            type: 'post'
        }
    }

    var getActivityRule = {
        load: {
            url: 'monkey://api/admin/live-activity-rule/list.htm',
            type: 'get'
        }
    }
    

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        prizeAdressList,
        virtualRecordList,
        prizeAdressUpdate,
        prizeAdressView,
        prizeAdressDeliver,
        prizeAddRemark,
        activityUserList,
        activityUserDel,
        activityUserView,
        getPhone,
        getUserInfo,
        batchDeliver,
        updateStatus,
        importExpressNumber,
        getActivityRule
    }

});