﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/timeline-config/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-keypoint/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/timeline-config/view.htm',
            type: 'get'
        }
    }

    var bizTypeList = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/timeline-config/biz-type-list.htm',
            type: 'get',
            format: function (json) {
                json.unshift({
                    key: '请选择',
                    value: ''
                });
                return json
            }
        }
    }

    var addRes = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/add-resource.htm',
            type: 'get'
        }
    }

    var getResData = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/timeline-config/get-resource-data.htm',
            type: 'get'
        }
    }

    var updateRes = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/update-resource.htm',
            type: 'get'
        }
    }

    var updateKeypoint = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/update-keypoint.htm',
            type: 'post'
        }
    }

    var importExcel = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/import-excel.htm',
            type: 'post'
        }
    }

    var batchDelete = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/batch-delete.htm',
            type: 'post'
        }
    }


    var lkyTypeList = {
        load: {
            url: 'jiakao-misc://api/admin/timeline-config/get-lu-kao-yi-type.htm',
            type: 'get',
            format: function (json) {
               
                return json.itemList
            }
        },
      
    }
    var timelineResType = {
        load: {
            url: 'jiakao-misc://api/admin/timeline-res/cover-image-config-list.htm',
            type: 'get'
        },
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        bizTypeList,
        addRes,
        getResData,
        updateRes,
        updateKeypoint: updateKeypoint,
        importExcel: importExcel,
        batchDelete: batchDelete,
        lkyTypeList,
        timelineResType
    }

});