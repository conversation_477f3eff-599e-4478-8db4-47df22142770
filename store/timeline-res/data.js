﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/timeline-res/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-res/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/timeline-res/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/timeline-res/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/timeline-res/view.htm',
            type: 'get'
        }
    }

    var bizTypeList = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/timeline-res/biz-type-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        bizTypeList,
    }

});