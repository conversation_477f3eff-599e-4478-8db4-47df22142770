﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/operation-position-template/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operation-position-template/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/operation-position-template/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operation-position-template/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/operation-position-template/view.htm',
            type: 'get'
        }
    }

    var templateList = {
        load: {
            url: 'jiakao-misc://api/admin/operation-position-template/get-template-list.htm',
            type: 'get',
            format: function (json) {
                json = json.map(item => {
                    item.id = String(item.id)
                    return item
                })
                return json
            }
        }
    }

    var applyToLesson = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/operation-position-template/apply-to-lesson.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        templateList: templateList,
        applyToLesson
    }

});