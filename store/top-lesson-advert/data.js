﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/view.htm',
            type: 'get'
        }
    }

    var generatePurchase = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/generate-purchase.htm',
            type: 'get'
        }
    }

    var listPurchaseTemp = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/list-purchase-template.htm',
            type: 'post'
        }
    }

    var createFromTemplate = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-advert/create-from-template.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        generatePurchase,
        listPurchaseTemp,
        createFromTemplate
    }

});