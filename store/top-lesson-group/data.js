﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-group/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-group/view.htm',
            type: 'get'
        }
    }

    var updateStatus = {

        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/update-status.htm',
            type: 'post'
        }

    }

    var bannerData = {

        load: {
            url: 'jiakao-misc://api/admin/top-lesson-group/banner-data.htm',
            type: 'get'
        }

    }

    var saveBannerData = {

        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/save-banner-data.htm',
            type: 'post'
        }

    }

    var getQuestionPopData = {

        load: {
            url: 'jiakao-misc://api/admin/config/get-vip-question-pop-config.htm',
            type: 'get'
        }

    }

    var saveQuestionPopData = {

        save: {
            url: 'jiakao-misc://api/admin/config/update-vip-question-pop-config.htm',
            type: 'post'
        }

    }

    var liveData = {

        load: {
            url: 'jiakao-misc://api/admin/top-lesson-group/get-live-lesson-entrance.htm',
            type: 'get'
        }

    }
    var saveliveData = {

        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/edit-live-lesson-entrance.htm',
            type: 'post'
        }

    }
    var sortliveData = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/replace-sort.htm',
            type: 'post'
        }
    }
    var batchRecommend = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/batch-recommend.htm',
            type: 'post'
        }
    }

    var createChannelGoods = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-group/create-channel-goods.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        updateStatus: updateStatus,
        bannerData: bannerData,
        saveBannerData: saveBannerData,
        getQuestionPopData: getQuestionPopData,
        saveQuestionPopData: saveQuestionPopData,
        liveData: liveData,
        saveliveData: saveliveData,
        sortliveData: sortliveData,
        batchRecommend: batchRecommend,
        createChannelGoods: createChannelGoods
    }

});