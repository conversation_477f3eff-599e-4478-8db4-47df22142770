﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item-template/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item-template/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item-template/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item-template/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item-template/view.htm',
            type: 'get'
        }
    }

    var templateList = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item-template/get-template-list.htm',
            type: 'get',
            format: function (json) {
                json = json.map(item => {
                    item.id = String(item.id)
                    return item
                })
                return json
            }
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        templateList: templateList
    }

});