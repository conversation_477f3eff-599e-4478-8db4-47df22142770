﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item/view.htm',
            type: 'get'
        }
    }
    var updateStatus = {

        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/update-status.htm',
            type: 'post'
        }

    }
    var updateTranscodeStatus = {
        primary: 'id',
        save: {
            url: 'jiakao-misc:///api/admin/top-lesson-item/update-transcode-status.htm',
            type: 'post'
        }

    }
    var groupList = {

        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item/group-list.htm',
            type: 'get'
        }

    }
    var adds = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/batch-update-status.htm',
            type: 'post'
        }
    }
    var deal = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/deal-playback-video.htm',
            type: 'post'
        }
    }
    var delBatch = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/delete-batch.htm',
            type: 'post'
        }
    }
    var lessonOnOff = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-on-off/save.htm',
            type: 'post'
        },
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-on-off/get.htm',
            type: 'post'
        }
    }
    var lessonOnOffBatch = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-on-off/batch-set.htm',
            type: 'post'
        }
    }

    var templateList = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item/get-message-plan-template.htm',
            type: 'get',
            format: function (json) {
                json = json.map(item => {
                    item.templateId = String(item.templateId)
                    return item
                })
                return json
            }
        }
    }
    var batchUpload = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-item/batch-upload.htm',
            type: 'post'
        }
    }
    var updateResource = {
        save: {
            url: 'jiakao-misc://api/admin/timeline-config/create-and-add-resource.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        updateStatus: updateStatus,
        updateTranscodeStatus: updateTranscodeStatus,
        groupList: groupList,
        adds: adds,
        deal: deal,
        delBatch: delBatch,
        lessonOnOff: lessonOnOff,
        lessonOnOffBatch: lessonOnOffBatch,
        templateList: templateList,
        batchUpload: batchUpload,
        updateResource: updateResource
    }
});
