﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-ranklist/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-ranklist/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-ranklist/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-ranklist/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-ranklist/view.htm',
            type: 'get'
        }
    }

    var sortliveData = {
        save: {
            url:'jiakao-misc://api/admin/top-lesson-ranklist/update.htm',
            type: 'post'
        }
    }

    var getSubDetail = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-ranklist/get-sub-detail.htm',
            type: 'get'
        }
    }

    var updateSubDetail = {
        save: {
            url: 'jiakao-misc://api/admin/top-lesson-ranklist/update-sub-detail.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getSubDetail: getSubDetail,
        updateSubDetail: updateSubDetail,
        sortliveData: sortliveData
    }

});