﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'monkey://api/admin/live-recommend-goods-template/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-recommend-goods-template/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'monkey://api/admin/live-recommend-goods-template/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-recommend-goods-template/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-recommend-goods-template/view.htm',
            type: 'get'
        }
    }

    var templateList = {
        load: {
            url: 'jiakao-misc://api/admin/top-lesson-item/get-recommend-goods-template-data.htm',
            type: 'get',
            format: function (json) {
                json = json.map(item => {
                    item.templateId = String(item.templateId)
                    return item
                })
                return json
            }
        }
    }

    var applyToLesson = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-recommend-goods-template/apply-to-lesson-item.htm',
            type: 'post'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        templateList: templateList,
        applyToLesson
    }

});