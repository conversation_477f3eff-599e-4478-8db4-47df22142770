﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'monkey://api/admin/live-sys-message/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-sys-message/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'monkey://api/admin/live-sys-message/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'monkey://api/admin/live-sys-message/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'monkey://api/admin/live-sys-message/view.htm',
            type: 'get'
        }
    }

    var getIntervalList = {
        load: {
            url: 'monkey://api/admin/live-sys-message/get-interval-list.htm',
            type: 'get'
        }
    }

    

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getIntervalList: getIntervalList
    }

});