﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/teacher-rank/list.htm',
            type: 'get'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/teacher-rank/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/teacher-rank/view.htm',
            type: 'get'
        }
    }
    var getChangeRecordList = {
        load: {
            url: 'jiakao-misc://api/admin/teacher-rank/get-change-record-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        update: update,
        view: view,
        getChangeRecordList
    }

});