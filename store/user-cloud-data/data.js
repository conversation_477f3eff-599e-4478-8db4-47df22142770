﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/user-data-query-log/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/user-data-query-log/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/user-data-query-log/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/user-data-query-log/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/user-data-query-log/view.htm',
            type: 'get'
        }
    }
    var getUseInfoData = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/user-info/get-use-info-data.htm',
            type: 'post'
        }
    }
    var comment = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/user-info/comment.htm',
            type: 'post'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getUseInfoData: getUseInfoData,
        comment: comment
    }

});