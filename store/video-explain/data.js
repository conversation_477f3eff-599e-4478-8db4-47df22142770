﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON>jia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jk-tiku://api/admin/video-explain/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jk-tiku://api/admin/video-explain/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jk-tiku://api/admin/video-explain/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jk-tiku://api/admin/video-explain/update.htm',
            type: 'post'
        }
    }
 
    var updateStatus = {
        primary: 'id',
        save: {
            url: 'jk-tiku://api/admin/video-explain/update-status.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jk-tiku://api/admin/video-explain/view.htm',
            type: 'get'
        }
    }
    var uploadBatchCoverH = {

        save: {
            url: 'jk-tiku://api/admin/video-explain/upload-batch-cover-h.htm',
            type: 'post'
        }
    }
    var onlineMore = {
        load:{
            url:'jk-tiku://api/admin/video-explain/batch-online.htm',
            type: 'get'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        uploadBatchCoverH:uploadBatchCoverH,
        updateStatus:updateStatus,
        onlineMore:onlineMore
    }

});