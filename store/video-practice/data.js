﻿/*
 * data v0.0.1
 *
 * name: xia<PERSON><PERSON><PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jk-tiku://api/admin/video-practice/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jk-tiku://api/admin/video-practice/delete.htm',
            type: 'post'
        }
    }


    var view = {
        primary: 'id',
        load: {
            url: 'jk-tiku://api/admin/video-practice/view.htm',
            type: 'get'
        }
    }

    var createBatch = {
        save: {
            url: 'jk-tiku://api/admin/video-practice/create-batch.htm',
            type: 'post'
        }
    }

    var updateStatus = {
        load: {
            url: 'jk-tiku://api/admin/video-practice/batch-update-status.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        view: view,
        createBatch: createBatch,
        updateStatus
    }

});
