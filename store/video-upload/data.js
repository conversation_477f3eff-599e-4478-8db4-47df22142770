﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/video-upload/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/video-upload/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/video-upload/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/video-upload/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/video-upload/view.htm',
            type: 'get'
        }
    }

    var getUploadKey = {
        save: {
            url: 'jiakao-misc://api/admin/video-upload/get-upload-key.htm',
            type: 'post'
        }
    }

    var uploadKeyVideo = {
        save: {
            url: 'jk-knowledge://api/admin/route-video-practice/upload-video.htm',
            type: 'post'
        }
    }

    var bizTypeList = {
        load: {
            url: 'jiakao-misc://api/admin/video-upload/biz-type-list.htm',
            type: 'get'
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getUploadKey: getUploadKey,
        bizTypeList: bizTypeList,
        uploadKeyVideo: uploadKeyVideo
    }

});
