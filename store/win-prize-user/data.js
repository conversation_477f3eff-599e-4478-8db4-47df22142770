﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function () {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/win-prize-user/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/win-prize-user/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/win-prize-user/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/win-prize-user/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/win-prize-user/view.htm',
            type: 'get'
        }
    }

    var viewPhone = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/win-prize-user/get-phone.htm',
            type: 'get'
        }
    }

    var viewPrice = {
        load: {
            url: 'jiakao-misc://api/admin/draw-prize/list.htm',
            type: 'get',
            format: function (json) {
                json = json.map(item => ({
                    key: item.id,
                    value: item.title
                }))
                console.log(json, 'json');
                return json
            }
        }
    }

    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        viewPhone: viewPhone,
        viewPrice: viewPrice
    }

});