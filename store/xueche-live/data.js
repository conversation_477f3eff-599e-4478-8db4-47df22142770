﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/xueche-live/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/create.htm',
            type: 'post'
        }
    }

    var insert1 = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/create-test-live.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/xueche-live/view.htm',
            type: 'get'
        }
    }


    var addAward = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/add-award.htm',
            type: 'post'
        }
    }

    var confirmAward = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/confirm-award.htm',
            type: 'post'
        }
    }


    var openAward = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/open-award.htm',
            type: 'post'
        }
    }


    var unAwardRefund = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-live/un-award-refund.htm',
            type: 'post'
        }
    }


    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        insert1: insert1,
        view: view,
        addAward: addAward,
        confirmAward: confirmAward,
        openAward: openAward,
        unAwardRefund: unAwardRefund
    }

});
