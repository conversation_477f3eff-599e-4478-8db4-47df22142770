﻿/*
 * data v0.0.1
 *
 * name: xiaoji<PERSON>
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/xueche-user-info/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/xueche-user-info/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-user-info/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/xueche-user-info/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/xueche-user-info/view.htm',
            type: 'get'
        }
    }
    var getPhone={
        load:{
            url:'jiakao-misc://api/admin/xueche-user-info/get-phone.htm',
            type: 'get'

        }
    }
    var genCode={
        save: {
            url: 'jiakao-misc://api/admin/xueche-user-info/generate-share-lottery-code.htm',
            type: 'post'
        }
    }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        getPhone:getPhone,
        genCode:genCode
    }

});