﻿/*
 * data v0.0.1
 *
 * name: xiaojia
 * date: 2013/10/12
 */

"use strict";

define(function() {

    var list = {
        load: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/list.htm',
            type: 'get'
        }
    }

    var del = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/delete.htm',
            type: 'post'
        }
    }

    var insert = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/create.htm',
            type: 'post'
        }
    }

    var update = {
        primary: 'id',
        save: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/update.htm',
            type: 'post'
        }
    }

    var view = {
        primary: 'id',
        load: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/view.htm',
            type: 'get'
        }
    }

    var confirm = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/confirm.htm',
            type: 'post'
        }
    }

    var refund = {
        save: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/refund.htm',
            type: 'post'
        }
    }

    var viewPhone = {
        load: {
            url: 'jiakao-misc://api/admin/xueche-win-prize-record/view-phone.htm',
            type: 'post'
        }
    }
   var  exportAward={
    load: {
        url: 'jiakao-misc://api/admin/xueche-win-prize-record/export-big-award-excel.htm',
        type: 'get'
    }
   }
    return {
        list: list,
        delete: del,
        update: update,
        insert: insert,
        view: view,
        confirm: confirm,
        refund: refund,
        viewPhone: viewPhone,
        exportAward:exportAward
    }

});
