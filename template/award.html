﻿<!--匹配标签-->
<style>
    .tag-map {
        padding: 5px 0;
    }

    .tag-map input {
        height: 32px;
        border-radius: 15px;
        margin-right: 5px;
    }

    .tag-map a {
        font-weight: bold;
        font-size: 18px;
        padding: 0 8px;
        height: 30px;
        width: 30px;
    }

    .tag-map .editBtn {
        width: inherit;
        font-size: 15px;
        line-height: 29px;
        margin-left: 10px;
    }

    .tag-map .tag-key-wrap {
        float: left;
        width: 43%;
        margin-right: 10px;
        height: 32px;
    }
</style>
<script id="jiakao-misc-template-award-main" type="text/html">

    <div class="clearfix tag-map" data-item="tag-map">
        <div class="tag-key-wrap"></div>
        <input data-item="tag-value" type="text" placeholder="value" value="<?=data[0]&&data[0].value?>">
        <a class="btn btn-sm btn-info" data-item="add-tag">+</a>
        <a class="btn btn-sm btn-info editBtn" data-item="edit-template"><?=data[0]&&data[0].value?'编辑':'设置'?></a>
    </div>
    <?for(var i=1; i<data.length; i++){?>
    <div class="clearfix tag-map" data-item="tag-map">
        <?include('jiakao-misc-template-award-item', {data: data[i]})?>
    </div>
    <?}?>
</script>

<script id="jiakao-misc-template-award-item" type="text/html">
    <div class="tag-key-wrap"></div>
    <input data-item="tag-value" type="text" value="<?=data&&data.value?>" placeholder="value">
    <a class="btn btn-sm btn-danger" data-item="del-tag">-</a>
    <a class="btn btn-sm btn-danger editBtn" data-item="edit-template"><?=data&&data.value?'编辑':'设置'?></a>
</script>
