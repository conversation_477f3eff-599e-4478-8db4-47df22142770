<style>
.cityReport tr,.cityReport{
    border:1px solid black ;
}
.cityReport tr{
    border-left:  none;
    text-align: center;
    

}
.cityReport th{
    text-align: center;
    font-weight: normal;
    white-space: nowrap;

}
.cityReport table{
    width: 100%;
    height:100%;

}
.cityReport{
    border-bottom:none;}
</style>
<script id="jiakao-misc-template-cityreport-main" type="text/html">
<div class='cityReport'>
 <table   cellpadding='5'>
     <tr style='border:none'><td colspan='4'><?=$method.cityName?>数据</td></tr>
    <tr >
        <th>行政区</th>
        <th>考场</th>
        <th>路线数</th>
        <th>转码状态</th>
    
    </tr>
    <?for(var i in list.data.list){?>
    <tr style='text-align:center;'>
        <td><?=list.data.list[i].areaName?></td>
        <td><?=list.data.list[i].name?></td>
        <td><?=list.data.list[i].itemCount?></td>
        <td><?=list.data.list[i].itemProcessCount?>/<?=list.data.list[i].itemCount?></td>
    </tr>
    <?}?>
    <tr style='text-align:center;'>
        <td colspan='4'>合计</td>
    </tr>
    <tr style='text-align:center;white-space: nowrap;'>
        <td>考场数:<?=list.data.metaCount?></td>
        <td colspan='2'>路线数：<?=list.data.itemCount?></td>
        <td >转码状态:<?if(list.data.itemFinish){?>已完成<?}
            else{
            ?>转码中<?}?></td>
    </tr>
 </table>
 


 
 </div>
 


</script>
 