<style>
    .list {
        display: flex;
        margin-top: 50px;
        flex-wrap: wrap;
    }

    .item {
        margin-left: 20px;
        height: 100px;
        width: 180px;
        background-color: #D7D7D7;
        padding: 10px;
        border-radius: 10px;
        margin-top: 20px;
    }

    .title {
        text-align: center;
        font-size: 16px;
    }

    .num {
        margin-top: 20px;
        text-align: center;
        font-weight: bold;
        font-size: 25px;
    }

    .right-btn {
        position: absolute;
        right: 0;
        display: flex;
    }

    .search-wraper {
        width: 350px;
        display: flex;
    }
</style>

<script id="jiakao-misc-template-examDataAdmin-main" type="text/html">
    <?
      var Utils = $method.Utils;
      var examData = data.examData;
  ?>
<h3><span data-item="description-big">考场数据管理<?=state?></span></h3>

<button class="btn btn-primary btn-sm" type="button" data-item="reload">刷新</button>

<div class="right-btn">
    <div class="col-sm-8" data-item="topic-select-city">
        需要插件支持
    </div>

    <div class="search-wraper">
        <input autocomplete="off" type="datetimepicker-date" name="beginTime" class="form-control" value=""
            placeholder="开始时间" data-item="beginTime">

        <input autocomplete="off" type="datetimepicker-date" name="endTime" class="form-control" value=""
            placeholder="结束时间" data-item="endTime">
    </div>

    <button class="btn btn-primary btn-sm" type="button" data-item="search">搜索</button>

</div>

<div class="list">
    <div class="item">
        <div class="title">考场数量</div>
        <div class="num">
            <?=examData.onlineMetaCount?> / <?=examData.metaCount?>
        </div>
    </div>

    <div class="item">
        <div class="title">路线数量</div>
        <div class="num">
            <?=examData.onlineItemCount?> / <?=examData.itemCount?>
        </div>
    </div>

    <div class="item">
        <div class="title">覆盖城市</div>
        <div class="num">
            <?=examData.openCityCount?> / <?=examData.cityCount?>
        </div>
    </div>

    <div class="item">
        <div class="title">更新考场</div>
        <div class="num">
            <?=examData.periodMetaCount?>
        </div>
    </div>

    <div class="item">
        <div class="title">更新路线</div>
        <div class="num">
            <?=examData.periodItemCount?>
        </div>
    </div>


    <div class="item">
        <div class="title">待更新考场(总)</div>
        <div class="num">
            <?=examData.updateMetaCount?>
        </div>
    </div>
    <div class="item">
        <div class="title">待更新路线(总)</div>
        <div class="num">
            <?=examData.updateItemCount?>
        </div>
    </div>

    <div class="item">
        <div class="title">待更新考场(新增)</div>
        <div class="num">
            <?=examData.periodUpdateMetaCount?>
        </div>
    </div>
    <div class="item">
        <div class="title">待更新路线(新增)</div>
        <div class="num">
            <?=examData.periodUpdateItemCount?>
        </div>
    </div>
</div>
</script>