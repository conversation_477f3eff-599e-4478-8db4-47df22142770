<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>加载中...</title>
</head>

<body>

    <script>
        var getCityData = function (call) {
            var src = 'https://web-resource.mucang.cn/public-data/city/city-area.js';
            var script = document.createElement('script');
            var old = document.getElementById('scriptcitydata');
            script.src = src;
            script.id = 'scriptcitydata';
            if (old) {
                old.parentNode.removeChild(old);
            }
            document.head.appendChild(script);
            window.getCDNCityData = call;

        }



        // 所有配置的路径最后都不以 "/" 结尾，程序会自动增加
        ! function () {

            // 加载成功之后执行的方法
            var success = function () {
                getCityData(function (data) {


                    console.log(data.initial);
                    var arr1 = [];

                    for (let i = 0, len = data.initial.length; i < len; ++i) {
                        let arr = data.initial[i][1]

                        for (let j = 0, len = arr.length; j < len; ++j) {
                            for (let k in arr[j].citys) {
                                if (arr[j].citys[k].counties == void 0) {
                                    arr[j].citys[k].counties = []
                                }
                            }
                            arr1.push({
                                name: arr[j].province.name,
                                code: arr[j].province.code,
                                pinyin: arr[j].province.pinyin,
                                cities: arr[j].citys
                            })
                        }

                    }

                    Object.defineProperty(Simple, "DISTRICT", {
                        value: arr1,
                        writable: false,
                        configurable: false
                    });
                })


                console.log(Simple);
            }

            var config = {

                // 产品标题
                title: '驾考混合项目',
                name: 'jiakao-misc',

                // true 则加载本地文件，默认从当前目录开始， false则从framework.root加载
                debug: false,

                // ajax.js判断host，如果找到匹配则替换，例：
                // host: {
                //     local: 'http://w3c.org'
                // }
                // Ajax.require('local://m.json') > Ajax.require('http://w3c.org/m.json');
                // PS：此配置从v1.1版本开始生效
                host: {
                    // API地址
                    local: 'https://jiakao-misc2.ttt.mucang.cn',
                    "jiakao-misc": 'https://jiakao-misc2.ttt.mucang.cn',
                    "jiakao-misc2": 'https://jiakao-misc2.ttt.mucang.cn',
                    "danmu": 'https://danmu.ttt.mucang.cn',
                    "monkey": 'https://monkey.ttt.mucang.cn',
                    "panda": "https://panda-admin.ttt.mucang.cn",
                    "mc-pan": "https://mc-pan.ttt.mucang.cn"
                },

                // 前端文件根目录，最后没有 "/"
                root: {
                    "jiakao-misc": window.location.href.replace(/\/[^/]*$/ig, ''),
                    "squirrel": 'https://admin.mucang.cn/squirrel.ttt.mucang.cn',
                    "danmu": 'https://admin.mucang.cn/danmu.ttt.mucang.cn',
                    "swallow": 'https://admin.mucang.cn/swallow.ttt.mucang.cn'
                },

                // 框架远程文件配置
                framework: {
                    // 远程文件路径，PS：不带版本目录
                    root: 'https://static.kakamobi.cn/simple-framework',
                    // 版本目录，{version: 1} 代表 "/v1/"
                    version: '1.1'
                },

                // 入口文件需要的JS和CSS
                file: {
                    js: [],
                    css: []
                }
            }

            var script = document.createElement('script');
            script.src = config.framework.root + '/v' + config.framework.version + '/resources/js/require.min.js';
            document.body.appendChild(script);

            script.onload = function () {
                require([config.framework.root + '/v' + config.framework.version + '/app/main.js'], function (
                    app) {
                    app.init(config, success);
                });
            }

        }();
    </script>

</body>

</html>