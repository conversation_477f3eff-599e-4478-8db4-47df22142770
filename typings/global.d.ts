declare module 'simple!core/store' {
    function Store(params: any): any;
    export = Store;
}

declare module 'simple!core/utils' {
    var exports: any;
    export = exports;
}

declare module '*.vue' {
    import type { DefineComponent } from 'vue'
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
    const component: DefineComponent<any, any, any>
    export default component
}