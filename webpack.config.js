const path = require('path');
const fs = require('fs');
const { VueLoaderPlugin } = require('vue-loader');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const Wpplugin = require('./wp-plugin');


/** 获取所有页面入口 */
function getAppEntries() {
	const mainDir = path.resolve(__dirname, 'app');
	const appDirs = fs.readdirSync(mainDir);
    const entries = {};
	appDirs.forEach(dir => {
		const entryFileNormal = path.resolve(mainDir, dir, 'index.js');
		const entryFileVue = path.resolve(mainDir, dir, 'index.vue.js');
		const entryFileVueTs = path.resolve(mainDir, dir, 'index.vue.ts');

		const fileConflict = () => fs.existsSync(entryFileNormal) && !fs.lstatSync(entryFileNormal).isSymbolicLink();

		if (fs.existsSync(entryFileVueTs)) {
			if (fileConflict()) {
				throw new Error('入口文件冲突: \n' + entryFileVueTs + '\n' + entryFileNormal);
			}
			entries[dir + '/index'] = entryFileVueTs;
		} else if (fs.existsSync(entryFileVue)) {
			if (fileConflict()) {
				throw new Error('入口文件冲突: \n' + entryFileVue + '\n' + entryFileNormal);
			}
			entries[dir + '/index'] = entryFileVue;
		}

	});

	return entries;
}

/** 获取项目名称 */
function getProjectName() {
	const indexFilePath = path.resolve(__dirname, 'index.html');
	const fileContent = fs.readFileSync(indexFilePath, 'utf8');
	const regx = /(name:\s*[\'\"](.+)[\'\"])/;
	const matches = fileContent.match(regx);
	const projectName = matches[2];
	console.log('项目名: ', projectName);
	return projectName;
}

const ProjectName = getProjectName();
const Entries = getAppEntries();




/** @type {import('webpack').Configuration} */
module.exports = {
	mode: 'development',
	devtool: 'source-map',
	entry: Entries,
	output: {
		path: path.resolve(__dirname, './dist'),
		filename: '[name].js',
		libraryTarget: 'amd',
		chunkLoadingGlobal: ProjectName
	},
	resolve: {
		alias: {
			":plugin": path.resolve(__dirname, "plugin"),
			":utils": path.resolve(__dirname, "utils"),
		}
	},
	externals: [/^simple!/, /^jiakao-misc!/],
	module: {
		rules: [
			{
				test: /\.(js|ts)$/,
				exclude: /node_modules/,
				use: 'babel-loader'
			},
			{
				test: /\.vue$/,
				use: ['vue-loader']
			},
			{
				test: /\.css$/,
				use: [
					'vue-style-loader',
					'css-loader'
				]
			},
			{
				test: /\.less$/,
				use: [
					'vue-style-loader',
					'css-loader',
					'less-loader'
				]
			},
			{
				test: /\.(png|jpg|gif|svg)$/,
				type: 'asset/resource',
			}
		]
	},
	plugins: [
		new CleanWebpackPlugin(),
		new VueLoaderPlugin(),
		new Wpplugin({
			projectName: ProjectName,
			entries: Entries
		})
	],
	optimization: {
		chunkIds: 'named',
		moduleIds: 'named',
		runtimeChunk: {
			name: 'runtime'
		},
		splitChunks: {
			chunks: 'all',
			cacheGroups: {
				commons: {
					name: 'commons',
					chunks: 'initial',
					minChunks: 2,
					priority: -2,
					maxSize: 0
				},
				vendors: {
					chunks: 'initial',
					test: /[\\/]node_modules[\\/]/,
					name: 'vendors',
					priority: -1
				},
			}
		}
	}
}