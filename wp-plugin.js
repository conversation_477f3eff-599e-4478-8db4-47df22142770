const path = require('path');
const fs = require('fs');
const { ReplaceSource } = require('webpack-sources');

class wpPlugin {

	constructor(options) {
		this.projectName = options.projectName
		this.entries = options.entries
	}

	/**
	 * 生成app入口重定向的txt记录文件
	 */
	generateRedirectRecords() {
		const redirectTxtPath = path.resolve(__dirname, 'redirect.txt');
		fs.writeFileSync(redirectTxtPath, Object.keys(this.entries).map(entry => 'app/' + entry).join('\n'));
	}

	/** 
	 * initialChunks本来只能在index.html中通过注入script和link标签来引入;
	 * 但是因为我们的入口生成的是amd模块，所以可以直接在入口amd模块中一并引入
	 * 
	 * @param {import('webpack').Compilation} compilation
	 */
	appendInitialChunks(compilation) {
		const assets = compilation.assets;

		compilation.entrypoints.forEach((entrypoint, entrypointName) => {
			const initialChunks = entrypoint.chunks.map(chunk => chunk.name).filter(name => name !== entrypointName);

			if (!initialChunks.length) {
				return;
			}

			const insertStr = initialChunks
				.map(chunkName => this.projectName + '!dist/' + chunkName)
				.map(modulePath => '"' + modulePath + '"')
				.join(',');

			const assetPath = entrypointName + '.js';

			let source = assets[assetPath];
			const code = source.source();

			// 修改 define([...]) 为 define(['test2!dist/runtime', 'test2!dist/vendors', ...])
			const regx1 = /(define\(\[.+?\])/;
			// 修改 define(() => {...}) 为 define(['test2!dist/runtime', 'test2!dist/vendors'], () => {...})
			const regx2 = /(define\(\(\)?)/;
			const matches1 = code.match(regx1);
			if (matches1) {
				const pos = matches1.index + matches1[1].length - 1;
				source = new ReplaceSource(source);
				source.insert(pos, ', ' + insertStr);
				assets[assetPath] = source;
				return;
			}
			const matches2 = code.match(regx2);
			if (matches2) {
				const pos = matches2.index + matches2[1].length - 2;
				source = new ReplaceSource(source);
				source.insert(pos, '[' + insertStr + '],');
				assets[assetPath] = source;
			}
		});
	}

	apply(compiler) {

		compiler.hooks.emit.tap('wpPlugin', (compilation) => {
			this.appendInitialChunks(compilation);
		})

		compiler.hooks.done.tap('wpPlugin', (compilation) => {
			this.generateRedirectRecords();
		})
	}

}

module.exports = wpPlugin;
